<?php
/**
 * إدارة الصلاحيات والأدوار المحسن
 * Enhanced Permissions and Roles Management
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/enhanced_permissions.php';

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من الصلاحيات (مدير فقط)
check_session();
require_permission('settings_permissions', 'full');

$success_message = '';
$error_message = '';

// معالجة تحديث الدور الأساسي
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_role'])) {
    $user_id = intval($_POST['user_id']);
    $new_role = clean_input($_POST['role']);
    
    // الحصول على قائمة الأدوار المتاحة
    $available_roles = get_available_roles();
    
    if ($user_id > 0 && array_key_exists($new_role, $available_roles)) {
        // الحصول على الدور القديم
        $old_data = get_user_data($user_id);
        
        if ($old_data) {
            // تحديث الدور
            $stmt = $conn->prepare("UPDATE users SET role = ? WHERE id = ?");
            $stmt->bind_param("si", $new_role, $user_id);
            
            if ($stmt->execute()) {
                // تسجيل التغيير في سجل المراجعة
                log_permission_audit('role_changed', null, $old_data['role'], $new_role, $_SESSION['user_id'], "تم تغيير دور المستخدم " . $old_data['full_name']);
                
                $success_message = "تم تحديث دور المستخدم بنجاح";
            } else {
                $error_message = "خطأ في تحديث الدور";
            }
        }
    }
}

// معالجة تحديث الصلاحيات المخصصة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_custom_permissions'])) {
    $user_id = intval($_POST['user_id']);
    $permissions = $_POST['permissions'] ?? [];
    
    if ($user_id > 0) {
        $conn->begin_transaction();
        try {
            // حذف الصلاحيات الحالية
            $delete_stmt = $conn->prepare("DELETE FROM user_custom_permissions WHERE user_id = ?");
            $delete_stmt->bind_param("i", $user_id);
            $delete_stmt->execute();
            
            // إضافة الصلاحيات الجديدة
            $insert_stmt = $conn->prepare("
                INSERT INTO user_custom_permissions
                (user_id, resource_key, permission_level, is_granted, granted_by, granted_at)
                VALUES (?, ?, ?, 1, ?, NOW())
            ");
            
            foreach ($permissions as $resource_key => $permission_level) {
                if (!empty($permission_level) && $permission_level !== 'none') {
                    $insert_stmt->bind_param("issi", $user_id, $resource_key, $permission_level, $_SESSION['user_id']);
                    $insert_stmt->execute();
                }
            }
            
            $conn->commit();
            
            // تسجيل التغيير
            log_permission_audit('permission_granted', null, null, count($permissions), $_SESSION['user_id'], "تم تحديث الصلاحيات المخصصة");
            
            $success_message = "تم تحديث الصلاحيات المخصصة بنجاح";
        } catch (Exception $e) {
            $conn->rollback();
            $error_message = "خطأ في تحديث الصلاحيات: " . $e->getMessage();
        }
    }
}

// الحصول على قائمة الأدوار المتاحة
function get_available_roles() {
    global $conn;
    
    $roles = [];
    $result = $conn->query("SELECT role_name, role_display_name FROM custom_roles WHERE is_active = 1 ORDER BY role_display_name");
    
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $roles[$row['role_name']] = $row['role_display_name'];
        }
    }
    
    return $roles;
}

// الحصول على قائمة الموارد
function get_system_resources() {
    global $conn;
    
    $resources = [];
    $result = $conn->query("
        SELECT resource_key, resource_name, resource_type, resource_description, parent_resource, icon
        FROM system_resources 
        WHERE is_active = 1 
        ORDER BY resource_type, sort_order, resource_name
    ");
    
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $resources[] = $row;
        }
    }
    
    return $resources;
}

// الحصول على صلاحيات المستخدم الحالية
function get_user_permissions($user_id) {
    global $conn;
    
    $permissions = [];
    $result = $conn->query("
        SELECT resource_key, permission_level 
        FROM user_custom_permissions 
        WHERE user_id = $user_id AND is_granted = 1
        AND (expires_at IS NULL OR expires_at > NOW())
    ");
    
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $permissions[$row['resource_key']] = $row['permission_level'];
        }
    }
    
    return $permissions;
}

// جلب المستخدمين
$users_query = "
    SELECT u.id, u.full_name, u.username, u.email, u.role, u.status,
           cr.role_display_name as custom_role_name
    FROM users u
    LEFT JOIN custom_roles cr ON u.custom_role_id = cr.id
    WHERE u.status != 'deleted' 
    ORDER BY u.full_name
";
$users_result = $conn->query($users_query);

// جلب الموارد
$system_resources = get_system_resources();
$available_roles = get_available_roles();

$page_title = 'إدارة الصلاحيات والأدوار المحسن';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-shield-alt me-2 text-primary"></i><?php echo $page_title; ?></h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="../settings/">الإعدادات</a></li>
                    <li class="breadcrumb-item active">إدارة الصلاحيات</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="roles_manager.php" class="btn btn-outline-primary">
                <i class="fas fa-user-tag me-1"></i>إدارة الأدوار
            </a>
            <a href="permissions_audit.php" class="btn btn-outline-info">
                <i class="fas fa-history me-1"></i>سجل التدقيق
            </a>
        </div>
    </div>

    <!-- رسائل التنبيه -->
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $users_result->num_rows; ?></h4>
                            <p class="mb-0">إجمالي المستخدمين</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo count($available_roles); ?></h4>
                            <p class="mb-0">الأدوار المتاحة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-tag fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo count($system_resources); ?></h4>
                            <p class="mb-0">موارد النظام</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-cogs fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>
                                <?php 
                                $custom_perms = $conn->query("SELECT COUNT(*) as count FROM user_custom_permissions WHERE is_granted = 1")->fetch_assoc();
                                echo $custom_perms['count'];
                                ?>
                            </h4>
                            <p class="mb-0">الصلاحيات المخصصة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-key fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- قائمة المستخدمين -->
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-users me-2"></i>إدارة صلاحيات المستخدمين</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="usersTable">
                            <thead>
                                <tr>
                                    <th>المستخدم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الدور الحالي</th>
                                    <th>الحالة</th>
                                    <th>الصلاحيات المخصصة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                $users_result->data_seek(0); // إعادة تعيين المؤشر
                                while ($user = $users_result->fetch_assoc()): 
                                    $user_permissions = get_user_permissions($user['id']);
                                ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                <?php echo strtoupper(substr($user['full_name'], 0, 1)); ?>
                                            </div>
                                            <div>
                                                <strong><?php echo htmlspecialchars($user['full_name']); ?></strong>
                                                <br><small class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo match($user['role']) {
                                                'admin' => 'danger',
                                                'financial_manager' => 'success',
                                                'teacher' => 'primary',
                                                'staff' => 'info',
                                                'student' => 'secondary',
                                                'parent' => 'warning',
                                                default => 'light'
                                            };
                                        ?>">
                                            <?php echo $available_roles[$user['role']] ?? $user['role']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                            <?php echo $user['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?php echo count($user_permissions); ?> صلاحية
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary" 
                                                    onclick="editUserRole(<?php echo $user['id']; ?>, '<?php echo $user['role']; ?>')">
                                                <i class="fas fa-edit"></i> تعديل الدور
                                            </button>
                                            <button type="button" class="btn btn-outline-success" 
                                                    onclick="editUserPermissions(<?php echo $user['id']; ?>)">
                                                <i class="fas fa-key"></i> الصلاحيات المخصصة
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    <!-- مودال تعديل الدور -->
<div class="modal fade" id="editRoleModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل دور المستخدم</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="user_id" id="editRoleUserId">
                    <input type="hidden" name="update_role" value="1">

                    <div class="mb-3">
                        <label for="editRoleSelect" class="form-label">اختر الدور الجديد</label>
                        <select class="form-select" name="role" id="editRoleSelect" required>
                            <option value="">-- اختر الدور --</option>
                            <?php foreach ($available_roles as $role_key => $role_name): ?>
                                <option value="<?php echo $role_key; ?>"><?php echo $role_name; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> تغيير الدور سيؤثر على جميع صلاحيات المستخدم.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- مودال تعديل الصلاحيات المخصصة -->
<div class="modal fade" id="editPermissionsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إدارة الصلاحيات المخصصة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="user_id" id="editPermissionsUserId">
                    <input type="hidden" name="update_custom_permissions" value="1">

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> الصلاحيات المخصصة لها أولوية أعلى من صلاحيات الدور الافتراضية.
                    </div>

                    <div id="permissionsContainer">
                        <!-- سيتم ملء هذا القسم بالجافا سكريبت -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">حفظ الصلاحيات</button>
                </div>
            </form>
        </div>
    </div>
</div>

</div>
</div>

<script>
// بيانات الموارد والصلاحيات
const systemResources = <?php echo json_encode($system_resources); ?>;
const permissionLevels = {
    'none': 'بدون صلاحية',
    'read': 'قراءة فقط',
    'write': 'قراءة وكتابة',
    'full': 'صلاحية كاملة'
};

// تعديل دور المستخدم
function editUserRole(userId, currentRole) {
    document.getElementById('editRoleUserId').value = userId;
    document.getElementById('editRoleSelect').value = currentRole;

    const modal = new bootstrap.Modal(document.getElementById('editRoleModal'));
    modal.show();
}

// تعديل صلاحيات المستخدم
function editUserPermissions(userId) {
    document.getElementById('editPermissionsUserId').value = userId;

    // جلب الصلاحيات الحالية للمستخدم
    fetch(`get_user_permissions.php?user_id=${userId}`)
        .then(response => response.json())
        .then(data => {
            buildPermissionsForm(data.permissions || {});
            const modal = new bootstrap.Modal(document.getElementById('editPermissionsModal'));
            modal.show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في جلب الصلاحيات');
        });
}

// بناء نموذج الصلاحيات
function buildPermissionsForm(userPermissions) {
    const container = document.getElementById('permissionsContainer');
    container.innerHTML = '';

    // تجميع الموارد حسب النوع
    const resourcesByType = {};
    systemResources.forEach(resource => {
        if (!resourcesByType[resource.resource_type]) {
            resourcesByType[resource.resource_type] = [];
        }
        resourcesByType[resource.resource_type].push(resource);
    });

    // إنشاء تبويبات للأنواع المختلفة
    const tabsHtml = `
        <ul class="nav nav-tabs" id="permissionsTabs" role="tablist">
            ${Object.keys(resourcesByType).map((type, index) => `
                <li class="nav-item" role="presentation">
                    <button class="nav-link ${index === 0 ? 'active' : ''}"
                            id="${type}-tab" data-bs-toggle="tab" data-bs-target="#${type}-pane"
                            type="button" role="tab">
                        ${getTypeDisplayName(type)}
                    </button>
                </li>
            `).join('')}
        </ul>
        <div class="tab-content" id="permissionsTabContent">
            ${Object.entries(resourcesByType).map(([type, resources], index) => `
                <div class="tab-pane fade ${index === 0 ? 'show active' : ''}"
                     id="${type}-pane" role="tabpanel">
                    <div class="mt-3">
                        ${resources.map(resource => `
                            <div class="row mb-2 align-items-center">
                                <div class="col-md-6">
                                    <label class="form-label">
                                        <i class="${resource.icon || 'fas fa-cog'} me-2"></i>
                                        ${resource.resource_name}
                                    </label>
                                    <small class="text-muted d-block">${resource.resource_description || ''}</small>
                                </div>
                                <div class="col-md-6">
                                    <select class="form-select form-select-sm"
                                            name="permissions[${resource.resource_key}]">
                                        <option value="">-- استخدام صلاحية الدور --</option>
                                        ${Object.entries(permissionLevels).map(([level, name]) => `
                                            <option value="${level}"
                                                    ${userPermissions[resource.resource_key] === level ? 'selected' : ''}>
                                                ${name}
                                            </option>
                                        `).join('')}
                                    </select>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `).join('')}
        </div>
    `;

    container.innerHTML = tabsHtml;
}

// الحصول على اسم النوع للعرض
function getTypeDisplayName(type) {
    const typeNames = {
        'page': 'الصفحات',
        'action': 'الإجراءات',
        'data': 'البيانات',
        'report': 'التقارير',
        'feature': 'الميزات'
    };
    return typeNames[type] || type;
}

// تهيئة DataTable
$(document).ready(function() {
    $('#usersTable').DataTable({
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
        },
        pageLength: 25,
        order: [[0, 'asc']],
        columnDefs: [
            { orderable: false, targets: [5] }
        ]
    });
});
</script>

<?php include_once '../includes/footer.php'; ?>
