<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    header('Location: ../dashboard/');
    exit();
}

$error_message = '';
$success_message = '';

// الحصول على المراحل الدراسية
$stages_query = "SELECT id, stage_name, stage_code FROM educational_stages WHERE status = 'active' ORDER BY sort_order";
$stages_result = $conn->query($stages_query);
$stages = [];
if ($stages_result) {
    while ($row = $stages_result->fetch_assoc()) {
        $stages[] = $row;
    }
}

// الحصول على الصفوف الدراسية
$grades_query = "
    SELECT g.id, g.grade_name, g.grade_code, g.stage_id, es.stage_name
    FROM grades g
    INNER JOIN educational_stages es ON g.stage_id = es.id
    WHERE g.status = 'active'
    ORDER BY es.sort_order, g.sort_order
";
$grades_result = $conn->query($grades_query);
$grades = [];
if ($grades_result) {
    while ($row = $grades_result->fetch_assoc()) {
        $grades[] = $row;
    }
}

// الحصول على المعاملات من URL
$selected_stage_id = isset($_GET['stage_id']) ? intval($_GET['stage_id']) : 0;
$selected_grade_id = isset($_GET['grade_id']) ? intval($_GET['grade_id']) : 0;

// الحصول على المواد المتاحة
$subjects_query = "
    SELECT s.id, s.subject_name, s.subject_code, g.grade_name, es.stage_name
    FROM subjects s
    LEFT JOIN grades g ON s.grade_id = g.id
    LEFT JOIN educational_stages es ON s.stage_id = es.id
    WHERE s.status = 'active'
    ORDER BY es.sort_order, g.sort_order, s.subject_name
";
$subjects_result = $conn->query($subjects_query);
$subjects = [];
if ($subjects_result) {
    while ($row = $subjects_result->fetch_assoc()) {
        $subjects[] = $row;
    }
}

// معالجة إضافة الفصل
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $class_name = clean_input($_POST['class_name'] ?? '');
    $grade_level = clean_input($_POST['grade_level'] ?? '');
    $stage_id = intval($_POST['stage_id'] ?? 0);
    $grade_id = intval($_POST['grade_id'] ?? 0);
    $capacity = intval($_POST['capacity'] ?? 0);
    $section = clean_input($_POST['section'] ?? '');
    $room_number = clean_input($_POST['room_number'] ?? '');
    $description = clean_input($_POST['description'] ?? '');
    $selected_subjects = isset($_POST['subject_ids']) ? $_POST['subject_ids'] : [];
    $status = 'active';

    $errors = [];
    if (empty($class_name)) {
        $errors[] = __('class_name') . ' ' . __('required_field');
    }
    if (empty($grade_level)) {
        $errors[] = __('grade_level') . ' ' . __('required_field');
    }
    if ($stage_id <= 0) {
        $errors[] = __('stage') . ' ' . __('required_field');
    }
    if ($grade_id <= 0) {
        $errors[] = __('grade') . ' ' . __('required_field');
    }
    if ($capacity <= 0) {
        $errors[] = __('capacity') . ' ' . __('required_field');
    }

    if (empty($errors)) {
        global $conn;

        // التحقق من وجود عمود description
        $check_column_query = "SHOW COLUMNS FROM classes LIKE 'description'";
        $check_result = $conn->query($check_column_query);
        $has_description = $check_result && $check_result->num_rows > 0;

        if ($has_description) {
            // إذا كان العمود موجوداً، استخدم الاستعلام الكامل
            $stmt = $conn->prepare("INSERT INTO classes (class_name, grade_level, stage_id, grade_id, capacity, section, room_number, description, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())");
            $stmt->bind_param("ssiiiisss", $class_name, $grade_level, $stage_id, $grade_id, $capacity, $section, $room_number, $description, $status);
        } else {
            // إذا لم يكن العمود موجوداً، استخدم استعلام بدون description
            $stmt = $conn->prepare("INSERT INTO classes (class_name, grade_level, stage_id, grade_id, capacity, section, room_number, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())");
            $stmt->bind_param("ssiiiiss", $class_name, $grade_level, $stage_id, $grade_id, $capacity, $section, $room_number, $status);
        }

        if ($stmt->execute()) {
            $class_id = $conn->insert_id;

            // إضافة التكليفات للمواد المحددة
            if (!empty($selected_subjects)) {
                $assignment_query = "INSERT INTO teacher_assignments (teacher_id, class_id, subject_id, status, weekly_hours, academic_year_id, semester, assigned_at, created_at, updated_at) VALUES (?, ?, ?, 'active', 4, 1, 'first', NOW(), NOW(), NOW())";
                $assignment_stmt = $conn->prepare($assignment_query);

                // للآن سنستخدم معلم افتراضي (ID = 1) - يمكن تحسين هذا لاحقاً
                $default_teacher_id = 1;

                foreach ($selected_subjects as $subject_id) {
                    $subject_id = intval($subject_id);
                    if ($subject_id > 0) {
                        $assignment_stmt->bind_param('iii', $default_teacher_id, $class_id, $subject_id);
                        $assignment_stmt->execute();
                    }
                }
                $assignment_stmt->close();
            }

            $_SESSION['success_message'] = __('class_added_successfully');
            header('Location: index.php');
            exit();
        } else {
            $error_message = __('error_occurred') . ': ' . $stmt->error;
        }
    } else {
        $error_message = implode('<br>', $errors);
    }
}

// تضمين header بعد معالجة النموذج
$page_title = __('add_class');
require_once '../includes/header.php';
?>
<link rel="stylesheet" href="../assets/css/modern-style.css">
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('add_class'); ?></h1>
            <p class="text-muted"><?php echo __('add_new_class_info'); ?></p>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
            </a>
        </div>
    </div>
    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php elseif (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $_SESSION['success_message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['success_message']); ?>
    <?php endif; ?>
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-school me-2"></i><?php echo __('class_information'); ?>
            </h5>
        </div>
        <div class="card-body">
            <form method="POST" class="needs-validation" novalidate>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="class_name" class="form-label"><?php echo __('class_name'); ?> <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="class_name" name="class_name" value="<?php echo htmlspecialchars($_POST['class_name'] ?? ''); ?>" required>
                        <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="stage_id" class="form-label"><?php echo __('educational_stage'); ?> <span class="text-danger">*</span></label>
                        <select class="form-select" id="stage_id" name="stage_id" required onchange="updateGrades()">
                            <option value=""><?php echo __('select_stage'); ?></option>
                            <?php foreach ($stages as $stage): ?>
                                <option value="<?php echo $stage['id']; ?>"
                                        <?php echo (isset($_POST['stage_id']) ? $_POST['stage_id'] : $selected_stage_id) == $stage['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($stage['stage_name']); ?> (<?php echo htmlspecialchars($stage['stage_code']); ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="grade_id" class="form-label"><?php echo __('school_grade'); ?> <span class="text-danger">*</span></label>
                        <select class="form-select" id="grade_id" name="grade_id" required>
                            <option value=""><?php echo __('select_grade_first'); ?></option>
                            <?php foreach ($grades as $grade): ?>
                                <option value="<?php echo $grade['id']; ?>"
                                        data-stage="<?php echo $grade['stage_id']; ?>"
                                        <?php echo (isset($_POST['grade_id']) ? $_POST['grade_id'] : $selected_grade_id) == $grade['id'] ? 'selected' : ''; ?>
                                        style="display: none;">
                                    <?php echo htmlspecialchars($grade['grade_name']); ?> (<?php echo htmlspecialchars($grade['grade_code']); ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                        <div class="form-text"><?php echo __('select_stage_first'); ?></div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="grade_level" class="form-label"><?php echo __('grade_level'); ?> <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="grade_level" name="grade_level" value="<?php echo htmlspecialchars($_POST['grade_level'] ?? ''); ?>" required>
                        <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="section" class="form-label"><?php echo __('section'); ?></label>
                        <input type="text" class="form-control" id="section" name="section" value="<?php echo htmlspecialchars($_POST['section'] ?? ''); ?>" placeholder="<?php echo __('optional'); ?>">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="capacity" class="form-label"><?php echo __('capacity'); ?> <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="capacity" name="capacity" value="<?php echo htmlspecialchars($_POST['capacity'] ?? '30'); ?>" required min="1" max="100">
                        <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="room_number" class="form-label"><?php echo __('room_number'); ?></label>
                        <input type="text" class="form-control" id="room_number" name="room_number" value="<?php echo htmlspecialchars($_POST['room_number'] ?? ''); ?>" placeholder="<?php echo __('optional'); ?>">
                    </div>
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label"><?php echo __('description'); ?></label>
                    <textarea class="form-control" id="description" name="description" rows="3" placeholder="<?php echo __('optional'); ?>"><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                </div>

                <!-- المواد المرتبطة بالفصل -->
                <div class="mb-3">
                    <label for="subject_ids" class="form-label">
                        <i class="fas fa-book me-2"></i>المواد المرتبطة بالفصل
                        <small class="text-muted">(اختياري)</small>
                    </label>
                    <select class="form-select" id="subject_ids" name="subject_ids[]" multiple size="6">
                        <?php
                        $current_stage = '';
                        foreach ($subjects as $subject):
                            // إضافة عنوان المرحلة إذا تغيرت
                            if ($current_stage != $subject['stage_name'] && !empty($subject['stage_name'])) {
                                if ($current_stage != '') echo '</optgroup>';
                                echo '<optgroup label="' . htmlspecialchars($subject['stage_name']) . '">';
                                $current_stage = $subject['stage_name'];
                            }

                            // تحديد النص المعروض للمادة
                            $display_text = $subject['subject_name'];
                            if (!empty($subject['subject_code'])) {
                                $display_text .= ' (' . $subject['subject_code'] . ')';
                            }
                            if (!empty($subject['grade_name'])) {
                                $display_text .= ' - ' . $subject['grade_name'];
                            }

                            $selected = isset($_POST['subject_ids']) && in_array($subject['id'], $_POST['subject_ids']) ? 'selected' : '';
                        ?>
                            <option value="<?php echo $subject['id']; ?>" <?php echo $selected; ?>>
                                <?php echo htmlspecialchars($display_text); ?>
                            </option>
                        <?php endforeach; ?>
                        <?php if ($current_stage != '') echo '</optgroup>'; ?>
                    </select>
                    <div class="form-text">
                        <i class="fas fa-info-circle me-1"></i>
                        اضغط Ctrl (أو Cmd) + النقر لاختيار عدة مواد. سيتم ربط الفصل بالمواد المحددة تلقائياً.
                    </div>
                </div>
                <div class="d-flex justify-content-end gap-2">
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i><?php echo __('add_class'); ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function updateGrades() {
    const stageSelect = document.getElementById('stage_id');
    const gradeSelect = document.getElementById('grade_id');
    const selectedStage = stageSelect.value;

    // إخفاء جميع الخيارات
    const gradeOptions = gradeSelect.querySelectorAll('option[data-stage]');
    gradeOptions.forEach(option => {
        option.style.display = 'none';
        option.selected = false;
    });

    // إعادة تعيين القيمة
    gradeSelect.value = '';

    if (selectedStage) {
        // إظهار الصفوف المرتبطة بالمرحلة المختارة
        const relevantOptions = gradeSelect.querySelectorAll(`option[data-stage="${selectedStage}"]`);
        relevantOptions.forEach(option => {
            option.style.display = 'block';
        });
    }
}

// إضافة مؤشر بصري للمواد المختارة
function updateSubjectsDisplay() {
    const subjectsSelect = document.getElementById('subject_ids');
    const selectedCount = subjectsSelect.selectedOptions.length;
    const label = document.querySelector('label[for="subject_ids"]');

    if (selectedCount > 0) {
        label.innerHTML = `<i class="fas fa-book me-2"></i>المواد المرتبطة بالفصل <span class="badge bg-primary">${selectedCount}</span> <small class="text-muted">(اختياري)</small>`;
    } else {
        label.innerHTML = `<i class="fas fa-book me-2"></i>المواد المرتبطة بالفصل <small class="text-muted">(اختياري)</small>`;
    }
}

// تشغيل التحديث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateGrades();

    // إضافة مستمع لتحديث عرض المواد
    const subjectsSelect = document.getElementById('subject_ids');
    if (subjectsSelect) {
        subjectsSelect.addEventListener('change', updateSubjectsDisplay);
        updateSubjectsDisplay(); // تحديث أولي
    }
});
</script>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        Array.prototype.forEach.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<style>
/* تحسين مظهر حقل المواد */
#subject_ids {
    min-height: 150px;
}

#subject_ids optgroup {
    font-weight: bold;
    color: #495057;
    background-color: #f8f9fa;
}

#subject_ids option {
    padding: 8px 12px;
    font-weight: normal;
}

#subject_ids option:checked {
    background-color: #007bff;
    color: white;
}

/* تحسين عرض الشارات */
.badge {
    font-size: 0.75em;
    vertical-align: middle;
}

/* تحسين النص التوضيحي */
.form-text {
    font-size: 0.875em;
    margin-top: 0.5rem;
}
</style>
<?php require_once '../includes/footer.php'; ?> 