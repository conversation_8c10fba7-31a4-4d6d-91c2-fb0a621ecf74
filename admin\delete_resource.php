<?php
/**
 * حذف مورد النظام - صفحة تأكيد مستقلة
 * Delete System Resource - Standalone Confirmation Page
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

// التحقق من معرف المورد
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: system_resources.php');
    exit();
}

$resource_id = intval($_GET['id']);
$success_message = '';
$error_message = '';

// جلب بيانات المورد
$resource_stmt = $conn->prepare("SELECT * FROM system_resources WHERE id = ?");
$resource_stmt->bind_param("i", $resource_id);
$resource_stmt->execute();
$resource = $resource_stmt->get_result()->fetch_assoc();

if (!$resource) {
    header('Location: system_resources.php');
    exit();
}

// التحقق من الاستخدام
$usage_stmt = $conn->prepare("SELECT COUNT(*) as count FROM user_custom_permissions WHERE permission_key = ?");
$usage_stmt->bind_param("s", $resource['resource_key']);
$usage_stmt->execute();
$usage_count = $usage_stmt->get_result()->fetch_assoc()['count'];

// معالجة الحذف
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_delete'])) {
    if ($usage_count > 0) {
        $error_message = "لا يمكن حذف هذا المورد لأنه مستخدم من قبل $usage_count مستخدم";
    } else {
        $stmt = $conn->prepare("DELETE FROM system_resources WHERE id = ?");
        $stmt->bind_param("i", $resource_id);
        
        if ($stmt->execute()) {
            header('Location: system_resources.php?deleted=1');
            exit();
        } else {
            $error_message = "خطأ في حذف المورد: " . $conn->error;
        }
    }
}

$page_title = 'حذف المورد: ' . $resource['resource_name'];
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-trash-alt me-2 text-danger"></i><?php echo $page_title; ?></h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="../admin/">الإدارة</a></li>
                    <li class="breadcrumb-item"><a href="system_resources.php">موارد النظام</a></li>
                    <li class="breadcrumb-item active">حذف المورد</li>
                </ol>
            </nav>
        </div>
        <a href="system_resources.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
        </a>
    </div>

    <!-- رسائل التنبيه -->
    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <?php if ($usage_count > 0): ?>
                <!-- المورد مستخدم - لا يمكن حذفه -->
                <div class="card border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>لا يمكن حذف هذا المورد</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-info-circle me-2"></i>المورد مستخدم حالياً</h6>
                            <p class="mb-0">
                                هذا المورد مستخدم من قبل <strong><?php echo $usage_count; ?></strong> مستخدم. 
                                يجب إزالة جميع الصلاحيات المرتبطة به قبل الحذف.
                            </p>
                        </div>

                        <!-- معلومات المورد -->
                        <div class="card bg-light">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>معلومات المورد:</h6>
                                        <table class="table table-borderless table-sm">
                                            <tr>
                                                <td><strong>النوع:</strong></td>
                                                <td>
                                                    <span class="badge bg-<?php 
                                                        echo match($resource['resource_type']) {
                                                            'page' => 'primary',
                                                            'action' => 'success',
                                                            'data' => 'info',
                                                            'report' => 'warning',
                                                            default => 'secondary'
                                                        };
                                                    ?>">
                                                        <?php 
                                                        $type_names = [
                                                            'page' => 'صفحة',
                                                            'action' => 'إجراء',
                                                            'data' => 'بيانات',
                                                            'report' => 'تقرير'
                                                        ];
                                                        echo $type_names[$resource['resource_type']] ?? $resource['resource_type'];
                                                        ?>
                                                    </span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>المفتاح:</strong></td>
                                                <td><code><?php echo htmlspecialchars($resource['resource_key']); ?></code></td>
                                            </tr>
                                            <tr>
                                                <td><strong>الاسم:</strong></td>
                                                <td><?php echo htmlspecialchars($resource['resource_name']); ?></td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>الاستخدام:</h6>
                                        <div class="text-center">
                                            <div class="display-4 text-warning"><?php echo $usage_count; ?></div>
                                            <p class="text-muted">مستخدم لديه هذه الصلاحية</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between mt-4">
                            <a href="system_resources.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
                            </a>
                            <div>
                                <a href="edit_resource.php?id=<?php echo $resource_id; ?>" class="btn btn-primary me-2">
                                    <i class="fas fa-edit me-2"></i>تعديل المورد
                                </a>
                                <a href="permissions_manager.php" class="btn btn-info">
                                    <i class="fas fa-users me-2"></i>إدارة الصلاحيات
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <!-- المورد غير مستخدم - يمكن حذفه -->
                <div class="card border-danger">
                    <div class="card-header bg-danger text-white">
                        <h5><i class="fas fa-trash-alt me-2"></i>تأكيد حذف المورد</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>تحذير: هذا الإجراء لا يمكن التراجع عنه!</h6>
                            <p class="mb-0">
                                سيتم حذف المورد نهائياً من النظام. تأكد من أنك تريد المتابعة.
                            </p>
                        </div>

                        <!-- معلومات المورد -->
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6>المورد المراد حذفه:</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <table class="table table-borderless table-sm">
                                            <tr>
                                                <td><strong>النوع:</strong></td>
                                                <td>
                                                    <span class="badge bg-<?php 
                                                        echo match($resource['resource_type']) {
                                                            'page' => 'primary',
                                                            'action' => 'success',
                                                            'data' => 'info',
                                                            'report' => 'warning',
                                                            default => 'secondary'
                                                        };
                                                    ?>">
                                                        <?php 
                                                        $type_names = [
                                                            'page' => 'صفحة',
                                                            'action' => 'إجراء',
                                                            'data' => 'بيانات',
                                                            'report' => 'تقرير'
                                                        ];
                                                        echo $type_names[$resource['resource_type']] ?? $resource['resource_type'];
                                                        ?>
                                                    </span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>المفتاح:</strong></td>
                                                <td><code><?php echo htmlspecialchars($resource['resource_key']); ?></code></td>
                                            </tr>
                                            <tr>
                                                <td><strong>الاسم:</strong></td>
                                                <td><?php echo htmlspecialchars($resource['resource_name']); ?></td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <table class="table table-borderless table-sm">
                                            <tr>
                                                <td><strong>الوصف:</strong></td>
                                                <td><?php echo htmlspecialchars($resource['resource_description'] ?: 'لا يوجد'); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>المسار:</strong></td>
                                                <td><?php echo htmlspecialchars($resource['resource_path'] ?: 'لا يوجد'); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>تاريخ الإنشاء:</strong></td>
                                                <td><?php echo date('Y-m-d', strtotime($resource['created_at'])); ?></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- نموذج التأكيد -->
                        <form method="POST" class="mt-4">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                                <label class="form-check-label" for="confirmDelete">
                                    <strong>أؤكد أنني أريد حذف هذا المورد نهائياً</strong>
                                </label>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="system_resources.php" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>إلغاء
                                </a>
                                <div>
                                    <a href="edit_resource.php?id=<?php echo $resource_id; ?>" class="btn btn-outline-primary me-2">
                                        <i class="fas fa-edit me-2"></i>تعديل بدلاً من الحذف
                                    </a>
                                    <button type="submit" name="confirm_delete" class="btn btn-danger" id="deleteButton" disabled>
                                        <i class="fas fa-trash-alt me-2"></i>حذف نهائياً
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            <?php endif; ?>

            <!-- معلومات إضافية -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">ما يحدث عند الحذف:</h6>
                            <ul class="small">
                                <li>يتم حذف المورد نهائياً من النظام</li>
                                <li>لا يمكن التراجع عن هذا الإجراء</li>
                                <li>لن يظهر المورد في قوائم الصلاحيات</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-warning">بدائل الحذف:</h6>
                            <ul class="small">
                                <li>تعطيل المورد بدلاً من حذفه</li>
                                <li>تعديل بيانات المورد</li>
                                <li>نقل الصلاحيات لمورد آخر</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تفعيل زر الحذف عند تأكيد الاختيار
document.getElementById('confirmDelete')?.addEventListener('change', function() {
    document.getElementById('deleteButton').disabled = !this.checked;
});
</script>

<?php include_once '../includes/footer.php'; ?>
