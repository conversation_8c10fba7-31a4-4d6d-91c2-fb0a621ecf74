<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';
require_once 'WhatsAppService.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('teacher')) {
    header('Location: ../dashboard/');
    exit();
}

global $conn;

// معالجة إضافة تقرير سلوك جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $student_id = intval($_POST['student_id'] ?? 0);
    $report_date = clean_input($_POST['report_date'] ?? date('Y-m-d'));
    $behavior_type = clean_input($_POST['behavior_type'] ?? '');
    $category = clean_input($_POST['category'] ?? '');
    $description = clean_input($_POST['description'] ?? '');
    $action_taken = clean_input($_POST['action_taken'] ?? '');
    $notify_parent = isset($_POST['notify_parent']) ? 1 : 0;
    
    $errors = [];
    
    // التحقق من البيانات
    if ($student_id <= 0) {
        $errors[] = __('select_student');
    }
    if (empty($behavior_type)) {
        $errors[] = __('behavior_type') . ' ' . __('required_field');
    }
    if (empty($category)) {
        $errors[] = __('category') . ' ' . __('required_field');
    }
    if (empty($description)) {
        $errors[] = __('behavior_description') . ' ' . __('required_field');
    }
    
    if (empty($errors)) {
        // الحصول على معرف المعلم
        $teacher_id = 1; // افتراضي - يمكن تحسينه لاحقاً
        
        // إدراج التقرير
        $insert_query = "
            INSERT INTO student_behavior_reports 
            (student_id, report_date, behavior_type, category, description, action_taken, teacher_id, notify_parent, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ";
        
        $stmt = $conn->prepare($insert_query);
        $stmt->bind_param('isssssii', 
            $student_id, 
            $report_date, 
            $behavior_type, 
            $category, 
            $description, 
            $action_taken, 
            $teacher_id, 
            $notify_parent
        );
        
        if ($stmt->execute()) {
            $report_id = $conn->insert_id;
            $stmt->close();
            
            // إرسال إشعار لولي الأمر إذا كان مطلوباً
            if ($notify_parent) {
                // جلب بيانات الطالب
                $student_query = "
                    SELECT 
                        s.id,
                        s.student_id as student_number,
                        u.full_name as student_name,
                        s.parent_phone
                    FROM students s
                    JOIN users u ON s.user_id = u.id
                    WHERE s.id = ?
                ";
                
                $stmt = $conn->prepare($student_query);
                $stmt->bind_param('i', $student_id);
                $stmt->execute();
                $student = $stmt->get_result()->fetch_assoc();
                $stmt->close();
                
                if ($student && !empty($student['parent_phone'])) {
                    // إنشاء رسالة الإشعار
                    $subject = '';
                    $message_template = '';
                    
                    switch ($behavior_type) {
                        case 'excellent':
                            $subject = 'تهنئة - سلوك ممتاز';
                            $message_template = 'تهنئة سلوك ممتاز';
                            break;
                        case 'good':
                            $subject = 'سلوك جيد';
                            $message_template = 'تهنئة سلوك ممتاز';
                            break;
                        case 'needs_improvement':
                            $subject = 'يحتاج تحسين';
                            $message_template = 'تحسين مطلوب';
                            break;
                        case 'concerning':
                            $subject = 'سلوك مثير للقلق';
                            $message_template = 'تحسين مطلوب';
                            break;
                    }
                    
                    $full_message = "السلام عليكم ولي أمر الطالب/ة " . $student['student_name'] . "\n\n";
                    $full_message .= "تقرير سلوك بتاريخ: " . $report_date . "\n";
                    $full_message .= "نوع السلوك: " . __($behavior_type) . "\n";
                    $full_message .= "الفئة: " . __($category) . "\n\n";
                    $full_message .= "التفاصيل:\n" . $description . "\n\n";
                    
                    if (!empty($action_taken)) {
                        $full_message .= "الإجراء المتخذ:\n" . $action_taken . "\n\n";
                    }
                    
                    $full_message .= "مع تحيات إدارة المدرسة";
                    
                    // حفظ الرسالة في قاعدة البيانات
                    $message_query = "
                        INSERT INTO parent_communications 
                        (student_id, parent_phone, message_type, subject, message, priority, sent_via, status, sent_by, created_at) 
                        VALUES (?, ?, 'behavior', ?, ?, 'medium', 'whatsapp', 'pending', ?, NOW())
                    ";
                    
                    $stmt = $conn->prepare($message_query);
                    $stmt->bind_param('isssi', 
                        $student_id, 
                        $student['parent_phone'], 
                        $subject, 
                        $full_message, 
                        $_SESSION['user_id']
                    );
                    
                    if ($stmt->execute()) {
                        $message_id = $conn->insert_id;
                        $stmt->close();
                        
                        // تحديث التقرير بمعرف الرسالة
                        $update_report = $conn->prepare("UPDATE student_behavior_reports SET communication_id = ?, parent_notified_at = NOW() WHERE id = ?");
                        $update_report->bind_param('ii', $message_id, $report_id);
                        $update_report->execute();
                        $update_report->close();
                        
                        // إرسال الرسالة عبر واتساب
                        $whatsapp = new WhatsAppService($conn);
                        $result = $whatsapp->sendMessage($student['parent_phone'], $full_message, $message_id);
                        
                        if ($result['success']) {
                            $_SESSION['success_message'] = __('report_saved_successfully') . ' وتم إرسال إشعار لولي الأمر';
                        } else {
                            $_SESSION['success_message'] = __('report_saved_successfully') . ' ولكن فشل إرسال الإشعار: ' . $result['error'];
                        }
                    } else {
                        $_SESSION['success_message'] = __('report_saved_successfully') . ' ولكن فشل في حفظ الإشعار';
                        $stmt->close();
                    }
                } else {
                    $_SESSION['success_message'] = __('report_saved_successfully') . ' ولكن لا يوجد رقم هاتف لولي الأمر';
                }
            } else {
                $_SESSION['success_message'] = __('report_saved_successfully');
            }
            
            header('Location: student_reports.php');
            exit();
        } else {
            $errors[] = 'فشل في حفظ التقرير: ' . $stmt->error;
            $stmt->close();
        }
    }
    
    if (!empty($errors)) {
        $_SESSION['error_message'] = implode('<br>', $errors);
    }
}

// جلب قائمة الطلاب
$students_query = "
    SELECT 
        s.id,
        s.student_id as student_number,
        u.full_name as student_name,
        c.class_name,
        s.parent_phone,
        CASE WHEN s.parent_phone IS NOT NULL AND s.parent_phone != '' THEN 1 ELSE 0 END as has_phone
    FROM students s
    JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    WHERE s.status = 'active'
    ORDER BY u.full_name
";

$students_result = $conn->query($students_query);
$students = [];
if ($students_result) {
    while ($row = $students_result->fetch_assoc()) {
        $students[] = $row;
    }
}

// جلب التقارير الحديثة
$reports_query = "
    SELECT 
        sbr.*,
        s.student_id as student_number,
        u.full_name as student_name,
        c.class_name,
        teacher_user.full_name as teacher_name,
        pc.status as notification_status
    FROM student_behavior_reports sbr
    JOIN students s ON sbr.student_id = s.id
    JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    LEFT JOIN teachers t ON sbr.teacher_id = t.id
    LEFT JOIN users teacher_user ON t.user_id = teacher_user.id
    LEFT JOIN parent_communications pc ON sbr.communication_id = pc.id
    ORDER BY sbr.created_at DESC
    LIMIT 20
";

$reports_result = $conn->query($reports_query);
$recent_reports = [];
if ($reports_result) {
    while ($row = $reports_result->fetch_assoc()) {
        $recent_reports[] = $row;
    }
}

$page_title = __('behavior_reports');
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-clipboard-list me-2"></i>
            <?php echo __('behavior_reports'); ?>
        </h2>
        <div class="btn-group">
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back'); ?>
            </a>
            <a href="whatsapp_interface.php" class="btn btn-success">
                <i class="fab fa-whatsapp me-2"></i><?php echo __('whatsapp_interface'); ?>
            </a>
        </div>
    </div>

    <!-- رسائل النجاح والخطأ -->
    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php elseif (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- نموذج إضافة تقرير سلوك -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-plus me-2"></i>إضافة تقرير سلوك جديد
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <!-- اختيار الطالب -->
                        <div class="mb-3">
                            <label for="student_id" class="form-label">
                                <i class="fas fa-user-graduate me-2"></i><?php echo __('select_student'); ?>
                                <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="student_id" name="student_id" required>
                                <option value="">اختر الطالب...</option>
                                <?php foreach ($students as $student): ?>
                                    <option value="<?php echo $student['id']; ?>" 
                                            data-phone="<?php echo $student['parent_phone']; ?>"
                                            data-class="<?php echo $student['class_name']; ?>">
                                        <?php echo safe_html($student['student_name']); ?> 
                                        (<?php echo safe_html($student['student_number']); ?>)
                                        <?php if ($student['class_name']): ?>
                                            - <?php echo safe_html($student['class_name']); ?>
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                        </div>

                        <!-- تاريخ التقرير -->
                        <div class="mb-3">
                            <label for="report_date" class="form-label">
                                <i class="fas fa-calendar me-2"></i>تاريخ التقرير
                            </label>
                            <input type="date" class="form-control" id="report_date" name="report_date" 
                                   value="<?php echo date('Y-m-d'); ?>" required>
                        </div>

                        <div class="row">
                            <!-- نوع السلوك -->
                            <div class="col-md-6 mb-3">
                                <label for="behavior_type" class="form-label">
                                    <i class="fas fa-star me-2"></i><?php echo __('behavior_type'); ?>
                                    <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="behavior_type" name="behavior_type" required>
                                    <option value="">اختر نوع السلوك...</option>
                                    <option value="excellent">😊 <?php echo __('excellent'); ?></option>
                                    <option value="good">👍 <?php echo __('good'); ?></option>
                                    <option value="needs_improvement">⚠️ <?php echo __('needs_improvement'); ?></option>
                                    <option value="concerning">🚨 <?php echo __('concerning'); ?></option>
                                </select>
                                <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                            </div>

                            <!-- الفئة -->
                            <div class="col-md-6 mb-3">
                                <label for="category" class="form-label">
                                    <i class="fas fa-tag me-2"></i>الفئة
                                    <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="category" name="category" required>
                                    <option value="">اختر الفئة...</option>
                                    <option value="discipline"><?php echo __('discipline'); ?></option>
                                    <option value="participation"><?php echo __('participation'); ?></option>
                                    <option value="homework"><?php echo __('homework'); ?></option>
                                    <option value="social"><?php echo __('social'); ?></option>
                                    <option value="academic"><?php echo __('academic'); ?></option>
                                </select>
                                <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                            </div>
                        </div>

                        <!-- وصف السلوك -->
                        <div class="mb-3">
                            <label for="description" class="form-label">
                                <i class="fas fa-comment me-2"></i><?php echo __('behavior_description'); ?>
                                <span class="text-danger">*</span>
                            </label>
                            <textarea class="form-control" id="description" name="description" rows="4" 
                                      placeholder="اكتب وصف مفصل للسلوك..." required></textarea>
                            <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                        </div>

                        <!-- الإجراء المتخذ -->
                        <div class="mb-3">
                            <label for="action_taken" class="form-label">
                                <i class="fas fa-tasks me-2"></i><?php echo __('action_taken'); ?>
                            </label>
                            <textarea class="form-control" id="action_taken" name="action_taken" rows="3" 
                                      placeholder="اكتب الإجراء المتخذ (اختياري)..."></textarea>
                        </div>

                        <!-- إشعار ولي الأمر -->
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="notify_parent" name="notify_parent" checked>
                                <label class="form-check-label" for="notify_parent">
                                    <i class="fab fa-whatsapp me-2"></i><?php echo __('notify_parent'); ?>
                                </label>
                            </div>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                سيتم إرسال إشعار تلقائي عبر واتساب لولي الأمر
                            </div>
                        </div>

                        <!-- أزرار الإجراء -->
                        <div class="d-flex justify-content-end gap-2">
                            <button type="button" class="btn btn-secondary" onclick="this.form.reset()">
                                <i class="fas fa-undo me-2"></i>إعادة تعيين
                            </button>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>حفظ التقرير
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- التقارير الحديثة -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>التقارير الحديثة
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($recent_reports)): ?>
                        <div class="timeline">
                            <?php foreach ($recent_reports as $report): ?>
                                <div class="timeline-item mb-3">
                                    <div class="d-flex">
                                        <div class="timeline-marker me-3">
                                            <?php
                                            $behavior_colors = [
                                                'excellent' => 'success',
                                                'good' => 'info',
                                                'needs_improvement' => 'warning',
                                                'concerning' => 'danger'
                                            ];
                                            $color = $behavior_colors[$report['behavior_type']] ?? 'secondary';
                                            ?>
                                            <span class="badge bg-<?php echo $color; ?> rounded-pill">
                                                <?php echo __($report['behavior_type']); ?>
                                            </span>
                                        </div>
                                        <div class="timeline-content flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="mb-1"><?php echo safe_html($report['student_name']); ?></h6>
                                                    <small class="text-muted">
                                                        <?php echo safe_html($report['class_name']); ?> • 
                                                        <?php echo __($report['category']); ?> • 
                                                        <?php echo date('Y-m-d', strtotime($report['report_date'])); ?>
                                                    </small>
                                                </div>
                                                <?php if ($report['notify_parent'] && $report['notification_status']): ?>
                                                    <span class="badge bg-<?php echo $report['notification_status'] == 'sent' ? 'success' : 'warning'; ?>">
                                                        <i class="fab fa-whatsapp me-1"></i>
                                                        <?php echo __($report['notification_status']); ?>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                            <p class="mb-1 small"><?php echo safe_html(mb_substr($report['description'], 0, 100)) . '...'; ?></p>
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i>
                                                <?php echo date('H:i', strtotime($report['created_at'])); ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد تقارير سلوك بعد</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        Array.prototype.forEach.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<style>
.timeline-item {
    position: relative;
}

.timeline-marker {
    min-width: 80px;
}

.timeline-content {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 8px;
    border-left: 3px solid #dee2e6;
}
</style>

<?php require_once '../includes/footer.php'; ?>
