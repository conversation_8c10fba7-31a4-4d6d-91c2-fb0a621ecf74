<?php
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

check_session();
if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    header('Location: ../../dashboard/');
    exit();
}

$page_title = __('pay_installment');
include_once '../../includes/header.php';

$error_message = '';
$success_message = '';

// معالجة إضافة قسط جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_installment') {
    // التحقق من رمز CSRF
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = __('invalid_request');
    } else {
        // جمع البيانات وتنظيفها
        $student_id = intval($_POST['student_id'] ?? 0);
        $installment_number = intval($_POST['installment_number'] ?? 0);
        $total_amount = floatval($_POST['total_amount'] ?? 0);
        $due_date = clean_input($_POST['due_date'] ?? '');
        $fee_type = clean_input($_POST['fee_type'] ?? 'tuition');
        $description = clean_input($_POST['description'] ?? '');

        // التحقق من صحة البيانات
        if ($student_id <= 0) {
            $error_message = __('invalid_student');
        } elseif ($installment_number <= 0) {
            $error_message = __('invalid_installment_number');
        } elseif ($total_amount <= 0) {
            $error_message = __('invalid_amount');
        } elseif (empty($due_date)) {
            $error_message = __('invalid_due_date');
        } else {
            // إضافة القسط الجديد (استخدام الأعمدة الموجودة فقط)
            $stmt = $conn->prepare("INSERT INTO student_installments (student_id, installment_number, total_amount, amount, paid_amount, due_date, status, notes, total_installments, created_at) VALUES (?, ?, ?, ?, 0, ?, 'pending', ?, 1, NOW())");

            if ($stmt) {
                // استخدام description في حقل notes
                $notes = $fee_type . ': ' . $description;
                $stmt->bind_param("iiddss", $student_id, $installment_number, $total_amount, $total_amount, $due_date, $notes);

                if ($stmt->execute()) {
                    $success_message = __('installment_added_successfully');
                    // إعادة توجيه لتحديث الصفحة
                    header("Location: pay.php?student_id=" . $student_id . "&success=1");
                    exit();
                } else {
                    $error_message = __('database_error') . ': ' . $conn->error;
                }
            } else {
                $error_message = __('database_error') . ': ' . $conn->error;
            }
        }
    }
}

// جلب قائمة الطلاب
$students = $conn->query("SELECT s.id, u.full_name FROM students s JOIN users u ON s.user_id = u.id WHERE s.status = 'active' ORDER BY u.full_name");

// التحقق من نجاح الاستعلام
if (!$students) {
    die("خطأ في جلب قائمة الطلاب: " . $conn->error);
}

// جلب بيانات القسط إذا تم تمرير installment_id
$installment_id = intval($_GET['installment_id'] ?? 0);
$student_id = intval($_GET['student_id'] ?? 0);
$installment_data = null;
$student_installments = [];
if ($installment_id > 0) {
    $stmt = $conn->prepare("SELECT si.*, u.full_name FROM student_installments si JOIN students s ON si.student_id = s.id JOIN users u ON s.user_id = u.id WHERE si.id = ?");
    if ($stmt) {
        $stmt->bind_param("i", $installment_id);
        $stmt->execute();
        $installment_data = $stmt->get_result()->fetch_assoc();
        if ($installment_data) {
            $student_id = $installment_data['student_id'];
        }
    }
}
if ($student_id > 0 && !$installment_data) {
    $stmt = $conn->prepare("SELECT si.*, u.full_name FROM student_installments si JOIN students s ON si.student_id = s.id JOIN users u ON s.user_id = u.id WHERE si.student_id = ? AND si.status != 'paid' ORDER BY si.due_date ASC");
    if ($stmt) {
        $stmt->bind_param("i", $student_id);
        $stmt->execute();
        $student_installments = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    } else {
        $student_installments = [];
    }
}

// جلب بيانات الطالب مع المرحلة الدراسية والإحصائيات المالية
$student_info = null;
$student_financial_summary = null;
if ($student_id > 0) {
    // جلب بيانات الطالب الأساسية
    $stmt = $conn->prepare("
        SELECT s.id, u.full_name, s.student_number, c.class_name, c.grade_level,
               s.phone, u.email, s.parent_name, s.parent_phone
        FROM students s
        JOIN users u ON s.user_id = u.id
        LEFT JOIN classes c ON s.class_id = c.id
        WHERE s.id = ?
    ");
    if ($stmt) {
        $stmt->bind_param("i", $student_id);
        $stmt->execute();
        $student_info = $stmt->get_result()->fetch_assoc();
    } else {
        $student_info = null;
        error_log("Failed to prepare student info query: " . $conn->error);
    }

    // جلب الملخص المالي للطالب
    $financial_stmt = $conn->prepare("
        SELECT
            COALESCE(COUNT(si.id), 0) as total_installments,
            COALESCE(SUM(si.total_amount), 0) as total_amount,
            COALESCE(SUM(si.paid_amount), 0) as total_paid,
            COALESCE(SUM(si.total_amount - si.paid_amount), 0) as total_remaining,
            COALESCE(COUNT(CASE WHEN si.status = 'paid' THEN 1 END), 0) as paid_count,
            COALESCE(COUNT(CASE WHEN si.status = 'pending' THEN 1 END), 0) as pending_count,
            COALESCE(COUNT(CASE WHEN si.status = 'overdue' THEN 1 END), 0) as overdue_count
        FROM student_installments si
        WHERE si.student_id = ?
    ");
    if ($financial_stmt) {
        $financial_stmt->bind_param("i", $student_id);
        $financial_stmt->execute();
        $student_financial_summary = $financial_stmt->get_result()->fetch_assoc();
    } else {
        // إذا فشل الاستعلام، استخدم قيم افتراضية
        $student_financial_summary = [
            'total_installments' => 0,
            'total_amount' => 0,
            'total_paid' => 0,
            'total_remaining' => 0,
            'paid_count' => 0,
            'pending_count' => 0,
            'overdue_count' => 0
        ];
    }
}

$success_message = '';
$error_message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['installment_id'])) {
    $student_id = intval($_POST['student_id'] ?? 0);
    $installment_id = intval($_POST['installment_id'] ?? 0);
    $pay_amount = floatval($_POST['pay_amount'] ?? 0);
    $pay_method = clean_input($_POST['pay_method'] ?? '');
    $pay_date = clean_input($_POST['pay_date'] ?? date('Y-m-d'));
    $pay_notes = clean_input($_POST['pay_notes'] ?? '');

    // جلب بيانات القسط للتحقق
    $stmt = $conn->prepare("SELECT * FROM student_installments WHERE id = ? AND student_id = ?");
    $stmt->bind_param("ii", $installment_id, $student_id);
    $stmt->execute();
    $installment = $stmt->get_result()->fetch_assoc();

    if (!$installment) {
        $error_message = __('installment_not_found');
    } elseif ($pay_amount <= 0 || $pay_amount > ($installment['total_amount'] - $installment['paid_amount'])) {
        $error_message = __('invalid_pay_amount');
    } else {
        $conn->begin_transaction();
        try {
            // تحديث القسط
            $new_paid = $installment['paid_amount'] + $pay_amount;
            $new_status = ($new_paid >= $installment['total_amount']) ? 'paid' : 'partial';
            $update_stmt = $conn->prepare("UPDATE student_installments SET paid_amount = ?, status = ?, paid_date = ? WHERE id = ?");
            $update_stmt->bind_param("dssi", $new_paid, $new_status, $pay_date, $installment_id);
            $update_stmt->execute();

            // تسجيل الدفعة
            $payment_reference = 'INST-' . date('Ymd') . '-' . str_pad($installment_id, 6, '0', STR_PAD_LEFT);
            $insert_stmt = $conn->prepare("INSERT INTO student_payments (student_id, installment_id, amount, payment_method, payment_date, notes, status, payment_reference, processed_by, processed_at, created_at) VALUES (?, ?, ?, ?, ?, ?, 'confirmed', ?, ?, NOW(), NOW())");
            $insert_stmt->bind_param("iidsssssi", $student_id, $installment_id, $pay_amount, $pay_method, $pay_date, $pay_notes, $payment_reference, $_SESSION['user_id']);
            $insert_stmt->execute();

            $conn->commit();
            $success_message = __('installment_paid_successfully');
            // إعادة التوجيه بعد النجاح
            header('Location: index.php?success=1');
            exit();
        } catch (Exception $e) {
            $conn->rollback();
            $error_message = __('db_error') . ': ' . $e->getMessage();
        }
    }
}
?>
<div class="container py-4">
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-money-bill-wave me-2"></i><?php echo __('pay_installment'); ?></h4>
                </div>
                <div class="card-body">
                    <?php if ($success_message): ?>
                        <div class="alert alert-success text-center"><?php echo $success_message; ?></div>
                    <?php endif; ?>
                    <?php if ($error_message): ?>
                        <div class="alert alert-danger text-center"><?php echo $error_message; ?></div>
                    <?php endif; ?>
                    <form method="post" id="payInstallmentForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label"><?php echo __('student'); ?> <span class="text-danger">*</span></label>
                                    <select class="form-select" name="student_id" id="student_id" required onchange="window.location='pay.php?student_id='+this.value;">
                                        <option value=""><?php echo __('choose_student'); ?></option>
                                        <?php $students->data_seek(0); while ($stu = $students->fetch_assoc()): ?>
                                            <option value="<?php echo $stu['id']; ?>" <?php if($student_id == $stu['id']) echo 'selected'; ?>><?php echo htmlspecialchars($stu['full_name']); ?></option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                            </div>
                            <?php if ($student_info): ?>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label"><?php echo __('class'); ?></label>
                                    <input type="text" class="form-control" value="<?php echo htmlspecialchars(($student_info['class_name'] ?? '') . ' - ' . ($student_info['grade_level'] ?? '')); ?>" readonly>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات الطالب التفصيلية -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label"><?php echo __('student_number'); ?></label>
                                    <input type="text" class="form-control" value="<?php echo htmlspecialchars($student_info['student_id'] ?? ''); ?>" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label"><?php echo __('parent_name'); ?></label>
                                    <input type="text" class="form-control" value="<?php echo htmlspecialchars($student_info['parent_name'] ?? ''); ?>" readonly>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                        <?php if ($student_id && !$installment_data): ?>
                            <?php if (!empty($student_installments)): ?>
                            <div class="mb-3">
                                <label class="form-label"><?php echo __('installment'); ?> <span class="text-danger">*</span></label>
                                <select class="form-select" name="installment_id" id="installment_id" required onchange="window.location='pay.php?student_id=<?php echo $student_id; ?>&installment_id='+this.value;">
                                    <option value=""><?php echo __('choose_installment'); ?></option>
                                    <?php foreach ($student_installments as $inst): ?>
                                        <option value="<?php echo $inst['id']; ?>" <?php if($installment_id == $inst['id']) echo 'selected'; ?>><?php echo __('installment') . ' #' . $inst['installment_number'] . ' - ' . number_format($inst['total_amount'],2) . ' (' . number_format($inst['total_amount'] - $inst['paid_amount'],2) . ' ' . __('remaining_amount') . ')'; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <?php else: ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong><?php echo __('no_pending_installments'); ?></strong><br>
                                <small><?php echo __('no_pending_installments_desc'); ?></small>
                                <br><br>
                                <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addInstallmentModal">
                                    <i class="fas fa-plus me-1"></i><?php echo __('add_installment'); ?>
                                </button>
                            </div>

                            <!-- نموذج إضافة قسط جديد -->
                            <div class="modal fade" id="addInstallmentModal" tabindex="-1">
                                <div class="modal-dialog modal-lg">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title"><?php echo __('add_installment'); ?></h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                        </div>
                                        <form method="POST" action="">
                                            <div class="modal-body">
                                                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                                <input type="hidden" name="student_id" value="<?php echo $student_id; ?>">
                                                <input type="hidden" name="action" value="add_installment">

                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label class="form-label"><?php echo __('installment_number'); ?> <span class="text-danger">*</span></label>
                                                            <input type="number" class="form-control" name="installment_number" min="1" required>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label class="form-label"><?php echo __('total_amount'); ?> <span class="text-danger">*</span></label>
                                                            <input type="number" class="form-control" name="total_amount" min="0" step="0.01" required>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label class="form-label"><?php echo __('due_date'); ?> <span class="text-danger">*</span></label>
                                                            <input type="date" class="form-control" name="due_date" required>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label class="form-label"><?php echo __('fee_type'); ?></label>
                                                            <select class="form-select" name="fee_type">
                                                                <option value="tuition"><?php echo __('tuition'); ?></option>
                                                                <option value="books"><?php echo __('books'); ?></option>
                                                                <option value="transport"><?php echo __('transport'); ?></option>
                                                                <option value="activities"><?php echo __('activities'); ?></option>
                                                                <option value="other"><?php echo __('other'); ?></option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="mb-3">
                                                    <label class="form-label"><?php echo __('description'); ?></label>
                                                    <textarea class="form-control" name="description" rows="3"></textarea>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo __('cancel'); ?></button>
                                                <button type="submit" class="btn btn-primary"><?php echo __('add_installment'); ?></button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        <?php endif; ?>
                        <?php if ($installment_data): ?>
                        <!-- معلومات القسط -->
                        <?php
                        $is_overdue = strtotime($installment_data['due_date']) < time() && $installment_data['status'] != 'paid';
                        $alert_class = $is_overdue ? 'alert-warning' : 'alert-info';
                        $icon_class = $is_overdue ? 'fas fa-exclamation-triangle' : 'fas fa-info-circle';
                        ?>
                        <div class="alert <?php echo $alert_class; ?>">
                            <h6 class="alert-heading"><i class="<?php echo $icon_class; ?> me-2"></i><?php echo __('installment_details'); ?>
                            <?php if ($is_overdue): ?>
                                <span class="badge bg-danger ms-2"><?php echo __('overdue'); ?></span>
                            <?php endif; ?>
                            </h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <strong><?php echo __('installment_number'); ?>:</strong> #<?php echo $installment_data['installment_number']; ?>
                                </div>
                                <div class="col-md-4">
                                    <strong><?php echo __('due_date'); ?>:</strong> <?php echo date('Y-m-d', strtotime($installment_data['due_date'])); ?>
                                </div>
                                <div class="col-md-4">
                                    <strong><?php echo __('status'); ?>:</strong>
                                    <span class="badge bg-<?php echo $installment_data['status'] == 'paid' ? 'success' : ($installment_data['status'] == 'overdue' ? 'danger' : 'warning'); ?>">
                                        <?php echo __($installment_data['status']); ?>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- المبالغ المالية -->
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label"><?php echo __('total_amount'); ?></label>
                                    <div class="input-group">
                                        <input type="text" class="form-control bg-light" value="<?php echo number_format($installment_data['total_amount'],2); ?>" readonly>
                                        <span class="input-group-text"><?php echo get_system_setting('currency_symbol', 'ر.س'); ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label"><?php echo __('paid_amount'); ?></label>
                                    <div class="input-group">
                                        <input type="text" class="form-control bg-light" value="<?php echo number_format($installment_data['paid_amount'],2); ?>" readonly>
                                        <span class="input-group-text"><?php echo get_system_setting('currency_symbol', 'ر.س'); ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label text-primary"><?php echo __('remaining_amount'); ?></label>
                                    <div class="input-group">
                                        <input type="text" class="form-control bg-primary text-white fw-bold" value="<?php echo number_format($installment_data['total_amount'] - $installment_data['paid_amount'],2); ?>" readonly>
                                        <span class="input-group-text bg-primary text-white"><?php echo get_system_setting('currency_symbol', 'ر.س'); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- معلومات الدفع -->
                        <div class="card border-success mb-3">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0"><i class="fas fa-credit-card me-2"></i><?php echo __('payment_details'); ?></h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label"><?php echo __('pay_amount'); ?> <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" name="pay_amount"
                                                       min="0.01"
                                                       max="<?php echo $installment_data['total_amount'] - $installment_data['paid_amount']; ?>"
                                                       step="0.01"
                                                       value="<?php echo $installment_data['total_amount'] - $installment_data['paid_amount']; ?>"
                                                       required>
                                                <span class="input-group-text"><?php echo get_system_setting('currency_symbol', 'ر.س'); ?></span>
                                            </div>
                                            <small class="text-muted"><?php echo __('max_amount'); ?>: <?php echo number_format($installment_data['total_amount'] - $installment_data['paid_amount'], 2); ?></small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label"><?php echo __('payment_method'); ?> <span class="text-danger">*</span></label>
                                            <select class="form-select" name="pay_method" required>
                                                <option value="cash"><?php echo __('cash'); ?></option>
                                                <option value="bank_transfer"><?php echo __('bank_transfer'); ?></option>
                                                <option value="check"><?php echo __('check'); ?></option>
                                                <option value="card"><?php echo __('card'); ?></option>
                                                <option value="online"><?php echo __('online'); ?></option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label"><?php echo __('payment_date'); ?> <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control" name="pay_date" value="<?php echo date('Y-m-d'); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label"><?php echo __('payment_reference'); ?></label>
                                            <input type="text" class="form-control" name="payment_reference" placeholder="<?php echo __('optional'); ?>">
                                            <small class="text-muted"><?php echo __('payment_reference_help'); ?></small>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label"><?php echo __('notes'); ?></label>
                                    <textarea class="form-control" name="pay_notes" rows="3" placeholder="<?php echo __('payment_notes_placeholder'); ?>"></textarea>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                        <div class="d-flex justify-content-between">
                            <a href="index.php" class="btn btn-secondary"><?php echo __('back'); ?></a>
                            <button type="submit" class="btn btn-success" <?php if(!$installment_data) echo 'disabled'; ?>><?php echo __('pay'); ?></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- العمود الجانبي - الملخص المالي -->
        <?php if ($student_info && $student_financial_summary): ?>
        <div class="col-lg-4">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-chart-pie me-2"></i><?php echo __('financial_summary'); ?></h6>
                </div>
                <div class="card-body">
                    <!-- معلومات الطالب -->
                    <div class="text-center mb-3">
                        <div class="bg-light rounded p-3">
                            <h6 class="mb-1"><?php echo htmlspecialchars($student_info['full_name']); ?></h6>
                            <small class="text-muted"><?php echo __('student_number'); ?>: <?php echo htmlspecialchars($student_info['student_id']); ?></small>
                        </div>
                    </div>

                    <!-- الإحصائيات المالية -->
                    <div class="row text-center mb-3">
                        <div class="col-6">
                            <div class="border rounded p-2">
                                <h5 class="text-primary mb-0"><?php echo number_format($student_financial_summary['total_installments']); ?></h5>
                                <small class="text-muted"><?php echo __('total_installments'); ?></small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="border rounded p-2">
                                <h5 class="text-success mb-0"><?php echo number_format($student_financial_summary['paid_count']); ?></h5>
                                <small class="text-muted"><?php echo __('paid_installments'); ?></small>
                            </div>
                        </div>
                    </div>

                    <!-- المبالغ -->
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-2">
                            <span><?php echo __('total_amount'); ?>:</span>
                            <strong class="text-primary"><?php echo number_format($student_financial_summary['total_amount'], 2); ?> <?php echo get_system_setting('currency_symbol', 'ر.س'); ?></strong>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span><?php echo __('total_paid'); ?>:</span>
                            <strong class="text-success"><?php echo number_format($student_financial_summary['total_paid'], 2); ?> <?php echo get_system_setting('currency_symbol', 'ر.س'); ?></strong>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span><?php echo __('total_remaining'); ?>:</span>
                            <strong class="text-danger"><?php echo number_format($student_financial_summary['total_remaining'], 2); ?> <?php echo get_system_setting('currency_symbol', 'ر.س'); ?></strong>
                        </div>
                    </div>

                    <!-- شريط التقدم -->
                    <?php
                    $payment_percentage = $student_financial_summary['total_amount'] > 0 ?
                        ($student_financial_summary['total_paid'] / $student_financial_summary['total_amount']) * 100 : 0;
                    ?>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <small><?php echo __('payment_progress'); ?></small>
                            <small><?php echo number_format($payment_percentage, 1); ?>%</small>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-success" style="width: <?php echo $payment_percentage; ?>%"></div>
                        </div>
                    </div>

                    <!-- معلومات الاتصال -->
                    <div class="border-top pt-3">
                        <h6 class="text-muted mb-2"><?php echo __('contact_info'); ?></h6>
                        <?php if (!empty($student_info['parent_name'])): ?>
                        <small class="d-block"><strong><?php echo __('parent_name'); ?>:</strong> <?php echo htmlspecialchars($student_info['parent_name']); ?></small>
                        <?php endif; ?>
                        <?php if (!empty($student_info['parent_phone'])): ?>
                        <small class="d-block"><strong><?php echo __('parent_phone'); ?>:</strong> <?php echo htmlspecialchars($student_info['parent_phone']); ?></small>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحسين تجربة المستخدم في نموذج الدفع
    const payAmountInput = document.querySelector('input[name="pay_amount"]');
    const payMethodSelect = document.querySelector('select[name="pay_method"]');
    const payDateInput = document.querySelector('input[name="pay_date"]');

    if (payAmountInput) {
        // إضافة تنسيق للمبلغ أثناء الكتابة
        payAmountInput.addEventListener('input', function() {
            const value = parseFloat(this.value);
            const maxAmount = parseFloat(this.getAttribute('max'));

            if (value > maxAmount) {
                this.setCustomValidity('<?php echo __("amount_exceeds_remaining"); ?>');
                this.classList.add('is-invalid');
            } else if (value <= 0) {
                this.setCustomValidity('<?php echo __("amount_must_be_positive"); ?>');
                this.classList.add('is-invalid');
            } else {
                this.setCustomValidity('');
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });

        // ملء المبلغ الكامل عند النقر على زر "دفع كامل"
        const fullPayButton = document.createElement('button');
        fullPayButton.type = 'button';
        fullPayButton.className = 'btn btn-outline-primary btn-sm mt-1';
        fullPayButton.innerHTML = '<i class="fas fa-money-bill-wave me-1"></i><?php echo __("pay_full_amount"); ?>';
        fullPayButton.onclick = function() {
            payAmountInput.value = payAmountInput.getAttribute('max');
            payAmountInput.dispatchEvent(new Event('input'));
        };
        payAmountInput.parentNode.appendChild(fullPayButton);
    }

    // تحسين اختيار طريقة الدفع
    if (payMethodSelect) {
        payMethodSelect.addEventListener('change', function() {
            const referenceInput = document.querySelector('input[name="payment_reference"]');
            const referenceLabel = referenceInput?.previousElementSibling;

            if (referenceInput && referenceLabel) {
                switch(this.value) {
                    case 'check':
                        referenceLabel.textContent = '<?php echo __("check_number"); ?>';
                        referenceInput.placeholder = '<?php echo __("enter_check_number"); ?>';
                        break;
                    case 'bank_transfer':
                        referenceLabel.textContent = '<?php echo __("transfer_reference"); ?>';
                        referenceInput.placeholder = '<?php echo __("enter_transfer_reference"); ?>';
                        break;
                    case 'card':
                        referenceLabel.textContent = '<?php echo __("transaction_id"); ?>';
                        referenceInput.placeholder = '<?php echo __("enter_transaction_id"); ?>';
                        break;
                    case 'online':
                        referenceLabel.textContent = '<?php echo __("payment_id"); ?>';
                        referenceInput.placeholder = '<?php echo __("enter_payment_id"); ?>';
                        break;
                    default:
                        referenceLabel.textContent = '<?php echo __("payment_reference"); ?>';
                        referenceInput.placeholder = '<?php echo __("optional"); ?>';
                }
            }
        });
    }

    // تأكيد الدفع
    const form = document.getElementById('payInstallmentForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            const amount = parseFloat(payAmountInput?.value || 0);
            const method = payMethodSelect?.value || '';

            if (amount > 0 && method) {
                const confirmMessage = `<?php echo __("confirm_payment"); ?>\n\n` +
                    `<?php echo __("amount"); ?>: ${amount.toFixed(2)} <?php echo get_system_setting('currency_symbol', 'ر.س'); ?>\n` +
                    `<?php echo __("method"); ?>: ${method}\n\n` +
                    `<?php echo __("continue_payment"); ?>`;

                if (!confirm(confirmMessage)) {
                    e.preventDefault();
                }
            }
        });
    }
});
</script>

<?php include_once '../../includes/footer.php'; ?>