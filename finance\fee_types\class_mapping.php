<?php
/**
 * ربط أنواع الرسوم بالصفوف الدراسية
 * Fee Types Class Mapping
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    header('Location: ../../dashboard/');
    exit();
}

$success_message = '';
$error_message = '';

// إنشاء الجدول الجديد إذا لم يكن موجوداً
$conn->query("
    CREATE TABLE IF NOT EXISTS fee_type_grades (
        id int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
        fee_type_id int(10) UNSIGNED NOT NULL,
        grade_id int(10) UNSIGNED NOT NULL,
        amount decimal(10,2) NOT NULL DEFAULT 0.00,
        is_active tinyint(1) DEFAULT 1,
        created_at timestamp NOT NULL DEFAULT current_timestamp(),
        updated_at timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (id),
        UNIQUE KEY unique_fee_grade (fee_type_id, grade_id),
        KEY idx_fee_type (fee_type_id),
        KEY idx_grade (grade_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
");

// نقل البيانات من الجدول القديم إذا كان موجوداً
$check_old_table = $conn->query("SHOW TABLES LIKE 'fee_type_classes'");
if ($check_old_table && $check_old_table->num_rows > 0) {
    // التحقق من وجود بيانات في الجدول الجديد
    $check_new_data = $conn->query("SELECT COUNT(*) as count FROM fee_type_grades");
    $new_data_count = $check_new_data->fetch_assoc()['count'];

    if ($new_data_count == 0) {
        // نقل البيانات من الجدول القديم
        $conn->query("
            INSERT INTO fee_type_grades (fee_type_id, grade_id, amount, is_active, created_at, updated_at)
            SELECT
                ftc.fee_type_id,
                c.grade_id,
                ftc.amount,
                ftc.is_active,
                ftc.created_at,
                ftc.updated_at
            FROM fee_type_classes ftc
            JOIN classes c ON ftc.class_id = c.id
            WHERE c.grade_id IS NOT NULL
            ON DUPLICATE KEY UPDATE
                amount = VALUES(amount),
                is_active = VALUES(is_active),
                updated_at = VALUES(updated_at)
        ");
    }
}

// إضافة بيانات تجريبية للصفوف الدراسية إذا لم تكن موجودة
$check_grades = $conn->query("SELECT COUNT(*) as count FROM grades");
$grades_count = $check_grades->fetch_assoc()['count'];

if ($grades_count == 0) {
    // إضافة المراحل التعليمية إذا لم تكن موجودة
    $conn->query("
        INSERT IGNORE INTO educational_stages (id, stage_name, stage_code, sort_order) VALUES
        (1, 'رياض الأطفال', 'KG', 1),
        (2, 'المرحلة الابتدائية', 'PRI', 2),
        (3, 'المرحلة المتوسطة', 'MID', 3),
        (4, 'المرحلة الثانوية', 'SEC', 4)
    ");

    // إضافة الصفوف الدراسية
    $conn->query("
        INSERT INTO grades (grade_name, grade_code, stage_id, sort_order) VALUES
        ('التمهيدي', 'KG1', 1, 1),
        ('الروضة', 'KG2', 1, 2),
        ('الصف الأول الابتدائي', 'G1', 2, 1),
        ('الصف الثاني الابتدائي', 'G2', 2, 2),
        ('الصف الثالث الابتدائي', 'G3', 2, 3),
        ('الصف الرابع الابتدائي', 'G4', 2, 4),
        ('الصف الخامس الابتدائي', 'G5', 2, 5),
        ('الصف السادس الابتدائي', 'G6', 2, 6),
        ('الصف الأول المتوسط', 'M1', 3, 1),
        ('الصف الثاني المتوسط', 'M2', 3, 2),
        ('الصف الثالث المتوسط', 'M3', 3, 3),
        ('الصف الأول الثانوي', 'S1', 4, 1),
        ('الصف الثاني الثانوي', 'S2', 4, 2),
        ('الصف الثالث الثانوي', 'S3', 4, 3)
    ");
}

// معالجة تحديث المبالغ
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'طلب غير صحيح';
    } else {
        $action = clean_input($_POST['action'] ?? '');
        
        if ($action === 'update_amounts') {
            $mappings = $_POST['mappings'] ?? [];
            $updated_count = 0;
            
            foreach ($mappings as $mapping_id => $amount) {
                $amount = floatval($amount);
                if ($amount >= 0) {
                    $update_stmt = $conn->prepare("UPDATE fee_type_grades SET amount = ?, updated_at = NOW() WHERE id = ?");
                    $update_stmt->bind_param("di", $amount, $mapping_id);
                    if ($update_stmt->execute()) {
                        $updated_count++;
                    }
                }
            }
            
            if ($updated_count > 0) {
                $success_message = "تم تحديث {$updated_count} مبلغ بنجاح";
            } else {
                $error_message = "لم يتم تحديث أي مبلغ";
            }
        }
        
        elseif ($action === 'toggle_status') {
            $mapping_id = intval($_POST['mapping_id'] ?? 0);
            if ($mapping_id > 0) {
                $toggle_stmt = $conn->prepare("UPDATE fee_type_grades SET is_active = !is_active WHERE id = ?");
                $toggle_stmt->bind_param("i", $mapping_id);
                if ($toggle_stmt->execute()) {
                    $success_message = "تم تغيير حالة الربط بنجاح";
                } else {
                    $error_message = "خطأ في تغيير حالة الربط";
                }
            }
        }

        elseif ($action === 'delete_mapping') {
            $mapping_id = intval($_POST['mapping_id'] ?? 0);
            if ($mapping_id > 0) {
                // جلب معلومات الربط قبل الحذف للتأكيد
                $mapping_info_stmt = $conn->prepare("
                    SELECT ft.type_name, g.grade_name
                    FROM fee_type_grades ftg
                    JOIN fee_types ft ON ftg.fee_type_id = ft.id
                    JOIN grades g ON ftg.grade_id = g.id
                    WHERE ftg.id = ?
                ");
                $mapping_info_stmt->bind_param("i", $mapping_id);
                $mapping_info_stmt->execute();
                $mapping_info = $mapping_info_stmt->get_result()->fetch_assoc();

                if ($mapping_info) {
                    $delete_stmt = $conn->prepare("DELETE FROM fee_type_grades WHERE id = ?");
                    $delete_stmt->bind_param("i", $mapping_id);
                    if ($delete_stmt->execute()) {
                        $success_message = "تم حذف ربط '{$mapping_info['type_name']}' مع '{$mapping_info['grade_name']}' بنجاح";
                    } else {
                        $error_message = "خطأ في حذف الربط: " . $conn->error;
                    }
                } else {
                    $error_message = "الربط غير موجود";
                }
            }
        }
        
        elseif ($action === 'add_mapping') {
            $fee_type_id = intval($_POST['fee_type_id'] ?? 0);
            $grade_id = intval($_POST['grade_id'] ?? 0);
            $amount = floatval($_POST['amount'] ?? 0);

            if ($fee_type_id > 0 && $grade_id > 0 && $amount >= 0) {
                $insert_stmt = $conn->prepare("
                    INSERT INTO fee_type_grades (fee_type_id, grade_id, amount, is_active)
                    VALUES (?, ?, ?, 1)
                    ON DUPLICATE KEY UPDATE amount = VALUES(amount), is_active = 1, updated_at = NOW()
                ");
                $insert_stmt->bind_param("iid", $fee_type_id, $grade_id, $amount);
                
                if ($insert_stmt->execute()) {
                    $success_message = "تم إضافة/تحديث الربط بنجاح";
                } else {
                    $error_message = "خطأ في إضافة الربط: " . $conn->error;
                }
            } else {
                $error_message = "يرجى ملء جميع الحقول بقيم صحيحة";
            }
        }
    }
}

// جلب البيانات
$fee_types = $conn->query("SELECT id, type_name FROM fee_types WHERE status = 'active' ORDER BY type_name");
$grades = $conn->query("
    SELECT g.id, g.grade_name, g.grade_code, es.stage_name
    FROM grades g
    LEFT JOIN educational_stages es ON g.stage_id = es.id
    WHERE g.status = 'active'
    ORDER BY es.sort_order, g.sort_order, g.grade_name
");

// جلب الروابط الحالية
$mappings_query = "
    SELECT
        ftg.*,
        ft.type_name,
        g.grade_name,
        g.grade_code,
        es.stage_name
    FROM fee_type_grades ftg
    JOIN fee_types ft ON ftg.fee_type_id = ft.id
    JOIN grades g ON ftg.grade_id = g.id
    LEFT JOIN educational_stages es ON g.stage_id = es.id
    ORDER BY ft.type_name, es.sort_order, g.sort_order, g.grade_name
";
$mappings = $conn->query($mappings_query);

$page_title = 'ربط الرسوم بالصفوف';
include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-link me-2"></i>ربط أنواع الرسوم بالصفوف الدراسية
            </h1>
            <p class="text-muted mb-0">تحديد مبالغ الرسوم لكل صف دراسي (الصف الأول، الثاني، الثالث...)</p>
            <small class="text-info">
                <i class="fas fa-info-circle me-1"></i>
                تم تحديث النظام ليعمل مع الصفوف الدراسية بدلاً من الفصول
            </small>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة لأنواع الرسوم
            </a>
        </div>
    </div>

    <!-- رسائل التنبيه -->
    <?php if (!empty($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <div class="row">
        <!-- نموذج إضافة ربط جديد -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-plus me-2"></i>إضافة ربط جديد
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        <input type="hidden" name="action" value="add_mapping">
                        
                        <div class="mb-3">
                            <label class="form-label">نوع الرسوم</label>
                            <select class="form-select" name="fee_type_id" required>
                                <option value="">اختر نوع الرسوم</option>
                                <?php if ($fee_types && $fee_types->num_rows > 0): ?>
                                    <?php while ($fee_type = $fee_types->fetch_assoc()): ?>
                                        <option value="<?php echo $fee_type['id']; ?>">
                                            <?php echo htmlspecialchars($fee_type['type_name']); ?>
                                        </option>
                                    <?php endwhile; ?>
                                <?php endif; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">الصف الدراسي</label>
                            <select class="form-select" name="grade_id" required>
                                <option value="">اختر الصف الدراسي</option>
                                <?php if ($grades && $grades->num_rows > 0): ?>
                                    <?php
                                    $grades->data_seek(0);
                                    while ($grade = $grades->fetch_assoc()): ?>
                                        <option value="<?php echo $grade['id']; ?>">
                                            <?php echo htmlspecialchars($grade['grade_name'] . ' - ' . ($grade['stage_name'] ?? 'غير محدد')); ?>
                                        </option>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <option value="" disabled>لا توجد صفوف دراسية متاحة</option>
                                <?php endif; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">المبلغ</label>
                            <div class="input-group">
                                <input type="number" class="form-control" name="amount" 
                                       min="0" step="0.01" required placeholder="0.00">
                                <span class="input-group-text"><?php echo get_currency_symbol(); ?></span>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-plus me-2"></i>إضافة الربط
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- جدول الروابط الحالية -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>الروابط الحالية
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php if ($mappings && $mappings->num_rows > 0): ?>
                    <form method="POST" action="" id="update_amounts_form">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        <input type="hidden" name="action" value="update_amounts">
                        
                        <div class="table-responsive">
                            <table class="table table-hover align-middle mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>نوع الرسوم</th>
                                        <th>الصف</th>
                                        <th>المرحلة</th>
                                        <th>المبلغ</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $current_fee_type = '';
                                    while ($mapping = $mappings->fetch_assoc()): 
                                        $is_new_fee_type = ($current_fee_type != $mapping['type_name']);
                                        if ($is_new_fee_type) {
                                            $current_fee_type = $mapping['type_name'];
                                        }
                                    ?>
                                    <tr <?php if (!$mapping['is_active']) echo 'class="table-secondary"'; ?>>
                                        <td>
                                            <?php if ($is_new_fee_type): ?>
                                                <strong><?php echo htmlspecialchars($mapping['type_name']); ?></strong>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($mapping['grade_name'] ?? 'غير محدد'); ?></strong>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?php echo htmlspecialchars($mapping['stage_name'] ?? 'غير محدد'); ?></small>
                                        </td>
                                        <td>
                                            <div class="input-group input-group-sm" style="width: 150px;">
                                                <input type="number" class="form-control" 
                                                       name="mappings[<?php echo $mapping['id']; ?>]"
                                                       value="<?php echo $mapping['amount']; ?>"
                                                       min="0" step="0.01"
                                                       <?php if (!$mapping['is_active']) echo 'disabled'; ?>>
                                                <span class="input-group-text"><?php echo get_currency_symbol(); ?></span>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if ($mapping['is_active']): ?>
                                                <span class="badge bg-success">نشط</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <!-- زر تفعيل/إلغاء تفعيل -->
                                                <form method="POST" action="" style="display: inline;">
                                                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                                    <input type="hidden" name="action" value="toggle_status">
                                                    <input type="hidden" name="mapping_id" value="<?php echo $mapping['id']; ?>">
                                                    <button type="submit" class="btn btn-outline-warning"
                                                            title="<?php echo $mapping['is_active'] ? 'إلغاء التفعيل' : 'تفعيل'; ?>">
                                                        <i class="fas fa-<?php echo $mapping['is_active'] ? 'pause' : 'play'; ?>"></i>
                                                    </button>
                                                </form>

                                                <!-- زر الحذف -->
                                                <form method="POST" action="" style="display: inline;">
                                                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                                    <input type="hidden" name="action" value="delete_mapping">
                                                    <input type="hidden" name="mapping_id" value="<?php echo $mapping['id']; ?>">
                                                    <button type="submit" class="btn btn-outline-danger"
                                                            title="حذف الربط"
                                                            onclick="return confirm('هل أنت متأكد من حذف ربط ' + '<?php echo addslashes($mapping['type_name'] ?? ''); ?>' + ' مع ' + '<?php echo addslashes($mapping['grade_name'] ?? ''); ?>' + '؟\\n\\nلا يمكن التراجع عن هذا الإجراء.')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="card-footer">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>حفظ جميع التعديلات
                            </button>
                        </div>
                    </form>
                    <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-link fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد روابط</h5>
                        <p class="text-muted">ابدأ بإضافة ربط بين نوع رسوم وصف دراسي</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحسين تجربة المستخدم
    const form = document.getElementById('update_amounts_form');
    if (form) {
        const inputs = form.querySelectorAll('input[type="number"]');
        let hasChanges = false;
        
        inputs.forEach(input => {
            const originalValue = input.value;
            input.addEventListener('input', function() {
                hasChanges = (this.value !== originalValue);
                updateSaveButton();
            });
        });
        
        function updateSaveButton() {
            const saveButton = form.querySelector('button[type="submit"]');
            if (hasChanges) {
                saveButton.classList.remove('btn-success');
                saveButton.classList.add('btn-warning');
                saveButton.innerHTML = '<i class="fas fa-save me-2"></i>حفظ التعديلات (تم التغيير)';
            } else {
                saveButton.classList.remove('btn-warning');
                saveButton.classList.add('btn-success');
                saveButton.innerHTML = '<i class="fas fa-save me-2"></i>حفظ جميع التعديلات';
            }
        }
    }
});
</script>

<?php include_once '../../includes/footer.php'; ?>
