<?php
/**
 * منح صلاحيات سريعة للمستخدم <EMAIL>
 * Quick Grant <NAME_EMAIL>
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

$user_email = '<EMAIL>';
$success_message = '';
$error_message = '';

// جلب بيانات المستخدم
$user_query = "SELECT * FROM users WHERE email = ?";
$user_stmt = $conn->prepare($user_query);
$user_stmt->bind_param("s", $user_email);
$user_stmt->execute();
$user = $user_stmt->get_result()->fetch_assoc();

if (!$user) {
    die("المستخدم غير موجود!");
}

// معالجة منح الصلاحيات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['grant_permissions'])) {
    $selected_permissions = $_POST['permissions'] ?? [];
    
    if (!empty($selected_permissions)) {
        $conn->begin_transaction();
        
        try {
            // حذف الصلاحيات الموجودة أولاً
            $delete_stmt = $conn->prepare("DELETE FROM user_custom_permissions WHERE user_id = ?");
            $delete_stmt->bind_param("i", $user['id']);
            $delete_stmt->execute();
            
            // إضافة الصلاحيات الجديدة
            $insert_stmt = $conn->prepare("
                INSERT INTO user_custom_permissions 
                (user_id, permission_key, is_granted, granted_by, granted_at) 
                VALUES (?, ?, 1, ?, NOW())
            ");
            
            $granted_count = 0;
            foreach ($selected_permissions as $permission) {
                $insert_stmt->bind_param("isi", $user['id'], $permission, $_SESSION['user_id']);
                if ($insert_stmt->execute()) {
                    $granted_count++;
                }
            }
            
            // تسجيل في سجل المراجعة
            $audit_stmt = $conn->prepare("
                INSERT INTO permissions_audit_log 
                (user_id, action_type, target_type, target_id, old_value, new_value, ip_address, user_agent, created_at)
                VALUES (?, 'bulk_permission_grant', 'user', ?, 'previous_permissions', ?, ?, 'Admin Panel', NOW())
            ");
            $ip_address = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
            $new_value = "Granted $granted_count permissions to " . $user['email'];
            $audit_stmt->bind_param("iiss", $_SESSION['user_id'], $user['id'], $new_value, $ip_address);
            $audit_stmt->execute();
            
            $conn->commit();
            $success_message = "تم منح $granted_count صلاحية للمستخدم بنجاح!";
            
        } catch (Exception $e) {
            $conn->rollback();
            $error_message = "خطأ في منح الصلاحيات: " . $e->getMessage();
        }
    } else {
        $error_message = "يرجى اختيار صلاحية واحدة على الأقل";
    }
}

// جلب الصلاحيات الحالية للمستخدم
$current_permissions = [];
$current_query = "SELECT permission_key FROM user_custom_permissions WHERE user_id = ? AND is_granted = 1";
$current_stmt = $conn->prepare($current_query);
$current_stmt->bind_param("i", $user['id']);
$current_stmt->execute();
$current_result = $current_stmt->get_result();
while ($row = $current_result->fetch_assoc()) {
    $current_permissions[] = $row['permission_key'];
}

// جلب جميع موارد النظام المتاحة
$resources_query = "SELECT * FROM system_resources WHERE is_active = 1 ORDER BY resource_type, sort_order, resource_name";
$resources_result = $conn->query($resources_query);
$all_resources = [];
while ($resource = $resources_result->fetch_assoc()) {
    $all_resources[$resource['resource_type']][] = $resource;
}

$page_title = 'منح صلاحيات سريعة: ' . $user['full_name'];
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-user-shield me-2"></i><?php echo $page_title; ?></h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="../settings/permissions.php">إدارة الصلاحيات</a></li>
                    <li class="breadcrumb-item active">منح صلاحيات سريعة</li>
                </ol>
            </nav>
        </div>
        <a href="../admin/debug_user_permissions.php" class="btn btn-outline-info">
            <i class="fas fa-bug me-2"></i>تشخيص الصلاحيات
        </a>
    </div>

    <!-- رسائل التنبيه -->
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- بيانات المستخدم -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5><i class="fas fa-user me-2"></i>بيانات المستخدم</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <strong>الاسم:</strong> <?php echo htmlspecialchars($user['full_name']); ?>
                </div>
                <div class="col-md-4">
                    <strong>البريد الإلكتروني:</strong> <?php echo htmlspecialchars($user['email']); ?>
                </div>
                <div class="col-md-4">
                    <strong>الدور الحالي:</strong> 
                    <span class="badge bg-info"><?php echo get_role_name_arabic($user['role']); ?></span>
                </div>
            </div>
        </div>
    </div>

    <form method="POST">
        <div class="row">
            <!-- الصلاحيات المقترحة -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-magic me-2"></i>صلاحيات مقترحة للمستخدم</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb me-2"></i>اختر الصلاحيات المناسبة:</h6>
                            <p class="mb-0">
                                بناءً على طلبك، هذه هي الصلاحيات المقترحة لإظهار الفصول والمواد والامتحانات والتقارير في لوحة التحكم.
                            </p>
                        </div>

                        <!-- صلاحيات سريعة -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card border-primary">
                                    <div class="card-header bg-primary text-white">
                                        <h6><i class="fas fa-bolt me-2"></i>صلاحيات أساسية</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="permissions[]" value="classes_view" 
                                                   id="classes_view" <?php echo in_array('classes_view', $current_permissions) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="classes_view">
                                                <i class="fas fa-school me-2"></i>عرض الفصول
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="permissions[]" value="subjects_view" 
                                                   id="subjects_view" <?php echo in_array('subjects_view', $current_permissions) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="subjects_view">
                                                <i class="fas fa-book me-2"></i>عرض المواد
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="permissions[]" value="exams_view" 
                                                   id="exams_view" <?php echo in_array('exams_view', $current_permissions) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="exams_view">
                                                <i class="fas fa-file-alt me-2"></i>عرض الامتحانات
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="permissions[]" value="reports_view" 
                                                   id="reports_view" <?php echo in_array('reports_view', $current_permissions) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="reports_view">
                                                <i class="fas fa-chart-bar me-2"></i>عرض التقارير
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-dark">
                                        <h6><i class="fas fa-chart-line me-2"></i>تقارير محددة</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="permissions[]" value="student_reports" 
                                                   id="student_reports" <?php echo in_array('student_reports', $current_permissions) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="student_reports">
                                                <i class="fas fa-user-graduate me-2"></i>تقارير الطلاب
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="permissions[]" value="attendance_reports" 
                                                   id="attendance_reports" <?php echo in_array('attendance_reports', $current_permissions) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="attendance_reports">
                                                <i class="fas fa-calendar-check me-2"></i>تقارير الحضور
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="permissions[]" value="exam_reports" 
                                                   id="exam_reports" <?php echo in_array('exam_reports', $current_permissions) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="exam_reports">
                                                <i class="fas fa-file-alt me-2"></i>تقارير الامتحانات
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="permissions[]" value="academic_reports" 
                                                   id="academic_reports" <?php echo in_array('academic_reports', $current_permissions) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="academic_reports">
                                                <i class="fas fa-graduation-cap me-2"></i>التقارير الأكاديمية
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار سريعة -->
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-success" onclick="selectBasicPermissions()">
                                        <i class="fas fa-check me-2"></i>اختيار الصلاحيات الأساسية
                                    </button>
                                    <button type="button" class="btn btn-outline-info" onclick="selectAllReports()">
                                        <i class="fas fa-chart-bar me-2"></i>اختيار جميع التقارير
                                    </button>
                                    <button type="button" class="btn btn-outline-warning" onclick="selectAll()">
                                        <i class="fas fa-check-double me-2"></i>اختيار الكل
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="clearAll()">
                                        <i class="fas fa-times me-2"></i>إلغاء الكل
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- جميع الصلاحيات المتاحة -->
                        <div class="accordion" id="allPermissionsAccordion">
                            <?php foreach ($all_resources as $type => $resources): ?>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="heading<?php echo $type; ?>">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                                data-bs-target="#collapse<?php echo $type; ?>">
                                            <?php 
                                            $type_names = [
                                                'page' => 'الصفحات',
                                                'action' => 'الإجراءات',
                                                'data' => 'البيانات',
                                                'report' => 'التقارير'
                                            ];
                                            echo $type_names[$type] ?? $type;
                                            ?> (<?php echo count($resources); ?>)
                                        </button>
                                    </h2>
                                    <div id="collapse<?php echo $type; ?>" class="accordion-collapse collapse" 
                                         data-bs-parent="#allPermissionsAccordion">
                                        <div class="accordion-body">
                                            <div class="row">
                                                <?php foreach ($resources as $resource): ?>
                                                    <div class="col-md-6 mb-2">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" 
                                                                   name="permissions[]" value="<?php echo $resource['resource_key']; ?>" 
                                                                   id="<?php echo $resource['resource_key']; ?>"
                                                                   <?php echo in_array($resource['resource_key'], $current_permissions) ? 'checked' : ''; ?>>
                                                            <label class="form-check-label" for="<?php echo $resource['resource_key']; ?>">
                                                                <i class="<?php echo $resource['icon']; ?> me-2"></i>
                                                                <?php echo htmlspecialchars($resource['resource_name']); ?>
                                                            </label>
                                                        </div>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <div class="d-flex justify-content-between mt-4">
                            <a href="../settings/permissions.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>العودة
                            </a>
                            <button type="submit" name="grant_permissions" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>حفظ الصلاحيات
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الصلاحيات الحالية -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h6><i class="fas fa-list me-2"></i>الصلاحيات الحالية (<?php echo count($current_permissions); ?>)</h6>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($current_permissions)): ?>
                            <ul class="list-group list-group-flush">
                                <?php foreach ($current_permissions as $permission): ?>
                                    <li class="list-group-item d-flex justify-content-between align-items-center p-2">
                                        <small><?php echo $permission; ?></small>
                                        <span class="badge bg-success">✓</span>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                لا توجد صلاحيات مخصصة حالياً
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- روابط سريعة -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-link me-2"></i>إجراءات أخرى</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="../admin/debug_user_permissions.php" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-bug me-2"></i>تشخيص مفصل
                            </a>
                            <a href="../admin/edit_user_role.php?id=<?php echo $user['id']; ?>" class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-user-edit me-2"></i>تغيير الدور
                            </a>
                            <a href="../admin/permissions_manager.php" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-users me-2"></i>جميع المستخدمين
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
function selectBasicPermissions() {
    const basicPermissions = ['classes_view', 'subjects_view', 'exams_view', 'reports_view'];
    basicPermissions.forEach(permission => {
        const checkbox = document.getElementById(permission);
        if (checkbox) checkbox.checked = true;
    });
}

function selectAllReports() {
    const reportPermissions = ['student_reports', 'attendance_reports', 'exam_reports', 'academic_reports'];
    reportPermissions.forEach(permission => {
        const checkbox = document.getElementById(permission);
        if (checkbox) checkbox.checked = true;
    });
}

function selectAll() {
    const checkboxes = document.querySelectorAll('input[name="permissions[]"]');
    checkboxes.forEach(checkbox => checkbox.checked = true);
}

function clearAll() {
    const checkboxes = document.querySelectorAll('input[name="permissions[]"]');
    checkboxes.forEach(checkbox => checkbox.checked = false);
}
</script>

<?php include_once '../includes/footer.php'; ?>
