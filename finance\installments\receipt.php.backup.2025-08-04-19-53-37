<?php
require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/security.php';

// التحقق من الجلسة والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

// جلب معرف القسط
$installment_id = intval($_GET['id'] ?? 0);

if ($installment_id <= 0) {
    header('Location: index.php?error=' . urlencode('معرف القسط غير صحيح'));
    exit();
}

// جلب بيانات القسط مع تفاصيل الطالب والحساب البنكي
$stmt = $conn->prepare("
    SELECT
        si.id,
        si.installment_number,
        si.receipt_number,
        si.total_amount,
        si.paid_amount,
        si.due_date,
        si.paid_date,
        si.status,
        si.notes,
        si.created_at,
        si.bank_account_id,
        u.full_name as student_name,
        s.student_id,
        s.parent_name,
        s.parent_phone,
        s.address,
        c.class_name,
        c.grade_level,
        ba.bank_name,
        ba.account_number,
        ba.account_name,
        ba.iban,
        ba.branch_name
    FROM student_installments si
    JOIN students s ON si.student_id = s.id
    JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    LEFT JOIN bank_accounts ba ON si.bank_account_id = ba.id
    WHERE si.id = ?
");

$stmt->bind_param("i", $installment_id);
$stmt->execute();
$installment = $stmt->get_result()->fetch_assoc();

if (!$installment) {
    header('Location: index.php?error=' . urlencode('القسط غير موجود'));
    exit();
}

// جلب تاريخ المدفوعات
$payments_stmt = $conn->prepare("
    SELECT
        amount,
        payment_date,
        payment_method,
        payment_reference,
        receipt_number,
        notes,
        created_at
    FROM student_payments
    WHERE installment_id = ?
    ORDER BY created_at DESC
");

$payments = [];
if ($payments_stmt) {
    $payments_stmt->bind_param("i", $installment_id);
    $payments_stmt->execute();
    $payments = $payments_stmt->get_result()->fetch_all(MYSQLI_ASSOC);
}

// جلب بيانات المدرسة من الإعدادات
$school_name = get_system_setting('school_name', 'مدرسة النموذج');
$school_address = get_system_setting('school_address', '');
$school_logo = get_system_setting('school_logo', '');

$remaining_amount = $installment['total_amount'] - $installment['paid_amount'];

// دالة تحويل الأرقام إلى كلمات (التفقيط)
function numberToWords($number) {
    $ones = array(
        '', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة',
        'عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر', 'ستة عشر',
        'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'
    );

    $tens = array(
        '', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'
    );

    $hundreds = array(
        '', 'مائة', 'مائتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة', 'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'
    );

    if ($number == 0) return 'صفر';

    $result = '';

    // معالجة الآلاف
    if ($number >= 1000) {
        $thousands = intval($number / 1000);
        if ($thousands == 1) {
            $result .= 'ألف ';
        } elseif ($thousands == 2) {
            $result .= 'ألفان ';
        } elseif ($thousands < 11) {
            $result .= $ones[$thousands] . ' آلاف ';
        } else {
            $result .= convertHundreds($thousands) . ' ألف ';
        }
        $number = $number % 1000;
    }

    // معالجة المئات والعشرات والآحاد
    $result .= convertHundreds($number);

    return trim($result);
}

function convertHundreds($number) {
    $ones = array(
        '', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة',
        'عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر', 'ستة عشر',
        'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'
    );

    $tens = array(
        '', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'
    );

    $hundreds = array(
        '', 'مائة', 'مائتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة', 'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'
    );

    $result = '';

    if ($number >= 100) {
        $result .= $hundreds[intval($number / 100)] . ' ';
        $number = $number % 100;
    }

    if ($number >= 20) {
        $result .= $tens[intval($number / 10)];
        if ($number % 10 > 0) {
            $result .= ' ' . $ones[$number % 10];
        }
    } elseif ($number > 0) {
        $result .= $ones[$number];
    }

    return trim($result);
}

$page_title = 'إيصال إيداع';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* إعدادات عامة */
        * {
            box-sizing: border-box;
        }

        @page {
            size: A4;
            margin: 0;
        }

        @page:first {
            margin: 0;
        }

        @page:last {
            display: none;
        }

        @media print {
            /* إخفاء العناصر غير المرغوبة فقط */
            .no-print {
                display: none !important;
            }

            /* إخفاء عناصر الصفحة الخارجية */
            body > *:not(.container) {
                display: none !important;
            }

            /* تنسيق الحاوية الرئيسية */
            .container {
                max-width: none !important;
                width: 100% !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            /* إخفاء كل شيء في الحاوية عدا النموذج */
            .container > *:not(.receipt-container) {
                display: none !important;
            }

            /* تنسيق النموذج للطباعة على A4 بدون هوامش - ورقة واحدة فقط */
            .receipt-container {
                margin: 0 !important;
                padding: 10px !important;
                box-shadow: none !important;
                border-radius: 0 !important;
                background: white !important;
                width: 100% !important;
                max-width: none !important;
                page-break-inside: avoid !important;
                page-break-after: avoid !important;
                page-break-before: avoid !important;
                transform: scale(0.95) !important;
                transform-origin: top left !important;
                height: calc(100vh - 20px) !important;
                max-height: calc(100vh - 20px) !important;
                overflow: hidden !important;
                box-sizing: border-box !important;
                position: relative !important;
            }

            /* إزالة الألوان من الطباعة - أبيض وأسود فقط */
            * {
                -webkit-print-color-adjust: economy !important;
                color-adjust: economy !important;
                print-color-adjust: economy !important;
                color: black !important;
                background-color: white !important;
                background-image: none !important;
                background: white !important;
                border-color: black !important;
            }

            /* تنسيق الصفحة بدون هوامش - منع الصفحات الإضافية */
            body {
                background: white !important;
                margin: 0 !important;
                padding: 0 !important;
                font-family: inherit !important;
                font-size: inherit !important;
                line-height: inherit !important;
                width: 100% !important;
                height: 100vh !important;
                max-height: 100vh !important;
                overflow: hidden !important;
                page-break-after: avoid !important;
            }

            html {
                margin: 0 !important;
                padding: 0 !important;
                width: 100% !important;
                height: 100vh !important;
                max-height: 100vh !important;
                overflow: hidden !important;
            }

            /* إزالة جميع الهوامش من العناصر ومنع كسر الصفحة */
            * {
                margin: 0 !important;
                box-sizing: border-box !important;
                page-break-after: avoid !important;
                page-break-before: avoid !important;
                page-break-inside: avoid !important;
            }

            .container {
                margin: 0 !important;
                padding: 0 !important;
                width: 100% !important;
                max-width: none !important;
                height: 100vh !important;
                max-height: 100vh !important;
                overflow: hidden !important;
            }

            /* منع إنشاء صفحات إضافية */
            body::after,
            .container::after,
            .receipt-container::after {
                content: none !important;
                display: none !important;
            }

            /* تحسين العناصر لاحتواء ورقة واحدة */
            .receipt-container h2 {
                font-size: 20px !important;
                margin: 8px 0 !important;
            }

            .receipt-container h6 {
                font-size: 16px !important;
                margin: 8px 0 !important;
            }

            .receipt-container p {
                font-size: 14px !important;
                margin: 6px 0 !important;
                line-height: 1.4 !important;
            }

            .receipt-container .receipt-title {
                font-size: clamp(20px, 3.5vw, 24px) !important;
                padding: 12px !important;
                margin: 15px 0 !important;
            }

            .receipt-container .info-box {
                padding: 12px !important;
                margin-bottom: 12px !important;
                font-size: 13px !important;
                min-height: 140px !important;
                display: flex !important;
                flex-direction: column !important;
            }

            /* تحسين عناوين المربعات - في المنتصف */
            .receipt-container .info-box h6 {
                font-size: clamp(14px, 3.5vw, 20px) !important;
                margin-bottom: 12px !important;
                flex-shrink: 0 !important;
                text-align: center !important;
                width: 100% !important;
                display: block !important;
            }

            /* تحسين الجداول داخل المربعات */
            .receipt-container .info-box table {
                flex-grow: 1 !important;
                display: table !important;
                width: 100% !important;
            }

            .receipt-container table {
                font-size: 12px !important;
                width: 100% !important;
            }

            /* احتواء تلقائي للخطوط في الجداول - تخطيط مضغوط */
            .receipt-container table td {
                font-size: clamp(11px, 2.5vw, 16px) !important;
                line-height: 1.3 !important;
                padding: 6px 3px !important;
                vertical-align: top !important;
                word-wrap: break-word !important;
                overflow-wrap: break-word !important;
                hyphens: auto !important;
            }

            .receipt-container table td strong {
                font-size: clamp(12px, 2.8vw, 17px) !important;
                font-weight: bold !important;
            }

            /* تحسين عرض النصوص - البيانات بجانب العناوين مباشرة */
            .receipt-container table {
                border-collapse: collapse !important;
                table-layout: auto !important;
            }

            .receipt-container table td:first-child {
                width: auto !important;
                min-width: auto !important;
                white-space: nowrap !important;
                padding-right: 10px !important;
            }

            .receipt-container table td:last-child {
                width: auto !important;
                font-size: clamp(12px, 3vw, 18px) !important;
                padding-left: 0 !important;
            }

            /* تحسين النصوص الطويلة */
            .receipt-container table td[colspan] {
                font-size: clamp(13px, 3.2vw, 19px) !important;
                text-align: center !important;
            }

            /* تخطيط البيانات بجانب العناوين مباشرة */
            .receipt-container table td {
                direction: rtl !important;
                text-align: right !important;
                unicode-bidi: embed !important;
                vertical-align: top !important;
            }

            .receipt-container table td:first-child {
                text-align: right !important;
                font-weight: bold !important;
                padding-left: 5px !important;
                border-left: none !important;
            }

            .receipt-container table td:last-child {
                text-align: right !important;
                color: #333 !important;
                padding-right: 0 !important;
            }

            /* تحسين تخطيط الصفوف */
            .receipt-container table tr {
                display: table-row !important;
            }

            .receipt-container table tr td {
                display: table-cell !important;
                border: none !important;
            }

            /* تحسين عرض أسماء الطلاب الطويلة - أسود */
            .receipt-container table tr:first-child td:last-child {
                font-size: clamp(13px, 3.5vw, 20px) !important;
                font-weight: 600 !important;
                color: black !important;
            }

            /* تحسين عرض أسماء الصفوف - أسود */
            .receipt-container table tr:nth-child(2) td:last-child {
                font-size: clamp(12px, 3.2vw, 18px) !important;
                font-weight: 500 !important;
                color: black !important;
            }

            /* تحسين عرض نوع الرسوم - أسود عريض */
            .receipt-container .info-box:last-child table tr:first-child td:last-child {
                font-size: clamp(13px, 3.3vw, 19px) !important;
                font-weight: bold !important;
                color: black !important;
            }

            /* تحسين التخطيط العام للجداول */
            .receipt-container .info-box table {
                margin: 0 !important;
                border-spacing: 0 !important;
                border-collapse: separate !important;
            }

            .receipt-container .info-box table tr {
                margin-bottom: 4px !important;
            }

            .receipt-container .info-box table tr td:first-child::after {
                content: " " !important;
                white-space: pre !important;
            }

            /* تحسين المسافة بين العنوان والقيمة */
            .receipt-container table td:first-child {
                position: relative !important;
            }

            .receipt-container table td:first-child strong::after {
                content: "" !important;
                margin-left: 5px !important;
            }

            /* تحسين عرض الأيقونات في العناوين */
            .receipt-container .info-box h6 i {
                margin-left: 8px !important;
                font-size: 1.1em !important;
                vertical-align: middle !important;
            }

            /* تحسين النص في العناوين */
            .receipt-container .info-box h6 {
                font-weight: bold !important;
                letter-spacing: 0.5px !important;
                text-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
            }

            .receipt-container .school-logo {
                max-height: 60px !important;
                max-width: 60px !important;
                position: relative !important;
                z-index: 10 !important;
            }

            /* ضمان عدم تأثر اللوجو بتنسيق العناوين */
            .receipt-container .school-logo img {
                display: block !important;
                margin: 0 auto !important;
            }

            .receipt-container .border.rounded {
                padding: 8px !important;
                border: 1px solid black !important;
                border-radius: 5px !important;
            }

            .receipt-container .border.rounded strong {
                font-size: 12px !important;
            }

            .receipt-container .border.rounded small {
                font-size: 10px !important;
            }

            .receipt-container .signature-box {
                margin-top: 15px !important;
                font-size: 12px !important;
            }

            .receipt-container .signature-line {
                width: 150px !important;
                height: 30px !important;
                border-bottom: 1px solid #333 !important;
            }

            .receipt-container .lead {
                font-size: 15px !important;
                line-height: 1.5 !important;
            }

            /* تحسين البيانات المهمة - خط أكبر قليلاً */
            .receipt-container .text-center h5 {
                font-size: clamp(16px, 3vw, 19px) !important;
                font-weight: bold !important;
                color: black !important;
                margin-bottom: 12px !important;
                line-height: 1.4 !important;
            }

            .receipt-container .text-center .lead {
                font-size: clamp(15px, 2.8vw, 18px) !important;
                line-height: 1.5 !important;
                font-weight: 500 !important;
                color: black !important;
                margin-bottom: 18px !important;
            }

            /* تحسين المبلغ - خط أكبر قليلاً */
            .receipt-container .text-center .lead strong {
                font-size: clamp(17px, 3.2vw, 21px) !important;
                color: black !important;
                font-weight: bold !important;
                background: white !important;
                border: none !important;
                padding: 0 !important;
                border-radius: 0 !important;
            }

            /* تحسين النص المكتوب بالحروف - خط أكبر قليلاً */
            .receipt-container .text-center .lead .text-muted {
                font-size: clamp(14px, 2.5vw, 17px) !important;
                color: black !important;
                font-style: italic !important;
                font-weight: 400 !important;
            }

            /* تحسين نص القسط - بدون حدود */
            .receipt-container .text-center .lead {
                padding: 10px !important;
                background: white !important;
                border-radius: 0 !important;
                border: none !important;
                margin: 10px 0 !important;
            }

            /* تعديل تخطيط الطباعة - جعل العناصر جنباً إلى جنب */

            /* تخطيط بيانات الطالب وتفاصيل الإيداع جنباً إلى جنب */
            .receipt-container .row:has(.info-box) {
                display: flex !important;
                flex-wrap: nowrap !important;
                gap: 20px !important;
            }

            .receipt-container .row:has(.info-box) .col-md-6 {
                flex: 1 !important;
                max-width: 48% !important;
                margin-bottom: 0 !important;
            }

            /* تخطيط التوقيعات جنباً إلى جنب */
            .receipt-container .signatures .row {
                display: flex !important;
                justify-content: space-between !important;
                align-items: flex-start !important;
                gap: 40px !important;
            }

            .receipt-container .signatures .col-md-6 {
                flex: 1 !important;
                max-width: 45% !important;
                text-align: center !important;
            }

            /* تحسين المسافات للاحتواء في ورقة واحدة */
            .receipt-container .info-box {
                height: auto !important;
                min-height: 120px !important;
                max-height: 150px !important;
            }

            .receipt-container .signature-box {
                margin-top: 10px !important;
                margin-bottom: 0 !important;
            }

            /* ضمان عدم تجاوز حدود الصفحة */
            .receipt-container .row {
                margin-bottom: 8px !important;
            }

            .receipt-container .signatures {
                margin-top: 15px !important;
            }

            .receipt-container .signatures .row {
                margin-bottom: 0 !important;
            }

            /* تقليل المسافات العامة */
            .receipt-container .mb-4 {
                margin-bottom: 1rem !important;
            }

            .receipt-container .mt-5 {
                margin-top: 1rem !important;
            }

            .receipt-container .mb-3 {
                margin-bottom: 0.5rem !important;
            }

            /* منع العناصر من إنشاء صفحة ثانية */
            .receipt-container > *:last-child {
                margin-bottom: 0 !important;
                padding-bottom: 0 !important;
            }

            .receipt-container .signatures {
                page-break-inside: avoid !important;
                page-break-after: avoid !important;
            }

            /* إزالة أي مسافات إضافية في النهاية */
            .receipt-container::after {
                content: none !important;
                display: none !important;
            }

            /* ضمان عدم تجاوز المحتوى */
            .receipt-container * {
                max-height: none !important;
                page-break-after: avoid !important;
            }

            /* منع المسافات الإضافية */
            .receipt-container p:last-child,
            .receipt-container div:last-child {
                margin-bottom: 0 !important;
                padding-bottom: 0 !important;
            }

            /* خط رفيع قبل تاريخ الإنشاء */
            .receipt-container .border-top {
                border-top: 1px solid black !important;
                margin-top: 20px !important;
                padding-top: 10px !important;
            }

            .receipt-container .text-center.mt-4.pt-3 {
                margin-top: 20px !important;
                padding-top: 10px !important;
            }

            .receipt-container .text-center.mt-4.pt-3 small {
                font-size: clamp(10px, 1.8vw, 12px) !important;
                color: black !important;
                line-height: 1.4 !important;
            }

            /* إزالة جميع الحدود من العناصر الأخرى */
            .receipt-container * {
                border: none !important;
                box-shadow: none !important;
                outline: none !important;
            }

            /* إنشاء مربعين بسيطين للبيانات */
            .receipt-container .info-box {
                border: 1px solid black !important;
                border-radius: 5px !important;
                padding: 15px !important;
                margin-bottom: 15px !important;
                background: white !important;
            }

            /* عناوين المربعات */
            .receipt-container .info-box h6 {
                border-bottom: 1px solid black !important;
                margin-bottom: 10px !important;
                padding-bottom: 5px !important;
            }

            /* خطوط التوقيع */
            .receipt-container .signature-line {
                border-bottom: 1px solid black !important;
                border-top: none !important;
                border-left: none !important;
                border-right: none !important;
            }

            /* تحسينات إضافية للنص المهم */
            .receipt-container .text-center {
                position: relative !important;
            }

            .receipt-container .text-center h5::before {
                content: "" !important;
                margin-left: 5px !important;
                font-size: 1em !important;
            }

            .receipt-container .text-center .lead::before {
                content: "" !important;
                display: none !important;
            }

            /* تحسين عرض المبلغ - بدون مربعات */
            .receipt-container .text-center .lead strong {
                display: inline !important;
                padding: 0 !important;
                border-radius: 0 !important;
                background: white !important;
                color: black !important;
                border: none !important;
                margin: 0 !important;
                font-weight: bold !important;
            }

            /* تحسين النص المكتوب بالحروف - بدون مربعات */
            .receipt-container .text-center .lead .text-muted {
                display: inline !important;
                margin: 5px 0 !important;
                padding: 0 !important;
                background: white !important;
                color: black !important;
                border-radius: 0 !important;
                border: none !important;
            }

            /* إضافة CSS لضمان التخطيط المناسب */
            .receipt-container .row {
                margin-left: 0 !important;
                margin-right: 0 !important;
            }

            .receipt-container .col-md-6 {
                padding-left: 10px !important;
                padding-right: 10px !important;
            }

            /* تحسين عرض عناوين المربعات */
            .receipt-container .info-box h6 {
                margin-bottom: 10px !important;
                border-bottom: 1px solid black !important;
                padding-bottom: 5px !important;
                text-align: center !important;
                background: white !important;
                color: black !important;
                margin: 0 0 10px 0 !important;
                padding: 5px 0 !important;
                border-radius: 0 !important;
                font-weight: bold !important;
                font-size: clamp(14px, 2.5vw, 16px) !important;
            }

            .receipt-container .info-box table {
                width: 100% !important;
                margin-bottom: 0 !important;
            }

            .receipt-container .info-box table td {
                padding: 8px 5px !important;
                vertical-align: top !important;
            }

            /* تحسين التوقيعات */
            .receipt-container .signature-line {
                margin: 0 auto 15px auto !important;
            }

            .receipt-container .signatures h6 {
                margin-bottom: 20px !important;
                text-align: center !important;
                font-size: 16px !important;
            }
        }
        .receipt-header {
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .school-logo {
            max-height: 80px;
            max-width: 80px;
        }
        .receipt-title {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
        }
        .info-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .signatures {
            margin-top: 30px;
            page-break-inside: avoid;
        }
        .signature-box {
            margin-top: 40px;
        }
        .signature-line {
            border-bottom: 2px solid #333;
            width: 200px;
            margin: 0 auto 10px auto;
            height: 40px;
        }
        .receipt-container {
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            padding: 30px;
            margin: 20px 0;
        }


            /* الحفاظ على المسافات الأصلية */
            .mb-1 { margin-bottom: 0.25rem !important; }
            .mb-2 { margin-bottom: 0.5rem !important; }
            .mb-3 { margin-bottom: 1rem !important; }
            .mb-4 { margin-bottom: 1.5rem !important; }
            .mb-5 { margin-bottom: 3rem !important; }
            .mt-1 { margin-top: 0.25rem !important; }
            .mt-2 { margin-top: 0.5rem !important; }
            .mt-3 { margin-top: 1rem !important; }
            .mt-4 { margin-top: 1.5rem !important; }
            .mt-5 { margin-top: 3rem !important; }
            .py-4 {
                padding: 1.5rem 0 !important;
            }
            /* الحفاظ على الألوان والتدرجات */
            .text-primary {
                color: #007bff !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }
            .text-success {
                color: #28a745 !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }
            .text-muted {
                color: #6c757d !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }
            .bg-light {
                background-color: #f8f9fa !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }

            /* ضمان طباعة جميع الألوان */
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            /* ضمان عدم تقسيم المحتوى */
            .receipt-header,
            .receipt-title,
            .info-box,
            .signatures {
                page-break-inside: avoid;
            }

            /* منع تقليص العناصر */
            .receipt-container,
            .card,
            .info-box,
            .receipt-title {
                transform: none !important;
                zoom: 1 !important;
                scale: 1 !important;
            }

            /* ضمان عدم تغيير الخطوط */
            h1, h2, h3, h4, h5, h6, p, span, div, td, th {
                font-family: inherit !important;
                font-weight: inherit !important;
            }
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <!-- أزرار التحكم -->
        <div class="no-print mb-3">
            <div class="d-flex justify-content-between">
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i><?php echo __('back'); ?>
                </a>
                <button onclick="window.print()" class="btn btn-primary">
                    <i class="fas fa-print me-2"></i><?php echo __('print'); ?>
                </button>
            </div>
        </div>

        <!-- الإيصال -->
        <div class="receipt-container">
            <div class="card shadow-lg">
                <div class="card-body">
                <!-- هيدر المدرسة -->
                <div class="receipt-header">
                    <div class="row align-items-center">
                        <div class="col-md-2 text-center">
                            <?php if (!empty($school_logo)): ?>
                                <img src="../../uploads/<?php echo htmlspecialchars($school_logo); ?>" alt="شعار المدرسة" class="school-logo">
                            <?php else: ?>
                                <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                                    <i class="fas fa-school fa-2x"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-8 text-center">
                            <h2 class="text-primary mb-1"><?php echo htmlspecialchars($school_name); ?></h2>
                            <?php if (!empty($school_address)): ?>
                                <p class="mb-1"><i class="fas fa-map-marker-alt me-2"></i><?php echo htmlspecialchars($school_address); ?></p>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-2 text-center">
                            <div class="border rounded p-2">
                                <small class="text-muted d-block mb-1">رقم الإيصال</small>
                                <strong><?php echo htmlspecialchars($installment['receipt_number'] ?? '#' . str_pad($installment['id'], 6, '0', STR_PAD_LEFT)); ?></strong>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- عنوان الإيصال -->
                <div class="receipt-title">
                    <h3 class="mb-0 text-center"><i class="fas fa-university me-2"></i>إيصال إيداع</h3>
                </div>

                <!-- نص الإيداع -->
                <div class="text-center mb-4">
                    <h5 class="mb-3">السيد / الأستاذ مدير بنك القاهرة - بورسعيد</h5>
                    <p class="lead">
                        برجاء قبول مبلغ <strong><?php echo number_format($installment['total_amount'], 0, '.', '.'); ?> <?php echo get_currency_symbol(); ?></strong>
                        <br>
                        <span class="text-muted" style="font-size: 0.9em;">
                            (فقط <?php echo numberToWords(intval($installment['total_amount'])); ?> <?php echo get_currency_name(); ?>اً لا غير)
                        </span>
                        <br>
                        وذلك قيمة
                        <?php
                        // تحديد نص رقم القسط
                        $installment_text = '';
                        switch ($installment['installment_number']) {
                            case 0:
                                $installment_text = 'إجمالي';
                                break;
                            case 1:
                                $installment_text = 'القسط الأول';
                                break;
                            case 2:
                                $installment_text = 'القسط الثاني';
                                break;
                            case 3:
                                $installment_text = 'القسط الثالث';
                                break;
                            default:
                                // في حالة وجود رقم غير متوقع، نعيد تعيينه إلى القسط الأول
                                $installment_text = 'القسط الأول';
                        }
                        ?>
                        <strong><?php echo $installment_text; ?></strong>
                        <strong>رسوم دراسية</strong>
                    </p>
                </div>

                <!-- تفاصيل الطالب -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="info-box">
                            <h6 class="text-primary mb-3"><i class="fas fa-user-graduate me-2"></i>بيانات الطالب</h6>
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td><strong>اسم الطالب:</strong></td>
                                    <td><?php echo htmlspecialchars($installment['student_name']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>الصف:</strong></td>
                                    <td><?php echo htmlspecialchars($installment['class_name'] ?? 'غير محدد'); ?> - <?php echo htmlspecialchars($installment['grade_level'] ?? ''); ?></td>
                                </tr>
                                <?php // تم إخفاء بيانات ولي الأمر ?>
                                <?php /*
                                <?php if (!empty($installment['parent_name'])): ?>
                                <tr>
                                    <td><strong>ولي الأمر:</strong></td>
                                    <td><?php echo htmlspecialchars($installment['parent_name']); ?></td>
                                </tr>
                                <?php endif; ?>
                                */ ?>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-box">
                            <h6 class="text-primary mb-3"><i class="fas fa-file-invoice me-2"></i>تفاصيل الإيداع</h6>
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td><strong>نوع الرسوم:</strong></td>
                                    <td>رسوم دراسية</td>
                                </tr>
                                <?php if (!empty($installment['bank_name'])): ?>
                                <tr>
                                    <td><strong>البنك:</strong></td>
                                    <td><?php echo htmlspecialchars($installment['bank_name']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>رقم الحساب:</strong></td>
                                    <td><?php echo htmlspecialchars($installment['account_number']); ?></td>
                                </tr>
                                <?php // تم إخفاء رقم الآيبان ?>
                                <?php /*
                                <?php if (!empty($installment['iban'])): ?>
                                <tr>
                                    <td><strong>رقم الآيبان:</strong></td>
                                    <td><?php echo htmlspecialchars($installment['iban']); ?></td>
                                </tr>
                                <?php endif; ?>
                                */ ?>
                                <?php // تم إخفاء اسم الفرع ?>
                                <?php /*
                                <?php if (!empty($installment['branch_name'])): ?>
                                <tr>
                                    <td><strong>الفرع:</strong></td>
                                    <td><?php echo htmlspecialchars($installment['branch_name']); ?></td>
                                </tr>
                                <?php endif; ?>
                                */ ?>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- التوقيعات -->
                <div class="signatures mt-5">
                    <div class="row">
                        <div class="col-md-6 text-center">
                            <div class="signature-box">
                                <div class="signature-line mb-2"></div>
                                <p class="mb-1"><strong>مديرة الحسابات</strong></p>
                                <p class="mb-0">ميرنا وجدي</p>
                            </div>
                        </div>
                        <div class="col-md-6 text-center">
                            <div class="signature-box">
                                <div class="signature-line mb-2"></div>
                                <p class="mb-1"><strong>مدير المدرسة</strong></p>
                                <p class="mb-0">موريس فريد</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- فوتر الإيصال -->
                <div class="text-center mt-4 pt-3 border-top">
                    <small class="text-muted">
                        <?php echo __('receipt_generated_on'); ?>: <?php echo date('Y-m-d H:i:s'); ?><br>
                        <?php echo __('generated_by'); ?>: <?php echo htmlspecialchars($_SESSION['user_name'] ?? 'النظام'); ?>
                    </small>
                </div>
            </div>
        </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
