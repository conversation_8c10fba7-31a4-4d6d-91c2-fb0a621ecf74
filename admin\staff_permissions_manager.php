<?php
/**
 * إدارة صلاحيات الإداري
 * Staff Permissions Manager
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من الصلاحيات (مدير أو إداري مع صلاحية إدارة الصلاحيات)
check_session();
if (!isset($_SESSION['role']) || ($_SESSION['role'] !== 'admin' && !has_permission('permissions_management'))) {
    header('Location: ../dashboard/');
    exit();
}

$user_email = '<EMAIL>';

$message = '';
$message_type = '';

// جلب بيانات المستخدم
$user_query = "SELECT * FROM users WHERE email = ?";
$user_stmt = $conn->prepare($user_query);
$user_stmt->bind_param("s", $user_email);
$user_stmt->execute();
$user = $user_stmt->get_result()->fetch_assoc();

if (!$user) {
    die("❌ المستخدم غير موجود!");
}

// جلب الصلاحيات الحالية
$current_permissions = [];
try {
    $permissions_query = "
        SELECT ucp.permission_key, ucp.is_granted, sr.resource_name, sr.resource_type, sr.icon
        FROM user_custom_permissions ucp
        LEFT JOIN system_resources sr ON ucp.permission_key = sr.resource_key
        WHERE ucp.user_id = ?
        ORDER BY sr.sort_order, ucp.permission_key
    ";
    $permissions_stmt = $conn->prepare($permissions_query);
    $permissions_stmt->bind_param("i", $user['id']);
    $permissions_stmt->execute();
    $permissions_result = $permissions_stmt->get_result();
    
    while ($perm = $permissions_result->fetch_assoc()) {
        $current_permissions[] = $perm;
    }
} catch (Exception $e) {
    $current_permissions = [];
}

// جلب جميع الصلاحيات المتاحة
$available_permissions = [];
try {
    $all_permissions_query = "
        SELECT resource_key, resource_name, resource_type, resource_description, icon, parent_resource
        FROM system_resources 
        WHERE is_active = 1 
        ORDER BY sort_order, resource_name
    ";
    $all_permissions_result = $conn->query($all_permissions_query);
    
    while ($perm = $all_permissions_result->fetch_assoc()) {
        $available_permissions[] = $perm;
    }
} catch (Exception $e) {
    $available_permissions = [];
}

// معالجة تحديث الصلاحيات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_permissions'])) {
    try {
        $selected_permissions = $_POST['permissions'] ?? [];
        
        // حذف جميع الصلاحيات الحالية
        $delete_stmt = $conn->prepare("DELETE FROM user_custom_permissions WHERE user_id = ?");
        $delete_stmt->bind_param("i", $user['id']);
        $delete_stmt->execute();
        
        // إضافة الصلاحيات المحددة
        $insert_stmt = $conn->prepare("
            INSERT INTO user_custom_permissions 
            (user_id, permission_key, is_granted, granted_by, granted_at) 
            VALUES (?, ?, 1, ?, NOW())
        ");
        
        $granted_count = 0;
        foreach ($selected_permissions as $permission) {
            $insert_stmt->bind_param("isi", $user['id'], $permission, $_SESSION['user_id']);
            if ($insert_stmt->execute()) {
                $granted_count++;
            }
        }
        
        $message = "✅ تم تحديث الصلاحيات بنجاح! تم منح $granted_count صلاحية.";
        $message_type = 'success';
        
        // إعادة جلب الصلاحيات المحدثة
        $permissions_stmt->execute();
        $permissions_result = $permissions_stmt->get_result();
        $current_permissions = [];
        while ($perm = $permissions_result->fetch_assoc()) {
            $current_permissions[] = $perm;
        }
        
    } catch (Exception $e) {
        $message = "❌ خطأ في تحديث الصلاحيات: " . $e->getMessage();
        $message_type = 'danger';
    }
}

$page_title = 'إدارة صلاحيات الإداري';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-user-cog me-2"></i><?php echo $page_title; ?></h2>
            <p class="text-muted">إدارة صلاحيات المستخدم: <?php echo $user['full_name']; ?> (<?php echo $user['email']; ?>)</p>
        </div>
        <div>
            <a href="setup_staff_permissions.php" class="btn btn-outline-primary me-2">
                <i class="fas fa-cogs me-2"></i>إعداد النظام
            </a>
            <a href="../settings/permissions.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة
            </a>
        </div>
    </div>

    <!-- رسائل التنبيه -->
    <?php if ($message): ?>
        <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show">
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- الصلاحيات الحالية -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-check-circle me-2"></i>الصلاحيات الحالية (<?php echo count($current_permissions); ?>)</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($current_permissions)): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            لا توجد صلاحيات ممنوحة حالياً
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>النوع</th>
                                        <th>الصلاحية</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($current_permissions as $perm): ?>
                                        <tr>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo match($perm['resource_type']) {
                                                        'management' => 'danger',
                                                        'page' => 'primary',
                                                        'action' => 'warning',
                                                        'data' => 'info',
                                                        'report' => 'success',
                                                        default => 'secondary'
                                                    };
                                                ?>">
                                                    <?php echo $perm['resource_type'] ?? 'غير محدد'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <i class="<?php echo $perm['icon'] ?? 'fas fa-key'; ?> me-2"></i>
                                                <?php echo $perm['resource_name'] ?? $perm['permission_key']; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $perm['is_granted'] ? 'success' : 'danger'; ?>">
                                                    <?php echo $perm['is_granted'] ? 'ممنوح' : 'مرفوض'; ?>
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- تحديث الصلاحيات -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-edit me-2"></i>تحديث الصلاحيات</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>تعليمات:</h6>
                            <ul class="mb-0 small">
                                <li>اختر الصلاحيات التي تريد منحها للإداري</li>
                                <li>الصلاحيات مقسمة حسب النوع والوظيفة</li>
                                <li>يمكن منح صلاحيات شئون الطلاب والعاملين</li>
                            </ul>
                        </div>

                        <?php
                        // تجميع الصلاحيات حسب النوع
                        $grouped_permissions = [];
                        foreach ($available_permissions as $perm) {
                            $type = $perm['resource_type'];
                            if (!isset($grouped_permissions[$type])) {
                                $grouped_permissions[$type] = [];
                            }
                            $grouped_permissions[$type][] = $perm;
                        }

                        // أسماء الأنواع بالعربية
                        $type_names = [
                            'management' => 'إدارة عامة',
                            'page' => 'صفحات النظام',
                            'action' => 'إجراءات',
                            'data' => 'الوصول للبيانات',
                            'report' => 'التقارير'
                        ];

                        $current_permission_keys = array_column($current_permissions, 'permission_key');
                        ?>

                        <?php foreach ($grouped_permissions as $type => $permissions): ?>
                            <div class="mb-4">
                                <h6 class="text-<?php 
                                    echo match($type) {
                                        'management' => 'danger',
                                        'page' => 'primary',
                                        'action' => 'warning',
                                        'data' => 'info',
                                        'report' => 'success',
                                        default => 'secondary'
                                    };
                                ?>">
                                    <i class="fas fa-<?php 
                                        echo match($type) {
                                            'management' => 'crown',
                                            'page' => 'file',
                                            'action' => 'cog',
                                            'data' => 'database',
                                            'report' => 'chart-bar',
                                            default => 'key'
                                        };
                                    ?> me-2"></i>
                                    <?php echo $type_names[$type] ?? $type; ?>
                                </h6>
                                
                                <div class="row">
                                    <?php foreach ($permissions as $perm): ?>
                                        <div class="col-md-6 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" 
                                                       name="permissions[]" 
                                                       value="<?php echo $perm['resource_key']; ?>"
                                                       id="perm_<?php echo $perm['resource_key']; ?>"
                                                       <?php echo in_array($perm['resource_key'], $current_permission_keys) ? 'checked' : ''; ?>>
                                                <label class="form-check-label small" for="perm_<?php echo $perm['resource_key']; ?>">
                                                    <i class="<?php echo $perm['icon'] ?? 'fas fa-key'; ?> me-1"></i>
                                                    <?php echo $perm['resource_name']; ?>
                                                    <?php if ($perm['resource_description']): ?>
                                                        <br><span class="text-muted"><?php echo substr($perm['resource_description'], 0, 50); ?>...</span>
                                                    <?php endif; ?>
                                                </label>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="button" class="btn btn-outline-success btn-sm" onclick="selectAll()">
                                    <i class="fas fa-check-double me-1"></i>تحديد الكل
                                </button>
                                <button type="button" class="btn btn-outline-warning btn-sm" onclick="selectNone()">
                                    <i class="fas fa-times me-1"></i>إلغاء الكل
                                </button>
                            </div>
                            <button type="submit" name="update_permissions" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ الصلاحيات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات إضافية -->
    <div class="card mt-4">
        <div class="card-header bg-secondary text-white">
            <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <h6 class="text-danger">صلاحيات الإدارة العامة:</h6>
                    <ul class="small">
                        <li><strong>شئون الطلاب:</strong> إدارة كاملة للطلاب</li>
                        <li><strong>شئون العاملين:</strong> إدارة المعلمين والموظفين</li>
                        <li><strong>إدارة الصلاحيات:</strong> منح صلاحيات محدودة</li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h6 class="text-primary">صلاحيات الصفحات:</h6>
                    <ul class="small">
                        <li><strong>عرض الطلاب:</strong> الوصول لصفحة الطلاب</li>
                        <li><strong>عرض المعلمين:</strong> الوصول لصفحة المعلمين</li>
                        <li><strong>عرض الفصول:</strong> الوصول لصفحة الفصول</li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h6 class="text-success">صلاحيات التقارير:</h6>
                    <ul class="small">
                        <li><strong>تقارير الطلاب:</strong> تقارير خاصة بالطلاب</li>
                        <li><strong>تقارير العاملين:</strong> تقارير خاصة بالعاملين</li>
                        <li><strong>تقارير الحضور:</strong> تقارير الحضور والغياب</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function selectAll() {
    const checkboxes = document.querySelectorAll('input[name="permissions[]"]');
    checkboxes.forEach(checkbox => checkbox.checked = true);
}

function selectNone() {
    const checkboxes = document.querySelectorAll('input[name="permissions[]"]');
    checkboxes.forEach(checkbox => checkbox.checked = false);
}
</script>

<?php include_once '../includes/footer.php'; ?>
