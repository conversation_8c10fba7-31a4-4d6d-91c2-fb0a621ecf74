<?php
/**
 * مثال على استخدام نظام الصلاحيات المتقدم
 * Example of Advanced Permissions System Usage
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من تسجيل الدخول
check_session();

// مثال 1: التحقق من صلاحية واحدة
if (!has_permission('finance.view')) {
    permission_denied('غير مسموح لك بعرض البيانات المالية');
}

// مثال 2: التحقق من صلاحيات متعددة (يجب توفر جميعها)
if (!has_all_permissions(['students.view', 'students.edit'])) {
    permission_denied('تحتاج صلاحيات عرض وتعديل الطلاب');
}

// مثال 3: التحقق من صلاحيات متعددة (يكفي توفر واحدة)
$can_manage_users = has_any_permission(['users.create', 'users.edit', 'users.delete']);

// مثال 4: استخدام require_permission للتحقق مع إعادة التوجيه
require_permission('finance.manage_fees', '../dashboard/');

$page_title = 'مثال على نظام الصلاحيات';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <h2>مثال على استخدام نظام الصلاحيات المتقدم</h2>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>صلاحياتك الحالية</h5>
                </div>
                <div class="card-body">
                    <p><strong>الدور:</strong> <?php echo $_SESSION['role']; ?></p>
                    <p><strong>الصلاحيات:</strong></p>
                    <ul>
                        <?php foreach (get_user_permissions() as $permission): ?>
                            <li><code><?php echo $permission; ?></code></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>اختبار الصلاحيات</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>صلاحيات النظام المالي:</strong>
                        <ul class="list-unstyled">
                            <li>
                                <?php if (has_permission('finance.view')): ?>
                                    <i class="fas fa-check text-success"></i> عرض البيانات المالية
                                <?php else: ?>
                                    <i class="fas fa-times text-danger"></i> عرض البيانات المالية
                                <?php endif; ?>
                            </li>
                            <li>
                                <?php if (has_permission('finance.create')): ?>
                                    <i class="fas fa-check text-success"></i> إضافة معاملات مالية
                                <?php else: ?>
                                    <i class="fas fa-times text-danger"></i> إضافة معاملات مالية
                                <?php endif; ?>
                            </li>
                            <li>
                                <?php if (has_permission('finance.manage_fees')): ?>
                                    <i class="fas fa-check text-success"></i> إدارة الرسوم
                                <?php else: ?>
                                    <i class="fas fa-times text-danger"></i> إدارة الرسوم
                                <?php endif; ?>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="mb-3">
                        <strong>صلاحيات الطلاب:</strong>
                        <ul class="list-unstyled">
                            <li>
                                <?php if (has_permission('students.view')): ?>
                                    <i class="fas fa-check text-success"></i> عرض الطلاب
                                <?php else: ?>
                                    <i class="fas fa-times text-danger"></i> عرض الطلاب
                                <?php endif; ?>
                            </li>
                            <li>
                                <?php if (has_permission('students.edit')): ?>
                                    <i class="fas fa-check text-success"></i> تعديل بيانات الطلاب
                                <?php else: ?>
                                    <i class="fas fa-times text-danger"></i> تعديل بيانات الطلاب
                                <?php endif; ?>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- أمثلة على الواجهات المشروطة -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>أزرار مشروطة حسب الصلاحيات</h5>
                </div>
                <div class="card-body">
                    <?php if (has_permission('students.create')): ?>
                        <button class="btn btn-primary me-2">
                            <i class="fas fa-plus"></i> إضافة طالب جديد
                        </button>
                    <?php endif; ?>
                    
                    <?php if (has_permission('finance.create')): ?>
                        <button class="btn btn-success me-2">
                            <i class="fas fa-money-bill"></i> إضافة معاملة مالية
                        </button>
                    <?php endif; ?>
                    
                    <?php if (has_permission('reports.export')): ?>
                        <button class="btn btn-info me-2">
                            <i class="fas fa-download"></i> تصدير التقارير
                        </button>
                    <?php endif; ?>
                    
                    <?php if (has_permission('system.settings')): ?>
                        <button class="btn btn-warning me-2">
                            <i class="fas fa-cog"></i> إعدادات النظام
                        </button>
                    <?php endif; ?>
                    
                    <?php if (!has_any_permission(['students.create', 'finance.create', 'reports.export', 'system.settings'])): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            لا تملك صلاحيات لأي من الإجراءات المتاحة في هذه الصفحة.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
