<?php
/**
 * إضافة موارد النظام المفقودة للفصول والمواد والامتحانات والتقارير
 * Add Missing System Resources for Classes, Subjects, Exams, and Reports
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

$success_message = '';
$error_message = '';

// معالجة إضافة الموارد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_resources'])) {
    $conn->begin_transaction();
    
    try {
        // موارد النظام المطلوبة
        $resources = [
            // موارد الفصول
            ['page', 'classes_view', 'عرض الفصول', 'صفحة عرض قائمة الفصول الدراسية', 'fas fa-school', 100],
            ['page', 'classes_manage', 'إدارة الفصول', 'صفحة إدارة وتعديل الفصول الدراسية', 'fas fa-edit', 101],
            ['action', 'classes_add', 'إضافة فصل', 'إضافة فصل دراسي جديد', 'fas fa-plus', 102],
            ['action', 'classes_edit', 'تعديل فصل', 'تعديل بيانات فصل دراسي', 'fas fa-edit', 103],
            ['action', 'classes_delete', 'حذف فصل', 'حذف فصل دراسي', 'fas fa-trash', 104],
            
            // موارد المواد
            ['page', 'subjects_view', 'عرض المواد', 'صفحة عرض قائمة المواد الدراسية', 'fas fa-book', 110],
            ['page', 'subjects_manage', 'إدارة المواد', 'صفحة إدارة وتعديل المواد الدراسية', 'fas fa-edit', 111],
            ['action', 'subjects_add', 'إضافة مادة', 'إضافة مادة دراسية جديدة', 'fas fa-plus', 112],
            ['action', 'subjects_edit', 'تعديل مادة', 'تعديل بيانات مادة دراسية', 'fas fa-edit', 113],
            ['action', 'subjects_delete', 'حذف مادة', 'حذف مادة دراسية', 'fas fa-trash', 114],
            
            // موارد الامتحانات
            ['page', 'exams_view', 'عرض الامتحانات', 'صفحة عرض قائمة الامتحانات', 'fas fa-file-alt', 120],
            ['page', 'exams_manage', 'إدارة الامتحانات', 'صفحة إدارة وتعديل الامتحانات', 'fas fa-edit', 121],
            ['action', 'exams_add', 'إضافة امتحان', 'إضافة امتحان جديد', 'fas fa-plus', 122],
            ['action', 'exams_edit', 'تعديل امتحان', 'تعديل بيانات امتحان', 'fas fa-edit', 123],
            ['action', 'exams_delete', 'حذف امتحان', 'حذف امتحان', 'fas fa-trash', 124],
            ['action', 'exams_grade', 'تصحيح الامتحانات', 'تصحيح ووضع درجات الامتحانات', 'fas fa-check', 125],
            
            // موارد التقارير
            ['page', 'reports_view', 'عرض التقارير', 'صفحة عرض التقارير العامة', 'fas fa-chart-bar', 130],
            ['report', 'student_reports', 'تقارير الطلاب', 'تقارير خاصة بالطلاب', 'fas fa-user-graduate', 131],
            ['report', 'attendance_reports', 'تقارير الحضور', 'تقارير الحضور والغياب', 'fas fa-calendar-check', 132],
            ['report', 'exam_reports', 'تقارير الامتحانات', 'تقارير الامتحانات والدرجات', 'fas fa-file-alt', 133],
            ['report', 'financial_reports', 'التقارير المالية', 'التقارير المالية والمحاسبية', 'fas fa-money-bill-wave', 134],
            ['report', 'academic_reports', 'التقارير الأكاديمية', 'التقارير الأكاديمية والتعليمية', 'fas fa-graduation-cap', 135],
            
            // موارد إضافية مفيدة
            ['data', 'student_grades', 'درجات الطلاب', 'الوصول لدرجات الطلاب', 'fas fa-star', 140],
            ['data', 'class_schedules', 'جداول الفصول', 'الوصول لجداول الفصول الدراسية', 'fas fa-calendar', 141],
            ['action', 'grade_entry', 'إدخال الدرجات', 'إدخال وتعديل درجات الطلاب', 'fas fa-keyboard', 142],
            ['action', 'schedule_manage', 'إدارة الجداول', 'إدارة جداول الحصص والفصول', 'fas fa-table', 143]
        ];
        
        $resource_stmt = $conn->prepare("
            INSERT IGNORE INTO system_resources 
            (resource_type, resource_key, resource_name, resource_description, icon, sort_order, is_active) 
            VALUES (?, ?, ?, ?, ?, ?, 1)
        ");
        
        $added_count = 0;
        foreach ($resources as $resource) {
            $resource_stmt->bind_param("sssssi", ...$resource);
            if ($resource_stmt->execute() && $resource_stmt->affected_rows > 0) {
                $added_count++;
            }
        }
        
        // تسجيل التحديث في سجل المراجعة
        $audit_stmt = $conn->prepare("
            INSERT INTO permissions_audit_log 
            (user_id, action_type, target_type, target_id, old_value, new_value, ip_address, user_agent, created_at)
            VALUES (?, 'system_update', 'missing_resources', NULL, 'missing_resources', ?, ?, 'System Update', NOW())
        ");
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
        $new_value = "Added $added_count missing resources";
        $audit_stmt->bind_param("iss", $_SESSION['user_id'], $new_value, $ip_address);
        $audit_stmt->execute();
        
        $conn->commit();
        $success_message = "تم إضافة $added_count مورد جديد بنجاح!";
        
    } catch (Exception $e) {
        $conn->rollback();
        $error_message = "خطأ في إضافة الموارد: " . $e->getMessage();
    }
}

// التحقق من الموارد الموجودة
$existing_resources = [];
$check_resources = ['classes_view', 'subjects_view', 'exams_view', 'reports_view', 'student_reports', 'attendance_reports', 'exam_reports'];
$placeholders = str_repeat('?,', count($check_resources) - 1) . '?';
$check_stmt = $conn->prepare("SELECT resource_key FROM system_resources WHERE resource_key IN ($placeholders)");
$check_stmt->bind_param(str_repeat('s', count($check_resources)), ...$check_resources);
$check_stmt->execute();
$result = $check_stmt->get_result();
while ($row = $result->fetch_assoc()) {
    $existing_resources[] = $row['resource_key'];
}

$missing_resources = array_diff($check_resources, $existing_resources);

$page_title = 'إضافة موارد النظام المفقودة';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-plus-circle me-2"></i><?php echo $page_title; ?></h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="../settings/permissions.php">إدارة الصلاحيات</a></li>
                    <li class="breadcrumb-item active">إضافة موارد مفقودة</li>
                </ol>
            </nav>
        </div>
        <a href="../settings/permissions.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة لإدارة الصلاحيات
        </a>
    </div>

    <!-- رسائل التنبيه -->
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row justify-content-center">
        <div class="col-lg-10">
            <?php if (!empty($missing_resources)): ?>
                <!-- توجد موارد مفقودة -->
                <div class="card border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>موارد مفقودة تحتاج إضافة</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>المشكلة:</h6>
                            <p class="mb-0">
                                بعض موارد النظام المطلوبة لعرض القائمة الجانبية غير موجودة. 
                                هذا يؤدي إلى عدم ظهور بعض العناصر في لوحة التحكم حتى لو كان المستخدم لديه صلاحيات.
                            </p>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-danger">الموارد المفقودة:</h6>
                                <ul class="list-group">
                                    <?php foreach ($missing_resources as $resource): ?>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <?php echo $resource; ?>
                                            <span class="badge bg-danger">مفقود</span>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success">الموارد الموجودة:</h6>
                                <ul class="list-group">
                                    <?php foreach ($existing_resources as $resource): ?>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <?php echo $resource; ?>
                                            <span class="badge bg-success">موجود</span>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>

                        <div class="alert alert-warning mt-3">
                            <h6><i class="fas fa-lightbulb me-2"></i>ما سيتم إضافته:</h6>
                            <ul class="mb-0">
                                <li><strong>موارد الفصول:</strong> عرض، إدارة، إضافة، تعديل، حذف</li>
                                <li><strong>موارد المواد:</strong> عرض، إدارة، إضافة، تعديل، حذف</li>
                                <li><strong>موارد الامتحانات:</strong> عرض، إدارة، إضافة، تعديل، حذف، تصحيح</li>
                                <li><strong>موارد التقارير:</strong> عامة، طلاب، حضور، امتحانات، مالية، أكاديمية</li>
                                <li><strong>موارد إضافية:</strong> درجات، جداول، إدخال درجات، إدارة جداول</li>
                            </ul>
                        </div>

                        <form method="POST">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="confirmAdd" required>
                                <label class="form-check-label" for="confirmAdd">
                                    <strong>أؤكد أنني أريد إضافة جميع موارد النظام المفقودة</strong>
                                </label>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="../settings/permissions.php" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>إلغاء
                                </a>
                                <button type="submit" name="add_resources" class="btn btn-warning" id="addButton" disabled>
                                    <i class="fas fa-plus me-2"></i>إضافة الموارد المفقودة
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            <?php else: ?>
                <!-- جميع الموارد موجودة -->
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-check-circle me-2"></i>جميع الموارد موجودة</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <h6><i class="fas fa-thumbs-up me-2"></i>ممتاز!</h6>
                            <p class="mb-0">
                                جميع موارد النظام المطلوبة موجودة. يمكنك الآن منح الصلاحيات للمستخدمين.
                            </p>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-success">الموارد المتاحة:</h6>
                                <ul class="list-group">
                                    <?php foreach ($existing_resources as $resource): ?>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <?php echo $resource; ?>
                                            <span class="badge bg-success">✓</span>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-info">الخطوات التالية:</h6>
                                <div class="d-grid gap-2">
                                    <a href="../admin/debug_user_permissions.php" class="btn btn-outline-info">
                                        <i class="fas fa-bug me-2"></i>تشخيص صلاحيات <EMAIL>
                                    </a>
                                    <a href="../admin/user_permissions_detail.php?email=<EMAIL>" class="btn btn-outline-primary">
                                        <i class="fas fa-user-edit me-2"></i>تعديل صلاحيات المستخدم
                                    </a>
                                    <a href="../admin/permissions_manager.php" class="btn btn-outline-success">
                                        <i class="fas fa-users me-2"></i>إدارة جميع المستخدمين
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- معلومات إضافية -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">كيف يعمل النظام:</h6>
                            <ol class="small">
                                <li>القائمة الجانبية تتحقق من وجود الموارد</li>
                                <li>إذا كان المورد موجود، تتحقق من صلاحية المستخدم</li>
                                <li>إذا كان لديه صلاحية، يظهر العنصر</li>
                                <li>إذا لم يكن لديه صلاحية، لا يظهر العنصر</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-warning">نصائح مهمة:</h6>
                            <ul class="small">
                                <li>تأكد من وجود جميع الموارد أولاً</li>
                                <li>امنح الصلاحيات المناسبة للمستخدمين</li>
                                <li>اختبر النظام بعد التغييرات</li>
                                <li>راجع سجل التغييرات دورياً</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تفعيل زر الإضافة عند تأكيد الاختيار
document.getElementById('confirmAdd')?.addEventListener('change', function() {
    document.getElementById('addButton').disabled = !this.checked;
});
</script>

<?php include_once '../includes/footer.php'; ?>
