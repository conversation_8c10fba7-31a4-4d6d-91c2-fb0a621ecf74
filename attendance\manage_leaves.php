<?php
/**
 * إدارة الإجازات
 * Leave Management
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';
require_once '../includes/header.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('teacher')) {
    header('Location: ../dashboard/');
    exit();
}

$user_id = $_SESSION['user_id'];
$tab = $_GET['tab'] ?? 'all';

// تحديد العنوان والرابط حسب التبويب
$page_title = 'إدارة الإجازات';
$back_link = 'smart_attendance.php';

switch ($tab) {
    case 'teachers':
        $page_title = 'إدارة إجازات المعلمين';
        $back_link = 'smart_attendance.php?tab=teachers&date=' . date('Y-m-d');
        break;
    case 'admins':
        $page_title = 'إدارة إجازات الإداريين';
        $back_link = 'smart_attendance.php?tab=admins&date=' . date('Y-m-d');
        break;
    default:
        $back_link = 'smart_attendance.php?date=' . date('Y-m-d');
        break;
}

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'طلب غير صالح';
    } else {
        $action = $_POST['action'] ?? '';
        $leave_id = intval($_POST['leave_id'] ?? 0);
        
        if ($action === 'approve' || $action === 'reject') {
            $status = $action === 'approve' ? 'approved' : 'rejected';
            $stmt = $conn->prepare("UPDATE staff_leaves SET status = ?, approved_by = ?, approved_at = NOW() WHERE id = ?");
            $stmt->bind_param("sii", $status, $user_id, $leave_id);
            
            if ($stmt->execute()) {
                $success_message = $action === 'approve' ? 'تم الموافقة على الإجازة' : 'تم رفض الإجازة';
                
                // تسجيل النشاط
                log_activity($user_id, $action . '_leave', 'staff_leaves', $leave_id);
            } else {
                $error_message = 'حدث خطأ أثناء تحديث الإجازة';
            }
        }
    }
}

// جلب الإجازات
$status_filter = $_GET['status'] ?? 'all';
$where_clause = "WHERE 1=1";
$params = [];
$types = "";

// فلترة حسب التبويب
switch ($tab) {
    case 'teachers':
        $where_clause .= " AND u.role = ?";
        $params[] = 'teacher';
        $types .= "s";
        break;
    case 'admins':
        $where_clause .= " AND u.role = ?";
        $params[] = 'staff';
        $types .= "s";
        break;
}

if ($status_filter !== 'all') {
    $where_clause .= " AND sl.status = ?";
    $params[] = $status_filter;
    $types .= "s";
}

$query = "
    SELECT sl.*, u.full_name, u.role,
           approver.full_name as approved_by_name
    FROM staff_leaves sl
    JOIN users u ON sl.user_id = u.id
    LEFT JOIN users approver ON sl.approved_by = approver.id
    $where_clause
    ORDER BY sl.created_at DESC
";

$stmt = $conn->prepare($query);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$leaves_result = $stmt->get_result();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar-check me-2"></i>
                            <?php echo $page_title; ?>
                        </h5>
                        <div>
                            <a href="leave_form.php<?php echo $tab !== 'all' ? '?tab=' . $tab : ''; ?>" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus me-2"></i>طلب إجازة جديد
                            </a>
                            <button type="button" class="btn btn-secondary btn-sm" onclick="goBackToAttendance()">
                                <i class="fas fa-arrow-left me-2"></i>رجوع
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (isset($success_message)): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($error_message)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                        </div>
                    <?php endif; ?>

                    <!-- فلتر الحالة -->
                    <div class="mb-3">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>ملاحظة:</strong> جميع الإجازات يتم تفعيلها مباشرة عند التسجيل دون الحاجة لانتظار الموافقة.
                        </div>
                    </div>

                    <!-- جدول الإجازات -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الموظف</th>
                                    <th>نوع الإجازة</th>
                                    <th>من تاريخ</th>
                                    <th>إلى تاريخ</th>
                                    <th>عدد الأيام</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($leaves_result->num_rows > 0): ?>
                                    <?php while ($leave = $leaves_result->fetch_assoc()): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($leave['full_name']); ?></strong>
                                                <br><small class="text-muted"><?php echo $leave['role']; ?></small>
                                            </td>
                                            <td>
                                                <?php
                                                $leave_types = [
                                                    'sick' => 'إجازة مرضية',
                                                    'regular' => 'إجازة اعتيادية',
                                                    'emergency' => 'إجازة طارئة'
                                                ];
                                                echo $leave_types[$leave['leave_type']] ?? $leave['leave_type'];
                                                ?>
                                            </td>
                                            <td><?php echo date('Y-m-d', strtotime($leave['start_date'])); ?></td>
                                            <td><?php echo date('Y-m-d', strtotime($leave['end_date'])); ?></td>
                                            <td><?php echo $leave['total_days']; ?> يوم</td>
                                            <td><?php echo date('Y-m-d', strtotime($leave['created_at'])); ?></td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <!-- زر العرض متاح للجميع -->
                                                    <a href="view_leave.php?id=<?php echo $leave['id']; ?>" class="btn btn-info btn-sm" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>

                                                    <?php if (check_permission('admin')): ?>
                                                        <!-- زر التعديل -->
                                                        <a href="edit_leave.php?id=<?php echo $leave['id']; ?>" class="btn btn-warning btn-sm" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </a>

                                                        <!-- زر الحذف -->
                                                        <a href="delete_leave.php?id=<?php echo $leave['id']; ?>"
                                                           class="btn btn-danger btn-sm"
                                                           title="حذف"
                                                           onclick="return confirm('هل أنت متأكد من حذف هذه الإجازة؟\n\nالموظف: <?php echo htmlspecialchars($leave['full_name']); ?>\nالنوع: <?php echo $leave_types[$leave['leave_type']] ?? $leave['leave_type']; ?>\nالفترة: <?php echo date('Y-m-d', strtotime($leave['start_date'])) . ' إلى ' . date('Y-m-d', strtotime($leave['end_date'])); ?>\n\nهذا الإجراء لا يمكن التراجع عنه.')">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="8" class="text-center">لا توجد إجازات</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// دالة الرجوع للحضور الذكي
function goBackToAttendance() {
    const urlParams = new URLSearchParams(window.location.search);
    const tab = urlParams.get('tab');
    const currentDate = new Date().toISOString().split('T')[0];

    let backUrl = 'smart_attendance.php?date=' + currentDate;

    if (tab === 'teachers') {
        backUrl = 'smart_attendance.php?tab=teachers&date=' + currentDate;
    } else if (tab === 'admins') {
        backUrl = 'smart_attendance.php?tab=admins&date=' + currentDate;
    }

    window.location.href = backUrl;
}
</script>

<?php require_once '../includes/footer.php'; ?>
