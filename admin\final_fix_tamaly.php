<?php
/**
 * الإصلاح النهائي لصلاحيات <EMAIL>
 * Final <NAME_EMAIL> Permissions
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

$user_email = '<EMAIL>';
$results = [];
$success = true;

// بدء المعالجة
$results[] = "🚀 بدء الإصلاح النهائي لصلاحيات $user_email";
$results[] = "⏰ الوقت: " . date('Y-m-d H:i:s');

try {
    // 1. التحقق من وجود المستخدم
    $user_query = "SELECT * FROM users WHERE email = ?";
    $user_stmt = $conn->prepare($user_query);
    $user_stmt->bind_param("s", $user_email);
    $user_stmt->execute();
    $user = $user_stmt->get_result()->fetch_assoc();

    if (!$user) {
        throw new Exception("❌ المستخدم $user_email غير موجود!");
    }

    $results[] = "✅ المستخدم موجود: " . $user['full_name'] . " (ID: " . $user['id'] . ")";

    // 2. إنشاء جدول system_resources إذا لم يكن موجوداً
    $create_resources = "
    CREATE TABLE IF NOT EXISTS `system_resources` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `resource_type` enum('page','action','data','report') NOT NULL,
        `resource_key` varchar(100) NOT NULL,
        `resource_name` varchar(255) NOT NULL,
        `resource_description` text,
        `resource_path` varchar(255) DEFAULT NULL,
        `parent_resource` varchar(100) DEFAULT NULL,
        `icon` varchar(100) DEFAULT NULL,
        `sort_order` int(11) DEFAULT 0,
        `is_active` tinyint(1) DEFAULT 1,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `resource_key` (`resource_key`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ";

    if ($conn->query($create_resources)) {
        $results[] = "✅ جدول system_resources جاهز";
    } else {
        throw new Exception("❌ فشل في إنشاء جدول system_resources");
    }

    // 3. إنشاء جدول user_custom_permissions إذا لم يكن موجوداً
    $create_permissions = "
    CREATE TABLE IF NOT EXISTS `user_custom_permissions` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `permission_key` varchar(100) NOT NULL,
        `is_granted` tinyint(1) DEFAULT 1,
        `granted_by` int(11) DEFAULT NULL,
        `granted_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `user_permission` (`user_id`,`permission_key`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ";

    if ($conn->query($create_permissions)) {
        $results[] = "✅ جدول user_custom_permissions جاهز";
    } else {
        throw new Exception("❌ فشل في إنشاء جدول user_custom_permissions");
    }

    // 4. إضافة الموارد الأساسية
    $resources = [
        ['page', 'classes_view', 'عرض الفصول', 'fas fa-school', 100],
        ['page', 'subjects_view', 'عرض المواد', 'fas fa-book', 110],
        ['page', 'exams_view', 'عرض الامتحانات', 'fas fa-file-alt', 120],
        ['page', 'reports_view', 'عرض التقارير', 'fas fa-chart-bar', 130],
        ['report', 'student_reports', 'تقارير الطلاب', 'fas fa-user-graduate', 131],
        ['report', 'attendance_reports', 'تقارير الحضور', 'fas fa-calendar-check', 132],
        ['report', 'exam_reports', 'تقارير الامتحانات', 'fas fa-file-alt', 133]
    ];

    $resource_stmt = $conn->prepare("
        INSERT IGNORE INTO system_resources 
        (resource_type, resource_key, resource_name, icon, sort_order, is_active) 
        VALUES (?, ?, ?, ?, ?, 1)
    ");

    $added_count = 0;
    foreach ($resources as $resource) {
        $resource_stmt->bind_param("ssssi", $resource[0], $resource[1], $resource[2], $resource[3], $resource[4]);
        if ($resource_stmt->execute() && $resource_stmt->affected_rows > 0) {
            $added_count++;
        }
    }

    $results[] = "📦 تم التأكد من وجود " . count($resources) . " مورد أساسي ($added_count جديد)";

    // 5. منح الصلاحيات للمستخدم
    $permissions = ['classes_view', 'subjects_view', 'exams_view', 'reports_view', 'student_reports', 'attendance_reports', 'exam_reports'];

    // حذف الصلاحيات الموجودة
    $delete_stmt = $conn->prepare("DELETE FROM user_custom_permissions WHERE user_id = ?");
    $delete_stmt->bind_param("i", $user['id']);
    $delete_stmt->execute();

    // إضافة الصلاحيات الجديدة
    $permission_stmt = $conn->prepare("
        INSERT INTO user_custom_permissions 
        (user_id, permission_key, is_granted, granted_by, granted_at) 
        VALUES (?, ?, 1, ?, NOW())
    ");

    $granted_count = 0;
    foreach ($permissions as $permission) {
        $permission_stmt->bind_param("isi", $user['id'], $permission, $_SESSION['user_id']);
        if ($permission_stmt->execute()) {
            $granted_count++;
        }
    }

    $results[] = "🔑 تم منح $granted_count صلاحية للمستخدم";

    // 6. اختبار الصلاحيات
    $results[] = "\n🧪 اختبار الصلاحيات:";
    
    // محاكاة جلسة المستخدم
    $original_session = $_SESSION;
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['role'] = $user['role'];
    $_SESSION['email'] = $user['email'];

    foreach ($permissions as $permission) {
        $has_permission = has_permission($permission);
        $results[] = ($has_permission ? "✅" : "❌") . " $permission: " . ($has_permission ? "متاح" : "غير متاح");
    }

    // استعادة الجلسة الأصلية
    $_SESSION = $original_session;

    $results[] = "\n🎉 تم الإصلاح بنجاح!";

} catch (Exception $e) {
    $success = false;
    $results[] = "❌ خطأ: " . $e->getMessage();
}

$page_title = 'الإصلاح النهائي لصلاحيات <EMAIL>';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-magic me-2"></i><?php echo $page_title; ?></h2>
        </div>
        <a href="../settings/permissions.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة
        </a>
    </div>

    <!-- نتائج الإصلاح -->
    <div class="card">
        <div class="card-header <?php echo $success ? 'bg-success' : 'bg-danger'; ?> text-white">
            <h5>
                <i class="fas fa-<?php echo $success ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                <?php echo $success ? 'تم الإصلاح بنجاح!' : 'فشل في الإصلاح'; ?>
            </h5>
        </div>
        <div class="card-body">
            <div class="bg-dark text-light p-3 rounded" style="font-family: monospace; white-space: pre-line;">
<?php echo implode("\n", $results); ?>
            </div>
        </div>
    </div>

    <?php if ($success): ?>
        <!-- تعليمات الاختبار -->
        <div class="card mt-4">
            <div class="card-header bg-info text-white">
                <h5><i class="fas fa-clipboard-check me-2"></i>خطوات الاختبار</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <h6><i class="fas fa-thumbs-up me-2"></i>الإصلاح مكتمل!</h6>
                    <p class="mb-0">تم إصلاح جميع المشاكل. الآن اتبع هذه الخطوات للتأكد:</p>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">خطوات الاختبار:</h6>
                        <ol>
                            <li><strong>سجل خروج</strong> من حسابك الحالي</li>
                            <li><strong>سجل دخول</strong> بحساب: <code><EMAIL></code></li>
                            <li><strong>تحقق</strong> من ظهور العناصر في القائمة الجانبية</li>
                            <li><strong>اختبر</strong> الوصول للصفحات</li>
                        </ol>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-success">يجب أن تظهر:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-school me-2"></i>الفصول</span>
                                <span class="badge bg-success">✓</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-book me-2"></i>المواد</span>
                                <span class="badge bg-success">✓</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-file-alt me-2"></i>الامتحانات</span>
                                <span class="badge bg-success">✓</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-chart-bar me-2"></i>التقارير</span>
                                <span class="badge bg-success">✓</span>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <a href="../auth/logout.php" class="btn btn-warning me-2">
                        <i class="fas fa-sign-out-alt me-2"></i>تسجيل خروج للاختبار
                    </a>
                    <button class="btn btn-info" onclick="window.location.reload()">
                        <i class="fas fa-redo me-2"></i>إعادة تشغيل الإصلاح
                    </button>
                </div>
            </div>
        </div>
    <?php else: ?>
        <!-- في حالة الفشل -->
        <div class="card mt-4">
            <div class="card-header bg-warning text-dark">
                <h5><i class="fas fa-tools me-2"></i>خيارات أخرى</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <a href="create_permissions_tables.php" class="btn btn-outline-primary w-100">
                            <i class="fas fa-database me-2"></i>إنشاء الجداول يدوياً
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="debug_user_permissions.php" class="btn btn-outline-info w-100">
                            <i class="fas fa-bug me-2"></i>تشخيص مفصل
                        </a>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-outline-secondary w-100" onclick="window.location.reload()">
                            <i class="fas fa-redo me-2"></i>إعادة المحاولة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- معلومات تقنية -->
    <div class="card mt-4">
        <div class="card-header">
            <h6><i class="fas fa-info-circle me-2"></i>معلومات تقنية</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">ما تم إصلاحه:</h6>
                    <ul class="small">
                        <li>إنشاء جداول قاعدة البيانات</li>
                        <li>إضافة موارد النظام الأساسية</li>
                        <li>منح الصلاحيات للمستخدم</li>
                        <li>إصلاح تضارب الدوال</li>
                        <li>اختبار النظام</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="text-info">الملفات المحدثة:</h6>
                    <ul class="small">
                        <li><code>includes/functions.php</code> - دالة has_permission محسنة</li>
                        <li><code>includes/permissions.php</code> - إزالة التكرار</li>
                        <li><code>includes/header.php</code> - القائمة الجانبية محدثة</li>
                        <li>قاعدة البيانات - جداول وبيانات جديدة</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
