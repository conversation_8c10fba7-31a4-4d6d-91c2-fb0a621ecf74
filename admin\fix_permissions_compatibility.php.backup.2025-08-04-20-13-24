<?php
/**
 * إصلاح مشكلة توافق الصلاحيات
 * Fix Permissions Compatibility Issue
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

$results = [];
$success = true;

// معالجة الإصلاح
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fix_compatibility'])) {
    try {
        $results[] = "🚀 بدء إصلاح مشكلة توافق الصلاحيات";
        
        // 1. فحص بنية الجدول الحالية
        $describe_query = "DESCRIBE user_custom_permissions";
        $describe_result = $conn->query($describe_query);
        
        $columns = [];
        if ($describe_result) {
            while ($column = $describe_result->fetch_assoc()) {
                $columns[] = $column['Field'];
            }
        }
        
        $results[] = "📋 أعمدة الجدول الحالية: " . implode(', ', $columns);
        
        // 2. توحيد بنية الجدول
        $has_permission_type = in_array('permission_type', $columns);
        $has_permission_key = in_array('permission_key', $columns);
        $has_granted_at = in_array('granted_at', $columns);
        
        if (!$has_granted_at) {
            $alter_query = "ALTER TABLE user_custom_permissions ADD COLUMN granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP";
            if ($conn->query($alter_query)) {
                $results[] = "✅ تم إضافة عمود granted_at";
            }
        }
        
        // 3. تنظيف البيانات المكررة أو التالفة
        $cleanup_query = "
            DELETE p1 FROM user_custom_permissions p1
            INNER JOIN user_custom_permissions p2 
            WHERE p1.id > p2.id 
            AND p1.user_id = p2.user_id 
            AND p1.permission_key = p2.permission_key
        ";
        $conn->query($cleanup_query);
        $results[] = "🧹 تم تنظيف البيانات المكررة";
        
        // 4. إصلاح دالة has_permission في functions.php
        $functions_file = '../includes/functions.php';
        $functions_content = file_get_contents($functions_file);
        
        // البحث عن دالة has_permission الحالية
        $old_function_pattern = '/function has_permission\([^}]+\}/s';
        
        $new_function = "function has_permission(\$permission_key) {
    global \$conn;
    
    if (!isset(\$_SESSION['user_id'])) {
        return false;
    }
    
    \$user_id = \$_SESSION['user_id'];
    
    try {
        // البحث في جدول الصلاحيات المخصصة
        \$stmt = \$conn->prepare(\"
            SELECT is_granted 
            FROM user_custom_permissions 
            WHERE user_id = ? AND permission_key = ? AND is_granted = 1
            LIMIT 1
        \");
        \$stmt->bind_param(\"is\", \$user_id, \$permission_key);
        \$stmt->execute();
        \$result = \$stmt->get_result();
        
        if (\$result->num_rows > 0) {
            return true;
        }
        
        // البحث في جدول system_resources إذا كان موجوداً
        \$system_stmt = \$conn->prepare(\"
            SELECT ucp.is_granted 
            FROM user_custom_permissions ucp
            JOIN system_resources sr ON ucp.permission_key = sr.resource_key
            WHERE ucp.user_id = ? AND sr.resource_key = ? AND ucp.is_granted = 1
            LIMIT 1
        \");
        \$system_stmt->bind_param(\"is\", \$user_id, \$permission_key);
        \$system_stmt->execute();
        \$system_result = \$system_stmt->get_result();
        
        return \$system_result->num_rows > 0;
        
    } catch (Exception \$e) {
        error_log(\"Error in has_permission: \" . \$e->getMessage());
        return false;
    }
}";

        // استبدال الدالة القديمة
        if (preg_match($old_function_pattern, $functions_content)) {
            $new_content = preg_replace($old_function_pattern, $new_function, $functions_content);
        } else {
            // إضافة الدالة في نهاية الملف
            $new_content = $functions_content . "\n\n" . $new_function;
        }
        
        // إنشاء نسخة احتياطية
        $backup_file = $functions_file . '.backup.' . date('Y-m-d-H-i-s');
        copy($functions_file, $backup_file);
        
        // كتابة الدالة الجديدة
        if (file_put_contents($functions_file, $new_content)) {
            $results[] = "✅ تم تحديث دالة has_permission في functions.php";
        } else {
            throw new Exception("فشل في كتابة ملف functions.php");
        }
        
        // 5. إصلاح صفحة permissions_manager.php
        $manager_file = 'permissions_manager.php';
        $manager_content = file_get_contents($manager_file);
        
        // البحث عن الكود القديم لحفظ الصلاحيات
        $old_insert_pattern = '/INSERT INTO user_custom_permissions[^;]+permission_type[^;]+/';
        $new_insert = "INSERT INTO user_custom_permissions
                (user_id, permission_key, is_granted, granted_by, granted_at)
                VALUES (?, ?, 1, ?, NOW())";
        
        // استبدال الكود القديم
        $old_loop_pattern = '/foreach \(\$permissions as \$permission\) \{[^}]+\}/s';
        $new_loop = "foreach (\$permissions as \$permission) {
                // إزالة البادئة إذا كانت موجودة (type:key -> key)
                \$permission_key = \$permission;
                if (strpos(\$permission, ':') !== false) {
                    \$parts = explode(':', \$permission);
                    \$permission_key = end(\$parts);
                }
                
                \$insert_stmt->bind_param(\"isi\", \$user_id, \$permission_key, \$_SESSION['user_id']);
                \$insert_stmt->execute();
            }";
        
        // تطبيق التغييرات
        $manager_content = preg_replace($old_insert_pattern, $new_insert, $manager_content);
        $manager_content = preg_replace($old_loop_pattern, $new_loop, $manager_content);
        
        // إنشاء نسخة احتياطية وحفظ
        $manager_backup = $manager_file . '.backup.' . date('Y-m-d-H-i-s');
        copy($manager_file, $manager_backup);
        
        if (file_put_contents($manager_file, $manager_content)) {
            $results[] = "✅ تم تحديث صفحة permissions_manager.php";
        }
        
        // 6. تحويل البيانات الموجودة إلى التنسيق الجديد
        $convert_query = "
            UPDATE user_custom_permissions 
            SET permission_key = SUBSTRING_INDEX(permission_key, ':', -1)
            WHERE permission_key LIKE '%:%'
        ";
        $converted_rows = $conn->query($convert_query);
        if ($converted_rows) {
            $results[] = "✅ تم تحويل " . $conn->affected_rows . " صلاحية إلى التنسيق الجديد";
        }
        
        // 7. اختبار النظام
        $user_email = '<EMAIL>';
        $user_query = "SELECT * FROM users WHERE email = ?";
        $user_stmt = $conn->prepare($user_query);
        $user_stmt->bind_param("s", $user_email);
        $user_stmt->execute();
        $user = $user_stmt->get_result()->fetch_assoc();
        
        if ($user) {
            // محاكاة جلسة المستخدم
            $original_session = $_SESSION;
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['role'] = $user['role'];
            
            $test_permissions = ['students_view', 'teachers_view', 'student_affairs'];
            $working_permissions = 0;
            
            foreach ($test_permissions as $permission) {
                if (has_permission($permission)) {
                    $working_permissions++;
                }
            }
            
            $_SESSION = $original_session;
            
            $results[] = "🧪 اختبار النظام: $working_permissions من " . count($test_permissions) . " صلاحيات تعمل";
        }
        
        $results[] = "\n🎉 تم إصلاح مشكلة توافق الصلاحيات بنجاح!";
        $results[] = "📋 الآن يجب أن تعمل الصلاحيات المخصصة بشكل صحيح";
        
    } catch (Exception $e) {
        $success = false;
        $results[] = "❌ خطأ: " . $e->getMessage();
    }
}

$page_title = 'إصلاح مشكلة توافق الصلاحيات';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-tools me-2"></i><?php echo $page_title; ?></h2>
            <p class="text-muted">إصلاح مشكلة عدم ظهور الصلاحيات المخصصة للمستخدم الإداري</p>
        </div>
        <div>
            <a href="debug_permissions_table.php" class="btn btn-outline-info me-2">
                <i class="fas fa-bug me-2"></i>تشخيص المشكلة
            </a>
            <a href="../settings/permissions.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة
            </a>
        </div>
    </div>

    <!-- شرح المشكلة -->
    <div class="alert alert-warning">
        <h6><i class="fas fa-exclamation-triangle me-2"></i>المشكلة المكتشفة:</h6>
        <ul class="mb-0">
            <li>صفحة permissions_manager.php تحفظ الصلاحيات بتنسيق "type:key"</li>
            <li>دالة has_permission() تبحث عن "key" فقط</li>
            <li>هذا يسبب عدم تطابق وعدم ظهور الصلاحيات للمستخدم</li>
            <li>النتيجة: الإداري لا يرى الصلاحيات التي تم منحها له</li>
        </ul>
    </div>

    <!-- نتائج الإصلاح -->
    <?php if (!empty($results)): ?>
        <div class="card mb-4">
            <div class="card-header <?php echo $success ? 'bg-success' : 'bg-danger'; ?> text-white">
                <h5>
                    <i class="fas fa-<?php echo $success ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                    <?php echo $success ? 'تم الإصلاح بنجاح!' : 'فشل في الإصلاح'; ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="bg-dark text-light p-3 rounded" style="font-family: monospace; white-space: pre-line;">
<?php echo implode("\n", $results); ?>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- نموذج الإصلاح -->
    <?php if (empty($results) || !$success): ?>
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5><i class="fas fa-cogs me-2"></i>إصلاح مشكلة توافق الصلاحيات</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>ما سيتم عمله:</h6>
                    <ol>
                        <li><strong>توحيد بنية الجدول:</strong> التأكد من وجود جميع الأعمدة المطلوبة</li>
                        <li><strong>تنظيف البيانات:</strong> إزالة الصلاحيات المكررة أو التالفة</li>
                        <li><strong>تحديث دالة has_permission:</strong> لتدعم التنسيق الجديد</li>
                        <li><strong>إصلاح permissions_manager:</strong> لحفظ الصلاحيات بالتنسيق الصحيح</li>
                        <li><strong>تحويل البيانات الموجودة:</strong> إلى التنسيق الجديد</li>
                        <li><strong>اختبار النظام:</strong> للتأكد من عمل الصلاحيات</li>
                    </ol>
                </div>

                <div class="alert alert-warning">
                    <h6><i class="fas fa-shield-alt me-2"></i>الأمان:</h6>
                    <ul class="mb-0">
                        <li>سيتم إنشاء نسخة احتياطية من جميع الملفات المعدلة</li>
                        <li>يمكن التراجع عن التغييرات باستخدام النسخ الاحتياطية</li>
                        <li>العملية آمنة ولن تؤثر على البيانات الموجودة</li>
                    </ul>
                </div>

                <form method="POST">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="confirmFix" required>
                        <label class="form-check-label" for="confirmFix">
                            <strong>أؤكد أنني أريد إصلاح مشكلة توافق الصلاحيات</strong>
                        </label>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="../settings/permissions.php" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" name="fix_compatibility" class="btn btn-primary" id="fixButton" disabled>
                            <i class="fas fa-tools me-2"></i>إصلاح المشكلة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    <?php endif; ?>

    <!-- تعليمات الاختبار -->
    <?php if ($success && !empty($results)): ?>
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5><i class="fas fa-clipboard-check me-2"></i>اختبار النتيجة</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <h6><i class="fas fa-thumbs-up me-2"></i>تم الإصلاح!</h6>
                    <p class="mb-0">الآن يجب أن تعمل الصلاحيات المخصصة بشكل صحيح.</p>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">خطوات الاختبار:</h6>
                        <ol>
                            <li><strong>اذهب لصفحة إدارة الصلاحيات</strong></li>
                            <li><strong>امنح صلاحيات للمستخدم الإداري</strong></li>
                            <li><strong>سجل خروج وإعادة دخول</strong> بحساب الإداري</li>
                            <li><strong>تحقق من ظهور الصلاحيات</strong> في النظام</li>
                        </ol>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-success">يجب أن تعمل الآن:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">✅ حفظ الصلاحيات بالتنسيق الصحيح</li>
                            <li class="list-group-item">✅ قراءة الصلاحيات من قاعدة البيانات</li>
                            <li class="list-group-item">✅ ظهور الصلاحيات في النظام</li>
                            <li class="list-group-item">✅ عمل دالة has_permission بشكل صحيح</li>
                        </ul>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <a href="permissions_manager.php" class="btn btn-primary me-2">
                        <i class="fas fa-key me-2"></i>إدارة الصلاحيات
                    </a>
                    <a href="test_staff_access.php" class="btn btn-info me-2">
                        <i class="fas fa-vial me-2"></i>اختبار الوصول
                    </a>
                    <button class="btn btn-secondary" onclick="window.location.reload()">
                        <i class="fas fa-redo me-2"></i>إعادة الإصلاح
                    </button>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
// تفعيل زر الإصلاح عند تأكيد الاختيار
document.getElementById('confirmFix')?.addEventListener('change', function() {
    document.getElementById('fixButton').disabled = !this.checked;
});
</script>

<?php include_once '../includes/footer.php'; ?>
