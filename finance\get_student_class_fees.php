<?php
/**
 * جلب الرسوم المرتبطة بصف الطالب
 * Get Student Class Related Fees
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit();
}

// التحقق من وجود معرف الطالب
if (!isset($_GET['student_id']) || !is_numeric($_GET['student_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'معرف الطالب مطلوب']);
    exit();
}

$student_id = intval($_GET['student_id']);

try {
    // جلب معلومات الطالب وصفه
    $student_stmt = $conn->prepare("
        SELECT s.id, s.class_id, u.full_name, c.class_name, c.grade_level
        FROM students s
        JOIN users u ON s.user_id = u.id
        LEFT JOIN classes c ON s.class_id = c.id
        WHERE s.id = ? AND s.status = 'active'
    ");
    
    $student_stmt->bind_param("i", $student_id);
    $student_stmt->execute();
    $student_result = $student_stmt->get_result();
    
    if ($student_result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'الطالب غير موجود']);
        exit();
    }
    
    $student = $student_result->fetch_assoc();
    
    if (!$student['class_id']) {
        echo json_encode(['success' => false, 'message' => 'الطالب غير مسجل في صف']);
        exit();
    }
    
    // جلب الرسوم المرتبطة بالصف الدراسي للطالب
    // أولاً نحصل على grade_id من class_id
    $grade_stmt = $conn->prepare("SELECT grade_id FROM classes WHERE id = ?");
    $grade_stmt->bind_param("i", $student['class_id']);
    $grade_stmt->execute();
    $grade_result = $grade_stmt->get_result();
    $grade_data = $grade_result->fetch_assoc();

    if (!$grade_data || !$grade_data['grade_id']) {
        echo json_encode(['success' => false, 'message' => 'الفصل غير مرتبط بصف دراسي محدد']);
        exit();
    }

    $fees_stmt = $conn->prepare("
        SELECT
            ftg.fee_type_id,
            ftg.amount,
            ft.type_name,
            ft.description,
            ft.default_amount,
            ftg.is_active
        FROM fee_type_grades ftg
        JOIN fee_types ft ON ftg.fee_type_id = ft.id
        WHERE ftg.grade_id = ?
        AND ftg.is_active = 1
        AND ft.status = 'active'
        ORDER BY ft.type_name
    ");

    $fees_stmt->bind_param("i", $grade_data['grade_id']);
    $fees_stmt->execute();
    $fees_result = $fees_stmt->get_result();
    
    $fees = [];
    while ($fee = $fees_result->fetch_assoc()) {
        $fees[] = [
            'fee_type_id' => $fee['fee_type_id'],
            'type_name' => $fee['type_name'],
            'description' => $fee['description'],
            'amount' => $fee['amount'],
            'default_amount' => $fee['default_amount']
        ];
    }
    
    // إرجاع النتيجة
    echo json_encode([
        'success' => true,
        'student' => [
            'id' => $student['id'],
            'name' => $student['full_name'],
            'class_name' => $student['class_name'],
            'grade_level' => $student['grade_level']
        ],
        'fees' => $fees,
        'fees_count' => count($fees)
    ]);
    
} catch (Exception $e) {
    error_log("Error in get_student_class_fees.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'خطأ في الخادم']);
}
?>
