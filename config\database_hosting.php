<?php
/**
 * Database Configuration for Hosting
 * إعدادات قاعدة البيانات للاستضافة
 * 
 * يجب تحديث هذه الإعدادات حسب بيانات الاستضافة الخاصة بك
 */

if (!defined('SYSTEM_INIT')) {
    die('Direct access not allowed');
}

// إعدادات قاعدة البيانات للاستضافة
// يجب تحديث هذه القيم حسب بيانات الاستضافة الخاصة بك

// مثال لإعدادات هوستنجر
define('DB_HOST', 'localhost');                    // عادة localhost
define('DB_NAME', 'u640615703_school_managem');    // اسم قاعدة البيانات من لوحة التحكم
define('DB_USER', 'u640615703_school_admin');      // اسم المستخدم من لوحة التحكم
define('DB_PASSWORD', 'YourDatabasePassword123');   // كلمة المرور من لوحة التحكم
define('DB_PASS', DB_PASSWORD);                     // للتوافق مع الكود القديم
define('DB_CHARSET', 'utf8mb4');

// إعدادات إضافية للاستضافة
define('DB_PORT', 3306);
define('DB_SOCKET', '');

// إعدادات الاتصال المتقدمة
$db_options = [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES => false,
    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET,
    PDO::ATTR_TIMEOUT => 30,
    PDO::ATTR_PERSISTENT => false
];

// إنشاء الاتصال
try {
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
    if (DB_PORT != 3306) {
        $dsn .= ";port=" . DB_PORT;
    }
    
    $pdo_conn = new PDO($dsn, DB_USER, DB_PASSWORD, $db_options);
    
    // إنشاء اتصال MySQLi للتوافق مع الكود القديم
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME, DB_PORT);
    
    if ($conn->connect_error) {
        throw new Exception('MySQLi connection failed: ' . $conn->connect_error);
    }
    
    $conn->set_charset(DB_CHARSET);
    
    // تسجيل نجاح الاتصال
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        error_log("Database connection established successfully");
    }
    
} catch (PDOException $e) {
    error_log("PDO Database connection failed: " . $e->getMessage());
    
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        die("Database connection failed: " . $e->getMessage());
    } else {
        die("عذراً، حدث خطأ في الاتصال بقاعدة البيانات. يرجى المحاولة لاحقاً.");
    }
} catch (Exception $e) {
    error_log("Database connection failed: " . $e->getMessage());
    
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        die("Database connection failed: " . $e->getMessage());
    } else {
        die("عذراً، حدث خطأ في الاتصال بقاعدة البيانات. يرجى المحاولة لاحقاً.");
    }
}

/**
 * دالة للتحقق من حالة الاتصال بقاعدة البيانات
 */
function check_database_connection() {
    global $conn;
    
    if (!$conn) {
        return false;
    }
    
    try {
        $result = $conn->query("SELECT 1");
        return $result !== false;
    } catch (Exception $e) {
        error_log("Database connection check failed: " . $e->getMessage());
        return false;
    }
}

/**
 * دالة لإعادة الاتصال بقاعدة البيانات في حالة انقطاع الاتصال
 */
function reconnect_database() {
    global $conn;
    
    try {
        if ($conn) {
            $conn->close();
        }
        
        $conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME, DB_PORT);
        
        if ($conn->connect_error) {
            throw new Exception('MySQLi reconnection failed: ' . $conn->connect_error);
        }
        
        $conn->set_charset(DB_CHARSET);
        
        return true;
    } catch (Exception $e) {
        error_log("Database reconnection failed: " . $e->getMessage());
        return false;
    }
}

/**
 * دالة للحصول على معلومات قاعدة البيانات
 */
function get_database_info() {
    global $conn;
    
    if (!$conn) {
        return null;
    }
    
    try {
        $info = [];
        
        // معلومات الخادم
        $info['server_version'] = $conn->server_info;
        $info['client_version'] = $conn->client_info;
        $info['host_info'] = $conn->host_info;
        $info['protocol_version'] = $conn->protocol_version;
        
        // معلومات قاعدة البيانات
        $result = $conn->query("SELECT DATABASE() as db_name");
        if ($result) {
            $row = $result->fetch_assoc();
            $info['database_name'] = $row['db_name'];
        }
        
        // معلومات الترميز
        $result = $conn->query("SELECT @@character_set_database as charset, @@collation_database as collation");
        if ($result) {
            $row = $result->fetch_assoc();
            $info['charset'] = $row['charset'];
            $info['collation'] = $row['collation'];
        }
        
        return $info;
    } catch (Exception $e) {
        error_log("Failed to get database info: " . $e->getMessage());
        return null;
    }
}

/**
 * دالة لتنفيذ استعلام آمن
 */
function safe_query($query, $params = []) {
    global $conn;
    
    if (!check_database_connection()) {
        if (!reconnect_database()) {
            return false;
        }
    }
    
    try {
        if (empty($params)) {
            return $conn->query($query);
        } else {
            $stmt = $conn->prepare($query);
            if (!$stmt) {
                throw new Exception("Prepare failed: " . $conn->error);
            }
            
            if (!empty($params)) {
                $types = '';
                $values = [];
                
                foreach ($params as $param) {
                    if (is_int($param)) {
                        $types .= 'i';
                    } elseif (is_float($param)) {
                        $types .= 'd';
                    } else {
                        $types .= 's';
                    }
                    $values[] = $param;
                }
                
                $stmt->bind_param($types, ...$values);
            }
            
            $stmt->execute();
            return $stmt->get_result();
        }
    } catch (Exception $e) {
        error_log("Query execution failed: " . $e->getMessage() . " | Query: " . $query);
        return false;
    }
}

// تسجيل معلومات الاتصال في حالة التطوير
if (defined('DEBUG_MODE') && DEBUG_MODE) {
    $db_info = get_database_info();
    if ($db_info) {
        error_log("Database Info: " . json_encode($db_info));
    }
}
?>
