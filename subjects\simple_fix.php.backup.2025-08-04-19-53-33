<?php
/**
 * إصلاح بسيط لعرض الفصول المرتبطة بالمواد
 * Simple Fix for Displaying Subject-Related Classes
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

check_session();
if (!check_permission('admin')) {
    die('ليس لديك صلاحية للوصول');
}

echo "<h2>إصلاح بسيط لعرض الفصول</h2>";

// 1. فحص البيانات الموجودة
echo "<h3>1. فحص البيانات الموجودة:</h3>";

$subjects_count = $conn->query("SELECT COUNT(*) as count FROM subjects")->fetch_assoc()['count'];
$classes_count = $conn->query("SELECT COUNT(*) as count FROM classes")->fetch_assoc()['count'];
$assignments_count = $conn->query("SELECT COUNT(*) as count FROM teacher_assignments WHERE status = 'active'")->fetch_assoc()['count'];

echo "<div style='background: #e8f5e8; padding: 10px; border-radius: 5px;'>";
echo "<p>✅ عدد المواد: <strong>$subjects_count</strong></p>";
echo "<p>✅ عدد الفصول: <strong>$classes_count</strong></p>";
echo "<p>✅ عدد التكليفات النشطة: <strong>$assignments_count</strong></p>";
echo "</div>";

// 2. فحص التكليفات مع الفصول الموجودة
echo "<h3>2. فحص التكليفات مع الفصول الموجودة:</h3>";

$valid_assignments_query = "
    SELECT 
        ta.id,
        ta.subject_id,
        ta.class_id,
        ta.teacher_id,
        s.subject_name,
        c.class_name,
        u.full_name as teacher_name
    FROM teacher_assignments ta
    JOIN subjects s ON ta.subject_id = s.id
    JOIN classes c ON ta.class_id = c.id
    JOIN teachers t ON ta.teacher_id = t.id
    JOIN users u ON t.user_id = u.id
    WHERE ta.status = 'active'
    ORDER BY ta.subject_id, ta.class_id
";

$result = $conn->query($valid_assignments_query);

if ($result && $result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #d4edda;'><th>ID</th><th>المادة</th><th>الفصل</th><th>المعلم</th></tr>";
    
    $subjects_with_classes = [];
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['id']}</td>";
        echo "<td>{$row['subject_name']} (ID: {$row['subject_id']})</td>";
        echo "<td>{$row['class_name']} (ID: {$row['class_id']})</td>";
        echo "<td>{$row['teacher_name']} (ID: {$row['teacher_id']})</td>";
        echo "</tr>";
        
        // تجميع المواد التي لها فصول
        if (!isset($subjects_with_classes[$row['subject_id']])) {
            $subjects_with_classes[$row['subject_id']] = [
                'subject_name' => $row['subject_name'],
                'classes' => []
            ];
        }
        $subjects_with_classes[$row['subject_id']]['classes'][] = [
            'class_id' => $row['class_id'],
            'class_name' => $row['class_name']
        ];
    }
    echo "</table>";
    
    echo "<p style='color: green;'>✅ تم العثور على <strong>" . $result->num_rows . "</strong> تكليف صالح</p>";
    
} else {
    echo "<p style='color: red;'>❌ لا توجد تكليفات صالحة</p>";
    
    // إضافة بيانات تجريبية
    echo "<h4>إضافة بيانات تجريبية:</h4>";
    
    // التحقق من وجود المعلمين والفصول والمواد
    $teacher_exists = $conn->query("SELECT id FROM teachers LIMIT 1")->fetch_assoc();
    $class_exists = $conn->query("SELECT id FROM classes LIMIT 1")->fetch_assoc();
    $subject_exists = $conn->query("SELECT id FROM subjects LIMIT 1")->fetch_assoc();
    
    if ($teacher_exists && $class_exists && $subject_exists) {
        $insert_assignment = "INSERT INTO teacher_assignments (teacher_id, class_id, subject_id, status, weekly_hours, academic_year_id, semester, assigned_at, created_at, updated_at) VALUES (?, ?, ?, 'active', 4, 1, 'first', NOW(), NOW(), NOW())";
        $stmt = $conn->prepare($insert_assignment);
        $stmt->bind_param('iii', $teacher_exists['id'], $class_exists['id'], $subject_exists['id']);
        
        if ($stmt->execute()) {
            echo "<p style='color: green;'>✅ تم إضافة تكليف تجريبي</p>";
        } else {
            echo "<p style='color: red;'>❌ خطأ في إضافة التكليف: " . $stmt->error . "</p>";
        }
        $stmt->close();
    } else {
        echo "<p style='color: red;'>❌ بيانات أساسية مفقودة (معلمين، فصول، أو مواد)</p>";
    }
}

// 3. اختبار الاستعلام المحسن
echo "<h3>3. اختبار الاستعلام المحسن:</h3>";

if (isset($subjects_with_classes)) {
    foreach ($subjects_with_classes as $subject_id => $subject_data) {
        echo "<h4>المادة: {$subject_data['subject_name']} (ID: $subject_id)</h4>";
        
        // الاستعلام المحسن
        $classes_query = "
            SELECT DISTINCT
                c.id,
                c.class_name,
                COALESCE(c.section, 'أ') as section,
                COALESCE(g.grade_name, 'غير محدد') as grade_name,
                COALESCE(es.stage_name, 'غير محدد') as stage_name,
                ta.teacher_id,
                COALESCE(ta.weekly_hours, 0) as weekly_hours,
                u.full_name as teacher_name
            FROM teacher_assignments ta
            INNER JOIN classes c ON ta.class_id = c.id
            LEFT JOIN grades g ON c.grade_id = g.id
            LEFT JOIN educational_stages es ON c.stage_id = es.id
            LEFT JOIN teachers t ON ta.teacher_id = t.id
            LEFT JOIN users u ON t.user_id = u.id
            WHERE ta.subject_id = ? 
            AND ta.status = 'active'
            ORDER BY c.id
        ";
        
        $stmt = $conn->prepare($classes_query);
        $stmt->bind_param('i', $subject_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #d1ecf1;'><th>Class ID</th><th>اسم الفصل</th><th>الشعبة</th><th>الصف</th><th>المرحلة</th><th>المعلم</th><th>ساعات</th></tr>";
            while ($class = $result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>{$class['id']}</td>";
                echo "<td>{$class['class_name']}</td>";
                echo "<td>{$class['section']}</td>";
                echo "<td>{$class['grade_name']}</td>";
                echo "<td>{$class['stage_name']}</td>";
                echo "<td>{$class['teacher_name']}</td>";
                echo "<td>{$class['weekly_hours']}</td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "<p style='color: green;'>✅ تم العثور على <strong>" . $result->num_rows . "</strong> فصل للمادة</p>";
        } else {
            echo "<p style='color: red;'>❌ لا توجد فصول للمادة $subject_id</p>";
        }
        $stmt->close();
    }
}

// 4. روابط الاختبار
echo "<h3>4. روابط الاختبار:</h3>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px;'>";

if (isset($subjects_with_classes)) {
    foreach ($subjects_with_classes as $subject_id => $subject_data) {
        echo "<p>📋 <a href='view.php?id=$subject_id' target='_blank'>عرض {$subject_data['subject_name']} (ID: $subject_id)</a></p>";
    }
}

echo "<p>🔍 <a href='debug_classes.php' target='_blank'>ملف التشخيص الشامل</a></p>";
echo "<p>📋 <a href='index.php' target='_blank'>قائمة المواد الرئيسية</a></p>";
echo "</div>";

// 5. تعليمات الاستخدام
echo "<h3>5. تعليمات الاستخدام:</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;'>";
echo "<ol>";
echo "<li><strong>إذا ظهرت تكليفات صالحة أعلاه:</strong> اختبر روابط عرض المواد</li>";
echo "<li><strong>إذا لم تظهر فصول في صفحة العرض:</strong> تحقق من error.log</li>";
echo "<li><strong>إذا كانت البيانات مفقودة:</strong> تحقق من استيراد قاعدة البيانات</li>";
echo "<li><strong>للتشخيص المتقدم:</strong> استخدم ملف debug_classes.php</li>";
echo "</ol>";
echo "</div>";

// 6. معلومات إضافية
echo "<h3>6. معلومات إضافية:</h3>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
echo "<p><strong>الاستعلام المستخدم في view.php:</strong></p>";
echo "<code style='background: #f8f9fa; padding: 10px; display: block; border-radius: 3px;'>";
echo "SELECT DISTINCT c.id, c.class_name, c.section, g.grade_name, es.stage_name<br>";
echo "FROM teacher_assignments ta<br>";
echo "INNER JOIN classes c ON ta.class_id = c.id<br>";
echo "WHERE ta.subject_id = ? AND ta.status = 'active'";
echo "</code>";
echo "</div>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: right; }
th { background-color: #f0f0f0; }
h3 { color: #333; border-bottom: 2px solid #ddd; padding-bottom: 5px; margin-top: 30px; }
h4 { color: #666; margin-top: 20px; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
code { font-family: monospace; }
</style>
