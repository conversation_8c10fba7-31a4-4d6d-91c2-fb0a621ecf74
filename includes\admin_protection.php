<?php
/**
 * حماية إضافية لصلاحيات المدير
 * Additional Admin Protection
 * 
 * يضمن أن المدير يحتفظ بجميع صلاحياته في كافة النظام
 */

// منع الوصول المباشر
if (!defined('SYSTEM_INIT') && !defined('FUNCTIONS_LOADED')) {
    die('Direct access not allowed');
}

/**
 * التحقق من كون المستخدم مدير مع حماية إضافية
 * 
 * @param int|null $user_id معرف المستخدم
 * @return bool
 */
function is_protected_admin($user_id = null) {
    global $conn;
    
    if ($user_id === null) {
        $user_id = $_SESSION['user_id'] ?? 0;
    }
    
    // التحقق من الجلسة
    if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin') {
        return true;
    }
    
    // التحقق من قاعدة البيانات
    try {
        $stmt = $conn->prepare("SELECT role FROM users WHERE id = ? AND role = 'admin' AND status = 'active' LIMIT 1");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        return $stmt->get_result()->num_rows > 0;
    } catch (Exception $e) {
        error_log("Error in is_protected_admin: " . $e->getMessage());
        return false;
    }
}

/**
 * فرض صلاحيات المدير الكاملة
 * 
 * @param string $page_name اسم الصفحة
 * @param string $action نوع العملية
 * @return bool
 */
function enforce_admin_privileges($page_name = '', $action = '') {
    if (is_protected_admin()) {
        // تسجيل وصول المدير
        log_admin_access($page_name, $action);
        return true;
    }
    return false;
}

/**
 * تسجيل وصول المدير
 * 
 * @param string $page_name اسم الصفحة
 * @param string $action نوع العملية
 */
function log_admin_access($page_name = '', $action = '') {
    global $conn;
    
    try {
        $user_id = $_SESSION['user_id'] ?? 0;
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $details = "وصول المدير - الصفحة: $page_name - العملية: $action";
        
        // التحقق من وجود جدول السجل
        $table_check = $conn->query("SHOW TABLES LIKE 'permissions_log'");
        if ($table_check->num_rows > 0) {
            $stmt = $conn->prepare("
                INSERT INTO permissions_log 
                (user_id, action, page_name, details, ip_address) 
                VALUES (?, 'admin_access', ?, ?, ?)
            ");
            $stmt->bind_param("isss", $user_id, $page_name, $details, $ip_address);
            $stmt->execute();
        }
        
    } catch (Exception $e) {
        error_log("Error in log_admin_access: " . $e->getMessage());
    }
}

/**
 * التحقق من صلاحيات المدير مع الحماية
 * 
 * @param string $page_name اسم الصفحة
 * @param string $action نوع العملية
 * @return bool
 */
function check_admin_permission($page_name, $action = 'view') {
    // المدير له جميع الصلاحيات دائماً
    if (is_protected_admin()) {
        log_admin_access($page_name, $action);
        return true;
    }
    
    // إذا لم يكن مدير، استخدم النظام العادي
    if (function_exists('check_page_permission')) {
        return check_page_permission($page_name, $action);
    }
    
    return false;
}

/**
 * فرض صلاحيات المدير مع إعادة التوجيه
 * 
 * @param string $page_name اسم الصفحة
 * @param string $action نوع العملية
 * @param string $redirect_url رابط إعادة التوجيه
 */
function require_admin_permission($page_name, $action = 'view', $redirect_url = '../dashboard/') {
    if (is_protected_admin()) {
        log_admin_access($page_name, $action);
        return; // المدير مسموح له
    }
    
    // إذا لم يكن مدير، استخدم النظام العادي
    if (function_exists('require_page_permission')) {
        require_page_permission($page_name, $action, $redirect_url);
    } else {
        // إعادة توجيه افتراضية
        header('Location: ' . $redirect_url . '?error=access_denied');
        exit();
    }
}

/**
 * الحصول على إحصائيات وصول المدير
 * 
 * @param int $days عدد الأيام
 * @return array
 */
function get_admin_access_stats($days = 7) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            SELECT 
                DATE(created_at) as access_date,
                COUNT(*) as access_count,
                COUNT(DISTINCT page_name) as unique_pages
            FROM permissions_log 
            WHERE action = 'admin_access' 
            AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            GROUP BY DATE(created_at)
            ORDER BY access_date DESC
        ");
        $stmt->bind_param("i", $days);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $stats = [];
        while ($row = $result->fetch_assoc()) {
            $stats[] = $row;
        }
        
        return $stats;
        
    } catch (Exception $e) {
        error_log("Error in get_admin_access_stats: " . $e->getMessage());
        return [];
    }
}

/**
 * التحقق من سلامة صلاحيات المدير
 * 
 * @return array
 */
function verify_admin_integrity() {
    global $conn;
    
    $checks = [];
    
    try {
        // فحص 1: التحقق من وجود حساب المدير
        $admin_count = $conn->query("SELECT COUNT(*) as count FROM users WHERE role = 'admin' AND status = 'active'")->fetch_assoc()['count'];
        $checks['admin_accounts'] = [
            'status' => $admin_count > 0,
            'count' => $admin_count,
            'message' => $admin_count > 0 ? "يوجد $admin_count حساب مدير نشط" : 'لا يوجد حساب مدير نشط'
        ];
        
        // فحص 2: التحقق من عدم وجود قيود على المدير
        $admin_restrictions = $conn->query("
            SELECT COUNT(*) as count 
            FROM user_page_permissions upp
            JOIN users u ON upp.user_id = u.id 
            WHERE u.role = 'admin'
        ")->fetch_assoc()['count'];
        
        $checks['admin_restrictions'] = [
            'status' => $admin_restrictions == 0,
            'count' => $admin_restrictions,
            'message' => $admin_restrictions == 0 ? 'لا توجد قيود على المدير' : "يوجد $admin_restrictions قيد على المدير (يجب إزالتها)"
        ];
        
        // فحص 3: التحقق من الجلسة الحالية
        $current_user_admin = isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
        $checks['current_session'] = [
            'status' => $current_user_admin,
            'message' => $current_user_admin ? 'المستخدم الحالي مدير' : 'المستخدم الحالي ليس مدير'
        ];
        
    } catch (Exception $e) {
        $checks['error'] = [
            'status' => false,
            'message' => 'خطأ في فحص سلامة المدير: ' . $e->getMessage()
        ];
    }
    
    return $checks;
}

/**
 * إصلاح مشاكل صلاحيات المدير
 * 
 * @return array
 */
function fix_admin_issues() {
    global $conn;
    
    $fixes = [];
    
    try {
        // إصلاح 1: إزالة أي قيود على المدير
        $stmt = $conn->prepare("
            DELETE upp FROM user_page_permissions upp
            JOIN users u ON upp.user_id = u.id 
            WHERE u.role = 'admin'
        ");
        $stmt->execute();
        $removed_restrictions = $stmt->affected_rows;
        
        if ($removed_restrictions > 0) {
            $fixes[] = "تم إزالة $removed_restrictions قيد من حسابات المدير";
        }
        
        // إصلاح 2: التأكد من تفعيل حسابات المدير
        $stmt = $conn->prepare("UPDATE users SET status = 'active' WHERE role = 'admin'");
        $stmt->execute();
        $activated_admins = $stmt->affected_rows;
        
        if ($activated_admins > 0) {
            $fixes[] = "تم تفعيل $activated_admins حساب مدير";
        }
        
        // تسجيل الإصلاحات
        if (!empty($fixes)) {
            log_admin_access('system', 'admin_fixes_applied');
        }
        
    } catch (Exception $e) {
        $fixes[] = 'خطأ في إصلاح مشاكل المدير: ' . $e->getMessage();
    }
    
    return $fixes;
}

// تعريف أن حماية المدير محملة
define('ADMIN_PROTECTION_LOADED', true);

// تسجيل تحميل النظام
if (defined('DEBUG_PERMISSIONS') && DEBUG_PERMISSIONS) {
    error_log("Admin Protection System loaded successfully");
}
?>
