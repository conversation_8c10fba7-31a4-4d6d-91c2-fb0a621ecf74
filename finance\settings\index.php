<?php
/**
 * إعدادات النظام المالي
 * Financial System Settings
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();

if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    header('Location: ../../dashboard/');
    exit();
}

// معالجة حفظ الإعدادات
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'طلب غير صحيح';
    } else {
        try {
            $conn->begin_transaction();

            // حفظ إعدادات العملة
            $currency_symbol = clean_input($_POST['currency_symbol'] ?? 'ر.س');
            $currency_code = clean_input($_POST['currency_code'] ?? 'SAR');
            $currency_name = clean_input($_POST['currency_name'] ?? 'ريال سعودي');

            // حفظ الإعدادات في قاعدة البيانات
            $settings = [
                'currency_symbol' => $currency_symbol,
                'currency_code' => $currency_code,
                'currency_name' => $currency_name
            ];

            foreach ($settings as $key => $value) {
                $stmt = $conn->prepare("
                    INSERT INTO system_settings (setting_key, setting_value, updated_at)
                    VALUES (?, ?, NOW())
                    ON DUPLICATE KEY UPDATE
                    setting_value = VALUES(setting_value),
                    updated_at = NOW()
                ");
                $stmt->bind_param("ss", $key, $value);
                $stmt->execute();
            }

            $conn->commit();
            $success_message = 'تم حفظ الإعدادات بنجاح';

        } catch (Exception $e) {
            $conn->rollback();
            $error_message = 'حدث خطأ أثناء حفظ الإعدادات: ' . $e->getMessage();
        }
    }
}

// جلب الإعدادات الحالية
$current_currency_symbol = get_system_setting('currency_symbol', 'ر.س');
$current_currency_code = get_system_setting('currency_code', 'SAR');
$current_currency_name = get_system_setting('currency_name', 'ريال سعودي');

$page_title = __('financial_settings');
include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('financial_settings'); ?></h1>
            <p class="text-muted"><?php echo __('manage_financial_system_settings'); ?></p>
        </div>
        <div>
            <a href="../index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_finance'); ?>
            </a>
        </div>
    </div>

    <!-- رسائل النجاح والخطأ -->
    <?php if (!empty($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <div class="row">
        <!-- إعدادات العملة -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-coins me-2"></i><?php echo __('currency_settings'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">

                        <div class="mb-3">
                            <label class="form-label">اسم العملة</label>
                            <select class="form-select" name="currency_name" onchange="updateCurrencyFields(this.value)">
                                <option value="ريال سعودي" <?php echo $current_currency_name === 'ريال سعودي' ? 'selected' : ''; ?>>ريال سعودي</option>
                                <option value="جنيه مصري" <?php echo $current_currency_name === 'جنيه مصري' ? 'selected' : ''; ?>>جنيه مصري</option>
                                <option value="درهم إماراتي" <?php echo $current_currency_name === 'درهم إماراتي' ? 'selected' : ''; ?>>درهم إماراتي</option>
                                <option value="دينار كويتي" <?php echo $current_currency_name === 'دينار كويتي' ? 'selected' : ''; ?>>دينار كويتي</option>
                                <option value="ريال قطري" <?php echo $current_currency_name === 'ريال قطري' ? 'selected' : ''; ?>>ريال قطري</option>
                                <option value="دولار أمريكي" <?php echo $current_currency_name === 'دولار أمريكي' ? 'selected' : ''; ?>>دولار أمريكي</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">رمز العملة</label>
                            <input type="text" class="form-control" name="currency_symbol" id="currency_symbol" value="<?php echo htmlspecialchars($current_currency_symbol); ?>" required>
                            <small class="text-muted">الرمز المستخدم لعرض العملة في النظام</small>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">كود العملة</label>
                            <input type="text" class="form-control" name="currency_code" id="currency_code" value="<?php echo htmlspecialchars($current_currency_code); ?>" required>
                            <small class="text-muted">الكود الدولي للعملة (مثل SAR, EGP)</small>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">عدد الخانات العشرية</label>
                            <select class="form-select" disabled>
                                <option value="2" selected>2</option>
                            </select>
                            <small class="text-muted">ثابت على خانتين عشريتين</small>
                        </div>

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>تنبيه:</strong> تغيير العملة سيؤثر على جميع المبالغ المعروضة في النظام المالي.
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ الإعدادات
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- إعدادات الرسوم -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-money-bill-wave me-2"></i><?php echo __('fee_settings'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label"><?php echo __('default_due_days'); ?></label>
                        <input type="number" class="form-control" value="30" readonly>
                        <small class="text-muted"><?php echo __('default_due_days_note'); ?></small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label"><?php echo __('late_fee_percentage'); ?></label>
                        <input type="number" class="form-control" value="0" step="0.01" readonly>
                        <small class="text-muted"><?php echo __('late_fee_percentage_note'); ?></small>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" checked disabled>
                            <label class="form-check-label">
                                <?php echo __('auto_payment_creation'); ?>
                            </label>
                            <small class="d-block text-muted"><?php echo __('auto_payment_creation_note'); ?></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إعدادات التقارير -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i><?php echo __('report_settings'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label"><?php echo __('default_report_period'); ?></label>
                        <select class="form-select" disabled>
                            <option value="current_year" selected><?php echo __('current_year'); ?></option>
                            <option value="current_month"><?php echo __('current_month'); ?></option>
                            <option value="last_month"><?php echo __('last_month'); ?></option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" checked disabled>
                            <label class="form-check-label">
                                <?php echo __('include_pending_fees'); ?>
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" checked disabled>
                            <label class="form-check-label">
                                <?php echo __('show_collection_rate'); ?>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- روابط سريعة -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-link me-2"></i><?php echo __('quick_links'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="../fees/" class="list-group-item list-group-item-action">
                            <i class="fas fa-money-bill-wave me-2 text-primary"></i>
                            <?php echo __('manage_fees'); ?>
                        </a>
                        <a href="../payments/" class="list-group-item list-group-item-action">
                            <i class="fas fa-credit-card me-2 text-success"></i>
                            <?php echo __('manage_payments'); ?>
                        </a>
                        <a href="../../reports/financial.php" class="list-group-item list-group-item-action">
                            <i class="fas fa-chart-bar me-2 text-info"></i>
                            <?php echo __('financial_reports'); ?>
                        </a>
                        <a href="../fees/types.php" class="list-group-item list-group-item-action">
                            <i class="fas fa-tags me-2 text-warning"></i>
                            <?php echo __('fee_types'); ?>
                        </a>
                        <a href="../books/" class="list-group-item list-group-item-action">
                            <i class="fas fa-book me-2 text-secondary"></i>
                            <?php echo __('books_management'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات النظام -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i><?php echo __('system_information'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <strong><?php echo __('system_version'); ?>:</strong><br>
                            <span class="text-muted">2.0.0</span>
                        </div>
                        <div class="col-md-3">
                            <strong><?php echo __('database_version'); ?>:</strong><br>
                            <span class="text-muted"><?php echo $conn->server_info; ?></span>
                        </div>
                        <div class="col-md-3">
                            <strong><?php echo __('last_backup'); ?>:</strong><br>
                            <span class="text-muted"><?php echo __('not_available'); ?></span>
                        </div>
                        <div class="col-md-3">
                            <strong><?php echo __('system_status'); ?>:</strong><br>
                            <span class="badge bg-success"><?php echo __('operational'); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أدوات الصيانة والإصلاح -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tools me-2"></i>أدوات الصيانة والإصلاح
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-database fa-2x text-primary mb-3"></i>
                                    <h6>فحص قاعدة البيانات</h6>
                                    <p class="text-muted small">فحص سلامة الجداول والبيانات المالية</p>
                                    <a href="../../check_fee_tables.php" class="btn btn-primary btn-sm" target="_blank">
                                        <i class="fas fa-search me-1"></i>فحص الآن
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-wrench fa-2x text-success mb-3"></i>
                                    <h6>إصلاح البيانات</h6>
                                    <p class="text-muted small">إصلاح الرسوم والمدفوعات الموجودة</p>
                                    <a href="../../fix_existing_fees.php" class="btn btn-success btn-sm" target="_blank">
                                        <i class="fas fa-tools me-1"></i>إصلاح الآن
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-vial fa-2x text-info mb-3"></i>
                                    <h6>اختبار النظام</h6>
                                    <p class="text-muted small">اختبار وظائف النظام المالي والتقارير</p>
                                    <a href="../../test_financial_reports.php" class="btn btn-info btn-sm" target="_blank">
                                        <i class="fas fa-play me-1"></i>اختبار الآن
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تنبيه:</strong> استخدم أدوات الصيانة بحذر. يُنصح بعمل نسخة احتياطية قبل تشغيل أي أداة إصلاح.
                    </div>

                    <!-- إحصائيات سريعة للأدوات -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h6><i class="fas fa-chart-line me-2"></i>إحصائيات سريعة:</h6>
                            <?php
                            // جلب إحصائيات للأدوات
                            $maintenance_stats = $conn->query("
                                SELECT
                                    (SELECT COUNT(*) FROM student_fees WHERE status = 'pending') as pending_fees,
                                    (SELECT COUNT(*) FROM student_fees WHERE status = 'paid') as paid_fees,
                                    (SELECT COUNT(*) FROM student_payments WHERE status = 'confirmed') as confirmed_payments,
                                    (SELECT COUNT(*) FROM fee_types WHERE status = 'active') as active_fee_types
                            ")->fetch_assoc();
                            ?>
                            <div class="row text-center">
                                <div class="col-3">
                                    <div class="border rounded p-2">
                                        <h5 class="text-warning mb-0"><?php echo number_format($maintenance_stats['pending_fees'] ?? 0); ?></h5>
                                        <small>رسوم معلقة</small>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="border rounded p-2">
                                        <h5 class="text-success mb-0"><?php echo number_format($maintenance_stats['paid_fees'] ?? 0); ?></h5>
                                        <small>رسوم مدفوعة</small>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="border rounded p-2">
                                        <h5 class="text-info mb-0"><?php echo number_format($maintenance_stats['confirmed_payments'] ?? 0); ?></h5>
                                        <small>مدفوعات مؤكدة</small>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="border rounded p-2">
                                        <h5 class="text-primary mb-0"><?php echo number_format($maintenance_stats['active_fee_types'] ?? 0); ?></h5>
                                        <small>أنواع رسوم نشطة</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateCurrencyFields(currencyName) {
    const symbolField = document.getElementById('currency_symbol');
    const codeField = document.getElementById('currency_code');

    const currencies = {
        'ريال سعودي': { symbol: 'ر.س', code: 'SAR' },
        'جنيه مصري': { symbol: 'ج.م', code: 'EGP' },
        'درهم إماراتي': { symbol: 'د.إ', code: 'AED' },
        'دينار كويتي': { symbol: 'د.ك', code: 'KWD' },
        'ريال قطري': { symbol: 'ر.ق', code: 'QAR' },
        'دولار أمريكي': { symbol: '$', code: 'USD' }
    };

    if (currencies[currencyName]) {
        symbolField.value = currencies[currencyName].symbol;
        codeField.value = currencies[currencyName].code;
    }
}
</script>

<?php include_once '../../includes/footer.php'; ?>
