<?php
/**
 * إصلاح جدول bank_account_fee_types المفقود
 * Fix Missing bank_account_fee_types Table
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

$page_title = 'إصلاح جدول bank_account_fee_types';
require_once '../includes/header.php';

$success_messages = [];
$error_messages = [];

// تطبيق الإصلاح
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['apply_fix'])) {
    
    try {
        $conn->begin_transaction();
        
        // 1. التحقق من وجود الجدول
        $check_table = $conn->query("SHOW TABLES LIKE 'bank_account_fee_types'");
        
        if ($check_table->num_rows === 0) {
            // إنشاء الجدول
            $create_table_sql = "
                CREATE TABLE `bank_account_fee_types` (
                  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
                  `bank_account_id` int(10) UNSIGNED NOT NULL,
                  `fee_type_id` int(10) UNSIGNED NOT NULL,
                  `is_active` tinyint(1) DEFAULT 1,
                  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
                  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                  PRIMARY KEY (`id`),
                  UNIQUE KEY `unique_bank_fee_type` (`bank_account_id`, `fee_type_id`),
                  KEY `bank_account_id` (`bank_account_id`),
                  KEY `fee_type_id` (`fee_type_id`),
                  KEY `is_active` (`is_active`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            
            $conn->query($create_table_sql);
            $success_messages[] = "تم إنشاء جدول bank_account_fee_types بنجاح";
        } else {
            $success_messages[] = "جدول bank_account_fee_types موجود بالفعل";
        }
        
        // 2. إضافة القيود الخارجية إذا لم تكن موجودة
        $constraints = [
            'bank_account_fee_types_bank_account_id_foreign' => "ALTER TABLE `bank_account_fee_types` ADD CONSTRAINT `bank_account_fee_types_bank_account_id_foreign` FOREIGN KEY (`bank_account_id`) REFERENCES `bank_accounts` (`id`) ON DELETE CASCADE",
            'bank_account_fee_types_fee_type_id_foreign' => "ALTER TABLE `bank_account_fee_types` ADD CONSTRAINT `bank_account_fee_types_fee_type_id_foreign` FOREIGN KEY (`fee_type_id`) REFERENCES `fee_types` (`id`) ON DELETE CASCADE"
        ];
        
        foreach ($constraints as $constraint_name => $sql) {
            // التحقق من وجود القيد
            $check_constraint = $conn->query("
                SELECT CONSTRAINT_NAME 
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = '" . DB_NAME . "' 
                AND TABLE_NAME = 'bank_account_fee_types' 
                AND CONSTRAINT_NAME = '$constraint_name'
            ");
            
            if ($check_constraint->num_rows === 0) {
                try {
                    $conn->query($sql);
                    $success_messages[] = "تم إضافة القيد الخارجي $constraint_name";
                } catch (Exception $e) {
                    // تجاهل الأخطاء إذا كان القيد موجود بالفعل
                    if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                        throw $e;
                    }
                }
            } else {
                $success_messages[] = "القيد الخارجي $constraint_name موجود بالفعل";
            }
        }
        
        // 3. إضافة بيانات تجريبية إذا كان الجدول فارغاً
        $count_result = $conn->query("SELECT COUNT(*) as count FROM bank_account_fee_types");
        $count = $count_result->fetch_assoc()['count'];
        
        if ($count == 0) {
            // التحقق من وجود الجداول المرجعية
            $bank_accounts = $conn->query("SELECT id FROM bank_accounts LIMIT 2");
            $fee_types = $conn->query("SELECT id FROM fee_types LIMIT 3");
            
            if ($bank_accounts->num_rows > 0 && $fee_types->num_rows > 0) {
                $sample_data = [
                    [1, 1, 1],
                    [2, 1, 2],
                    [3, 2, 3]
                ];
                
                $insert_stmt = $conn->prepare("
                    INSERT IGNORE INTO bank_account_fee_types 
                    (id, bank_account_id, fee_type_id, is_active, created_at, updated_at) 
                    VALUES (?, ?, ?, 1, NOW(), NOW())
                ");
                
                foreach ($sample_data as $data) {
                    $insert_stmt->bind_param("iii", $data[0], $data[1], $data[2]);
                    $insert_stmt->execute();
                }
                
                $success_messages[] = "تم إضافة البيانات التجريبية";
            }
        }
        
        $conn->commit();
        $success_messages[] = "تم تطبيق جميع الإصلاحات بنجاح!";
        
    } catch (Exception $e) {
        $conn->rollback();
        $error_messages[] = "خطأ في تطبيق الإصلاح: " . $e->getMessage();
    }
}

// فحص حالة الجدول
$table_exists = false;
$table_count = 0;
$constraints_exist = [];

try {
    $check_table = $conn->query("SHOW TABLES LIKE 'bank_account_fee_types'");
    $table_exists = $check_table->num_rows > 0;
    
    if ($table_exists) {
        $count_result = $conn->query("SELECT COUNT(*) as count FROM bank_account_fee_types");
        $table_count = $count_result->fetch_assoc()['count'];
        
        // فحص القيود الخارجية
        $constraint_names = [
            'bank_account_fee_types_bank_account_id_foreign',
            'bank_account_fee_types_fee_type_id_foreign'
        ];
        
        foreach ($constraint_names as $constraint_name) {
            $check_constraint = $conn->query("
                SELECT CONSTRAINT_NAME 
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = '" . DB_NAME . "' 
                AND TABLE_NAME = 'bank_account_fee_types' 
                AND CONSTRAINT_NAME = '$constraint_name'
            ");
            $constraints_exist[$constraint_name] = $check_constraint->num_rows > 0;
        }
    }
} catch (Exception $e) {
    $error_messages[] = "خطأ في فحص الجدول: " . $e->getMessage();
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">🔧 إصلاح جدول bank_account_fee_types</h3>
                </div>
                <div class="card-body">
                    
                    <?php if (!empty($success_messages)): ?>
                        <div class="alert alert-success">
                            <h5>✅ تم تطبيق الإصلاحات بنجاح:</h5>
                            <ul>
                                <?php foreach ($success_messages as $message): ?>
                                    <li><?php echo $message; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($error_messages)): ?>
                        <div class="alert alert-danger">
                            <h5>❌ أخطاء:</h5>
                            <ul>
                                <?php foreach ($error_messages as $error): ?>
                                    <li><?php echo $error; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                    
                    <div class="alert alert-info">
                        <h5>📋 حالة الجدول الحالية:</h5>
                        <ul>
                            <li><strong>وجود الجدول:</strong> 
                                <?php echo $table_exists ? '<span class="badge bg-success">موجود</span>' : '<span class="badge bg-danger">غير موجود</span>'; ?>
                            </li>
                            <?php if ($table_exists): ?>
                                <li><strong>عدد السجلات:</strong> <?php echo $table_count; ?></li>
                                <li><strong>القيود الخارجية:</strong>
                                    <ul>
                                        <?php foreach ($constraints_exist as $constraint => $exists): ?>
                                            <li><?php echo $constraint; ?>: 
                                                <?php echo $exists ? '<span class="badge bg-success">موجود</span>' : '<span class="badge bg-warning">غير موجود</span>'; ?>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </div>
                    
                    <?php if (!$table_exists || $table_count == 0 || in_array(false, $constraints_exist)): ?>
                        <div class="alert alert-warning">
                            <h5>⚠️ يحتاج إلى إصلاح</h5>
                            <p>الجدول أو بعض مكوناته مفقودة. اضغط على الزر أدناه لتطبيق الإصلاح.</p>
                        </div>
                        
                        <form method="POST" class="mb-4">
                            <button type="submit" name="apply_fix" class="btn btn-warning btn-lg">
                                <i class="fas fa-tools"></i> تطبيق الإصلاح
                            </button>
                        </form>
                    <?php else: ?>
                        <div class="alert alert-success">
                            <h5>✅ الجدول في حالة جيدة</h5>
                            <p>جميع المكونات موجودة وتعمل بشكل صحيح.</p>
                        </div>
                    <?php endif; ?>
                    
                    <div class="mt-4">
                        <a href="installments/bank_accounts.php" class="btn btn-primary">
                            <i class="fas fa-university"></i> إدارة الحسابات البنكية
                        </a>
                        <a href="../dashboard/" class="btn btn-secondary">
                            <i class="fas fa-home"></i> العودة للوحة التحكم
                        </a>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
