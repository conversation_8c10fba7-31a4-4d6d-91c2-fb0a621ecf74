<?php
/**
 * صفحة تسجيل الدخول
 * Login Page
 */

define('SYSTEM_INIT', true);
require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/functions.php';
require_once 'includes/security.php';

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// إعادة توجيه المستخدم المسجل دخوله بالفعل
if (is_logged_in()) {
    header('Location: dashboard/');
    exit();
}

$error_message = '';
$success_message = '';

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = clean_input($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember_me = isset($_POST['remember_me']);
    
    if (empty($username) || empty($password)) {
        $error_message = __('required_field');
    } else {
        // البحث عن المستخدم
        $user = get_user_by_username($username);
        
        if (!$user) {
            $user = get_user_by_email($username);
        }
        
        if ($user && verify_password($password, $user['password'])) {
            if ($user['status'] === 'active') {
                // تسجيل الدخول بنجاح
                login_user($user);
                
                // تذكر المستخدم إذا طلب ذلك
                if ($remember_me) {
                    $token = bin2hex(random_bytes(32));
                    setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30 يوم
                    
                    // حفظ الرمز في قاعدة البيانات
                    global $conn;
                    $stmt = $conn->prepare("UPDATE users SET remember_token = ? WHERE id = ?");
                    $stmt->bind_param("si", $token, $user['id']);
                    $stmt->execute();
                }
                
                // إعادة التوجيه حسب نوع المستخدم
                switch ($user['role']) {
                    case 'admin':
                        header('Location: dashboard/');
                        break;
                    case 'teacher':
                        header('Location: dashboard/');
                        break;
                    case 'student':
                        header('Location: dashboard/');
                        break;
                    default:
                        header('Location: dashboard/');
                }
                exit();
            } else {
                $error_message = __('account_disabled');
            }
        } else {
            $error_message = __('invalid_credentials');
        }
    }
}

// دالة تنظيف الرموز المنتهية الصلاحية (تشغل مرة واحدة يومياً)
function cleanup_expired_tokens() {
    global $conn;

    // التحقق من آخر مرة تم فيها التنظيف
    $last_cleanup_file = 'temp/last_token_cleanup.txt';
    $should_cleanup = true;

    if (file_exists($last_cleanup_file)) {
        $last_cleanup = file_get_contents($last_cleanup_file);
        $should_cleanup = (time() - intval($last_cleanup)) > (24 * 60 * 60); // 24 ساعة
    }

    if ($should_cleanup) {
        // إنشاء مجلد temp إذا لم يكن موجوداً
        if (!is_dir('temp')) {
            mkdir('temp', 0755, true);
        }

        // التحقق من وجود عمود remember_token وإضافته إذا لم يكن موجوداً
        $check_column = "SHOW COLUMNS FROM users LIKE 'remember_token'";
        $result = $conn->query($check_column);

        if ($result->num_rows == 0) {
            // إضافة عمود remember_token
            $add_column = "ALTER TABLE users ADD COLUMN remember_token VARCHAR(255) NULL DEFAULT NULL AFTER password_reset_expires";
            $conn->query($add_column);
        }

        // حذف الرموز القديمة (أكثر من 30 يوم)
        $cleanup_query = "UPDATE users SET remember_token = NULL WHERE remember_token IS NOT NULL AND updated_at < DATE_SUB(NOW(), INTERVAL 30 DAY)";
        $conn->query($cleanup_query);

        // حفظ وقت التنظيف الحالي
        file_put_contents($last_cleanup_file, time());
    }
}

// تشغيل تنظيف الرموز
cleanup_expired_tokens();

// التحقق من رمز التذكر
if (isset($_COOKIE['remember_token']) && !is_logged_in()) {
    $token = $_COOKIE['remember_token'];
    global $conn;

    // التحقق من صحة الرمز وأنه لم ينته
    $stmt = $conn->prepare("
        SELECT * FROM users
        WHERE remember_token = ?
        AND status = 'active'
        AND updated_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
    ");
    $stmt->bind_param("s", $token);
    $stmt->execute();
    $user = $stmt->get_result()->fetch_assoc();

    if ($user) {
        // تحديث وقت آخر استخدام للرمز
        $update_stmt = $conn->prepare("UPDATE users SET updated_at = NOW() WHERE id = ?");
        $update_stmt->bind_param("i", $user['id']);
        $update_stmt->execute();

        login_user($user);
        header('Location: dashboard/');
        exit();
    } else {
        // حذف الكوكيز إذا كان الرمز غير صالح
        setcookie('remember_token', '', time() - 3600, '/');
    }
}

// الحصول على إعدادات النظام
$system_name = get_system_setting('school_name', __('system_name'));
// لا داعي لتعريف $current_language أو تحميل ملف الترجمة هنا، لأن الهيدر سيتكفل بذلك
require_once 'includes/header.php';
?>
<!DOCTYPE html>
<html lang="<?php echo get_current_language(); ?>" dir="<?php echo get_current_language() === 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo __('login') . ' - ' . $system_name; ?></title>
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }
        
        body {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            margin: 20px;
        }
        
        .login-left {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        
        .login-right {
            padding: 60px 40px;
        }
        
        .logo {
            font-size: 3rem;
            margin-bottom: 20px;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }
        
        .language-switcher {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 10px;
            padding: 8px 15px;
            color: white;
            transition: all 0.3s ease;
        }
        
        .language-switcher:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }
        
        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 70%;
            right: 10%;
            animation-delay: 2s;
        }
        
        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            top: 40%;
            left: 80%;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
        }
        
        .fade-in {
            animation: fadeIn 0.8s ease-in;
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .input-group-text {
            background: transparent;
            border: 2px solid #e9ecef;
            border-right: none;
            border-radius: 10px 0 0 10px;
        }
        
        .input-group .form-control {
            border-left: none;
            border-radius: 0 10px 10px 0;
        }
        
        .input-group:focus-within .input-group-text {
            border-color: var(--primary-color);
        }
        
        @media (max-width: 768px) {
            .login-left {
                padding: 40px 20px;
            }
            
            .login-right {
                padding: 40px 20px;
            }
            
            .logo {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Language Switcher -->
    <div class="dropdown position-absolute" style="top: 20px; right: 20px; z-index: 1000;">
        <button class="language-switcher dropdown-toggle" data-bs-toggle="dropdown">
            <i class="fas fa-globe me-1"></i>
            <?php echo get_current_language() === 'ar' ? 'العربية' : 'English'; ?>
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="?lang=ar">العربية</a></li>
            <li><a class="dropdown-item" href="?lang=en">English</a></li>
        </ul>
    </div>

    <div class="login-container fade-in">
        <div class="row g-0">
            <!-- Left Side -->
            <div class="col-lg-6 login-left position-relative">
                <div class="floating-shapes">
                    <div class="shape"></div>
                    <div class="shape"></div>
                    <div class="shape"></div>
                </div>
                
                <div class="logo">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <h2 class="mb-3"><?php echo $system_name; ?></h2>
                <p class="lead mb-4"><?php echo __('welcome_message'); ?></p>
                <div class="features">
                    <div class="feature mb-3">
                        <i class="fas fa-users me-2"></i>
                        <?php echo __('manage_students_teachers'); ?>
                    </div>
                    <div class="feature mb-3">
                        <i class="fas fa-chart-line me-2"></i>
                        <?php echo __('track_grades_attendance'); ?>
                    </div>
                    <div class="feature mb-3">
                        <i class="fas fa-file-alt me-2"></i>
                        <?php echo __('create_manage_exams'); ?>
                    </div>
                </div>
            </div>
            
            <!-- Right Side -->
            <div class="col-lg-6 login-right">
                <div class="text-center mb-4">
                    <h3 class="mb-2"><?php echo __('login'); ?></h3>
                    <p class="text-muted"><?php echo __('enter_credentials'); ?></p>
                </div>
                
                <?php if ($error_message): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>
                
                <?php if ($success_message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>
                
                <form method="POST" class="needs-validation" novalidate>
                    <div class="mb-3">
                        <label for="username" class="form-label"><?php echo __('username_or_email'); ?></label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user"></i>
                            </span>
                            <input type="text" 
                                   class="form-control" 
                                   id="username" 
                                   name="username" 
                                   value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                                   placeholder="<?php echo __('enter_username_email'); ?>"
                                   required>
                            <div class="invalid-feedback">
                                <?php echo __('required_field'); ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label"><?php echo __('password'); ?></label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" 
                                   class="form-control" 
                                   id="password" 
                                   name="password" 
                                   placeholder="<?php echo __('enter_password'); ?>"
                                   required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                <i class="fas fa-eye" id="passwordToggleIcon"></i>
                            </button>
                            <div class="invalid-feedback">
                                <?php echo __('required_field'); ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                        <label class="form-check-label" for="remember_me">
                            <?php echo __('remember_me'); ?>
                        </label>
                    </div>
                    
                    <div class="d-grid mb-3">
                        <button type="submit" class="btn btn-login btn-primary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            <?php echo __('login'); ?>
                        </button>
                    </div>
                    
                    <div class="text-center">
                        <a href="forgot_password.php" class="text-decoration-none">
                            <?php echo __('forgot_password'); ?>
                        </a>
                    </div>
                </form>
                
                <!-- Demo Accounts -->
                <div class="mt-4 p-3 bg-light rounded">
                    <h6 class="mb-2"><?php echo __('demo_accounts'); ?>:</h6>
                    <small class="text-muted">
                        <strong><?php echo __('admin'); ?>:</strong> admin / password<br>
                        <strong><?php echo __('teacher'); ?>:</strong> teacher1 / password<br>
                        <strong><?php echo __('student'); ?>:</strong> student1 / password
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                const forms = document.getElementsByClassName('needs-validation');
                Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
        
        // Toggle password visibility
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('passwordToggleIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }
        
        // Language switcher
        document.addEventListener('DOMContentLoaded', function() {
            const langLinks = document.querySelectorAll('.dropdown-item[href*="lang="]');
            langLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const lang = this.href.split('lang=')[1];
                    
                    // Save language preference
                    fetch('api/save_preference.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            key: 'language',
                            value: lang
                        })
                    }).then(() => {
                        location.reload();
                    });
                });
            });
        });
        
        // Auto-hide alerts
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Enter to submit form
            if (e.key === 'Enter' && !e.shiftKey) {
                const form = document.querySelector('form');
                if (form && document.activeElement.tagName !== 'BUTTON') {
                    form.submit();
                }
            }
        });
    </script>
</body>
</html>
