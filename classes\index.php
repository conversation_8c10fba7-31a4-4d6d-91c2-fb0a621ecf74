<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// معالجة تغيير اللغة من URL
if (isset($_GET['lang']) && in_array($_GET['lang'], ['ar', 'en'])) {
    $_SESSION['system_language'] = $_GET['lang'];
}

/**
 * صفحة إدارة الفصول
 * Classes Management Page
 */

// التحقق من الصلاحيات قبل أي إخراج
// التحقق من الصلاحيات أولاً قبل إرسال أي محتوى
check_session();

// معالجة الحذف قبل أي إخراج HTML
if (isset($_POST['delete_class']) && check_permission('admin')) {
    $class_id = intval($_POST['class_id']);

    global $conn;
    $conn->begin_transaction();

    try {
        // التحقق من وجود طلاب في الفصل
        $check_students = $conn->prepare("SELECT COUNT(*) as count FROM students WHERE class_id = ?");
        $check_students->bind_param("i", $class_id);
        $check_students->execute();
        $student_count = $check_students->get_result()->fetch_assoc()['count'];
        $check_students->close();

        if ($student_count > 0) {
            throw new Exception("Cannot delete class with students");
        }

        // حذف الفصل
        $stmt = $conn->prepare("DELETE FROM classes WHERE id = ?");
        $stmt->bind_param("i", $class_id);
        $stmt->execute();

        $conn->commit();

        // تسجيل النشاط (يمكن إضافة هذا لاحقاً)
        // log_activity($_SESSION['user_id'], 'delete_class', 'classes', $class_id);

        $_SESSION['success_message'] = __('deleted_successfully');
        header('Location: index.php');
        exit();
    } catch (Exception $e) {
        $conn->rollback();
        if (strpos($e->getMessage(), "Cannot delete class with students") !== false) {
            $_SESSION['error_message'] = __('cannot_delete_class_with_students');
        } else {
            $_SESSION['error_message'] = __('error_occurred');
        }
        header('Location: index.php');
        exit();
    }
}

// تخصيص عرض الفصول للطالب/الطالبة
if (isset($_SESSION['role']) && $_SESSION['role'] === 'student') {
    global $conn;
    $user_id = $_SESSION['user_id'];
    $stmt = $conn->prepare("SELECT c.* FROM students s JOIN classes c ON s.class_id = c.id WHERE s.user_id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $student_classes = [];
    while ($row = $result->fetch_assoc()) {
        $student_classes[] = $row;
    }
    $page_title = __('classes');
    require_once '../includes/header.php';
    ?>
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0"><?php echo __('classes'); ?></h1>
        </div>
        <?php if (count($student_classes) > 0): ?>
            <div class="row">
                <?php foreach ($student_classes as $class): ?>
                    <div class="col-lg-6 col-md-8 mb-4">
                        <div class="card h-100 shadow-sm">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-school me-2"></i>
                                    <?php echo htmlspecialchars($class['class_name']); ?>
                                </h6>
                                <span class="badge bg-success"><?php echo __('active'); ?></span>
                            </div>
                            <div class="card-body">
                                <div class="mb-2"><strong><?php echo __('grade_level'); ?>:</strong>
                                    <?php echo !empty($class['grade_name']) ? htmlspecialchars($class['grade_name']) : htmlspecialchars($class['grade_level']); ?>
                                </div>
                                <div class="mb-2"><strong><?php echo __('capacity'); ?>:</strong> <?php echo htmlspecialchars($class['capacity']); ?></div>
                                <?php if (isset($class['description'])): ?>
                                    <div class="mb-2"><strong><?php echo __('description'); ?>:</strong> <?php echo nl2br(htmlspecialchars($class['description'] ?? '')); ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="alert alert-info text-center">
                <i class="fas fa-info-circle me-2"></i><?php echo __('no_classes_found'); ?>
            </div>
        <?php endif; ?>
    </div>
    <?php
    require_once '../includes/footer.php';
    exit();
}

// التحقق من الصلاحيات قبل أي إخراج
// التحقق من الصلاحيات أولاً قبل إرسال أي محتوى
check_session();
if (!check_permission('teacher') && !check_permission('staff') && !has_permission('classes_view')) {
    header('Location: ../dashboard/');
    exit();
}

$page_title = __('classes');
require_once '../includes/header.php';

// معالجة الحذف تمت في أعلى الملف

// معالجة البحث والفلترة
$search = clean_input($_GET['search'] ?? '');
$grade_filter = clean_input($_GET['grade_id'] ?? '');
$status_filter = clean_input($_GET['status'] ?? '');

// بناء استعلام البحث
$where_conditions = ["1=1"];
$params = [];
$types = "";

if (!empty($search)) {
    $where_conditions[] = "(c.class_name LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param]);
    $types .= "s";
}

if (!empty($grade_filter)) {
    $where_conditions[] = "c.grade_id = ?";
    $params[] = $grade_filter;
    $types .= "i";
}

if (!empty($status_filter)) {
    $where_conditions[] = "c.status = ?";
    $params[] = $status_filter;
    $types .= "s";
}

$where_clause = implode(" AND ", $where_conditions);

// الحصول على عدد الصفحات
$count_query = "
    SELECT COUNT(*) as total
    FROM classes c
    WHERE $where_clause
";

$count_stmt = $conn->prepare($count_query);
if (!empty($params)) {
    $count_stmt->bind_param($types, ...$params);
}
$count_stmt->execute();
$total_records = $count_stmt->get_result()->fetch_assoc()['total'];

// إظهار جميع الفصول في صفحة واحدة (بدون ترقيم)
// $page = intval($_GET['page'] ?? 1);
// $records_per_page = ITEMS_PER_PAGE;
// $total_pages = ceil($total_records / $records_per_page);
// $offset = ($page - 1) * $records_per_page;

// جلب الفصول مع إحصائيات الطلاب والمراحل الدراسية مرتبة حسب المراحل والصفوف
$query = "
    SELECT
        c.*,
        u.full_name as teacher_name,
        es.stage_name,
        es.stage_code,
        es.sort_order as stage_sort_order,
        g.grade_name,
        g.sort_order as grade_sort_order,
        COUNT(s.id) as student_count
    FROM classes c
    LEFT JOIN educational_stages es ON c.stage_id = es.id
    LEFT JOIN grades g ON c.grade_id = g.id
    LEFT JOIN teachers t ON c.class_teacher_id = t.id
    LEFT JOIN users u ON t.user_id = u.id
    LEFT JOIN students s ON c.id = s.class_id
    WHERE $where_clause
    GROUP BY c.id
    ORDER BY es.sort_order ASC, g.sort_order ASC, c.section ASC, c.class_name ASC
";

$stmt = $conn->prepare($query);
if (!$stmt) {
    die("SQL Error: " . $conn->error . "<br>Query: " . htmlspecialchars($query));
}
// إزالة معاملات الترقيم
// $params[] = $records_per_page;
// $params[] = $offset;
// $types .= "ii";

if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$classes = $stmt->get_result();

// جلب قائمة الصفوف الدراسية للفلترة مرتبة حسب المراحل والصفوف
$grades = $conn->query("
    SELECT DISTINCT
        g.id,
        g.grade_name,
        g.grade_code,
        es.stage_name,
        es.sort_order as stage_sort_order,
        g.sort_order as grade_sort_order
    FROM grades g
    LEFT JOIN educational_stages es ON g.stage_id = es.id
    WHERE g.status = 'active'
    ORDER BY es.sort_order ASC, g.sort_order ASC
");

// إحصائيات سريعة
$stats_query = "
    SELECT
        COUNT(*) as total_classes,
        SUM(CASE WHEN c.status = 'active' THEN 1 ELSE 0 END) as active_classes,
        COUNT(DISTINCT c.grade_id) as total_grades,
        AVG(c.capacity) as avg_capacity
    FROM classes c
    LEFT JOIN grades g ON c.grade_id = g.id
";
$stats_result = $conn->query($stats_query);
$stats = $stats_result->fetch_assoc();

// إحصائيات الطلاب
$student_stats_query = "
    SELECT 
        COUNT(*) as total_students,
        COUNT(DISTINCT class_id) as classes_with_students
    FROM students
";
$student_stats_result = $conn->query($student_stats_query);
$student_stats = $student_stats_result->fetch_assoc();
?>

<div class="container-fluid">
    <!-- Messages -->
    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Custom CSS for Stage Grouping -->
    <style>
        /* تحسين التصميم العام */
        .stage-group {
            margin-bottom: 4rem;
        }

        .stage-title {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.2);
            text-align: center;
        }

        .stage-title h4 {
            margin: 0;
            font-weight: 600;
            font-size: 1.5rem;
        }

        .stage-badge {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
        }

        /* تحسين بطاقات الفصول */
        .class-card {
            transition: all 0.3s ease;
            border: 1px solid #e3e6f0;
            border-radius: 12px;
            height: 100%;
            overflow: hidden;
        }

        .class-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border-color: #007bff;
        }

        .class-card .card-header {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-bottom: 2px solid #007bff;
            padding: 15px;
        }

        .class-card .card-body {
            padding: 20px;
            flex-grow: 1;
        }

        .class-card .card-footer {
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            padding: 15px;
        }

        /* شارة المستوى */
        .grade-level-badge {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            font-size: 0.85rem;
            padding: 6px 12px;
            border-radius: 15px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 15px;
        }

        /* تقدم الطلاب */
        .student-progress {
            background: linear-gradient(45deg, #f8f9fa, #ffffff);
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .progress {
            height: 8px;
            border-radius: 10px;
            overflow: hidden;
            background: #e9ecef;
        }

        .progress-bar {
            transition: width 0.8s ease;
            border-radius: 10px;
        }

        /* الفهرس السريع */
        .quick-nav-item {
            transition: all 0.3s ease;
            border-radius: 10px;
            padding: 15px;
            text-decoration: none;
            display: block;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
        }

        .quick-nav-item:hover {
            background: linear-gradient(45deg, #e3f2fd, #f3e5f5);
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            text-decoration: none;
        }

        /* المراحل الفارغة */
        .empty-stage {
            text-align: center;
            padding: 60px 20px;
            background: linear-gradient(45deg, #f8f9fa, #ffffff);
            border-radius: 15px;
            border: 2px dashed #dee2e6;
            margin: 20px 0;
        }

        /* تحسين الاستجابة */
        @media (max-width: 768px) {
            .stage-title h4 {
                font-size: 1.3rem;
            }

            .class-card {
                margin-bottom: 20px;
            }

            .stage-group {
                margin-bottom: 3rem;
            }

            .quick-nav-item {
                margin-bottom: 10px;
            }
        }

        /* تحسين الألوان والتباين */
        .text-primary {
            color: #007bff !important;
        }

        .bg-primary {
            background: linear-gradient(135deg, #007bff, #0056b3) !important;
        }

        /* تحسين الأزرار */
        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            border-width: 1px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-outline-info:hover {
            background-color: #17a2b8;
            border-color: #17a2b8;
        }

        .btn-outline-primary:hover {
            background-color: #007bff;
            border-color: #007bff;
        }

        .btn-outline-danger:hover {
            background-color: #dc3545;
            border-color: #dc3545;
        }

        /* تحسين التنقل السلس */
        html {
            scroll-behavior: smooth;
        }

        /* تحسين الظلال */
        .shadow-sm {
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08) !important;
        }

        /* تحسين الحدود */
        .border-0 {
            border: none !important;
        }

        /* تحسين المسافات */
        .g-4 > * {
            padding: 0.75rem;
        }

        /* تحسين النصوص */
        .text-dark {
            color: #2c3e50 !important;
        }

        .text-muted {
            color: #6c757d !important;
        }

        /* تحسين البطاقات للجوال */
        @media (max-width: 576px) {
            .class-card .card-body {
                padding: 15px;
            }

            .stage-title {
                padding: 15px;
                margin-bottom: 20px;
            }

            .stage-title h4 {
                font-size: 1.2rem;
            }

            .quick-nav-item {
                padding: 10px;
                margin-bottom: 10px;
            }

            .btn-sm {
                font-size: 0.8rem;
                padding: 0.4rem 0.8rem;
            }
        }
    </style>

    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('classes'); ?></h1>
            <p class="text-muted"><?php echo __('manage_classes_info'); ?></p>
        </div>
        <div>
            <a href="organized_view.php" class="btn btn-info me-2">
                <i class="fas fa-layer-group me-2"></i>عرض مرتب حسب المراحل
            </a>
            <?php if (check_permission('admin')): ?>
            <a href="add.php" class="btn btn-primary me-2">
                <i class="fas fa-plus me-2"></i><?php echo __('add_class'); ?>
            </a>
            <a href="schedule.php" class="btn btn-success">
                <i class="fas fa-calendar me-2"></i><?php echo __('class_schedule'); ?>
            </a>
            <?php endif; ?>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-gradient p-3 rounded-3">
                                <i class="fas fa-school text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($stats['total_classes']); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('total_classes'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-gradient p-3 rounded-3">
                                <i class="fas fa-check-circle text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($stats['active_classes']); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('active_classes'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-gradient p-3 rounded-3">
                                <i class="fas fa-users text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($student_stats['total_students']); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('total_students'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-gradient p-3 rounded-3">
                                <i class="fas fa-layer-group text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($stats['total_grades']); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('grade_levels'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label"><?php echo __('search'); ?></label>
                    <input type="text" 
                           class="form-control" 
                           id="search" 
                           name="search" 
                           value="<?php echo htmlspecialchars($search); ?>"
                           placeholder="<?php echo __('search_by_name_code_room'); ?>">
                </div>
                
                <div class="col-md-3">
                    <label for="grade_id" class="form-label"><?php echo __('grade_level'); ?></label>
                    <select class="form-select" id="grade_id" name="grade_id">
                        <option value=""><?php echo __('all_grades'); ?></option>
                        <?php
                        $current_stage = '';
                        while ($grade = $grades->fetch_assoc()):
                            // إضافة عنوان المرحلة إذا تغيرت
                            if ($current_stage != $grade['stage_name'] && !empty($grade['stage_name'])) {
                                if ($current_stage != '') echo '</optgroup>';
                                echo '<optgroup label="' . htmlspecialchars($grade['stage_name']) . '">';
                                $current_stage = $grade['stage_name'];
                            }
                        ?>
                            <option value="<?php echo $grade['id']; ?>"
                                    <?php echo ($grade_filter == $grade['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($grade['grade_name']); ?>
                            </option>
                        <?php endwhile; ?>
                        <?php if ($current_stage != '') echo '</optgroup>'; ?>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="status" class="form-label"><?php echo __('status'); ?></label>
                    <select class="form-select" id="status" name="status">
                        <option value=""><?php echo __('all_statuses'); ?></option>
                        <option value="active" <?php echo ($status_filter == 'active') ? 'selected' : ''; ?>>
                            <?php echo __('active'); ?>
                        </option>
                        <option value="inactive" <?php echo ($status_filter == 'inactive') ? 'selected' : ''; ?>>
                            <?php echo __('inactive'); ?>
                        </option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i><?php echo __('search'); ?>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Classes Grid - Grouped by Educational Stage -->
    <?php if ($classes->num_rows > 0): ?>
        <?php
        // تجميع الفصول حسب المرحلة الدراسية
        $classes_by_stage = [];
        $classes_data = [];

        // جلب جميع البيانات أولاً
        while ($class = $classes->fetch_assoc()) {
            $classes_data[] = $class;
        }

        // تجميع البيانات حسب المرحلة
        foreach ($classes_data as $class) {
            $stage_key = $class['stage_name'] ?: 'غير مصنف';
            if (!isset($classes_by_stage[$stage_key])) {
                $classes_by_stage[$stage_key] = [
                    'stage_info' => $class,
                    'classes' => []
                ];
            }
            $classes_by_stage[$stage_key]['classes'][] = $class;
        }

        // ترتيب المراحل حسب الطلب
        $stage_order = [
            'مرحلة ما قبل رياض الأطفال' => 1,
            'رياض الأطفال' => 2,
            'المرحلة الابتدائية' => 3,
            'المرحلة الاعدادية' => 4,
            'مرحلة رياض الأطفال' => 2,
            'المرحلة الإعدادية' => 4,
            'المرحلة المتوسطة' => 4,
            'المرحلة الثانوية' => 5,
            'التعليم الفني' => 6,
            'مرحلة الطفولة المبكرة' => 1,
            'غير مصنف' => 99
        ];

        uksort($classes_by_stage, function($a, $b) use ($stage_order) {
            $order_a = $stage_order[$a] ?? 50;
            $order_b = $stage_order[$b] ?? 50;
            return $order_a - $order_b;
        });
        ?>

        <!-- فهرس سريع للمراحل -->
        <div class="card mb-5 shadow-sm border-0">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-compass me-2"></i>التنقل السريع بين المراحل
                </h5>
            </div>
            <div class="card-body p-4">
                <div class="row g-3">
                    <?php
                    $stage_icons = [
                        'مرحلة ما قبل رياض الأطفال' => 'fas fa-baby',
                        'رياض الأطفال' => 'fas fa-child',
                        'مرحلة رياض الأطفال' => 'fas fa-child',
                        'المرحلة الابتدائية' => 'fas fa-school',
                        'المرحلة الاعدادية' => 'fas fa-user-graduate',
                        'المرحلة الإعدادية' => 'fas fa-user-graduate',
                        'المرحلة المتوسطة' => 'fas fa-user-graduate',
                        'المرحلة الثانوية' => 'fas fa-graduation-cap',
                        'التعليم الفني' => 'fas fa-tools',
                        'مرحلة الطفولة المبكرة' => 'fas fa-baby',
                        'غير مصنف' => 'fas fa-question-circle'
                    ];

                    $stage_colors = [
                        'مرحلة ما قبل رياض الأطفال' => '#ff9ff3',
                        'رياض الأطفال' => '#ff6b6b',
                        'مرحلة رياض الأطفال' => '#ff6b6b',
                        'المرحلة الابتدائية' => '#4ecdc4',
                        'المرحلة الاعدادية' => '#45b7d1',
                        'المرحلة الإعدادية' => '#45b7d1',
                        'المرحلة المتوسطة' => '#45b7d1',
                        'المرحلة الثانوية' => '#f9ca24',
                        'التعليم الفني' => '#6c5ce7',
                        'مرحلة الطفولة المبكرة' => '#ff6b6b',
                        'غير مصنف' => '#a0a0a0'
                    ];
                    ?>
                    <?php foreach ($classes_by_stage as $stage_name => $stage_data): ?>
                        <div class="col-lg-2 col-md-4 col-sm-6">
                            <a href="#stage-<?php echo md5($stage_name); ?>" class="quick-nav-item text-decoration-none">
                                <div class="text-center">
                                    <div class="mb-3">
                                        <i class="<?php echo $stage_icons[$stage_name] ?? 'fas fa-school'; ?> fa-2x"
                                           style="color: <?php echo $stage_colors[$stage_name] ?? '#007bff'; ?>"></i>
                                    </div>
                                    <h6 class="mb-1 text-dark"><?php echo htmlspecialchars($stage_name); ?></h6>
                                    <span class="badge rounded-pill"
                                          style="background-color: <?php echo $stage_colors[$stage_name] ?? '#007bff'; ?>">
                                        <?php echo count($stage_data['classes']); ?> فصل
                                    </span>
                                </div>
                            </a>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <?php foreach ($classes_by_stage as $stage_name => $stage_data): ?>
            <div class="stage-group" id="stage-<?php echo md5($stage_name); ?>">
                <!-- عنوان المرحلة -->
                <div class="stage-title">
                    <div class="d-flex align-items-center justify-content-center">
                        <i class="<?php echo $stage_icons[$stage_name] ?? 'fas fa-graduation-cap'; ?> fa-2x me-3"></i>
                        <div>
                            <h4 class="mb-1"><?php echo htmlspecialchars($stage_name); ?></h4>
                            <span class="stage-badge"><?php echo count($stage_data['classes']); ?> فصل دراسي</span>
                        </div>
                    </div>
                </div>

                <!-- فصول المرحلة -->
                <?php if (empty($stage_data['classes'])): ?>
                    <div class="empty-stage">
                        <i class="<?php echo $stage_icons[$stage_name] ?? 'fas fa-school'; ?> fa-4x mb-4"
                           style="color: <?php echo $stage_colors[$stage_name] ?? '#007bff'; ?>; opacity: 0.3;"></i>
                        <h4 class="text-muted mb-3">لا توجد فصول في هذه المرحلة</h4>
                        <p class="text-muted mb-4">يمكنك إضافة فصول جديدة لهذه المرحلة من خلال النقر على الزر أدناه</p>
                        <?php if (check_permission('admin')): ?>
                            <a href="add.php" class="btn btn-primary btn-lg">
                                <i class="fas fa-plus me-2"></i>إضافة فصل جديد
                            </a>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="row g-4">
                    <?php foreach ($stage_data['classes'] as $class): ?>
                        <div class="col-xl-4 col-lg-6 col-md-6">
                            <div class="card class-card h-100 border-0 shadow-sm">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="card-title mb-0 fw-bold text-dark">
                                        <i class="fas fa-door-open me-2" style="color: <?php echo $stage_colors[$stage_name] ?? '#007bff'; ?>"></i>
                                        <?php echo htmlspecialchars($class['class_name']); ?>
                                    </h6>
                            <?php
                            $status_class = '';
                            $status_text = '';
                            switch ($class['status']) {
                                case 'active':
                                    $status_class = 'bg-success';
                                    $status_text = __('active');
                                    break;
                                case 'inactive':
                                    $status_class = 'bg-warning';
                                    $status_text = __('inactive');
                                    break;
                                default:
                                    $status_class = 'bg-secondary';
                                    $status_text = $class['status'];
                            }
                            ?>
                            <span class="badge <?php echo $status_class; ?>">
                                <?php echo $status_text; ?>
                            </span>
                        </div>
                                <div class="card-body">
                                    <!-- معلومات الصف -->
                                    <?php if (!empty($class['grade_name']) || !empty($class['grade_level'])): ?>
                                        <div class="mb-3">
                                            <span class="grade-level-badge">
                                                <i class="fas fa-layer-group me-1"></i>
                                                <?php echo !empty($class['grade_name']) ? htmlspecialchars($class['grade_name']) : htmlspecialchars($class['grade_level']); ?>
                                            </span>
                                        </div>
                                    <?php endif; ?>

                                    <!-- معلومات إضافية -->
                                    <?php if (!empty($class['room_number'])): ?>
                                        <div class="mb-3">
                                            <div class="d-flex align-items-center text-muted">
                                                <i class="fas fa-map-marker-alt me-2"></i>
                                                <small>غرفة رقم <?php echo htmlspecialchars($class['room_number']); ?></small>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <?php if (!empty($class['teacher_name'])): ?>
                                        <div class="mb-3">
                                            <div class="d-flex align-items-center text-muted">
                                                <i class="fas fa-user-tie me-2"></i>
                                                <small><?php echo htmlspecialchars($class['teacher_name']); ?></small>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <!-- إحصائيات الطلاب -->
                                    <div class="student-progress">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="fw-bold text-dark">
                                                <i class="fas fa-users me-1" style="color: <?php echo $stage_colors[$stage_name] ?? '#007bff'; ?>"></i>
                                                الطلاب المسجلون
                                            </span>
                                            <span class="fw-bold fs-5">
                                                <?php echo $class['student_count']; ?> / <?php echo $class['capacity']; ?>
                                            </span>
                                        </div>
                                        <?php
                                        $percentage = $class['capacity'] > 0 ? ($class['student_count'] / $class['capacity']) * 100 : 0;
                                        $progress_class = $percentage >= 90 ? 'bg-danger' : ($percentage >= 70 ? 'bg-warning' : 'bg-success');
                                        ?>
                                        <div class="progress mb-2">
                                            <div class="progress-bar <?php echo $progress_class; ?>"
                                                 style="width: <?php echo min($percentage, 100); ?>%"
                                                 role="progressbar"
                                                 aria-valuenow="<?php echo $percentage; ?>"
                                                 aria-valuemin="0"
                                                 aria-valuemax="100"></div>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <small class="text-muted">
                                                <?php echo number_format($percentage, 1); ?>% ممتلئ
                                            </small>
                                            <small class="text-muted">
                                                <?php echo ($class['capacity'] - $class['student_count']); ?> مقعد متاح
                                            </small>
                                        </div>
                                    </div>
                                </div>

                                <!-- أزرار العمليات -->
                                <div class="card-footer bg-transparent border-0 pt-0">
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                        <a href="view.php?id=<?php echo $class['id']; ?>"
                                           class="btn btn-outline-info btn-sm flex-fill">
                                            <i class="fas fa-eye me-1"></i>عرض التفاصيل
                                        </a>
                                        <?php if (check_permission('admin')): ?>
                                        <a href="edit.php?id=<?php echo $class['id']; ?>"
                                           class="btn btn-outline-primary btn-sm flex-fill">
                                            <i class="fas fa-edit me-1"></i>تعديل
                                        </a>
                                        <button type="button"
                                                class="btn btn-outline-danger btn-sm flex-fill"
                                                onclick="confirmDelete(<?php echo $class['id']; ?>, '<?php echo htmlspecialchars($class['class_name'], ENT_QUOTES); ?>', <?php echo $class['student_count']; ?>)">
                                            <i class="fas fa-trash me-1"></i>حذف
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        <?php endforeach; ?>

        <!-- تم إزالة نظام الترقيم - جميع الفصول تظهر في صفحة واحدة -->
        <div class="mt-4 text-center">
            <small class="text-muted">
                عرض جميع الفصول (<?php echo $total_records; ?> فصل)
            </small>
        </div>

    <?php else: ?>
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-school fa-3x text-muted mb-3"></i>
                <h5 class="text-muted"><?php echo __('no_classes_found'); ?></h5>
                <p class="text-muted"><?php echo __('try_different_search'); ?></p>
                <?php if (check_permission('admin')): ?>
                    <a href="add.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i><?php echo __('add_first_class'); ?>
                    </a>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Hidden form for deletion -->
<form id="deleteForm" method="POST" style="display: none;">
    <input type="hidden" name="class_id" id="deleteClassId">
    <input type="hidden" name="delete_class" value="1">
</form>

<!-- Add Class Modal -->
<!-- تم تعطيل مودال الإضافة -->
<!-- Edit Class Modal -->
<!-- تم تعطيل مودال التعديل -->
<!-- View Class Modal -->
<!-- تم تعطيل مودال العرض -->

<script>
function confirmDelete(classId, className, studentCount) {
    if (studentCount > 0) {
        // إذا كان هناك طلاب في الفصل، عرض تحذير فقط
        Swal.fire({
            title: '<?php echo __('cannot_delete'); ?>',
            html: `
                <div class="text-center mb-3">
                    <i class="fas fa-ban fa-3x text-danger"></i>
                </div>
                <p><?php echo __('cannot_delete_class_with_students'); ?></p>
                <div class="alert alert-warning">
                    <strong><?php echo __('class_name'); ?>:</strong> ${className}
                </div>
                <p class="text-muted small"><?php echo __('students_count'); ?>: ${studentCount}</p>
            `,
            icon: 'error',
            confirmButtonText: '<?php echo __('ok'); ?>',
            customClass: {
                confirmButton: 'btn btn-primary'
            },
            buttonsStyling: false
        });
        return;
    }

    // استخدام SweetAlert للتأكيد
    Swal.fire({
        title: '<?php echo __('confirm_delete'); ?>',
        html: `
            <div class="text-center mb-3">
                <i class="fas fa-trash-alt fa-3x text-danger"></i>
            </div>
            <p><?php echo __('are_you_sure_delete_class'); ?></p>
            <div class="alert alert-warning">
                <strong><?php echo __('class_name'); ?>:</strong> ${className}
            </div>
            <p class="text-muted small"><?php echo __('this_action_cannot_be_undone'); ?></p>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '<i class="fas fa-trash me-2"></i><?php echo __('delete'); ?>',
        cancelButtonText: '<i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>',
        reverseButtons: true,
        customClass: {
            confirmButton: 'btn btn-danger',
            cancelButton: 'btn btn-secondary'
        },
        buttonsStyling: false
    }).then((result) => {
        if (result.isConfirmed) {
            // إظهار رسالة تحميل
            Swal.fire({
                title: '<?php echo __('deleting'); ?>...',
                text: '<?php echo __('please_wait'); ?>',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // إرسال النموذج
            document.getElementById('deleteClassId').value = classId;
            document.getElementById('deleteForm').submit();
        }
    });
}

    // Auto-submit search form on input
    document.getElementById('search').addEventListener('input', function() {
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.form.submit();
        }, 500);
    });

    // منطق فتح مودال التعديل وتعبئة البيانات
    // تم تعطيل مودال التعديل
    // منطق فتح مودال العرض وتعبئة البيانات
    // تم تعطيل مودال العرض
    // منطق فتح مودال الإضافة عند الضغط على زر الإضافة
    // تم تعطيل مودال الإضافة
</script>

<?php require_once '../includes/footer.php'; ?>
