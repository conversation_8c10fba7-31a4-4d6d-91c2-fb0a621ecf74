<?php
/**
 * سكريبت لنقل المستخدمين بدور staff إلى جدول staff
 * Script to migrate users with staff role to staff table
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    die('غير مسموح لك بالوصول لهذه الصفحة');
}

echo "<h2>تحديث وتوحيد بيانات الإداريين</h2>";

// أولاً: التحقق من وجود الحقول المطلوبة في جدول staff
echo "<h3>1. التحقق من هيكل جدول staff:</h3>";

$columns_check = $conn->query("SHOW COLUMNS FROM staff LIKE 'qualification'");
if ($columns_check->num_rows == 0) {
    echo "<p style='color: orange;'>⚠️ حقل 'qualification' غير موجود. يجب إضافته يدوياً.</p>";
    echo "<code>ALTER TABLE staff ADD COLUMN qualification varchar(100) DEFAULT NULL AFTER national_id;</code><br><br>";
} else {
    echo "<p style='color: green;'>✅ حقل 'qualification' موجود</p>";
}

$columns_check = $conn->query("SHOW COLUMNS FROM staff LIKE 'experience_years'");
if ($columns_check->num_rows == 0) {
    echo "<p style='color: orange;'>⚠️ حقل 'experience_years' غير موجود. يجب إضافته يدوياً.</p>";
    echo "<code>ALTER TABLE staff ADD COLUMN experience_years tinyint(3) UNSIGNED DEFAULT 0 AFTER qualification;</code><br><br>";
} else {
    echo "<p style='color: green;'>✅ حقل 'experience_years' موجود</p>";
}

echo "<h3>2. نقل بيانات المستخدمين:</h3>";

try {
    // أولاً: إضافة الحقول المفقودة إذا لم تكن موجودة
    echo "<h3>إضافة الحقول المفقودة:</h3>";

    // إضافة حقل qualification
    $add_qualification = "ALTER TABLE staff ADD COLUMN qualification varchar(100) DEFAULT NULL AFTER national_id";
    if ($conn->query($add_qualification)) {
        echo "<p style='color: green;'>✅ تم إضافة حقل qualification</p>";
    } else {
        if (strpos($conn->error, 'Duplicate column name') !== false) {
            echo "<p style='color: blue;'>ℹ️ حقل qualification موجود بالفعل</p>";
        } else {
            echo "<p style='color: red;'>❌ خطأ في إضافة حقل qualification: " . $conn->error . "</p>";
        }
    }

    // إضافة حقل experience_years
    $add_experience = "ALTER TABLE staff ADD COLUMN experience_years tinyint(3) UNSIGNED DEFAULT 0 AFTER qualification";
    if ($conn->query($add_experience)) {
        echo "<p style='color: green;'>✅ تم إضافة حقل experience_years</p>";
    } else {
        if (strpos($conn->error, 'Duplicate column name') !== false) {
            echo "<p style='color: blue;'>ℹ️ حقل experience_years موجود بالفعل</p>";
        } else {
            echo "<p style='color: red;'>❌ خطأ في إضافة حقل experience_years: " . $conn->error . "</p>";
        }
    }

    // جلب المستخدمين بدور staff الذين ليس لهم سجل في جدول staff
    $query = "
        SELECT u.id, u.full_name, u.username, u.email, u.phone
        FROM users u
        LEFT JOIN staff s ON u.id = s.user_id
        WHERE u.role = 'staff' AND s.id IS NULL
    ";
    
    $result = $conn->query($query);
    
    if ($result->num_rows > 0) {
        echo "<p>تم العثور على " . $result->num_rows . " مستخدم بدور staff بحاجة لإنشاء سجل في جدول staff:</p>";
        echo "<ul>";
        
        $conn->begin_transaction();
        
        while ($user = $result->fetch_assoc()) {
            // إنشاء سجل في جدول staff
            $insert_stmt = $conn->prepare("
                INSERT INTO staff (user_id, status, created_at) 
                VALUES (?, 'active', NOW())
            ");
            $insert_stmt->bind_param("i", $user['id']);
            
            if ($insert_stmt->execute()) {
                echo "<li>✅ تم إنشاء سجل للمستخدم: " . htmlspecialchars($user['full_name']) . " (" . htmlspecialchars($user['username']) . ")</li>";
            } else {
                echo "<li>❌ فشل في إنشاء سجل للمستخدم: " . htmlspecialchars($user['full_name']) . "</li>";
            }
        }
        
        $conn->commit();
        echo "</ul>";
        echo "<p><strong>تم الانتهاء من نقل البيانات بنجاح!</strong></p>";
        
    } else {
        echo "<p>✅ جميع المستخدمين بدور staff لديهم سجلات في جدول staff بالفعل.</p>";
    }
    
    // عرض إحصائيات
    $stats_query = "
        SELECT 
            COUNT(*) as total_staff_users,
            (SELECT COUNT(*) FROM staff) as total_staff_records
        FROM users 
        WHERE role = 'staff'
    ";
    
    $stats_result = $conn->query($stats_query);
    $stats = $stats_result->fetch_assoc();
    
    echo "<h3>الإحصائيات:</h3>";
    echo "<ul>";
    echo "<li>إجمالي المستخدمين بدور staff: " . $stats['total_staff_users'] . "</li>";
    echo "<li>إجمالي السجلات في جدول staff: " . $stats['total_staff_records'] . "</li>";
    echo "</ul>";
    
    if ($stats['total_staff_users'] == $stats['total_staff_records']) {
        echo "<p style='color: green;'><strong>✅ البيانات متطابقة! يمكنك الآن الذهاب إلى <a href='index.php'>صفحة الإداريين</a></strong></p>";
    } else {
        echo "<p style='color: red;'><strong>❌ هناك عدم تطابق في البيانات</strong></p>";
    }
    
} catch (Exception $e) {
    $conn->rollback();
    echo "<p style='color: red;'>حدث خطأ: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    direction: rtl;
}
h2, h3 {
    color: #333;
}
ul {
    background: #f5f5f5;
    padding: 15px;
    border-radius: 5px;
}
li {
    margin: 5px 0;
}
a {
    color: #007bff;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
</style>
