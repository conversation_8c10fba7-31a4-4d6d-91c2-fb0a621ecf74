<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';
/**
 * صفحة إدارة الامتحانات
 * Exams Management Page
 */

// التحقق من الجلسة قبل أي إخراج
check_session();

$user_role = $_SESSION['role'] ?? '';
$user_id = $_SESSION['user_id'] ?? 0;

// التحقق من الصلاحيات
if (!in_array($_SESSION['role'], ['student', 'teacher', 'admin'])) {
    header('Location: ../dashboard/');
    exit();
}
include_once '../includes/header.php';

// معالجة الحذف
if (isset($_POST['delete_exam']) && check_permission('teacher')) {
    $exam_id = intval($_POST['exam_id']);

    // التحقق من ملكية الامتحان للمعلم الحالي
    $user_role = $_SESSION['role'];
    $user_id = $_SESSION['user_id'];

    global $conn;

    // إذا كان المستخدم معلم، تحقق من ملكية الامتحان
    if ($user_role === 'teacher') {
        $teacher_stmt = $conn->prepare("SELECT id FROM teachers WHERE user_id = ?");
        $teacher_stmt->bind_param("i", $user_id);
        $teacher_stmt->execute();
        $teacher_result = $teacher_stmt->get_result();
        $teacher_data = $teacher_result->fetch_assoc();
        $teacher_id = $teacher_data['id'] ?? 0;

        $check_stmt = $conn->prepare("SELECT id FROM exams WHERE id = ? AND teacher_id = ?");
        $check_stmt->bind_param("ii", $exam_id, $teacher_id);
        $check_stmt->execute();

        if ($check_stmt->get_result()->num_rows === 0) {
            $_SESSION['error_message'] = __('access_denied');
            header('Location: index.php');
            exit();
        }
    }

    $conn->begin_transaction();

    try {
        // حذف محاولات الامتحان
        $stmt = $conn->prepare("DELETE FROM exam_attempts WHERE exam_id = ?");
        $stmt->bind_param("i", $exam_id);
        $stmt->execute();

        // حذف أسئلة الامتحان
        $stmt = $conn->prepare("DELETE FROM exam_questions WHERE exam_id = ?");
        $stmt->bind_param("i", $exam_id);
        $stmt->execute();

        // حذف الامتحان
        $stmt = $conn->prepare("DELETE FROM exams WHERE id = ?");
        $stmt->bind_param("i", $exam_id);
        $stmt->execute();

        $conn->commit();

        // تسجيل النشاط
        log_activity($_SESSION['user_id'], 'delete_exam', 'exams', $exam_id);

        $_SESSION['success_message'] = __('deleted_successfully');
    } catch (Exception $e) {
        $conn->rollback();
        $_SESSION['error_message'] = __('error_occurred');
        log_error("Error deleting exam: " . $e->getMessage());
    }

    header('Location: index.php');
    exit();
}

// معالجة البحث والفلترة
$search = clean_input($_GET['search'] ?? '');
$subject_filter = clean_input($_GET['subject_id'] ?? '');
$class_filter = clean_input($_GET['class_id'] ?? '');
$status_filter = clean_input($_GET['status'] ?? '');
$type_filter = clean_input($_GET['exam_type'] ?? '');

// بناء استعلام البحث
$where_conditions = ["1=1"];
$params = [];
$types = "";

// إذا كان المستخدم معلم، عرض امتحاناته فقط
$user_role = $_SESSION['role'];
$user_id = $_SESSION['user_id'];

if ($user_role === 'teacher') {
    $teacher_stmt = $conn->prepare("SELECT id FROM teachers WHERE user_id = ?");
    $teacher_stmt->bind_param("i", $user_id);
    $teacher_stmt->execute();
    $teacher_result = $teacher_stmt->get_result();
    $teacher_data = $teacher_result->fetch_assoc();
    $teacher_id = $teacher_data['id'] ?? 0;

    $where_conditions[] = "e.teacher_id = ?";
    $params[] = $teacher_id;
    $types .= "i";
}

if (!empty($search)) {
    $where_conditions[] = "(e.exam_title LIKE ? OR e.exam_description LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param]);
    $types .= "ss";
}

if (!empty($subject_filter)) {
    $where_conditions[] = "e.subject_id = ?";
    $params[] = $subject_filter;
    $types .= "i";
}

if (!empty($class_filter)) {
    $where_conditions[] = "e.class_id = ?";
    $params[] = $class_filter;
    $types .= "i";
}

if (!empty($status_filter)) {
    $where_conditions[] = "e.status = ?";
    $params[] = $status_filter;
    $types .= "s";
}

if (!empty($type_filter)) {
    $where_conditions[] = "e.exam_type = ?";
    $params[] = $type_filter;
    $types .= "s";
}

// تعديل منطق جلب الامتحانات حسب الدور
if ($user_role === 'student') {
    // جلب معرف الطالب
    $student_stmt = $conn->prepare("SELECT id FROM students WHERE user_id = ?");
    $student_stmt->bind_param("i", $user_id);
    $student_stmt->execute();
    $student_data = $student_stmt->get_result()->fetch_assoc();
    $student_id = $student_data['id'] ?? 0;
    // جلب فقط الامتحانات الخاصة بفصل الطالب
    $class_stmt = $conn->prepare("SELECT class_id FROM students WHERE id = ?");
    $class_stmt->bind_param("i", $student_id);
    $class_stmt->execute();
    $class_data = $class_stmt->get_result()->fetch_assoc();
    $class_id = $class_data['class_id'] ?? 0;
    $where_conditions[] = "e.class_id = ?";
    $params[] = $class_id;
    $types .= "i";
}

$where_clause = implode(" AND ", $where_conditions);

// الحصول على عدد الصفحات
$count_query = "
    SELECT COUNT(*) as total
    FROM exams e
    LEFT JOIN subjects s ON e.subject_id = s.id
    LEFT JOIN classes c ON e.class_id = c.id
    LEFT JOIN teachers t ON e.teacher_id = t.id
    LEFT JOIN users u ON t.user_id = u.id
    WHERE $where_clause
";

$count_stmt = $conn->prepare($count_query);
if (!empty($params)) {
    $count_stmt->bind_param($types, ...$params);
}
$count_stmt->execute();
$total_records = $count_stmt->get_result()->fetch_assoc()['total'];

// إعدادات الترقيم
$page = intval($_GET['page'] ?? 1);
$records_per_page = ITEMS_PER_PAGE;
$total_pages = ceil($total_records / $records_per_page);
$offset = ($page - 1) * $records_per_page;

// جلب الامتحانات
$query = "
    SELECT
        e.*,
        s.subject_name,
        c.class_name,
        c.grade_level,
        u.full_name as teacher_name,
        COUNT(DISTINCT eq.id) as question_count,
        COUNT(DISTINCT ea.id) as attempt_count
    FROM exams e
    LEFT JOIN subjects s ON e.subject_id = s.id
    LEFT JOIN classes c ON e.class_id = c.id
    LEFT JOIN teachers t ON e.teacher_id = t.id
    LEFT JOIN users u ON t.user_id = u.id
    LEFT JOIN exam_questions eq ON e.id = eq.exam_id
    LEFT JOIN exam_attempts ea ON e.id = ea.exam_id
    WHERE $where_clause
    GROUP BY e.id
    ORDER BY e.created_at DESC
    LIMIT ? OFFSET ?
";

$stmt = $conn->prepare($query);
if (!$stmt) {
    die("SQL Error: " . $conn->error . "<br>Query: " . htmlspecialchars($query));
}
$params[] = $records_per_page;
$params[] = $offset;
$types .= "ii";

if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$exams = $stmt->get_result();

// جلب قوائم الفلترة
$subjects_query = "SELECT id, subject_name FROM subjects WHERE status = 'active' ORDER BY subject_name";
if ($user_role === 'teacher') {
    $subjects_query = "
        SELECT DISTINCT s.id, s.subject_name
        FROM subjects s
        JOIN teacher_assignments ta ON s.id = ta.subject_id
        WHERE ta.teacher_id = ? AND ta.status = 'active'
        ORDER BY s.subject_name
    ";
    $subjects_stmt = $conn->prepare($subjects_query);
    $subjects_stmt->bind_param("i", $teacher_id);
    $subjects_stmt->execute();
    $subjects = $subjects_stmt->get_result();
} else {
    $subjects = $conn->query($subjects_query);
}

$classes_query = "SELECT id, class_name, grade_level FROM classes WHERE status = 'active' ORDER BY grade_level, class_name";
if ($user_role === 'teacher') {
    $classes_query = "
        SELECT DISTINCT c.id, c.class_name, c.grade_level
        FROM classes c
        JOIN teacher_assignments ta ON c.id = ta.class_id
        WHERE ta.teacher_id = ? AND ta.status = 'active'
        ORDER BY c.grade_level, c.class_name
    ";
    $classes_stmt = $conn->prepare($classes_query);
    $classes_stmt->bind_param("i", $teacher_id);
    $classes_stmt->execute();
    $classes = $classes_stmt->get_result();
} else {
    $classes = $conn->query($classes_query);
}

// إحصائيات سريعة
$stats_where = $user_role === 'teacher' ? "WHERE teacher_id = $teacher_id" : "";
$stats_query = "
    SELECT
        COUNT(*) as total_exams,
        SUM(CASE WHEN status = 'published' THEN 1 ELSE 0 END) as published_exams,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_exams,
        SUM(CASE WHEN status = 'draft' THEN 1 ELSE 0 END) as draft_exams
    FROM exams e
    $stats_where
";
$stats_result = $conn->query($stats_query);
$stats = $stats_result->fetch_assoc();

// التأكد من أن القيم ليست null
$stats = [
    'total_exams' => $stats['total_exams'] ?? 0,
    'published_exams' => $stats['published_exams'] ?? 0,
    'completed_exams' => $stats['completed_exams'] ?? 0,
    'draft_exams' => $stats['draft_exams'] ?? 0
];
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('exams'); ?></h1>
            <p class="text-muted"><?php echo __('manage_exams_info'); ?></p>
        </div>
        <?php if (check_permission('teacher')): ?>
        <div>
            <a href="add.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i><?php echo __('create_exam'); ?>
            </a>
            <a href="question_bank.php" class="btn btn-success">
                <i class="fas fa-question-circle me-2"></i><?php echo __('question_bank'); ?>
            </a>
        </div>
        <?php endif; ?>
    </div>

    <!-- رسائل التنبيه -->
    <?php if (isset($_GET['success']) && $_GET['success'] == 1): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo __('exam_added_successfully'); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_GET['deleted']) && $_GET['deleted'] == 1): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>تم حذف الامتحان بنجاح
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-gradient p-3 rounded-3">
                                <i class="fas fa-file-alt text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($stats['total_exams']); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('total_exams'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-gradient p-3 rounded-3">
                                <i class="fas fa-check-circle text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            
                            <p class="text-muted mb-0"><?php echo __('published_exams'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-gradient p-3 rounded-3">
                                <i class="fas fa-play-circle text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <p class="text-muted mb-0"><?php echo __('active_exams'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-gradient p-3 rounded-3">
                                <i class="fas fa-edit text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <p class="text-muted mb-0"><?php echo __('draft_exams'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label"><?php echo __('search'); ?></label>
                    <input type="text"
                           class="form-control"
                           id="search"
                           name="search"
                           value="<?php echo htmlspecialchars($search); ?>"
                           placeholder="<?php echo __('search_by_title'); ?>">
                </div>

                <div class="col-md-2">
                    <label for="subject_id" class="form-label"><?php echo __('subject'); ?></label>
                    <select class="form-select" id="subject_id" name="subject_id">
                        <option value=""><?php echo __('all_subjects'); ?></option>
                        <?php while ($subject = $subjects->fetch_assoc()): ?>
                            <option value="<?php echo $subject['id']; ?>"
                                    <?php echo ($subject_filter == $subject['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($subject['subject_name']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="class_id" class="form-label"><?php echo __('class'); ?></label>
                    <select class="form-select" id="class_id" name="class_id">
                        <option value=""><?php echo __('all_classes'); ?></option>
                        <?php while ($class = $classes->fetch_assoc()): ?>
                            <option value="<?php echo $class['id']; ?>"
                                    <?php echo ($class_filter == $class['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($class['class_name'] . ' - ' . $class['grade_level']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="exam_type" class="form-label"><?php echo __('exam_type'); ?></label>
                    <select class="form-select" id="exam_type" name="exam_type">
                        <option value=""><?php echo __('all_types'); ?></option>
                        <option value="quiz" <?php echo ($type_filter == 'quiz') ? 'selected' : ''; ?>>
                            <?php echo __('quiz'); ?>
                        </option>
                        <option value="midterm" <?php echo ($type_filter == 'midterm') ? 'selected' : ''; ?>>
                            <?php echo __('midterm'); ?>
                        </option>
                        <option value="final" <?php echo ($type_filter == 'final') ? 'selected' : ''; ?>>
                            <?php echo __('final'); ?>
                        </option>
                        <option value="assignment" <?php echo ($type_filter == 'assignment') ? 'selected' : ''; ?>>
                            <?php echo __('assignment'); ?>
                        </option>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="status" class="form-label"><?php echo __('status'); ?></label>
                    <select class="form-select" id="status" name="status">
                        <option value=""><?php echo __('all_statuses'); ?></option>
                        <option value="draft" <?php echo ($status_filter == 'draft') ? 'selected' : ''; ?>>
                            <?php echo __('draft'); ?>
                        </option>
                        <option value="published" <?php echo ($status_filter == 'published') ? 'selected' : ''; ?>>
                            <?php echo __('published'); ?>
                        </option>
                        <option value="active" <?php echo ($status_filter == 'active') ? 'selected' : ''; ?>>
                            <?php echo __('active'); ?>
                        </option>
                        <option value="completed" <?php echo ($status_filter == 'completed') ? 'selected' : ''; ?>>
                            <?php echo __('completed'); ?>
                        </option>
                    </select>
                </div>

                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Exams List -->
    <?php if ($exams->num_rows > 0): ?>
        <div class="row">
            <?php while ($exam = $exams->fetch_assoc()): ?>
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="card-title mb-0">
                                <?php echo htmlspecialchars($exam['exam_title']); ?>
                            </h6>
                            <?php
                            $status_class = '';
                            $status_text = '';
                            switch ($exam['status']) {
                                case 'draft':
                                    $status_class = 'bg-secondary';
                                    $status_text = __('draft');
                                    break;
                                case 'published':
                                    $status_class = 'bg-info';
                                    $status_text = __('published');
                                    break;
                                case 'active':
                                    $status_class = 'bg-success';
                                    $status_text = __('active');
                                    break;
                                case 'completed':
                                    $status_class = 'bg-warning';
                                    $status_text = __('completed');
                                    break;
                                default:
                                    $status_class = 'bg-secondary';
                                    $status_text = $exam['status'];
                            }
                            ?>
                            <span class="badge <?php echo $status_class; ?>">
                                <?php echo $status_text; ?>
                            </span>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-6">
                                    <small class="text-muted"><?php echo __('exam_type'); ?></small>
                                    <div class="fw-bold">
                                        <?php
                                        switch ($exam['exam_type']) {
                                            case 'quiz':
                                                echo '<i class="fas fa-question-circle me-1"></i>' . __('quiz');
                                                break;
                                            case 'midterm':
                                                echo '<i class="fas fa-file-alt me-1"></i>' . __('midterm');
                                                break;
                                            case 'final':
                                                echo '<i class="fas fa-graduation-cap me-1"></i>' . __('final');
                                                break;
                                            case 'assignment':
                                                echo '<i class="fas fa-tasks me-1"></i>' . __('assignment');
                                                break;
                                            default:
                                                echo $exam['exam_type'];
                                        }
                                        ?>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted"><?php echo __('total_marks'); ?></small>
                                    <div class="fw-bold"><?php echo $exam['total_marks']; ?></div>
                                </div>
                            </div>

                            <?php if (!empty($exam['subject_name'])): ?>
                                <div class="mb-2">
                                    <small class="text-muted"><?php echo __('subject'); ?></small>
                                    <div>
                                        <span class="badge bg-info">
                                            <?php echo htmlspecialchars($exam['subject_name']); ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($exam['class_name'])): ?>
                                <div class="mb-2">
                                    <small class="text-muted"><?php echo __('class'); ?></small>
                                    <div>
                                        <span class="badge bg-secondary">
                                            <?php echo htmlspecialchars($exam['class_name'] . ' - ' . $exam['grade_level']); ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if ($user_role === 'admin' && !empty($exam['teacher_name'])): ?>
                                <div class="mb-2">
                                    <small class="text-muted"><?php echo __('teacher'); ?></small>
                                    <div>
                                        <i class="fas fa-user me-1"></i>
                                        <?php echo htmlspecialchars($exam['teacher_name']); ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <div class="row mb-2">
                                <div class="col-6">
                                    <small class="text-muted"><?php echo __('questions'); ?></small>
                                    <div>
                                        <i class="fas fa-question me-1"></i>
                                        <?php echo $exam['question_count']; ?>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted"><?php echo __('attempts'); ?></small>
                                    <div>
                                        <i class="fas fa-users me-1"></i>
                                        <?php echo $exam['attempt_count']; ?>
                                    </div>
                                </div>
                            </div>

                            <?php if ($exam['duration']): ?>
                                <div class="mb-2">
                                    <small class="text-muted"><?php echo __('duration'); ?></small>
                                    <div>
                                        <i class="fas fa-clock me-1"></i>
                                        <?php echo $exam['duration']; ?> <?php echo __('minutes'); ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if ($exam['start_time'] && $exam['end_time']): ?>
                                <div class="mb-2">
                                    <small class="text-muted"><?php echo __('schedule'); ?></small>
                                    <div class="small">
                                        <i class="fas fa-calendar me-1"></i>
                                        <?php echo format_datetime($exam['start_time'], 'Y/m/d H:i'); ?>
                                        <br>
                                        <i class="fas fa-arrow-right me-1"></i>
                                        <?php echo format_datetime($exam['end_time'], 'Y/m/d H:i'); ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($exam['exam_description'])): ?>
                                <div class="mb-2">
                                    <small class="text-muted"><?php echo __('description'); ?></small>
                                    <div class="small">
                                        <?php echo nl2br(htmlspecialchars(truncate_text($exam['exam_description'], 100))); ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="card-footer bg-transparent">
                            <div class="btn-group w-100">
                                <a href="view.php?id=<?php echo $exam['id']; ?>"
                                   class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-eye me-1"></i><?php echo __('view'); ?>
                                </a>

                                <?php if (check_permission('teacher') && ($user_role === 'admin' || $exam['teacher_id'] == ($teacher_id ?? 0))): ?>
                                <a href="edit.php?id=<?php echo $exam['id']; ?>"
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-edit me-1"></i><?php echo __('edit'); ?>
                                </a>

                                <a href="questions.php?exam_id=<?php echo $exam['id']; ?>"
                                   class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-question me-1"></i><?php echo __('questions'); ?>
                                </a>

                                <a href="delete_exam.php?id=<?php echo $exam['id']; ?>"
                                   class="btn btn-outline-danger btn-sm">
                                    <i class="fas fa-trash me-1"></i><?php echo __('delete'); ?>
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endwhile; ?>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <nav aria-label="Exams pagination" class="mt-4">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo ($page - 1); ?>&search=<?php echo urlencode($search); ?>&subject_id=<?php echo urlencode($subject_filter); ?>&class_id=<?php echo urlencode($class_filter); ?>&status=<?php echo urlencode($status_filter); ?>&exam_type=<?php echo urlencode($type_filter); ?>">
                                <?php echo __('previous'); ?>
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php
                    $start = max(1, $page - 2);
                    $end = min($total_pages, $page + 2);

                    for ($i = $start; $i <= $end; $i++):
                    ?>
                        <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&subject_id=<?php echo urlencode($subject_filter); ?>&class_id=<?php echo urlencode($class_filter); ?>&status=<?php echo urlencode($status_filter); ?>&exam_type=<?php echo urlencode($type_filter); ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                    <?php endfor; ?>

                    <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo ($page + 1); ?>&search=<?php echo urlencode($search); ?>&subject_id=<?php echo urlencode($subject_filter); ?>&class_id=<?php echo urlencode($class_filter); ?>&status=<?php echo urlencode($status_filter); ?>&exam_type=<?php echo urlencode($type_filter); ?>">
                                <?php echo __('next'); ?>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        <?php endif; ?>

    <?php else: ?>
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted"><?php echo __('no_exams_found'); ?></h5>
                <p class="text-muted"><?php echo __('try_different_search'); ?></p>
                <?php if (check_permission('teacher')): ?>
                    <a href="add.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i><?php echo __('create_first_exam'); ?>
                    </a>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
</div>



<script>


    // Auto-submit search form on input
    document.getElementById('search').addEventListener('input', function() {
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.form.submit();
        }, 500);
    });
</script>

<?php include_once '../includes/footer.php'; ?>