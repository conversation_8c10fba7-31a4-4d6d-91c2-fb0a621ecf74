<?php
/**
 * تعديل فئة مصروفات
 * Edit Expense Category
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    header('Location: ../../dashboard/');
    exit();
}

$success_message = '';
$error_message = '';
$category_id = intval($_GET['id'] ?? 0);

// التحقق من وجود الفئة
if ($category_id <= 0) {
    header('Location: categories.php');
    exit();
}

// جلب بيانات الفئة
$category_query = $conn->prepare("SELECT * FROM expense_categories WHERE id = ?");
$category_query->bind_param("i", $category_id);
$category_query->execute();
$category = $category_query->get_result()->fetch_assoc();

if (!$category) {
    header('Location: categories.php');
    exit();
}

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $category_name = clean_input($_POST['category_name'] ?? '');
    $description = clean_input($_POST['description'] ?? '');
    $icon = clean_input($_POST['icon'] ?? 'fas fa-money-bill');
    $color = clean_input($_POST['color'] ?? '#007bff');
    $daily_limit = floatval($_POST['daily_limit'] ?? 0);
    $monthly_limit = floatval($_POST['monthly_limit'] ?? 0);
    
    if (empty($category_name)) {
        $error_message = 'يرجى إدخال اسم الفئة';
    } else {
        $update_stmt = $conn->prepare("
            UPDATE expense_categories SET 
                category_name = ?, description = ?, icon = ?, color = ?, 
                daily_limit = ?, monthly_limit = ?, updated_at = NOW()
            WHERE id = ?
        ");
        $update_stmt->bind_param("ssssddi", 
            $category_name, $description, $icon, $color, 
            $daily_limit, $monthly_limit, $category_id
        );
        
        if ($update_stmt->execute()) {
            $success_message = 'تم تحديث الفئة بنجاح';
            // تحديث البيانات المحلية
            $category['category_name'] = $category_name;
            $category['description'] = $description;
            $category['icon'] = $icon;
            $category['color'] = $color;
            $category['daily_limit'] = $daily_limit;
            $category['monthly_limit'] = $monthly_limit;
        } else {
            $error_message = 'فشل في تحديث الفئة';
        }
    }
}

$page_title = 'تعديل الفئة: ' . $category['category_name'];
include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-edit me-2"></i>تعديل الفئة: <?php echo htmlspecialchars($category['category_name']); ?>
            </h1>
            <p class="text-muted mb-0">تحديث بيانات فئة المصروفات</p>
        </div>
        <div>
            <a href="categories.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة للفئات
            </a>
        </div>
    </div>

    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-8">
            <!-- نموذج تعديل الفئة -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-edit me-2"></i>بيانات الفئة
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">اسم الفئة <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="category_name" 
                                   value="<?php echo htmlspecialchars($category['category_name']); ?>" 
                                   required placeholder="مثال: مصروفات إدارية">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">الوصف</label>
                            <textarea class="form-control" name="description" rows="3" 
                                      placeholder="وصف مختصر للفئة..."><?php echo htmlspecialchars($category['description'] ?? ''); ?></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الأيقونة</label>
                                <select class="form-select" name="icon">
                                    <option value="fas fa-money-bill" <?php echo ($category['icon'] == 'fas fa-money-bill') ? 'selected' : ''; ?>>💵 نقود</option>
                                    <option value="fas fa-tools" <?php echo ($category['icon'] == 'fas fa-tools') ? 'selected' : ''; ?>>🔧 أدوات</option>
                                    <option value="fas fa-lightbulb" <?php echo ($category['icon'] == 'fas fa-lightbulb') ? 'selected' : ''; ?>>💡 كهرباء</option>
                                    <option value="fas fa-broom" <?php echo ($category['icon'] == 'fas fa-broom') ? 'selected' : ''; ?>>🧹 نظافة</option>
                                    <option value="fas fa-graduation-cap" <?php echo ($category['icon'] == 'fas fa-graduation-cap') ? 'selected' : ''; ?>>🎓 تعليمية</option>
                                    <option value="fas fa-clipboard-list" <?php echo ($category['icon'] == 'fas fa-clipboard-list') ? 'selected' : ''; ?>>📋 إدارية</option>
                                    <option value="fas fa-bus" <?php echo ($category['icon'] == 'fas fa-bus') ? 'selected' : ''; ?>>🚌 نقل</option>
                                    <option value="fas fa-coffee" <?php echo ($category['icon'] == 'fas fa-coffee') ? 'selected' : ''; ?>>☕ ضيافة</option>
                                    <option value="fas fa-heartbeat" <?php echo ($category['icon'] == 'fas fa-heartbeat') ? 'selected' : ''; ?>>❤️ طبية</option>
                                    <option value="fas fa-shield-alt" <?php echo ($category['icon'] == 'fas fa-shield-alt') ? 'selected' : ''; ?>>🛡️ أمن</option>
                                    <option value="fas fa-wrench" <?php echo ($category['icon'] == 'fas fa-wrench') ? 'selected' : ''; ?>>🔧 صيانة</option>
                                    <option value="fas fa-car" <?php echo ($category['icon'] == 'fas fa-car') ? 'selected' : ''; ?>>🚗 مواصلات</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اللون</label>
                                <input type="color" class="form-control form-control-color" name="color" 
                                       value="<?php echo $category['color']; ?>">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الحد اليومي (<?php echo get_currency_symbol(); ?>)</label>
                                <input type="number" class="form-control" name="daily_limit" step="0.01" min="0"
                                       value="<?php echo $category['daily_limit'] ?: ''; ?>" 
                                       placeholder="0.00">
                                <div class="form-text">اتركه فارغاً إذا لم تريد حد أقصى</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الحد الشهري (<?php echo get_currency_symbol(); ?>)</label>
                                <input type="number" class="form-control" name="monthly_limit" step="0.01" min="0"
                                       value="<?php echo $category['monthly_limit'] ?: ''; ?>" 
                                       placeholder="0.00">
                                <div class="form-text">اتركه فارغاً إذا لم تريد حد أقصى</div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="categories.php" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ التعديلات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- معلومات الفئة الحالية -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>معلومات الفئة الحالية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="card border">
                        <div class="card-header d-flex align-items-center" style="background-color: <?php echo $category['color']; ?>; color: white;">
                            <i class="<?php echo $category['icon']; ?> me-2"></i>
                            <h6 class="mb-0"><?php echo htmlspecialchars($category['category_name']); ?></h6>
                        </div>
                        <div class="card-body">
                            <p class="card-text text-muted small">
                                <?php echo htmlspecialchars($category['description'] ?: 'لا يوجد وصف'); ?>
                            </p>
                            
                            <?php if ($category['daily_limit'] || $category['monthly_limit']): ?>
                                <div class="alert alert-info py-2">
                                    <small>
                                        <?php if ($category['daily_limit']): ?>
                                            <i class="fas fa-calendar-day me-1"></i>يومي: <?php echo format_currency($category['daily_limit']); ?>
                                        <?php endif; ?>
                                        <?php if ($category['monthly_limit']): ?>
                                            <br><i class="fas fa-calendar-alt me-1"></i>شهري: <?php echo format_currency($category['monthly_limit']); ?>
                                        <?php endif; ?>
                                    </small>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات الفئة -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>إحصائيات الفئة
                    </h6>
                </div>
                <div class="card-body">
                    <?php
                    $stats_query = $conn->prepare("
                        SELECT 
                            COUNT(*) as expenses_count,
                            COALESCE(SUM(amount), 0) as total_amount,
                            COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_count,
                            SUM(CASE WHEN status = 'approved' THEN amount ELSE 0 END) as approved_amount
                        FROM daily_expenses 
                        WHERE category_id = ?
                    ");
                    $stats_query->bind_param("i", $category_id);
                    $stats_query->execute();
                    $stats = $stats_query->get_result()->fetch_assoc();
                    ?>
                    
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <h5 class="text-primary mb-0"><?php echo $stats['expenses_count']; ?></h5>
                            <small class="text-muted">إجمالي المصروفات</small>
                        </div>
                        <div class="col-6 mb-3">
                            <h5 class="text-success mb-0"><?php echo format_currency($stats['total_amount']); ?></h5>
                            <small class="text-muted">إجمالي المبلغ</small>
                        </div>
                        <div class="col-6">
                            <h5 class="text-info mb-0"><?php echo $stats['approved_count']; ?></h5>
                            <small class="text-muted">المعتمدة</small>
                        </div>
                        <div class="col-6">
                            <h5 class="text-warning mb-0"><?php echo format_currency($stats['approved_amount']); ?></h5>
                            <small class="text-muted">المبلغ المعتمد</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- روابط سريعة -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-link me-2"></i>روابط سريعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="index.php?category=<?php echo $category_id; ?>" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-eye me-2"></i>عرض مصروفات هذه الفئة
                        </a>
                        <a href="add.php?category=<?php echo $category_id; ?>" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-plus me-2"></i>إضافة مصروف لهذه الفئة
                        </a>
                        <?php if ($stats['expenses_count'] == 0): ?>
                            <a href="delete_category.php?id=<?php echo $category_id; ?>" 
                               class="btn btn-outline-danger btn-sm"
                               onclick="return confirm('هل أنت متأكد من حذف هذه الفئة؟')">
                                <i class="fas fa-trash me-2"></i>حذف الفئة
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once '../../includes/footer.php'; ?>

<style>
.card {
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.alert {
    border: none;
    border-radius: 10px;
}
</style>
