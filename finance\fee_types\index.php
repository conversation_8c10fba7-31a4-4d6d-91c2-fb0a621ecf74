<?php
/**
 * إدارة أنواع الرسوم
 * Fee Types Management
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    header('Location: ../../dashboard/');
    exit();
}

$success_message = '';
$error_message = '';

// معالجة حذف نوع الرسوم
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $fee_type_id = intval($_GET['delete']);

    // التحقق من عدم استخدام نوع الرسوم
    $usage_check = $conn->prepare("
        SELECT
            (SELECT COUNT(*) FROM student_fees WHERE fee_type_id = ?) as fees_count,
            (SELECT COUNT(*) FROM student_payments sp
             JOIN student_fees sf ON sp.student_fee_id = sf.id
             WHERE sf.fee_type_id = ?) as payments_count,
            (SELECT COUNT(*) FROM student_installments si
             JOIN student_fees sf ON si.student_fee_id = sf.id
             WHERE sf.fee_type_id = ?) as installments_count
    ");
    $usage_check->bind_param("iii", $fee_type_id, $fee_type_id, $fee_type_id);
    $usage_check->execute();
    $usage_result = $usage_check->get_result()->fetch_assoc();

    $total_usage = $usage_result['fees_count'] + $usage_result['payments_count'] + $usage_result['installments_count'];

    if ($total_usage > 0) {
        $error_message = "لا يمكن حذف هذا النوع لأنه مستخدم في " . $total_usage . " سجل " .
                        "(رسوم: " . $usage_result['fees_count'] .
                        " | مدفوعات: " . $usage_result['payments_count'] .
                        " | أقساط: " . $usage_result['installments_count'] . ")";
    } else {
        $delete_stmt = $conn->prepare("DELETE FROM fee_types WHERE id = ?");
        $delete_stmt->bind_param("i", $fee_type_id);

        if ($delete_stmt->execute()) {
            $success_message = "تم حذف نوع الرسوم بنجاح";
        } else {
            $error_message = "خطأ في حذف نوع الرسوم: " . $conn->error;
        }
    }
}



// معالجة تغيير حالة نوع الرسوم
if (isset($_GET['toggle_status']) && is_numeric($_GET['toggle_status'])) {
    $fee_type_id = intval($_GET['toggle_status']);
    
    $toggle_stmt = $conn->prepare("UPDATE fee_types SET status = IF(status = 'active', 'inactive', 'active') WHERE id = ?");
    $toggle_stmt->bind_param("i", $fee_type_id);
    
    if ($toggle_stmt->execute()) {
        $success_message = "تم تغيير حالة نوع الرسوم بنجاح";
    } else {
        $error_message = "خطأ في تغيير حالة نوع الرسوم: " . $conn->error;
    }
}

// جلب أنواع الرسوم مع إحصائيات الاستخدام الصحيحة
$fee_types_query = "
    SELECT
        ft.*,
        (SELECT COUNT(*) FROM student_fees sf WHERE sf.fee_type_id = ft.id) as fees_count,
        (SELECT COUNT(*) FROM student_payments sp
         JOIN student_fees sf ON sp.student_fee_id = sf.id
         WHERE sf.fee_type_id = ft.id) as payments_count,
        (SELECT COUNT(*) FROM student_installments si
         JOIN student_fees sf ON si.student_fee_id = sf.id
         WHERE sf.fee_type_id = ft.id) as installments_count,
        (SELECT SUM(sf.final_amount) FROM student_fees sf WHERE sf.fee_type_id = ft.id) as total_amount,
        (SELECT SUM(sp.amount) FROM student_payments sp
         JOIN student_fees sf ON sp.student_fee_id = sf.id
         WHERE sf.fee_type_id = ft.id AND sp.status = 'confirmed') as total_paid
    FROM fee_types ft
    ORDER BY ft.type_name
";

$fee_types = $conn->query($fee_types_query);

// حساب الإحصائيات العامة
$total_fee_types = 0;
$total_active_types = 0;
$total_fees_amount = 0;
$total_paid_amount = 0;
$total_usage_count = 0;
$test_data_types = 0;

if ($fee_types && $fee_types->num_rows > 0) {
    $fee_types->data_seek(0);
    while ($row = $fee_types->fetch_assoc()) {
        $total_fee_types++;
        if ($row['status'] == 'active') $total_active_types++;
        $total_fees_amount += $row['total_amount'] ?? 0;
        $total_paid_amount += $row['total_paid'] ?? 0;
        $total_usage_count += ($row['fees_count'] + $row['payments_count'] + $row['installments_count']);

        // تحديد أنواع الرسوم التي تحتوي على بيانات تجريبية
        if (in_array($row['id'], [3, 4, 5]) && ($row['fees_count'] > 0)) {
            $test_data_types++;
        }
    }
    $fee_types->data_seek(0); // إعادة تعيين المؤشر
}

$page_title = 'إدارة أنواع الرسوم';
include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-tags me-2"></i>إدارة أنواع الرسوم
            </h1>
            <p class="text-muted mb-0">إنشاء وإدارة أنواع الرسوم المختلفة (رسوم دراسية، كتب، أنشطة، إلخ)</p>
        </div>
        <div>
            <a href="add.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة نوع رسوم جديد
            </a>
            <a href="class_mapping.php" class="btn btn-success">
                <i class="fas fa-link me-2"></i>ربط الرسوم بالصفوف
            </a>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-info dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-tools me-2"></i>أدوات إضافية
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="help.php">
                        <i class="fas fa-question-circle me-2"></i>دليل الاستخدام
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item text-warning" href="clear_test_data.php">
                        <i class="fas fa-trash-alt me-2"></i>حذف البيانات التجريبية
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="../index.php">
                        <i class="fas fa-home me-2"></i>النظام المالي الرئيسي
                    </a></li>
                    <li><a class="dropdown-item" href="../../dashboard/">
                        <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                    </a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- رسائل التنبيه -->
    <?php if (!empty($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- تحذير البيانات التجريبية -->
    <?php
    $test_data_check = $conn->query("SELECT COUNT(*) as count FROM student_fees WHERE fee_type_id IN (3, 4, 5)");
    $test_data_count = $test_data_check->fetch_assoc()['count'];
    if ($test_data_count > 0):
    ?>
    <div class="alert alert-warning border-0 shadow-sm mb-4">
        <div class="d-flex align-items-center justify-content-between">
            <div class="d-flex align-items-center">
                <div class="me-3">
                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                </div>
                <div>
                    <h6 class="mb-1">تم العثور على بيانات تجريبية</h6>
                    <p class="mb-0 small">
                        يوجد <?php echo $test_data_count; ?> سجل من البيانات التجريبية في النظام.
                        هذه البيانات تمنع حذف بعض أنواع الرسوم. يمكنك حذفها من قائمة "أدوات إضافية".
                    </p>
                </div>
            </div>
            <a href="clear_test_data.php" class="btn btn-outline-warning btn-sm">
                <i class="fas fa-trash-alt me-1"></i>حذف البيانات التجريبية
            </a>
        </div>
    </div>
    <?php endif; ?>

    <!-- معلومات مساعدة -->
    <div class="alert alert-info border-0 shadow-sm mb-4">
        <div class="d-flex align-items-center">
            <div class="me-3">
                <i class="fas fa-info-circle fa-2x"></i>
            </div>
            <div>
                <h6 class="mb-1">ما هي أنواع الرسوم؟</h6>
                <p class="mb-0 small">
                    أنواع الرسوم هي التصنيفات المختلفة للرسوم في النظام مثل: الرسوم الدراسية، رسوم الكتب، رسوم الأنشطة، رسوم النقل، إلخ.
                    يمكنك إنشاء أنواع مختلفة وربطها بالصفوف الدراسية بمبالغ مختلفة.
                </p>
            </div>
        </div>
    </div>

    <!-- إحصائيات عامة -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h4 class="mb-0"><?php echo $total_fee_types; ?></h4>
                    <small>إجمالي أنواع الرسوم</small>
                    <div class="mt-1">
                        <small><?php echo $total_active_types; ?> نشط</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h5 class="mb-0"><?php echo format_currency($total_fees_amount); ?></h5>
                    <small>إجمالي مبلغ الرسوم</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h5 class="mb-0"><?php echo format_currency($total_paid_amount); ?></h5>
                    <small>إجمالي المدفوعات</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-dark">
                <div class="card-body text-center">
                    <h4 class="mb-0"><?php echo $total_usage_count; ?></h4>
                    <small>إجمالي الاستخدامات</small>
                </div>
            </div>
        </div>
        <?php if ($test_data_types > 0): ?>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h4 class="mb-0"><?php echo $test_data_types; ?></h4>
                    <small>أنواع بها بيانات تجريبية</small>
                    <div class="mt-1">
                        <small>تمنع الحذف</small>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-primary mb-2">
                        <i class="fas fa-tags fa-2x"></i>
                    </div>
                    <h4 class="mb-0"><?php echo $fee_types ? $fee_types->num_rows : 0; ?></h4>
                    <small class="text-muted">إجمالي أنواع الرسوم</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-success mb-2">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                    <h4 class="mb-0">
                        <?php 
                        $active_count = 0;
                        if ($fee_types) {
                            $fee_types->data_seek(0);
                            while ($row = $fee_types->fetch_assoc()) {
                                if ($row['status'] == 'active') $active_count++;
                            }
                        }
                        echo $active_count;
                        ?>
                    </h4>
                    <small class="text-muted">أنواع نشطة</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-warning mb-2">
                        <i class="fas fa-pause-circle fa-2x"></i>
                    </div>
                    <h4 class="mb-0">
                        <?php 
                        $inactive_count = 0;
                        if ($fee_types) {
                            $fee_types->data_seek(0);
                            while ($row = $fee_types->fetch_assoc()) {
                                if ($row['status'] == 'inactive') $inactive_count++;
                            }
                        }
                        echo $inactive_count;
                        ?>
                    </h4>
                    <small class="text-muted">أنواع غير نشطة</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-info mb-2">
                        <i class="fas fa-money-bill-wave fa-2x"></i>
                    </div>
                    <h4 class="mb-0">
                        <?php 
                        $total_revenue = 0;
                        if ($fee_types) {
                            $fee_types->data_seek(0);
                            while ($row = $fee_types->fetch_assoc()) {
                                $total_revenue += $row['total_amount'] ?? 0;
                            }
                        }
                        echo format_currency($total_revenue, false);
                        ?>
                    </h4>
                    <small class="text-muted">إجمالي الإيرادات</small>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول أنواع الرسوم -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>قائمة أنواع الرسوم
            </h5>
        </div>
        <div class="card-body p-0">
            <?php if ($fee_types && $fee_types->num_rows > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>اسم النوع</th>
                            <th>الوصف</th>
                            <th>المبلغ الافتراضي</th>
                            <th>الحالة</th>
                            <th>عدد الاستخدامات</th>
                            <th>إجمالي الإيرادات</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $fee_types->data_seek(0);
                        while ($fee_type = $fee_types->fetch_assoc()):
                            $total_usage = $fee_type['fees_count'] + $fee_type['payments_count'] + $fee_type['installments_count'];
                            $can_delete = ($total_usage == 0);
                            $is_test_data = in_array($fee_type['id'], [3, 4, 5]); // رسوم الكتب والنشاطات والنقل
                        ?>
                        <tr <?php echo $is_test_data ? 'class="table-warning"' : ''; ?>>
                            <td>
                                <strong><?php echo htmlspecialchars($fee_type['type_name']); ?></strong>
                                <?php if ($is_test_data): ?>
                                    <br><small class="badge bg-warning text-dark">بيانات تجريبية</small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <small class="text-muted">
                                    <?php echo htmlspecialchars($fee_type['description'] ?? 'لا يوجد وصف'); ?>
                                </small>
                            </td>
                            <td>
                                <?php if ($fee_type['default_amount'] > 0): ?>
                                    <span class="badge bg-info">
                                        <?php echo format_currency($fee_type['default_amount']); ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-muted">غير محدد</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($fee_type['status'] == 'active'): ?>
                                    <span class="badge bg-success">نشط</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">غير نشط</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-primary"><?php echo $total_usage; ?></span>
                                <?php if ($total_usage > 0): ?>
                                    <small class="text-muted d-block">
                                        رسوم: <?php echo $fee_type['fees_count']; ?> |
                                        مدفوعات: <?php echo $fee_type['payments_count']; ?> |
                                        أقساط: <?php echo $fee_type['installments_count']; ?>
                                    </small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($fee_type['total_amount'] > 0): ?>
                                    <strong class="text-success">
                                        <?php echo format_currency($fee_type['total_amount']); ?>
                                    </strong>
                                    <?php if ($fee_type['total_paid'] > 0): ?>
                                        <br><small class="text-info">
                                            مدفوع: <?php echo format_currency($fee_type['total_paid']); ?>
                                        </small>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="text-muted">0</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <small class="text-muted">
                                    <?php echo date('Y-m-d', strtotime($fee_type['created_at'])); ?>
                                </small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="edit.php?id=<?php echo $fee_type['id']; ?>" 
                                       class="btn btn-outline-primary" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    
                                    <a href="?toggle_status=<?php echo $fee_type['id']; ?>" 
                                       class="btn btn-outline-warning" 
                                       title="<?php echo $fee_type['status'] == 'active' ? 'إلغاء التفعيل' : 'تفعيل'; ?>"
                                       onclick="return confirm('هل أنت متأكد من تغيير حالة هذا النوع؟')">
                                        <i class="fas fa-<?php echo $fee_type['status'] == 'active' ? 'pause' : 'play'; ?>"></i>
                                    </a>
                                    
                                    <?php if ($total_usage == 0): ?>
                                    <a href="?delete=<?php echo $fee_type['id']; ?>" 
                                       class="btn btn-outline-danger" title="حذف"
                                       onclick="return confirm('هل أنت متأكد من حذف هذا النوع؟ لا يمكن التراجع عن هذا الإجراء.')">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                    <?php else: ?>
                                    <button class="btn btn-outline-secondary" disabled
                                            title="لا يمكن الحذف - مستخدم في <?php echo $total_usage; ?> سجل (رسوم: <?php echo $fee_type['fees_count']; ?> | مدفوعات: <?php echo $fee_type['payments_count']; ?> | أقساط: <?php echo $fee_type['installments_count']; ?>)">
                                        <i class="fas fa-lock"></i>
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
            <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد أنواع رسوم</h5>
                <p class="text-muted">ابدأ بإضافة أول نوع رسوم للنظام</p>
                <a href="add.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة نوع رسوم جديد
                </a>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include_once '../../includes/footer.php'; ?>
