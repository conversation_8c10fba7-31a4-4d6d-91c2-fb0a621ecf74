<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار دالة الحذف</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-test-tube me-2"></i>اختبار دالة الحذف
                        </h5>
                    </div>
                    <div class="card-body">
                        <p>هذا اختبار لدالة الحذف المحدثة:</p>
                        
                        <div class="alert alert-info">
                            <h6>التحديثات المطبقة:</h6>
                            <ul class="mb-0">
                                <li>تغيير من GET request إلى POST request</li>
                                <li>إضافة حقل confirm_delete</li>
                                <li>إنشاء نموذج ديناميكي للإرسال</li>
                            </ul>
                        </div>
                        
                        <button type="button" class="btn btn-danger" onclick="testDelete()">
                            <i class="fas fa-trash me-2"></i>اختبار الحذف
                        </button>
                        
                        <hr>
                        
                        <h6>الكود المحدث:</h6>
                        <pre class="bg-light p-3 rounded"><code>// إنشاء نموذج وإرساله
const form = document.createElement('form');
form.method = 'POST';
form.action = 'delete.php?id=' + subjectId;

const confirmInput = document.createElement('input');
confirmInput.type = 'hidden';
confirmInput.name = 'confirm_delete';
confirmInput.value = '1';

form.appendChild(confirmInput);
document.body.appendChild(form);
form.submit();</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    function testDelete() {
        const subjectId = 1;
        const subjectName = "مادة تجريبية";
        
        Swal.fire({
            title: 'تأكيد الحذف',
            html: `
                <div class="text-center mb-3">
                    <i class="fas fa-trash-alt fa-3x text-danger"></i>
                </div>
                <p>هل أنت متأكد من حذف هذه المادة؟</p>
                <div class="alert alert-warning">
                    <strong>اسم المادة:</strong> ${subjectName}
                </div>
                <p class="text-muted small">لا يمكن التراجع عن هذا الإجراء</p>
            `,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-trash me-2"></i>حذف',
            cancelButtonText: '<i class="fas fa-times me-2"></i>إلغاء',
            reverseButtons: true,
            customClass: {
                confirmButton: 'btn btn-danger',
                cancelButton: 'btn btn-secondary'
            },
            buttonsStyling: false
        }).then((result) => {
            if (result.isConfirmed) {
                Swal.fire({
                    title: 'جاري الحذف...',
                    text: 'يرجى الانتظار',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });
                
                // محاكاة الإرسال (بدلاً من الإرسال الفعلي)
                setTimeout(() => {
                    Swal.fire({
                        title: 'تم الاختبار!',
                        text: 'الدالة تعمل بشكل صحيح. سيتم إرسال POST request مع confirm_delete=1',
                        icon: 'success',
                        confirmButtonText: 'ممتاز!'
                    });
                }, 2000);
                
                // الكود الفعلي (معلق للاختبار):
                /*
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = 'subjects/delete.php?id=' + subjectId;
                
                const confirmInput = document.createElement('input');
                confirmInput.type = 'hidden';
                confirmInput.name = 'confirm_delete';
                confirmInput.value = '1';
                
                form.appendChild(confirmInput);
                document.body.appendChild(form);
                form.submit();
                */
            }
        });
    }
    </script>
</body>
</html>
