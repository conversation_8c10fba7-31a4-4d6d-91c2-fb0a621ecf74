<?php
/**
 * إدارة الصلاحيات المخصصة للمستخدمين
 * Custom Permissions Manager for Users
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

$message = '';
$message_type = '';

// جلب جميع المستخدمين (عدا المديرين)
$users_query = "SELECT id, full_name, email, role, status FROM users WHERE role != 'admin' ORDER BY full_name";
$users_result = $conn->query($users_query);

// جلب المستخدم المحدد
$selected_user = null;
$user_permissions = [];
if (isset($_GET['user_id'])) {
    $user_id = (int)$_GET['user_id'];
    
    // جلب بيانات المستخدم
    $user_query = "SELECT * FROM users WHERE id = ? AND role != 'admin'";
    $user_stmt = $conn->prepare($user_query);
    $user_stmt->bind_param("i", $user_id);
    $user_stmt->execute();
    $selected_user = $user_stmt->get_result()->fetch_assoc();
    
    if ($selected_user) {
        // جلب صلاحيات المستخدم
        $permissions_query = "SELECT permission_key, is_granted FROM user_custom_permissions WHERE user_id = ?";
        $permissions_stmt = $conn->prepare($permissions_query);
        $permissions_stmt->bind_param("i", $user_id);
        $permissions_stmt->execute();
        $permissions_result = $permissions_stmt->get_result();
        
        while ($perm = $permissions_result->fetch_assoc()) {
            $user_permissions[$perm['permission_key']] = $perm['is_granted'];
        }
    }
}

// الصلاحيات المتاحة مجمعة حسب الفئة
$available_permissions = [
    'access' => [
        'name' => 'صلاحيات الوصول الأساسية',
        'permissions' => [
            'teacher_access' => 'وصول المعلم',
            'staff_access' => 'وصول الإداري',
            'student_access' => 'وصول الطالب'
        ]
    ],
    'students' => [
        'name' => 'إدارة الطلاب',
        'permissions' => [
            'students_view' => 'عرض الطلاب',
            'student_add' => 'إضافة طالب',
            'student_edit' => 'تعديل الطالب',
            'student_delete' => 'حذف الطالب',
            'student_affairs' => 'شئون الطلاب (كاملة)'
        ]
    ],
    'teachers' => [
        'name' => 'إدارة المعلمين',
        'permissions' => [
            'teachers_view' => 'عرض المعلمين',
            'teacher_add' => 'إضافة معلم',
            'teacher_edit' => 'تعديل المعلم',
            'teacher_delete' => 'حذف المعلم',
            'staff_affairs' => 'شئون العاملين (كاملة)'
        ]
    ],
    'academic' => [
        'name' => 'الشئون الأكاديمية',
        'permissions' => [
            'classes_view' => 'عرض الفصول',
            'class_manage' => 'إدارة الفصول',
            'subjects_view' => 'عرض المواد',
            'subject_manage' => 'إدارة المواد',
            'exams_view' => 'عرض الامتحانات',
            'exam_manage' => 'إدارة الامتحانات'
        ]
    ],
    'reports' => [
        'name' => 'التقارير',
        'permissions' => [
            'reports_view' => 'عرض التقارير',
            'student_reports' => 'تقارير الطلاب',
            'staff_reports' => 'تقارير العاملين',
            'attendance_reports' => 'تقارير الحضور',
            'academic_reports' => 'التقارير الأكاديمية'
        ]
    ],
    'financial' => [
        'name' => 'الشئون المالية',
        'permissions' => [
            'financial_access' => 'الوصول للشئون المالية',
            'fees_view' => 'عرض الرسوم',
            'fees_manage' => 'إدارة الرسوم',
            'payments_view' => 'عرض المدفوعات',
            'payments_manage' => 'إدارة المدفوعات'
        ]
    ],
    'system' => [
        'name' => 'إدارة النظام',
        'permissions' => [
            'settings_view' => 'عرض الإعدادات',
            'settings_manage' => 'إدارة الإعدادات',
            'users_manage' => 'إدارة المستخدمين',
            'backup_access' => 'النسخ الاحتياطي'
        ]
    ]
];

// معالجة تحديث الصلاحيات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_permissions']) && $selected_user) {
    try {
        $selected_permissions = $_POST['permissions'] ?? [];
        
        // حذف جميع الصلاحيات الحالية
        $delete_stmt = $conn->prepare("DELETE FROM user_custom_permissions WHERE user_id = ?");
        $delete_stmt->bind_param("i", $selected_user['id']);
        $delete_stmt->execute();
        
        // إضافة الصلاحيات المحددة
        $insert_stmt = $conn->prepare("
            INSERT INTO user_custom_permissions 
            (user_id, permission_key, is_granted, granted_by, granted_at) 
            VALUES (?, ?, 1, ?, NOW())
        ");
        
        $granted_count = 0;
        foreach ($selected_permissions as $permission) {
            $insert_stmt->bind_param("isi", $selected_user['id'], $permission, $_SESSION['user_id']);
            if ($insert_stmt->execute()) {
                $granted_count++;
            }
        }
        
        $message = "✅ تم تحديث الصلاحيات بنجاح! تم منح $granted_count صلاحية.";
        $message_type = 'success';
        
        // إعادة جلب الصلاحيات المحدثة
        $user_permissions = [];
        $permissions_stmt->execute();
        $permissions_result = $permissions_stmt->get_result();
        while ($perm = $permissions_result->fetch_assoc()) {
            $user_permissions[$perm['permission_key']] = $perm['is_granted'];
        }
        
    } catch (Exception $e) {
        $message = "❌ خطأ في تحديث الصلاحيات: " . $e->getMessage();
        $message_type = 'danger';
    }
}

$page_title = 'إدارة الصلاحيات المخصصة';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-user-cog me-2"></i><?php echo $page_title; ?></h2>
            <p class="text-muted">إدارة الصلاحيات المخصصة لكل مستخدم (المدير له جميع الصلاحيات تلقائياً)</p>
        </div>
        <a href="../settings/permissions.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة
        </a>
    </div>

    <!-- رسائل التنبيه -->
    <?php if ($message): ?>
        <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show">
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- قائمة المستخدمين -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-users me-2"></i>المستخدمون</h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        <?php if ($users_result && $users_result->num_rows > 0): ?>
                            <?php while ($user = $users_result->fetch_assoc()): ?>
                                <a href="?user_id=<?php echo $user['id']; ?>" 
                                   class="list-group-item list-group-item-action <?php echo (isset($_GET['user_id']) && $_GET['user_id'] == $user['id']) ? 'active' : ''; ?>">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1"><?php echo safe_html($user['full_name']); ?></h6>
                                            <small><?php echo safe_html($user['email']); ?></small>
                                        </div>
                                        <div>
                                            <span class="badge bg-<?php 
                                                echo match($user['role']) {
                                                    'teacher' => 'success',
                                                    'staff' => 'warning',
                                                    'student' => 'info',
                                                    default => 'secondary'
                                                };
                                            ?>">
                                                <?php echo safe_html($user['role']); ?>
                                            </span>
                                        </div>
                                    </div>
                                </a>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <div class="list-group-item">
                                <p class="text-muted mb-0">لا توجد مستخدمون</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- إدارة الصلاحيات -->
        <div class="col-lg-8">
            <?php if ($selected_user): ?>
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-key me-2"></i>صلاحيات: <?php echo safe_html($selected_user['full_name']); ?></h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>ملاحظة مهمة:</h6>
                                <p class="mb-0">المدير العام له جميع الصلاحيات تلقائياً ولا يحتاج لصلاحيات مخصصة. هذه الصفحة لإدارة صلاحيات المستخدمين الآخرين فقط.</p>
                            </div>

                            <?php foreach ($available_permissions as $category_key => $category): ?>
                                <div class="card mb-3">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-<?php 
                                                echo match($category_key) {
                                                    'access' => 'key',
                                                    'students' => 'user-graduate',
                                                    'teachers' => 'chalkboard-teacher',
                                                    'academic' => 'book',
                                                    'reports' => 'chart-bar',
                                                    'financial' => 'dollar-sign',
                                                    'system' => 'cogs',
                                                    default => 'folder'
                                                };
                                            ?> me-2"></i>
                                            <?php echo $category['name']; ?>
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <?php foreach ($category['permissions'] as $perm_key => $perm_name): ?>
                                                <div class="col-md-6 mb-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" 
                                                               name="permissions[]" 
                                                               value="<?php echo $perm_key; ?>"
                                                               id="perm_<?php echo $perm_key; ?>"
                                                               <?php echo isset($user_permissions[$perm_key]) && $user_permissions[$perm_key] ? 'checked' : ''; ?>>
                                                        <label class="form-check-label" for="perm_<?php echo $perm_key; ?>">
                                                            <?php echo $perm_name; ?>
                                                        </label>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>

                            <div class="d-flex justify-content-between">
                                <div>
                                    <button type="button" class="btn btn-outline-success btn-sm" onclick="selectAll()">
                                        <i class="fas fa-check-double me-1"></i>تحديد الكل
                                    </button>
                                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="selectNone()">
                                        <i class="fas fa-times me-1"></i>إلغاء الكل
                                    </button>
                                </div>
                                <button type="submit" name="update_permissions" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>حفظ الصلاحيات
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            <?php else: ?>
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-user-plus fa-3x text-muted mb-3"></i>
                        <h5>اختر مستخدماً لإدارة صلاحياته</h5>
                        <p class="text-muted">اختر مستخدماً من القائمة على اليسار لبدء إدارة صلاحياته المخصصة.</p>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function selectAll() {
    const checkboxes = document.querySelectorAll('input[name="permissions[]"]');
    checkboxes.forEach(checkbox => checkbox.checked = true);
}

function selectNone() {
    const checkboxes = document.querySelectorAll('input[name="permissions[]"]');
    checkboxes.forEach(checkbox => checkbox.checked = false);
}
</script>

<?php include_once '../includes/footer.php'; ?>
