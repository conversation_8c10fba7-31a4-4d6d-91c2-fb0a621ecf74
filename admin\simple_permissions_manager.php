<?php
/**
 * إدارة الصلاحيات البسيطة
 * Simple Permissions Manager
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/simple_permissions.php';

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من الصلاحيات (مدير فقط)
if (!is_admin_user()) {
    header('Location: ../dashboard/?error=access_denied');
    exit();
}

$success_message = '';
$error_message = '';

// معالجة منح الصلاحيات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['grant_permission'])) {
    $user_id = intval($_POST['user_id']);
    $page_name = $_POST['page_name'];
    $permissions = [
        'view' => isset($_POST['can_view']) ? 1 : 0,
        'add' => isset($_POST['can_add']) ? 1 : 0,
        'edit' => isset($_POST['can_edit']) ? 1 : 0,
        'delete' => isset($_POST['can_delete']) ? 1 : 0
    ];
    $notes = $_POST['notes'] ?? '';
    
    if (grant_page_permission($user_id, $page_name, $permissions, $notes)) {
        $success_message = 'تم منح الصلاحيات بنجاح';
    } else {
        $error_message = 'خطأ في منح الصلاحيات';
    }
}

// معالجة إلغاء الصلاحيات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['revoke_permission'])) {
    $user_id = intval($_POST['user_id']);
    $page_name = $_POST['page_name'];
    
    if (revoke_page_permission($user_id, $page_name)) {
        $success_message = 'تم إلغاء الصلاحيات بنجاح';
    } else {
        $error_message = 'خطأ في إلغاء الصلاحيات';
    }
}

// الحصول على قائمة المستخدمين
$users_query = "SELECT id, username, full_name, role FROM users WHERE role != 'admin' ORDER BY full_name";
$users_result = $conn->query($users_query);

// الحصول على قائمة الصفحات المتاحة
$available_pages = get_available_pages();

// الحصول على الصلاحيات الحالية
$current_permissions = [];
if ($users_result->num_rows > 0) {
    $users_result->data_seek(0);
    while ($user = $users_result->fetch_assoc()) {
        $current_permissions[$user['id']] = get_user_page_permissions($user['id']);
    }
}

$page_title = 'إدارة الصلاحيات البسيطة';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-user-shield me-2 text-primary"></i><?php echo $page_title; ?></h2>
            <p class="text-muted">نظام بسيط لإدارة صلاحيات المستخدمين للصفحات المخصصة</p>
        </div>
        <div>
            <a href="../settings/" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>العودة للإعدادات
            </a>
        </div>
    </div>

    <!-- رسائل التنبيه -->
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- منح صلاحيات جديدة -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-plus me-2"></i>منح صلاحيات جديدة</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">المستخدم</label>
                            <select name="user_id" class="form-select" required>
                                <option value="">اختر المستخدم</option>
                                <?php 
                                $users_result->data_seek(0);
                                while ($user = $users_result->fetch_assoc()): 
                                ?>
                                    <option value="<?php echo $user['id']; ?>">
                                        <?php echo htmlspecialchars($user['full_name'] . ' (' . $user['username'] . ')'); ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">الصفحة</label>
                            <select name="page_name" class="form-select" required>
                                <option value="">اختر الصفحة</option>
                                <?php foreach ($available_pages as $page): ?>
                                    <option value="<?php echo $page['page_name']; ?>">
                                        <?php echo htmlspecialchars($page['page_title']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">الصلاحيات</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="can_view" id="can_view" checked>
                                <label class="form-check-label" for="can_view">مشاهدة</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="can_add" id="can_add">
                                <label class="form-check-label" for="can_add">إضافة</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="can_edit" id="can_edit">
                                <label class="form-check-label" for="can_edit">تعديل</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="can_delete" id="can_delete">
                                <label class="form-check-label" for="can_delete">حذف</label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea name="notes" class="form-control" rows="2" placeholder="ملاحظات اختيارية"></textarea>
                        </div>

                        <button type="submit" name="grant_permission" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>منح الصلاحيات
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- عرض الصلاحيات الحالية -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-list me-2"></i>الصلاحيات الحالية</h5>
                </div>
                <div class="card-body">
                    <?php
                    $has_permissions = false;
                    if (!empty($current_permissions)) {
                        foreach ($current_permissions as $user_perms) {
                            if ($user_perms === 'all' || (is_array($user_perms) && count($user_perms) > 0)) {
                                $has_permissions = true;
                                break;
                            }
                        }
                    }

                    if (!$has_permissions):
                    ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-info-circle fa-3x mb-3"></i>
                            <p>لا توجد صلاحيات مخصصة حالياً</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>المستخدم</th>
                                        <th>الصفحة</th>
                                        <th>مشاهدة</th>
                                        <th>إضافة</th>
                                        <th>تعديل</th>
                                        <th>حذف</th>
                                        <th>إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $users_result->data_seek(0);
                                    while ($user = $users_result->fetch_assoc()):
                                        $user_permissions = $current_permissions[$user['id']] ?? [];

                                        // التعامل مع المدير
                                        if ($user_permissions === 'all') {
                                            // عرض صف واحد للمدير
                                            ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($user['full_name']); ?></strong><br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($user['username']); ?></small>
                                                </td>
                                                <td colspan="5">
                                                    <span class="badge bg-success">مدير النظام - جميع الصلاحيات</span>
                                                </td>
                                                <td>-</td>
                                            </tr>
                                            <?php
                                            continue;
                                        }

                                        if (empty($user_permissions) || !is_array($user_permissions)) continue;

                                        foreach ($user_permissions as $page_name => $perms):
                                    ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($user['full_name']); ?></strong><br>
                                                <small class="text-muted"><?php echo htmlspecialchars($user['username']); ?></small>
                                            </td>
                                            <td>
                                                <?php 
                                                $page_title = $page_name;
                                                foreach ($available_pages as $page) {
                                                    if ($page['page_name'] === $page_name) {
                                                        $page_title = $page['page_title'];
                                                        break;
                                                    }
                                                }
                                                echo htmlspecialchars($page_title);
                                                ?>
                                            </td>
                                            <td>
                                                <?php if ($perms['view']): ?>
                                                    <i class="fas fa-check text-success"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-times text-danger"></i>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($perms['add']): ?>
                                                    <i class="fas fa-check text-success"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-times text-danger"></i>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($perms['edit']): ?>
                                                    <i class="fas fa-check text-success"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-times text-danger"></i>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($perms['delete']): ?>
                                                    <i class="fas fa-check text-success"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-times text-danger"></i>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                    <input type="hidden" name="page_name" value="<?php echo $page_name; ?>">
                                                    <button type="submit" name="revoke_permission" class="btn btn-sm btn-outline-danger" 
                                                            onclick="return confirm('هل أنت متأكد من إلغاء هذه الصلاحيات؟')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    <?php 
                                        endforeach;
                                    endwhile; 
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- الصفحات المتاحة -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-sitemap me-2"></i>الصفحات المتاحة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($available_pages as $page): ?>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card border">
                                    <div class="card-body">
                                        <h6 class="card-title"><?php echo htmlspecialchars($page['page_title']); ?></h6>
                                        <p class="card-text">
                                            <small class="text-muted"><?php echo htmlspecialchars($page['description']); ?></small>
                                        </p>
                                        <div class="d-flex gap-1">
                                            <span class="badge bg-primary">مشاهدة</span>
                                            <?php if ($page['requires_add']): ?>
                                                <span class="badge bg-success">إضافة</span>
                                            <?php endif; ?>
                                            <?php if ($page['requires_edit']): ?>
                                                <span class="badge bg-warning">تعديل</span>
                                            <?php endif; ?>
                                            <?php if ($page['requires_delete']): ?>
                                                <span class="badge bg-danger">حذف</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحديث خيارات الصلاحيات حسب الصفحة المختارة
document.querySelector('select[name="page_name"]').addEventListener('change', function() {
    const selectedPage = this.value;
    const pages = <?php echo json_encode($available_pages); ?>;
    
    const page = pages.find(p => p.page_name === selectedPage);
    
    if (page) {
        document.getElementById('can_add').disabled = !page.requires_add;
        document.getElementById('can_edit').disabled = !page.requires_edit;
        document.getElementById('can_delete').disabled = !page.requires_delete;
        
        if (!page.requires_add) document.getElementById('can_add').checked = false;
        if (!page.requires_edit) document.getElementById('can_edit').checked = false;
        if (!page.requires_delete) document.getElementById('can_delete').checked = false;
    }
});
</script>

<?php include_once '../includes/footer.php'; ?>
