<?php
/**
 * استيراد الطلاب من ملف CSV
 * Import Students from CSV
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();

$user_role = $_SESSION['role'] ?? '';
$user_id = $_SESSION['user_id'] ?? 0;

// التحقق من الصلاحيات - فقط الإداريين
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

$error_message = '';
$success_message = '';
$import_report = [];

// معالجة الاستيراد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['import_file'])) {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = __('invalid_request');
    } else {
        $file = $_FILES['import_file'];
        if ($file['error'] === UPLOAD_ERR_OK) {
            $ext = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            if (!in_array($ext, ['csv'])) {
                $error_message = __('invalid_file_type');
            } else {
                $handle = fopen($file['tmp_name'], 'r');
                if ($handle) {
                    // قراءة المحتوى وتحويل الترميز
                    $content = file_get_contents($file['tmp_name']);

                    // إزالة BOM إذا كان موجوداً
                    $content = str_replace("\xEF\xBB\xBF", '', $content);

                    // تحويل الترميز إلى UTF-8 إذا لزم الأمر
                    if (!mb_check_encoding($content, 'UTF-8')) {
                        $content = mb_convert_encoding($content, 'UTF-8', 'auto');
                    }

                    // كتابة المحتوى المحسن إلى ملف مؤقت
                    $temp_file = tempnam(sys_get_temp_dir(), 'csv_import_');
                    file_put_contents($temp_file, $content);

                    // إعادة فتح الملف المحسن
                    fclose($handle);
                    $handle = fopen($temp_file, 'r');

                    $header = fgetcsv($handle);
                    $row_num = 1;
                    $added = 0;
                    $errors = 0;
                    
                    while (($row = fgetcsv($handle)) !== false) {
                        $row_num++;
                        $data = array_combine($header, $row);
                        $row_errors = [];
                        
                        // جمع البيانات
                        $full_name = clean_input($data['full_name'] ?? '');
                        $username = clean_input($data['username'] ?? '');
                        $email = clean_input($data['email'] ?? '');
                        $phone = clean_input($data['phone'] ?? '');
                        $student_id = clean_input($data['student_id'] ?? '');
                        $national_id = clean_input($data['national_id'] ?? '');
                        $date_of_birth = clean_input($data['date_of_birth'] ?? '');
                        $gender = clean_input($data['gender'] ?? '');
                        $address = clean_input($data['address'] ?? '');
                        $parent_name = clean_input($data['parent_name'] ?? '');
                        $parent_phone = clean_input($data['parent_phone'] ?? '');
                        $parent_email = clean_input($data['parent_email'] ?? '');
                        $parent_national_id = clean_input($data['parent_national_id'] ?? '');
                        $emergency_contact_name = clean_input($data['emergency_contact_name'] ?? '');
                        $emergency_contact_phone = clean_input($data['emergency_contact_phone'] ?? '');
                        $medical_conditions = clean_input($data['medical_conditions'] ?? '');
                        $class_name = clean_input($data['class_name'] ?? '');
                        $enrollment_date = clean_input($data['enrollment_date'] ?? date('Y-m-d'));
                        $password = clean_input($data['password'] ?? generate_random_password());
                        $status = clean_input($data['status'] ?? 'active');
                        
                        // التحقق من صحة البيانات
                        if (empty($full_name)) {
                            $row_errors[] = __('full_name_required');
                        }
                        if (empty($username)) {
                            $row_errors[] = __('username_required');
                        }
                        if (empty($student_id)) {
                            $row_errors[] = __('student_id_required');
                        }
                        if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                            $row_errors[] = __('invalid_email');
                        }
                        if (!empty($date_of_birth) && !validateDate($date_of_birth)) {
                            $row_errors[] = __('invalid_date_format');
                        }
                        if (!in_array($gender, ['male', 'female'])) {
                            $row_errors[] = __('invalid_gender');
                        }
                        if (!in_array($status, ['active', 'inactive'])) {
                            $row_errors[] = __('invalid_status');
                        }
                        
                        // التحقق من عدم تكرار اسم المستخدم
                        if (!empty($username)) {
                            $check_username = $conn->prepare("SELECT id FROM users WHERE username = ?");
                            $check_username->bind_param("s", $username);
                            $check_username->execute();
                            if ($check_username->get_result()->num_rows > 0) {
                                $row_errors[] = __('username_exists');
                            }
                        }
                        
                        // التحقق من عدم تكرار رقم الطالب
                        if (!empty($student_id)) {
                            $check_student_id = $conn->prepare("SELECT id FROM students WHERE student_id = ?");
                            $check_student_id->bind_param("s", $student_id);
                            $check_student_id->execute();
                            if ($check_student_id->get_result()->num_rows > 0) {
                                $row_errors[] = __('student_id_exists');
                            }
                        }
                        
                        // البحث عن الفصل
                        $class_id = null;
                        if (!empty($class_name)) {
                            $class_stmt = $conn->prepare("SELECT id FROM classes WHERE class_name = ?");
                            $class_stmt->bind_param("s", $class_name);
                            $class_stmt->execute();
                            $class_result = $class_stmt->get_result();
                            if ($class_result->num_rows > 0) {
                                $class_id = $class_result->fetch_assoc()['id'];
                            } else {
                                $row_errors[] = sprintf(__('class_not_found'), $class_name);
                            }
                        }
                        
                        // إدراج البيانات إذا لم تكن هناك أخطاء
                        if (empty($row_errors)) {
                            $conn->begin_transaction();
                            try {
                                // إنشاء المستخدم
                                $hashed_password = hash_password($password);
                                $user_stmt = $conn->prepare("
                                    INSERT INTO users (username, email, password, full_name, phone, role, status, created_at) 
                                    VALUES (?, ?, ?, ?, ?, 'student', ?, NOW())
                                ");
                                $user_stmt->bind_param("ssssss", $username, $email, $hashed_password, $full_name, $phone, $status);
                                $user_stmt->execute();
                                $user_id_new = $conn->insert_id;
                                
                                // إنشاء سجل الطالب
                                $student_stmt = $conn->prepare("
                                    INSERT INTO students (
                                        user_id, student_id, national_id, date_of_birth, gender, 
                                        address, parent_name, parent_phone, parent_email, parent_national_id,
                                        emergency_contact_name, emergency_contact_phone, medical_conditions, 
                                        class_id, enrollment_date, status, created_at
                                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
                                ");
                                $student_stmt->bind_param(
                                    "issssssssssssiss",
                                    $user_id_new, $student_id, $national_id, $date_of_birth, $gender,
                                    $address, $parent_name, $parent_phone, $parent_email, $parent_national_id,
                                    $emergency_contact_name, $emergency_contact_phone, $medical_conditions, 
                                    $class_id, $enrollment_date, $status
                                );
                                $student_stmt->execute();
                                
                                $conn->commit();
                                $added++;
                                $import_report[] = [
                                    'row' => $row_num,
                                    'status' => 'success',
                                    'message' => __('student_added_successfully'),
                                    'name' => $full_name
                                ];
                            } catch (Exception $e) {
                                $conn->rollback();
                                $errors++;
                                $import_report[] = [
                                    'row' => $row_num,
                                    'status' => 'error',
                                    'message' => $e->getMessage(),
                                    'name' => $full_name
                                ];
                            }
                        } else {
                            $errors++;
                            $import_report[] = [
                                'row' => $row_num,
                                'status' => 'error',
                                'message' => implode('; ', $row_errors),
                                'name' => $full_name
                            ];
                        }
                    }
                    fclose($handle);

                    // تنظيف الملف المؤقت
                    if (isset($temp_file) && file_exists($temp_file)) {
                        unlink($temp_file);
                    }

                    $success_message = sprintf(__('import_summary'), $added, $errors);
                } else {
                    $error_message = __('file_open_error');
                }
            }
        } else {
            $error_message = __('file_upload_error');
        }
    }
}

include_once '../includes/header.php';
?>

<div class="container-fluid my-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-upload me-2"></i><?php echo __('import_students'); ?>
                        </h5>
                        <a href="index.php" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-left me-1"></i><?php echo __('back_to_students'); ?>
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <?php if ($error_message): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo $error_message; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success_message): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo $success_message; ?>
                        </div>
                    <?php endif; ?>

                    <!-- تعليمات الاستيراد -->
                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i><?php echo __('import_instructions'); ?>
                        </h6>
                        <ul class="mb-2">
                            <li><?php echo __('file_must_be_csv'); ?> مع ترميز UTF-8</li>
                            <li><?php echo __('first_row_headers'); ?></li>
                            <li><?php echo __('required_columns'); ?>: full_name, username, student_id, gender</li>
                            <li><?php echo __('optional_columns'); ?>: email, phone, national_id, date_of_birth, address, parent_name, parent_phone, parent_email, class_name</li>
                            <li>استخدم النموذج المرفق لضمان التوافق مع النظام</li>
                        </ul>
                        <div class="alert alert-warning mb-0">
                            <small>
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                <strong>ملاحظة:</strong> لضمان ظهور النصوص العربية بشكل صحيح، يُنصح بتحميل النموذج واستخدامه مباشرة.
                            </small>
                        </div>
                    </div>

                    <!-- نموذج الاستيراد -->
                    <form method="post" enctype="multipart/form-data">
                        <?php echo csrf_token_field(); ?>
                        
                        <div class="mb-3">
                            <label for="import_file" class="form-label"><?php echo __('choose_csv_file'); ?> *</label>
                            <input type="file" class="form-control" id="import_file" name="import_file" 
                                   accept=".csv" required>
                            <div class="form-text"><?php echo __('max_file_size'); ?>: 5MB</div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="generate_csv_sample.php" class="btn btn-outline-info">
                                <i class="fas fa-download me-2"></i><?php echo __('download_sample'); ?>
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-upload me-2"></i><?php echo __('import_students'); ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- تقرير نتائج الاستيراد -->
<?php if (!empty($import_report)): ?>
<div class="container-fluid my-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i><?php echo __('import_results'); ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th><?php echo __('row_number'); ?></th>
                                    <th><?php echo __('student_name'); ?></th>
                                    <th><?php echo __('status'); ?></th>
                                    <th><?php echo __('message'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($import_report as $report): ?>
                                    <tr>
                                        <td><?php echo $report['row']; ?></td>
                                        <td><?php echo htmlspecialchars($report['name'] ?? '-'); ?></td>
                                        <td>
                                            <?php if ($report['status'] === 'success'): ?>
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i><?php echo __('success'); ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-times me-1"></i><?php echo __('error'); ?>
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small><?php echo htmlspecialchars($report['message']); ?></small>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-3">
                        <a href="index.php" class="btn btn-primary">
                            <i class="fas fa-list me-2"></i><?php echo __('view_students'); ?>
                        </a>
                        <button type="button" class="btn btn-secondary" onclick="window.location.reload()">
                            <i class="fas fa-redo me-2"></i><?php echo __('import_more'); ?>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من نوع الملف
    const fileInput = document.getElementById('import_file');
    fileInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            const ext = file.name.split('.').pop().toLowerCase();
            if (ext !== 'csv') {
                alert('<?php echo __('please_select_csv_file'); ?>');
                this.value = '';
                return;
            }

            // التحقق من حجم الملف (5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert('<?php echo __('file_too_large'); ?>');
                this.value = '';
                return;
            }
        }
    });

    // تأكيد قبل الإرسال
    document.querySelector('form').addEventListener('submit', function(e) {
        const file = fileInput.files[0];
        if (!file) {
            e.preventDefault();
            alert('<?php echo __('please_select_file'); ?>');
            return;
        }

        if (!confirm('<?php echo __('confirm_import_students'); ?>')) {
            e.preventDefault();
        }
    });
});
</script>

<?php include_once '../includes/footer.php'; ?>
