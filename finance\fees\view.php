<?php
require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/security.php';

// التحقق من الجلسة والصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    header('Location: ../../dashboard/');
    exit();
}

// جلب معرف الرسم
$fee_id = intval($_GET['id'] ?? 0);

if ($fee_id <= 0) {
    header('Location: index.php?error=' . urlencode('معرف الرسم غير صحيح'));
    exit();
}

// جلب بيانات الرسم مع تفاصيل الطالب
$stmt = $conn->prepare("
    SELECT
        sf.*,
        u.full_name as student_name,
        s.student_id as student_number,
        s.parent_name,
        s.parent_phone,
        s.address,
        c.class_name,
        c.grade_level,
        ft.type_name as fee_type_name,
        ft.description as fee_type_description
    FROM student_fees sf
    JOIN students s ON sf.student_id = s.id
    JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    LEFT JOIN fee_types ft ON sf.fee_type_id = ft.id
    WHERE sf.id = ?
");

if (!$stmt) {
    die("خطأ في تحضير الاستعلام: " . $conn->error);
}

$stmt->bind_param("i", $fee_id);
$stmt->execute();
$fee = $stmt->get_result()->fetch_assoc();

if (!$fee) {
    header('Location: index.php?error=' . urlencode('الرسم غير موجود'));
    exit();
}

// جلب المدفوعات المرتبطة بهذا الرسم
$payments_stmt = $conn->prepare("
    SELECT 
        sp.*,
        u.full_name as processed_by_name
    FROM student_payments sp
    LEFT JOIN users u ON sp.processed_by = u.id
    WHERE sp.student_fee_id = ?
    ORDER BY sp.payment_date DESC
");

$payments = [];
if ($payments_stmt) {
    $payments_stmt->bind_param("i", $fee_id);
    $payments_stmt->execute();
    $payments = $payments_stmt->get_result()->fetch_all(MYSQLI_ASSOC);
}

$page_title = __('fee_details');
include_once '../../includes/header.php';
?>

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0"><i class="fas fa-file-invoice-dollar me-2"></i><?php echo __('fee_details'); ?></h4>
                        <div class="d-flex gap-2">
                            <a href="index.php" class="btn btn-light btn-sm">
                                <i class="fas fa-arrow-left me-1"></i><?php echo __('back'); ?>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (isset($_GET['edit_success']) && $_GET['edit_success'] == 1): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i><?php echo __('fee_updated_successfully'); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php endif; ?>

                    <!-- معلومات الطالب -->
                    <div class="card border-primary mb-4">
                        <div class="card-header bg-primary text-white">
                            <h6 class="mb-0"><i class="fas fa-user-graduate me-2"></i><?php echo __('student_information'); ?></h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td class="fw-bold"><?php echo __('student_name'); ?>:</td>
                                            <td><?php echo htmlspecialchars($fee['student_name']); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold"><?php echo __('student_number'); ?>:</td>
                                            <td><?php echo htmlspecialchars($fee['student_number']); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold"><?php echo __('class'); ?>:</td>
                                            <td><?php echo htmlspecialchars($fee['class_name'] ?? 'غير محدد'); ?> - <?php echo htmlspecialchars($fee['grade_level'] ?? ''); ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-sm table-borderless">
                                        <?php if (!empty($fee['parent_name'])): ?>
                                        <tr>
                                            <td class="fw-bold"><?php echo __('parent_name'); ?>:</td>
                                            <td><?php echo htmlspecialchars($fee['parent_name']); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                        <?php if (!empty($fee['parent_phone'])): ?>
                                        <tr>
                                            <td class="fw-bold"><?php echo __('parent_phone'); ?>:</td>
                                            <td><?php echo htmlspecialchars($fee['parent_phone']); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                        <?php if (!empty($fee['address'])): ?>
                                        <tr>
                                            <td class="fw-bold"><?php echo __('address'); ?>:</td>
                                            <td><?php echo htmlspecialchars($fee['address']); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تفاصيل الرسم -->
                    <div class="card border-success mb-4">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0"><i class="fas fa-file-invoice-dollar me-2"></i><?php echo __('fee_information'); ?></h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td class="fw-bold"><?php echo __('fee_type'); ?>:</td>
                                            <td><?php echo htmlspecialchars($fee['fee_type_name'] ?? __('general_fee')); ?></td>
                                        </tr>
                                        <?php if (!empty($fee['fee_type_description'])): ?>
                                        <tr>
                                            <td class="fw-bold"><?php echo __('description'); ?>:</td>
                                            <td><?php echo htmlspecialchars($fee['fee_type_description']); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                        <tr>
                                            <td class="fw-bold"><?php echo __('semester'); ?>:</td>
                                            <td><?php echo __($fee['semester']); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold"><?php echo __('due_date'); ?>:</td>
                                            <td><?php echo date('Y-m-d', strtotime($fee['due_date'])); ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td class="fw-bold"><?php echo __('status'); ?>:</td>
                                            <td>
                                                <?php
                                                $status_classes = [
                                                    'pending' => 'bg-warning',
                                                    'partial' => 'bg-info',
                                                    'paid' => 'bg-success',
                                                    'overdue' => 'bg-danger',
                                                    'cancelled' => 'bg-secondary'
                                                ];
                                                $status_class = $status_classes[$fee['status']] ?? 'bg-secondary';
                                                ?>
                                                <span class="badge <?php echo $status_class; ?>">
                                                    <?php echo __($fee['status']); ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold"><?php echo __('created_at'); ?>:</td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($fee['created_at'])); ?></td>
                                        </tr>
                                        <?php if (!empty($fee['updated_at'])): ?>
                                        <tr>
                                            <td class="fw-bold"><?php echo __('updated_at'); ?>:</td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($fee['updated_at'])); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- المبالغ المالية -->
                    <div class="card border-warning mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0"><i class="fas fa-calculator me-2"></i><?php echo __('financial_details'); ?></h6>
                        </div>
                        <div class="card-body">
                            <?php if ($fee['discount_amount'] > 0): ?>
                            <!-- عرض تفصيلي إذا كان هناك خصم -->
                            <div class="row text-center">
                                <div class="col-md-4">
                                    <div class="card border-primary">
                                        <div class="card-body">
                                            <h5 class="text-primary"><?php echo number_format($fee['base_amount'], 2); ?></h5>
                                            <small class="text-muted"><?php echo __('base_amount'); ?></small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card border-success">
                                        <div class="card-body">
                                            <h5 class="text-success">-<?php echo number_format($fee['discount_amount'], 2); ?></h5>
                                            <small class="text-muted"><?php echo __('discount'); ?></small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card border-info">
                                        <div class="card-body">
                                            <h5 class="text-info"><?php echo number_format($fee['final_amount'], 2); ?></h5>
                                            <small class="text-muted"><?php echo __('fee_amount'); ?></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php else: ?>
                            <!-- عرض مبسط إذا لم يكن هناك خصم -->
                            <div class="text-center">
                                <div class="card border-primary d-inline-block" style="min-width: 200px;">
                                    <div class="card-body">
                                        <h3 class="text-primary mb-2"><?php echo number_format($fee['final_amount'], 2); ?></h3>
                                        <h6 class="text-muted mb-0"><?php echo __('fee_amount'); ?></h6>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>

                            <div class="text-center mt-3">
                                <h6 class="text-muted"><?php echo get_system_setting('currency_symbol', 'ر.س'); ?></h6>
                            </div>

                            <!-- حالة الدفع -->
                            <div class="text-center mt-3">
                                <?php
                                $status_info = [
                                    'paid' => ['class' => 'success', 'icon' => 'check-circle', 'text' => __('fee_paid')],
                                    'pending' => ['class' => 'warning', 'icon' => 'clock', 'text' => __('fee_pending')],
                                    'overdue' => ['class' => 'danger', 'icon' => 'exclamation-triangle', 'text' => __('fee_overdue')],
                                    'cancelled' => ['class' => 'secondary', 'icon' => 'times-circle', 'text' => __('fee_cancelled')]
                                ];
                                $status = $status_info[$fee['status']] ?? $status_info['pending'];
                                ?>
                                <div class="alert alert-<?php echo $status['class']; ?> d-inline-block">
                                    <i class="fas fa-<?php echo $status['icon']; ?> me-2"></i>
                                    <strong><?php echo $status['text']; ?></strong>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تاريخ المدفوعات -->
                    <?php if (!empty($payments)): ?>
                    <div class="card border-info mb-4">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0"><i class="fas fa-history me-2"></i><?php echo __('payment_history'); ?> (<?php echo count($payments); ?>)</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead class="table-info">
                                        <tr>
                                            <th><?php echo __('payment_date'); ?></th>
                                            <th><?php echo __('amount'); ?></th>
                                            <th><?php echo __('payment_method'); ?></th>
                                            <th><?php echo __('reference'); ?></th>
                                            <th><?php echo __('processed_by'); ?></th>
                                            <th><?php echo __('status'); ?></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($payments as $payment): ?>
                                        <tr>
                                            <td><?php echo date('Y-m-d', strtotime($payment['payment_date'])); ?></td>
                                            <td><strong><?php echo number_format($payment['amount'], 2); ?> <?php echo get_system_setting('currency_symbol', 'ر.س'); ?></strong></td>
                                            <td><?php echo __($payment['payment_method']); ?></td>
                                            <td><?php echo htmlspecialchars($payment['payment_reference'] ?? '-'); ?></td>
                                            <td><?php echo htmlspecialchars($payment['processed_by_name'] ?? 'النظام'); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $payment['status'] == 'confirmed' ? 'success' : 'warning'; ?>">
                                                    <?php echo __($payment['status']); ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- ملاحظات -->
                    <?php if (!empty($fee['notes'])): ?>
                    <div class="card border-secondary mb-4">
                        <div class="card-header bg-secondary text-white">
                            <h6 class="mb-0"><i class="fas fa-sticky-note me-2"></i><?php echo __('notes'); ?></h6>
                        </div>
                        <div class="card-body">
                            <p class="mb-0"><?php echo nl2br(htmlspecialchars($fee['notes'])); ?></p>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- أزرار الإجراءات -->
                    <div class="d-flex justify-content-between mt-4">
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
                        </a>
                        <div class="d-flex gap-2">
                            <a href="edit.php?id=<?php echo $fee['id']; ?>" class="btn btn-warning">
                                <i class="fas fa-edit me-2"></i><?php echo __('edit'); ?>
                            </a>
                            <a href="delete.php?id=<?php echo $fee['id']; ?>" class="btn btn-danger" onclick="return confirm('<?php echo __('confirm_delete_fee'); ?>');">
                                <i class="fas fa-trash me-2"></i><?php echo __('delete'); ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once '../../includes/footer.php'; ?>
