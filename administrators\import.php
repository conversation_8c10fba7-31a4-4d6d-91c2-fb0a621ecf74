<?php
/**
 * استيراد الإداريين من CSV
 * Import Administrators from CSV
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    redirect_to('../dashboard/');
}

$page_title = __('import_administrators');

$success_message = '';
$error_message = '';
$import_results = [];

// معالجة رفع الملف
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csv_file'])) {
    $uploaded_file = $_FILES['csv_file'];
    
    // التحقق من الملف
    if ($uploaded_file['error'] !== UPLOAD_ERR_OK) {
        $error_message = 'خطأ في رفع الملف';
    } elseif ($uploaded_file['size'] === 0) {
        $error_message = 'الملف فارغ';
    } elseif (pathinfo($uploaded_file['name'], PATHINFO_EXTENSION) !== 'csv') {
        $error_message = 'يجب أن يكون الملف من نوع CSV';
    } else {
        // قراءة الملف
        $file_content = file_get_contents($uploaded_file['tmp_name']);
        $lines = explode("\n", $file_content);
        
        if (count($lines) < 2) {
            $error_message = 'الملف يجب أن يحتوي على بيانات بالإضافة إلى العناوين';
        } else {
            $header = str_getcsv(array_shift($lines));
            $expected_headers = [
                'full_name', 'username', 'email', 'password', 'employee_id', 'phone', 'address',
                'date_of_birth', 'gender', 'nationality', 'national_id', 'qualification', 
                'position', 'department', 'experience_years', 'hire_date', 'salary', 
                'bank_account', 'emergency_contact_name', 'emergency_contact_phone', 'status'
            ];
            
            // التحقق من العناوين
            $missing_headers = array_diff($expected_headers, $header);
            if (!empty($missing_headers)) {
                $error_message = 'العناوين المفقودة: ' . implode(', ', $missing_headers);
            } else {
                // معالجة البيانات
                $success_count = 0;
                $error_count = 0;
                $import_results = [];
                
                foreach ($lines as $line_number => $line) {
                    $line = trim($line);
                    if (empty($line)) continue;
                    
                    $data = str_getcsv($line);
                    $row_data = array_combine($header, $data);
                    
                    // التحقق من البيانات المطلوبة
                    $row_errors = [];
                    
                    if (empty($row_data['full_name'])) {
                        $row_errors[] = 'الاسم الكامل مطلوب';
                    }
                    
                    if (empty($row_data['username'])) {
                        $row_errors[] = 'اسم المستخدم مطلوب';
                    }
                    
                    if (empty($row_data['email'])) {
                        $row_errors[] = 'البريد الإلكتروني مطلوب';
                    } elseif (!filter_var($row_data['email'], FILTER_VALIDATE_EMAIL)) {
                        $row_errors[] = 'البريد الإلكتروني غير صحيح';
                    }
                    
                    if (empty($row_data['password'])) {
                        $row_errors[] = 'كلمة المرور مطلوبة';
                    }
                    
                    // التحقق من عدم تكرار البيانات
                    if (!empty($row_data['username'])) {
                        $check_stmt = $conn->prepare("SELECT id FROM users WHERE username = ?");
                        $check_stmt->bind_param("s", $row_data['username']);
                        $check_stmt->execute();
                        if ($check_stmt->get_result()->num_rows > 0) {
                            $row_errors[] = 'اسم المستخدم موجود مسبقاً';
                        }
                    }
                    
                    if (!empty($row_data['email'])) {
                        $check_stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
                        $check_stmt->bind_param("s", $row_data['email']);
                        $check_stmt->execute();
                        if ($check_stmt->get_result()->num_rows > 0) {
                            $row_errors[] = 'البريد الإلكتروني موجود مسبقاً';
                        }
                    }
                    
                    if (!empty($row_data['employee_id'])) {
                        $check_stmt = $conn->prepare("SELECT id FROM staff WHERE employee_id = ?");
                        $check_stmt->bind_param("s", $row_data['employee_id']);
                        $check_stmt->execute();
                        if ($check_stmt->get_result()->num_rows > 0) {
                            $row_errors[] = 'رقم الموظف موجود مسبقاً';
                        }
                    }

                    if (!empty($row_data['national_id'])) {
                        $check_stmt = $conn->prepare("SELECT id FROM staff WHERE national_id = ?");
                        $check_stmt->bind_param("s", $row_data['national_id']);
                        $check_stmt->execute();
                        if ($check_stmt->get_result()->num_rows > 0) {
                            $row_errors[] = 'الرقم القومي موجود مسبقاً';
                        }
                    }
                    
                    if (empty($row_errors)) {
                        try {
                            $conn->begin_transaction();
                            
                            // إضافة المستخدم
                            $hashed_password = hash_password($row_data['password']);
                            $status = !empty($row_data['status']) ? $row_data['status'] : 'active';
                            
                            $user_stmt = $conn->prepare("
                                INSERT INTO users (username, email, password, full_name, phone, role, status, created_at) 
                                VALUES (?, ?, ?, ?, ?, 'staff', ?, NOW())
                            ");
                            $user_stmt->bind_param("ssssss", 
                                $row_data['username'], $row_data['email'], $hashed_password, 
                                $row_data['full_name'], $row_data['phone'], $status
                            );
                            $user_stmt->execute();
                            $user_id = $conn->insert_id;
                            
                            // إضافة الإداري
                            $admin_stmt = $conn->prepare("
                                INSERT INTO staff (
                                    user_id, employee_id, phone, address, date_of_birth, gender, nationality, national_id,
                                    qualification, position, department, experience_years, hire_date, salary, bank_account,
                                    emergency_contact_name, emergency_contact_phone, status, created_at
                                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
                            ");

                            $experience_years = !empty($row_data['experience_years']) ? intval($row_data['experience_years']) : 0;
                            $salary = !empty($row_data['salary']) ? floatval($row_data['salary']) : 0;
                            $date_of_birth = !empty($row_data['date_of_birth']) ? $row_data['date_of_birth'] : null;
                            $hire_date = !empty($row_data['hire_date']) ? $row_data['hire_date'] : null;

                            $admin_stmt->bind_param(
                                "issssssssssissdsss",
                                $user_id, $row_data['employee_id'], $row_data['phone'], $row_data['address'],
                                $date_of_birth, $row_data['gender'], $row_data['nationality'], $row_data['national_id'],
                                $row_data['qualification'], $row_data['position'], $row_data['department'],
                                $experience_years, $hire_date, $salary, $row_data['bank_account'],
                                $row_data['emergency_contact_name'], $row_data['emergency_contact_phone'], $status
                            );
                            $admin_stmt->execute();
                            
                            $conn->commit();
                            $success_count++;
                            
                            $import_results[] = [
                                'line' => $line_number + 2,
                                'name' => $row_data['full_name'],
                                'status' => 'success',
                                'message' => 'تم الإضافة بنجاح'
                            ];
                            
                        } catch (Exception $e) {
                            $conn->rollback();
                            $error_count++;
                            $import_results[] = [
                                'line' => $line_number + 2,
                                'name' => $row_data['full_name'],
                                'status' => 'error',
                                'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
                            ];
                        }
                    } else {
                        $error_count++;
                        $import_results[] = [
                            'line' => $line_number + 2,
                            'name' => $row_data['full_name'] ?? 'غير محدد',
                            'status' => 'error',
                            'message' => implode(', ', $row_errors)
                        ];
                    }
                }
                
                // تسجيل النشاط
                log_activity($_SESSION['user_id'], 'import_administrators', 'administrators', null, null, [
                    'success_count' => $success_count,
                    'error_count' => $error_count,
                    'total_rows' => count($lines)
                ]);
                
                if ($success_count > 0) {
                    $success_message = "تم استيراد $success_count إداري بنجاح";
                    if ($error_count > 0) {
                        $success_message .= " مع $error_count أخطاء";
                    }
                } else {
                    $error_message = "فشل في استيراد جميع السجلات ($error_count أخطاء)";
                }
            }
        }
    }
}

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-file-import text-primary me-2"></i>
                <?php echo __('import_administrators'); ?>
            </h2>
            <p class="text-muted mb-0"><?php echo __('import_administrators_from_csv'); ?></p>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_administrators'); ?>
            </a>
        </div>
    </div>

    <!-- Messages -->
    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Upload Form -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-upload me-2"></i>رفع ملف CSV
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="csv_file" class="form-label">اختر ملف CSV</label>
                            <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                            <div class="form-text">
                                يجب أن يكون الملف بصيغة CSV ويحتوي على العناوين المطلوبة
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload me-2"></i>رفع واستيراد
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>تعليمات الاستيراد
                    </h5>
                </div>
                <div class="card-body">
                    <h6>تنسيق الملف المطلوب:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>ملف CSV بترميز UTF-8</li>
                        <li><i class="fas fa-check text-success me-2"></i>الصف الأول يحتوي على العناوين</li>
                        <li><i class="fas fa-check text-success me-2"></i>فصل الأعمدة بفاصلة (,)</li>
                    </ul>
                    
                    <h6 class="mt-3">العناوين المطلوبة:</h6>
                    <div class="row">
                        <div class="col-6">
                            <small class="text-muted">
                                • full_name<br>
                                • username<br>
                                • email<br>
                                • password<br>
                                • employee_id<br>
                                • phone<br>
                                • address<br>
                                • date_of_birth<br>
                                • gender<br>
                                • nationality<br>
                                • national_id
                            </small>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">
                                • qualification<br>
                                • position<br>
                                • department<br>
                                • experience_years<br>
                                • hire_date<br>
                                • salary<br>
                                • bank_account<br>
                                • emergency_contact_name<br>
                                • emergency_contact_phone<br>
                                • status
                            </small>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <a href="sample_administrators.csv" class="btn btn-outline-info btn-sm" download>
                            <i class="fas fa-download me-2"></i>تحميل ملف نموذجي
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Import Results -->
    <?php if (!empty($import_results)): ?>
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>نتائج الاستيراد
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>رقم السطر</th>
                                <th>الاسم</th>
                                <th>الحالة</th>
                                <th>الرسالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($import_results as $result): ?>
                                <tr>
                                    <td><?php echo $result['line']; ?></td>
                                    <td><?php echo htmlspecialchars($result['name']); ?></td>
                                    <td>
                                        <?php if ($result['status'] === 'success'): ?>
                                            <span class="badge bg-success">نجح</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">فشل</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($result['message']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php require_once '../includes/footer.php'; ?>
