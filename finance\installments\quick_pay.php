<?php
require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/security.php';

// التحقق من الجلسة والصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    header('Location: ../../dashboard/');
    exit();
}

$error_message = '';
$success_message = '';

// معالجة الدفع السريع
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من رمز CSRF
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = __('invalid_request');
    } else {
        // جمع البيانات وتنظيفها
        $installment_id = intval($_POST['installment_id'] ?? 0);
        $pay_amount = floatval($_POST['pay_amount'] ?? 0);
        $payment_method = clean_input($_POST['payment_method'] ?? 'cash');
        $payment_date = clean_input($_POST['payment_date'] ?? date('Y-m-d'));
        $payment_reference = clean_input($_POST['payment_reference'] ?? '');
        $notes = clean_input($_POST['notes'] ?? '');
        
        // التحقق من صحة البيانات
        if ($installment_id <= 0) {
            $error_message = __('invalid_installment');
        } elseif ($pay_amount <= 0) {
            $error_message = __('invalid_amount');
        } else {
            try {
                // بدء المعاملة
                $conn->begin_transaction();
                
                // جلب بيانات القسط الحالية
                $stmt = $conn->prepare("SELECT total_amount, paid_amount, status FROM student_installments WHERE id = ?");
                $stmt->bind_param("i", $installment_id);
                $stmt->execute();
                $installment = $stmt->get_result()->fetch_assoc();
                
                if (!$installment) {
                    throw new Exception(__('installment_not_found'));
                }
                
                $remaining = $installment['total_amount'] - $installment['paid_amount'];
                
                // التحقق من أن المبلغ لا يتجاوز المتبقي
                if ($pay_amount > $remaining) {
                    throw new Exception(__('amount_exceeds_remaining'));
                }
                
                // حساب المبلغ الجديد المدفوع
                $new_paid_amount = $installment['paid_amount'] + $pay_amount;
                
                // تحديد الحالة الجديدة
                $new_status = 'pending';
                if ($new_paid_amount >= $installment['total_amount']) {
                    $new_status = 'paid';
                } elseif ($new_paid_amount > 0) {
                    $new_status = 'partial';
                }
                
                // تحديث القسط
                $update_stmt = $conn->prepare("UPDATE student_installments SET paid_amount = ?, status = ?, paid_date = ? WHERE id = ?");
                $paid_date = ($new_status === 'paid') ? $payment_date : null;
                $update_stmt->bind_param("dssi", $new_paid_amount, $new_status, $paid_date, $installment_id);
                
                if (!$update_stmt->execute()) {
                    throw new Exception(__('database_error') . ': ' . $conn->error);
                }
                
                // إضافة سجل الدفعة في جدول student_payments
                $receipt_number = 'PAY-' . date('Ymd') . '-' . $installment_id . rand(1000, 9999);
                $payment_stmt = $conn->prepare("INSERT INTO student_payments (student_id, installment_id, payment_method, amount, payment_reference, payment_date, receipt_number, status, notes, processed_by, processed_at, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, 'confirmed', ?, ?, NOW(), NOW())");
                if ($payment_stmt) {
                    // جلب student_id من القسط
                    $student_stmt = $conn->prepare("SELECT student_id FROM student_installments WHERE id = ?");
                    $student_stmt->bind_param("i", $installment_id);
                    $student_stmt->execute();
                    $student_result = $student_stmt->get_result()->fetch_assoc();
                    $student_id = $student_result['student_id'];

                    $processed_by = $_SESSION['user_id'] ?? 1;
                    $payment_stmt->bind_param("iisdssssi", $student_id, $installment_id, $payment_method, $pay_amount, $payment_reference, $payment_date, $receipt_number, $notes, $processed_by);
                    $payment_stmt->execute();
                }
                
                // تأكيد المعاملة
                $conn->commit();
                
                $success_message = __('payment_processed_successfully');
                
                // إعادة توجيه مع رسالة نجاح
                header("Location: index.php?payment_success=1&amount=" . $pay_amount);
                exit();
                
            } catch (Exception $e) {
                // إلغاء المعاملة
                $conn->rollback();
                $error_message = $e->getMessage();
                error_log("Quick payment error: " . $e->getMessage());
            }
        }
    }
}

// إذا كان هناك خطأ، العودة لصفحة الأقساط مع رسالة الخطأ
if (!empty($error_message)) {
    header("Location: index.php?payment_error=" . urlencode($error_message));
    exit();
}

// إذا لم يتم إرسال النموذج، العودة لصفحة الأقساط
header("Location: index.php");
exit();
?>
