<?php

/**
 * التحقق من صلاحية مخصصة للمستخدم (مع الحفاظ على صلاحيات المدير)
 * Check if user has a specific custom permission (with admin having all permissions)
 */
function has_permission($permission_key) {
    global $conn;
    
    if (!isset($_SESSION['user_id'])) {
        return false;
    }
    
    // المدير العام له جميع الصلاحيات
    if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin') {
        return true;
    }
    
    $user_id = $_SESSION['user_id'];
    
    try {
        // البحث في جدول الصلاحيات المخصصة للمستخدمين غير المديرين
        $stmt = $conn->prepare("
            SELECT is_granted 
            FROM user_custom_permissions 
            WHERE user_id = ? AND permission_key = ? AND is_granted = 1
            LIMIT 1
        ");
        $stmt->bind_param("is", $user_id, $permission_key);
        $stmt->execute();
        $result = $stmt->get_result();
        
        return $result->num_rows > 0;
        
    } catch (Exception $e) {
        error_log("Error in has_permission: " . $e->getMessage());
        return false;
    }
}

/**
 * التحقق من وجود جدول الصلاحيات
 * Check if permissions tables exist
 */
function permissions_tables_exist() {
    global $conn;

    try {
        $tables = ['system_resources', 'user_custom_permissions'];
        foreach ($tables as $table) {
            $result = $conn->query("SHOW TABLES LIKE '$table'");
            if ($result->num_rows == 0) {
                return false;
            }
        }
        return true;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Include advanced permissions system
 */
if (file_exists(__DIR__ . '/permissions.php')) {
    require_once __DIR__ . '/permissions.php';
}

/**
 * Get current language
 */
function get_current_language() {
    // التحقق من معامل URL أولاً
    if (isset($_GET['lang']) && in_array($_GET['lang'], ['ar', 'en'])) {
        $_SESSION['system_language'] = $_GET['lang'];
        return $_GET['lang'];
    }

    // الأفضلية للغة المختارة في الجلسة
    if (isset($_SESSION['system_language']) && in_array($_SESSION['system_language'], ['ar', 'en'])) {
        return $_SESSION['system_language'];
    }

    // إذا لم تكن اللغة محددة في الجلسة، استخدم تفضيل المستخدم
    if (is_logged_in()) {
        $user_lang = get_user_setting($_SESSION['user_id'], 'language');
        if ($user_lang && in_array($user_lang, ['ar', 'en'])) {
            $_SESSION['system_language'] = $user_lang;
            return $user_lang;
        }
    }

    // إعداد النظام الافتراضي
    $default_lang = get_system_setting('language', defined('DEFAULT_LANGUAGE') ? DEFAULT_LANGUAGE : 'ar');
    $_SESSION['system_language'] = $default_lang;
    return $default_lang;
}

/**
 * Get system setting
 */
function get_system_setting($key, $default = null) {
    global $conn;

    if (!$conn) {
        return $default;
    }

    try {
        $stmt = $conn->prepare("SELECT setting_value FROM system_settings WHERE setting_key = ?");
        $stmt->bind_param("s", $key);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($row = $result->fetch_assoc()) {
            return $row['setting_value'];
        }

        return $default;
    } catch (Exception $e) {
        log_error("Get system setting error: " . $e->getMessage());
        return $default;
    }
}

/**
 * Get current academic year
 */
function get_current_academic_year() {
    return get_system_setting('academic_year', date('Y') . '-' . (date('Y') + 1));
}

/**
 * Get currency symbol from system settings
 */
function get_currency_symbol() {
    return get_system_setting('currency_symbol', 'ج.م');
}

/**
 * Get currency code from system settings
 */
function get_currency_code() {
    return get_system_setting('currency_code', 'EGP');
}

/**
 * Get currency name from system settings
 */
function get_currency_name() {
    return get_system_setting('currency_name', 'جنيه مصري');
}

/**
 * Format amount with currency
 */
function format_currency($amount, $show_symbol = true) {
    // التعامل مع القيم الفارغة أو null
    if ($amount === null || $amount === '' || !is_numeric($amount)) {
        $amount = 0;
    }

    $formatted_amount = number_format(floatval($amount), 2);
    if ($show_symbol) {
        $currency_symbol = get_currency_symbol();
        return $formatted_amount . ' ' . $currency_symbol;
    }
    return $formatted_amount;
}

/**
 * Check maintenance mode
 */
function is_maintenance_mode() {
    return get_system_setting('maintenance_mode', 'false') === 'true';
}

/**
 * Get user notifications
 */
function get_user_notifications($user_id, $limit = 10) {
    global $conn;

    if (!$conn) {
        return [];
    }

    try {
        $stmt = $conn->prepare("
            SELECT n.*,
                   CASE
                       WHEN n.created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN 'today'
                       WHEN n.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 'this_week'
                       ELSE 'older'
                   END as time_group
            FROM notifications n
            WHERE (n.user_id = ? OR n.user_id IS NULL OR n.is_global = 1)
            AND (n.expires_at IS NULL OR n.expires_at > NOW())
            ORDER BY n.priority DESC, n.created_at DESC
            LIMIT ?
        ");
        $stmt->bind_param("ii", $user_id, $limit);
        $stmt->execute();
        return $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    } catch (Exception $e) {
        log_error("Get notifications error: " . $e->getMessage());
        return [];
    }
}

/**
 * Count unread notifications
 */
function count_unread_notifications($user_id) {
    global $conn;

    if (!$conn) {
        return 0;
    }

    try {
        $stmt = $conn->prepare("
            SELECT COUNT(*) as count FROM notifications
            WHERE (user_id = ? OR user_id IS NULL OR is_global = 1)
            AND is_read = 0
            AND (expires_at IS NULL OR expires_at > NOW())
        ");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        return $row['count'] ?? 0;
    } catch (Exception $e) {
        log_error("Count notifications error: " . $e->getMessage());
        return 0;
    }
}

/**
 * Log user activity
 */
function log_user_activity($user_id, $action, $description = '', $table_name = '', $record_id = null, $old_values = null, $new_values = null) {
    global $conn;

    if (!$conn) {
        return false;
    }

    try {
        $ip_address = get_client_ip();
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $session_id = session_id();

        $stmt = $conn->prepare("
            INSERT INTO activity_logs
            (user_id, action, description, table_name, record_id, old_values, new_values, ip_address, user_agent, session_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $old_json = $old_values ? json_encode($old_values) : null;
        $new_json = $new_values ? json_encode($new_values) : null;

        $stmt->bind_param("isssisssss",
            $user_id, $action, $description, $table_name, $record_id,
            $old_json, $new_json, $ip_address, $user_agent, $session_id
        );

        return $stmt->execute();
    } catch (Exception $e) {
        log_error("Log activity error: " . $e->getMessage());
        return false;
    }
}

/**
 * Log error
 */
function log_error($message, $error_type = 'general', $file_path = '', $line_number = 0, $context = []) {
    global $conn;

    // Always log to PHP error log
    error_log($message);

    if (!$conn) {
        return false;
    }

    try {
        $ip_address = get_client_ip();
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $request_url = $_SERVER['REQUEST_URI'] ?? '';
        $request_method = $_SERVER['REQUEST_METHOD'] ?? '';
        $user_id = $_SESSION['user_id'] ?? null;
        $session_id = session_id();

        $context_json = !empty($context) ? json_encode($context) : null;
        $request_data = json_encode($_REQUEST);

        $stmt = $conn->prepare("
            INSERT INTO error_logs
            (error_type, error_message, file_path, line_number, context, ip_address, user_agent,
             request_url, request_method, request_data, user_id, session_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $stmt->bind_param("sssississsis",
            $error_type, $message, $file_path, $line_number, $context_json,
            $ip_address, $user_agent, $request_url, $request_method, $request_data,
            $user_id, $session_id
        );

        return $stmt->execute();
    } catch (Exception $e) {
        error_log("Error logging failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Set remember me cookie
 */
function set_remember_me_cookie($user_id) {
    global $conn;

    if (!$conn) {
        return false;
    }

    try {
        $token = bin2hex(random_bytes(32));
        $expires_at = date('Y-m-d H:i:s', strtotime('+30 days'));

        $stmt = $conn->prepare("INSERT INTO remember_tokens (user_id, token, expires_at) VALUES (?, ?, ?)");
        $stmt->bind_param("iss", $user_id, $token, $expires_at);

        if ($stmt->execute()) {
            setcookie('remember_token', $token, strtotime('+30 days'), '/', '', true, true);
            return true;
        }

        return false;
    } catch (Exception $e) {
        log_error("Set remember me cookie error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get user setting
 */
function get_user_setting($user_id, $setting_key, $default = null) {
    global $conn;

    if (!$conn) {
        return $default;
    }

    try {
        $stmt = $conn->prepare("SELECT preferences FROM users WHERE id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($row = $result->fetch_assoc()) {
            $preferences = json_decode($row['preferences'], true);
            return $preferences[$setting_key] ?? $default;
        }

        return $default;
    } catch (Exception $e) {
        log_error("Get user setting error: " . $e->getMessage());
        return $default;
    }
}

/**
 * تنظيف وعرض النص بأمان
 */
function safe_html($text, $default = '') {
    if (empty($text)) {
        return $default;
    }
    return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
}



/**
 * التحقق من صلاحية المستخدم حسب الدور (للتوافق مع الكود القديم)
 */
function check_permission($required_role) {
    // بدء الجلسة إذا لم تكن مبدوءة
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    if (!isset($_SESSION['role'])) {
        return false;
    }

    $user_role = $_SESSION['role'];

    // المدير العام له جميع الصلاحيات
    if ($user_role === 'admin') {
        return true;
    }

    // للتوافق مع الكود القديم، نحول الأدوار إلى صلاحيات مخصصة
    switch ($required_role) {
        case 'admin':
            return has_permission('admin_access');
        case 'teacher':
            return has_permission('teacher_access');
        case 'staff':
            return has_permission('staff_access');
        case 'student':
            return has_permission('student_access');
        case 'financial_manager':
            return has_permission('financial_access');
        case 'student_affairs_admin':
            return has_permission('student_affairs');
        case 'staff_affairs_admin':
            return has_permission('staff_affairs');
        default:
            return has_permission($required_role . '_access');
    }
}

/**
 * التحقق من تسجيل الدخول
 */
function is_logged_in() {
    // بدء الجلسة إذا لم تكن مبدوءة
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    return isset($_SESSION['user_id']) && isset($_SESSION['email']);
}

/**
 * التحقق من الجلسة
 */
function check_session() {
    if (!is_logged_in()) {
        // تحديد URL النظام
        $system_url = defined('SYSTEM_URL') ? SYSTEM_URL : 'http://localhost/school_system_v2';
        $login_url = rtrim($system_url, '/') . '/login.php';

        // التحقق من إمكانية إرسال headers
        if (!headers_sent()) {
            header('Location: ' . $login_url);
            exit();
        } else {
            // استخدام JavaScript redirect إذا تم إرسال headers بالفعل
            echo '<script>window.location.href = "' . $login_url . '";</script>';
            exit();
        }
    }

    // التحقق من انتهاء الجلسة
    if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity']) > SESSION_TIMEOUT) {
        session_destroy();
        if (!headers_sent()) {
            header('Location: ' . SYSTEM_URL . '/login.php?timeout=1');
            exit();
        } else {
            echo '<script>window.location.href = "' . SYSTEM_URL . '/login.php?timeout=1";</script>';
            exit();
        }
    }

    $_SESSION['last_activity'] = time();
}

/**
 * تسجيل دخول المستخدم
 */
function login_user($user_data) {
    $_SESSION['user_id'] = $user_data['id'];
    $_SESSION['username'] = $user_data['username'];
    $_SESSION['full_name'] = $user_data['full_name'];
    $_SESSION['email'] = $user_data['email'];
    $_SESSION['role'] = $user_data['role'];
    $_SESSION['last_activity'] = time();
    
    // تحديث آخر دخول في قاعدة البيانات
    update_last_login($user_data['id']);
    
    // تسجيل النشاط
    log_activity($user_data['id'], 'login');

    return true;
}

/**
 * تسجيل خروج المستخدم
 */
function logout_user() {
    if (is_logged_in()) {
        log_activity($_SESSION['user_id'], 'logout');
    }
    
    session_destroy();
    if (!headers_sent()) {
        header('Location: ' . SYSTEM_URL . '/login.php');
        exit();
    } else {
        echo '<script>window.location.href = "' . SYSTEM_URL . '/login.php";</script>';
        exit();
    }
}

/**
 * دوال التحقق من صحة البيانات
 */

/**
 * التحقق من صحة البريد الإلكتروني
 */
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * التحقق من قوة كلمة المرور
 */
function validate_password($password) {
    if (strlen($password) < PASSWORD_MIN_LENGTH) {
        return false;
    }
    
    // يمكن إضافة شروط أخرى هنا
    return true;
}

/**
 * التحقق من رقم الهاتف
 */
function validate_phone($phone) {
    // يقبل أي رقم دولي أو تجريبي من 5 إلى 20 رقم
    $phone = preg_replace('/[^0-9]/', '', $phone);
    return strlen($phone) >= 5 && strlen($phone) <= 20;
}

/**
 * التحقق من الرقم الوطني السعودي
 */
function validate_national_id($id) {
    // يقبل أي رقم من 5 إلى 20 رقم (مرن)
    $id = preg_replace('/[^0-9]/', '', $id);
    return strlen($id) >= 5 && strlen($id) <= 20;
}

/**
 * دوال معالجة الملفات
 */

/**
 * رفع ملف
 */
function upload_file($file, $destination_folder, $allowed_types = [], $max_size = null) {
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        return ['success' => false, 'message' => 'لم يتم اختيار ملف'];
    }
    
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'حدث خطأ أثناء رفع الملف'];
    }
    
    $max_size = $max_size ?: MAX_FILE_SIZE;
    if ($file['size'] > $max_size) {
        return ['success' => false, 'message' => 'حجم الملف كبير جداً'];
    }
    
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    if (!empty($allowed_types) && !in_array($file_extension, $allowed_types)) {
        return ['success' => false, 'message' => 'نوع الملف غير مسموح'];
    }
    
    // إنشاء اسم ملف فريد
    $new_filename = uniqid() . '_' . time() . '.' . $file_extension;
    $destination_path = UPLOADS_PATH . '/' . $destination_folder . '/' . $new_filename;
    
    // إنشاء المجلد إذا لم يكن موجوداً
    $folder_path = UPLOADS_PATH . '/' . $destination_folder;
    if (!is_dir($folder_path)) {
        mkdir($folder_path, 0755, true);
    }
    
    if (move_uploaded_file($file['tmp_name'], $destination_path)) {
        return [
            'success' => true,
            'filename' => $new_filename,
            'path' => $destination_path,
            'url' => SYSTEM_URL . '/uploads/' . $destination_folder . '/' . $new_filename
        ];
    } else {
        return ['success' => false, 'message' => 'فشل في حفظ الملف'];
    }
}

/**
 * حذف ملف
 */
function delete_file($file_path) {
    if (file_exists($file_path)) {
        return unlink($file_path);
    }
    return false;
}

/**
 * الحصول على حجم الملف بتنسيق قابل للقراءة
 */
function format_file_size($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, 2) . ' ' . $units[$i];
}

/**
 * دوال التنسيق والعرض
 */

/**
 * تنسيق التاريخ والوقت
 */
function format_datetime($datetime, $format = 'Y-m-d H:i:s') {
    if (empty($datetime)) return '';
    
    $timestamp = is_numeric($datetime) ? $datetime : strtotime($datetime);
    if (!$timestamp) return '';
    
    return date($format, $timestamp);
}

/**
 * تنسيق التاريخ فقط
 */
function format_date($date, $format = 'Y-m-d') {
    return format_datetime($date, $format);
}

/**
 * تنسيق الوقت فقط
 */
function format_time($time, $format = 'H:i') {
    return format_datetime($time, $format);
}

/**
 * حساب العمر
 */
function calculate_age($birth_date) {
    if (empty($birth_date)) return 0;
    
    $birth = new DateTime($birth_date);
    $today = new DateTime();
    $age = $today->diff($birth);
    
    return $age->y;
}

/**
 * تحويل الدرجة إلى حرف
 */
function grade_to_letter($percentage) {
    if ($percentage >= 95) return 'A+';
    if ($percentage >= 90) return 'A';
    if ($percentage >= 85) return 'B+';
    if ($percentage >= 80) return 'B';
    if ($percentage >= 75) return 'C+';
    if ($percentage >= 70) return 'C';
    if ($percentage >= 65) return 'D+';
    if ($percentage >= 60) return 'D';
    return 'F';
}

/**
 * تحويل الدرجة إلى تقدير
 */
function grade_to_text($percentage) {
    if ($percentage >= 90) return 'ممتاز';
    if ($percentage >= 80) return 'جيد جداً';
    if ($percentage >= 70) return 'جيد';
    if ($percentage >= 60) return 'مقبول';
    return 'راسب';
}

/**
 * دوال الإشعارات
 */

/**
 * إضافة إشعار
 */
function add_notification($user_id, $title, $message, $type = 'info', $action_url = null) {
    global $conn;
    
    $stmt = $conn->prepare("
        INSERT INTO notifications (user_id, title, message, type, action_url) 
        VALUES (?, ?, ?, ?, ?)
    ");
    
    $stmt->bind_param("issss", $user_id, $title, $message, $type, $action_url);
    return $stmt->execute();
}

/**
 * تحديد الإشعار كمقروء
 */
function mark_notification_read($notification_id, $user_id) {
    global $conn;
    
    $stmt = $conn->prepare("UPDATE notifications SET is_read = 1 WHERE id = ? AND user_id = ?");
    $stmt->bind_param("ii", $notification_id, $user_id);
    return $stmt->execute();
}

/**
 * دوال الترقيم
 */

/**
 * إنشاء روابط الترقيم
 */
function generate_pagination($current_page, $total_pages, $base_url, $params = []) {
    $pagination = '';
    
    if ($total_pages <= 1) return $pagination;
    
    $query_string = '';
    if (!empty($params)) {
        $query_string = '&' . http_build_query($params);
    }
    
    $pagination .= '<nav aria-label="Page navigation">';
    $pagination .= '<ul class="pagination justify-content-center">';
    
    // الصفحة السابقة
    if ($current_page > 1) {
        $prev_page = $current_page - 1;
        $pagination .= '<li class="page-item">';
        $pagination .= '<a class="page-link" href="' . $base_url . '?page=' . $prev_page . $query_string . '">السابق</a>';
        $pagination .= '</li>';
    }
    
    // أرقام الصفحات
    $start = max(1, $current_page - 2);
    $end = min($total_pages, $current_page + 2);
    
    for ($i = $start; $i <= $end; $i++) {
        $active_class = ($i == $current_page) ? ' active' : '';
        $pagination .= '<li class="page-item' . $active_class . '">';
        $pagination .= '<a class="page-link" href="' . $base_url . '?page=' . $i . $query_string . '">' . $i . '</a>';
        $pagination .= '</li>';
    }
    
    // الصفحة التالية
    if ($current_page < $total_pages) {
        $next_page = $current_page + 1;
        $pagination .= '<li class="page-item">';
        $pagination .= '<a class="page-link" href="' . $base_url . '?page=' . $next_page . $query_string . '">التالي</a>';
        $pagination .= '</li>';
    }
    
    $pagination .= '</ul>';
    $pagination .= '</nav>';
    
    return $pagination;
}

/**
 * دوال متنوعة
 */

/**
 * إنشاء رقم عشوائي
 */
function generate_random_number($length = 6) {
    return str_pad(mt_rand(0, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
}

/**
 * إنشاء كود عشوائي
 */
function generate_random_code($length = 8) {
    $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $code = '';
    
    for ($i = 0; $i < $length; $i++) {
        $code .= $characters[rand(0, strlen($characters) - 1)];
    }
    
    return $code;
}

/**
 * تحويل النص إلى رابط آمن
 */
function slugify($text) {
    // تحويل النص العربي
    $text = trim($text);
    $text = preg_replace('/[^\p{L}\p{N}\s\-_]/u', '', $text);
    $text = preg_replace('/[\s\-_]+/', '-', $text);
    $text = trim($text, '-');
    
    return strtolower($text);
}

/**
 * اقتطاع النص
 */
function truncate_text($text, $length = 100, $suffix = '...') {
    if (mb_strlen($text) <= $length) {
        return $text;
    }
    
    return mb_substr($text, 0, $length) . $suffix;
}

/**
 * تحويل المصفوفة إلى خيارات HTML
 */
function array_to_options($array, $value_key = 'id', $text_key = 'name', $selected = null) {
    $options = '';
    
    foreach ($array as $item) {
        $value = is_array($item) ? $item[$value_key] : $item;
        $text = is_array($item) ? $item[$text_key] : $item;
        $selected_attr = ($value == $selected) ? ' selected' : '';
        
        $options .= '<option value="' . htmlspecialchars($value) . '"' . $selected_attr . '>';
        $options .= htmlspecialchars($text);
        $options .= '</option>';
    }
    
    return $options;
}

/**
 * تحميل ملف اللغة
 * Load language file
 */
if (!function_exists('load_language')) {
    function load_language($force_reload = false) {
        global $lang;
        static $language_loaded = false;

        // إذا كان الملف محمل بالفعل ولا نريد إعادة التحميل
        if ($language_loaded && !$force_reload && isset($lang) && is_array($lang)) {
            return true;
        }

        $current_language = get_current_language();
        $language_file = __DIR__ . '/languages/' . $current_language . '.php';

        if (file_exists($language_file)) {
            // إعادة تعيين المصفوفة قبل التحميل
            $lang = [];
            include $language_file;
            $language_loaded = true;
            return true;
        } else {
            // تحميل اللغة الافتراضية إذا لم يوجد الملف المطلوب
            $default_file = __DIR__ . '/languages/ar.php';
            if (file_exists($default_file)) {
                $lang = [];
                include $default_file;
                $language_loaded = true;
                return true;
            }
        }

        return false;
    }
}

if (!function_exists('__')) {
    function __($text) {
        global $lang;

        // التحقق من وجود المصفوفة وتحميل ملف اللغة إذا لزم الأمر
        if (!isset($lang) || !is_array($lang)) {
            load_language();
        }

        // إرجاع الترجمة إذا وجدت، وإلا إرجاع النص الأصلي
        if (isset($lang[$text])) {
            return $lang[$text];
        }
        return $text;
    }
}

if (!function_exists('validate_date')) {
    function validate_date($date, $format = 'Y-m-d') {
        $d = DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }
}

/**
 * حذف مستخدم مع جميع البيانات المرتبطة به
 * Delete user with all related data
 */
if (!function_exists('delete_user_with_relations')) {
    function delete_user_with_relations($user_id, $delete_type = 'user') {
        global $conn;

        if (!$conn) {
            throw new Exception('Database connection not available');
        }

        // تعطيل فحص القيود الخارجية مؤقتاً
        $conn->query("SET FOREIGN_KEY_CHECKS = 0");

        $conn->begin_transaction();

        try {
            $deleted_relations = [];

            // التحقق من وجود بيانات مرتبطة
            // Check for student data
            $stmt = $conn->prepare("SELECT id FROM students WHERE user_id = ?");
            $stmt->bind_param("i", $user_id);
            $stmt->execute();
            $result = $stmt->get_result();
            if ($result->num_rows > 0) {
                $student_data = $result->fetch_assoc();
                $deleted_relations['student_id'] = $student_data['id'];

                // حذف الطالب
                $stmt2 = $conn->prepare("DELETE FROM students WHERE user_id = ?");
                $stmt2->bind_param("i", $user_id);
                $stmt2->execute();
                $stmt2->close();
            }
            $stmt->close();

            // Check for teacher data
            $stmt = $conn->prepare("SELECT id FROM teachers WHERE user_id = ?");
            $stmt->bind_param("i", $user_id);
            $stmt->execute();
            $result = $stmt->get_result();
            if ($result->num_rows > 0) {
                $teacher_data = $result->fetch_assoc();
                $deleted_relations['teacher_id'] = $teacher_data['id'];

                // حذف المعلم
                $stmt2 = $conn->prepare("DELETE FROM teachers WHERE user_id = ?");
                $stmt2->bind_param("i", $user_id);
                $stmt2->execute();
                $stmt2->close();
            }
            $stmt->close();

            // Check for staff data (administrators)
            $stmt = $conn->prepare("SELECT id FROM staff WHERE user_id = ?");
            $stmt->bind_param("i", $user_id);
            $stmt->execute();
            $result = $stmt->get_result();
            if ($result->num_rows > 0) {
                $staff_data = $result->fetch_assoc();
                $deleted_relations['staff_id'] = $staff_data['id'];

                // حذف سجلات الإجازات المرتبطة
                $stmt2 = $conn->prepare("DELETE FROM staff_leaves WHERE user_id = ?");
                $stmt2->bind_param("i", $user_id);
                $stmt2->execute();
                $stmt2->close();

                // حذف سجلات الحضور المرتبطة (إذا كانت موجودة)
                $check_table = $conn->query("SHOW TABLES LIKE 'staff_attendance'");
                if ($check_table->num_rows > 0) {
                    $stmt2 = $conn->prepare("DELETE FROM staff_attendance WHERE staff_id = ?");
                    $stmt2->bind_param("i", $staff_data['id']);
                    $stmt2->execute();
                    $stmt2->close();
                }

                // حذف الإداري
                $stmt2 = $conn->prepare("DELETE FROM staff WHERE user_id = ?");
                $stmt2->bind_param("i", $user_id);
                $stmt2->execute();
                $stmt2->close();
            }
            $stmt->close();

            // حذف المستخدم
            $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
            $stmt->bind_param("i", $user_id);
            if (!$stmt->execute()) {
                throw new Exception('Failed to delete user');
            }
            $stmt->close();

            $conn->commit();

            // إعادة تفعيل فحص القيود الخارجية
            $conn->query("SET FOREIGN_KEY_CHECKS = 1");

            // تسجيل النشاط
            if (function_exists('log_activity') && isset($_SESSION['user_id'])) {
                log_activity($_SESSION['user_id'], 'delete_' . $delete_type, 'users', $user_id, null, array_merge([
                    'user_id' => $user_id,
                    'delete_type' => $delete_type
                ], $deleted_relations));
            }

            return true;

        } catch (Exception $e) {
            $conn->rollback();
            // إعادة تفعيل فحص القيود الخارجية في حالة الخطأ
            $conn->query("SET FOREIGN_KEY_CHECKS = 1");
            throw $e;
        }
    }
}

/**
 * توليد كلمة مرور عشوائية
 * Generate random password
 */
if (!function_exists('generate_random_password')) {
    function generate_random_password($length = 8) {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $password = '';
        for ($i = 0; $i < $length; $i++) {
            $password .= $characters[rand(0, strlen($characters) - 1)];
        }
        return $password;
    }
}

/**
 * التحقق من صحة تنسيق التاريخ
 * Validate date format
 */
if (!function_exists('validateDate')) {
    function validateDate($date, $format = 'Y-m-d') {
        $d = DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }
}

// دالة hash_password موجودة في security.php

// الدوال التالية موجودة في security.php:
// - get_client_ip()
// - log_activity()
// - update_last_login()

/**
 * تنسيق الأرقام مع التعامل مع القيم الفارغة
 * Safe number formatting
 */
if (!function_exists('safe_number_format')) {
    function safe_number_format($number, $decimals = 0, $decimal_separator = '.', $thousands_separator = ',') {
        if ($number === null || $number === '') {
            return '0';
        }
        return number_format((float)$number, $decimals, $decimal_separator, $thousands_separator);
    }
}

/**
 * تحديث سجلات الحضور للإجازة
 * Update attendance records for leave
 */
if (!function_exists('update_attendance_for_leave')) {
    function update_attendance_for_leave($user_id, $user_role, $leave_status, $start_date, $end_date, $note = '') {
        global $conn;

        $updated_records = 0;

        try {
            if ($user_role === 'teacher') {
                // للمعلمين: تحديث جدول teacher_attendance
                $teacher_stmt = $conn->prepare("SELECT id FROM teachers WHERE user_id = ?");
                $teacher_stmt->bind_param("i", $user_id);
                $teacher_stmt->execute();
                $teacher_result = $teacher_stmt->get_result()->fetch_assoc();

                if ($teacher_result) {
                    // التحقق من وجود جدول teacher_attendance
                    $check_table = $conn->query("SHOW TABLES LIKE 'teacher_attendance'");
                    if ($check_table->num_rows > 0) {
                        $update_stmt = $conn->prepare("
                            UPDATE teacher_attendance
                            SET status = ?,
                                check_in_time = NULL,
                                check_out_time = NULL,
                                notes = CONCAT(COALESCE(notes, ''), IF(COALESCE(notes, '') = '', '', ' - '), ?)
                            WHERE teacher_id = ?
                            AND attendance_date BETWEEN ? AND ?
                        ");
                        $update_stmt->bind_param("ssiss", $leave_status, $note, $teacher_result['id'], $start_date, $end_date);
                        $update_stmt->execute();
                        $updated_records = $update_stmt->affected_rows;
                    }
                }
            } elseif ($user_role === 'student') {
                // للطلاب: تحديث جدول attendance
                $student_stmt = $conn->prepare("SELECT id FROM students WHERE user_id = ?");
                $student_stmt->bind_param("i", $user_id);
                $student_stmt->execute();
                $student_result = $student_stmt->get_result()->fetch_assoc();

                if ($student_result) {
                    $update_stmt = $conn->prepare("
                        UPDATE attendance
                        SET status = ?,
                            check_in_time = NULL,
                            check_out_time = NULL,
                            notes = CONCAT(COALESCE(notes, ''), IF(COALESCE(notes, '') = '', '', ' - '), ?)
                        WHERE student_id = ?
                        AND attendance_date BETWEEN ? AND ?
                    ");
                    $update_stmt->bind_param("ssiss", $leave_status, $note, $student_result['id'], $start_date, $end_date);
                    $update_stmt->execute();
                    $updated_records = $update_stmt->affected_rows;
                }
            } else {
                // للإداريين والموظفين: تحديث جدول admin_attendance (يفترض أن الجدول موجود بالفعل)
                $update_stmt = $conn->prepare("
                    UPDATE admin_attendance
                    SET status = ?,
                        check_in_time = NULL,
                        check_out_time = NULL,
                        notes = CONCAT(COALESCE(notes, ''), IF(COALESCE(notes, '') = '', '', ' - '), ?)
                    WHERE user_id = ?
                    AND attendance_date BETWEEN ? AND ?
                ");
                $update_stmt->bind_param("ssiss", $leave_status, $note, $user_id, $start_date, $end_date);
                $update_stmt->execute();
                $updated_records = $update_stmt->affected_rows;
            }
        } catch (Exception $e) {
            error_log("Error updating attendance for leave: " . $e->getMessage());
        }

        return $updated_records;
    }
}

/**
 * دالة مختصرة لتسجيل النشاط
 * Short function for logging activity
 */
if (!function_exists('log_activity')) {
    function log_activity($user_id, $action, $table_name = '', $record_id = null, $old_values = null, $new_values = null) {
        return log_user_activity($user_id, $action, $action, $table_name, $record_id, $old_values, $new_values);
    }
}

?>
