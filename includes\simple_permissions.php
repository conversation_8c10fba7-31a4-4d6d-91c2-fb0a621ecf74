<?php
/**
 * نظام صلاحيات بسيط ومخصص
 * Simple Custom Permissions System
 * 
 * هذا النظام منفصل تماماً عن الأنظمة الأخرى لتجنب التضارب
 */

// منع الوصول المباشر
if (!defined('SYSTEM_INIT') && !defined('FUNCTIONS_LOADED')) {
    die('Direct access not allowed');
}

// تحميل حماية المدير
if (file_exists(__DIR__ . '/admin_protection.php')) {
    require_once __DIR__ . '/admin_protection.php';
}

/**
 * التحقق من صلاحية المستخدم لصفحة معينة
 * 
 * @param string $page_name اسم الصفحة
 * @param string $action نوع العملية (view, add, edit, delete)
 * @param int|null $user_id معرف المستخدم (افتراضي: المستخدم الحالي)
 * @return bool
 */
function check_page_permission($page_name, $action = 'view', $user_id = null) {
    global $conn;

    // التحقق من تسجيل الدخول
    if (!isset($_SESSION['user_id'])) {
        return false;
    }

    // استخدام المستخدم الحالي إذا لم يتم تحديد معرف
    if ($user_id === null) {
        $user_id = $_SESSION['user_id'];
    }

    // استخدام حماية المدير المحسنة
    if (function_exists('is_protected_admin') && is_protected_admin($user_id)) {
        if (function_exists('log_admin_access')) {
            log_admin_access($page_name, $action);
        }
        return true;
    }

    // التحقق التقليدي من دور المدير (احتياطي)
    if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin') {
        return true;
    }
    
    try {
        // تحديد العمود المناسب حسب نوع العملية
        $column_map = [
            'view' => 'can_view',
            'add' => 'can_add', 
            'edit' => 'can_edit',
            'delete' => 'can_delete'
        ];
        
        if (!isset($column_map[$action])) {
            return false;
        }
        
        $column = $column_map[$action];
        
        // البحث عن الصلاحية في قاعدة البيانات
        $stmt = $conn->prepare("
            SELECT {$column} 
            FROM user_page_permissions 
            WHERE user_id = ? AND page_name = ? 
            LIMIT 1
        ");
        $stmt->bind_param("is", $user_id, $page_name);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            return (bool) $row[$column];
        }
        
        // إذا لم توجد صلاحية مخصصة، ارجع false
        return false;
        
    } catch (Exception $e) {
        error_log("Error in check_page_permission: " . $e->getMessage());
        return false;
    }
}

/**
 * التحقق من الصلاحية مع إعادة التوجيه
 * 
 * @param string $page_name اسم الصفحة
 * @param string $action نوع العملية
 * @param string $redirect_url رابط إعادة التوجيه
 */
function require_page_permission($page_name, $action = 'view', $redirect_url = '../dashboard/') {
    if (!check_page_permission($page_name, $action)) {
        // تسجيل محاولة الوصول غير المصرح بها
        log_permission_action('access_denied', $page_name, "محاولة $action غير مصرح بها");
        
        // إعادة التوجيه
        header('Location: ' . $redirect_url . '?error=access_denied&page=' . urlencode($page_name));
        exit();
    }
}

/**
 * الحصول على جميع صلاحيات المستخدم
 * 
 * @param int|null $user_id معرف المستخدم
 * @return array
 */
function get_user_page_permissions($user_id = null) {
    global $conn;
    
    if ($user_id === null) {
        $user_id = $_SESSION['user_id'] ?? 0;
    }
    
    // المدير العام له جميع الصلاحيات في كافة النظام
    if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin') {
        return 'all'; // إشارة خاصة للمدير
    }

    // التحقق المزدوج من دور المدير
    if (isset($_SESSION['user_id'])) {
        $stmt = $conn->prepare("SELECT role FROM users WHERE id = ? AND role = 'admin' LIMIT 1");
        $stmt->bind_param("i", $_SESSION['user_id']);
        $stmt->execute();
        if ($stmt->get_result()->num_rows > 0) {
            return 'all';
        }
    }
    
    try {
        $stmt = $conn->prepare("
            SELECT page_name, can_view, can_add, can_edit, can_delete 
            FROM user_page_permissions 
            WHERE user_id = ?
        ");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $permissions = [];
        while ($row = $result->fetch_assoc()) {
            $permissions[$row['page_name']] = [
                'view' => (bool) $row['can_view'],
                'add' => (bool) $row['can_add'],
                'edit' => (bool) $row['can_edit'],
                'delete' => (bool) $row['can_delete']
            ];
        }
        
        return $permissions;
        
    } catch (Exception $e) {
        error_log("Error in get_user_page_permissions: " . $e->getMessage());
        return [];
    }
}

/**
 * منح صلاحية للمستخدم
 * 
 * @param int $user_id معرف المستخدم
 * @param string $page_name اسم الصفحة
 * @param array $permissions الصلاحيات (view, add, edit, delete)
 * @param string $notes ملاحظات
 * @return bool
 */
function grant_page_permission($user_id, $page_name, $permissions, $notes = '') {
    global $conn;
    
    try {
        // التحقق من وجود الصفحة في قائمة الصفحات المتاحة
        $page_check = $conn->prepare("SELECT id FROM available_pages WHERE page_name = ?");
        $page_check->bind_param("s", $page_name);
        $page_check->execute();
        
        if ($page_check->get_result()->num_rows == 0) {
            return false; // الصفحة غير موجودة
        }
        
        // تحضير البيانات
        $can_view = isset($permissions['view']) ? (int) $permissions['view'] : 0;
        $can_add = isset($permissions['add']) ? (int) $permissions['add'] : 0;
        $can_edit = isset($permissions['edit']) ? (int) $permissions['edit'] : 0;
        $can_delete = isset($permissions['delete']) ? (int) $permissions['delete'] : 0;
        
        // إدراج أو تحديث الصلاحية
        $stmt = $conn->prepare("
            INSERT INTO user_page_permissions 
            (user_id, page_name, can_view, can_add, can_edit, can_delete, notes) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE 
            can_view = VALUES(can_view),
            can_add = VALUES(can_add),
            can_edit = VALUES(can_edit),
            can_delete = VALUES(can_delete),
            notes = VALUES(notes),
            updated_at = CURRENT_TIMESTAMP
        ");
        
        $stmt->bind_param("isiiiis", $user_id, $page_name, $can_view, $can_add, $can_edit, $can_delete, $notes);
        $success = $stmt->execute();
        
        if ($success) {
            // تسجيل العملية
            $details = "منح صلاحيات: " . implode(', ', array_keys(array_filter($permissions)));
            log_permission_action('permission_granted', $page_name, $details, $user_id);
        }
        
        return $success;
        
    } catch (Exception $e) {
        error_log("Error in grant_page_permission: " . $e->getMessage());
        return false;
    }
}

/**
 * إلغاء صلاحية المستخدم
 * 
 * @param int $user_id معرف المستخدم
 * @param string $page_name اسم الصفحة
 * @return bool
 */
function revoke_page_permission($user_id, $page_name) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            DELETE FROM user_page_permissions 
            WHERE user_id = ? AND page_name = ?
        ");
        $stmt->bind_param("is", $user_id, $page_name);
        $success = $stmt->execute();
        
        if ($success) {
            log_permission_action('permission_revoked', $page_name, 'إلغاء جميع الصلاحيات', $user_id);
        }
        
        return $success;
        
    } catch (Exception $e) {
        error_log("Error in revoke_page_permission: " . $e->getMessage());
        return false;
    }
}

/**
 * الحصول على قائمة الصفحات المتاحة
 * 
 * @return array
 */
function get_available_pages() {
    global $conn;
    
    try {
        $result = $conn->query("
            SELECT page_name, page_title, page_path, category, description,
                   requires_add, requires_edit, requires_delete
            FROM available_pages 
            WHERE is_active = 1 
            ORDER BY category, page_title
        ");
        
        $pages = [];
        while ($row = $result->fetch_assoc()) {
            $pages[] = $row;
        }
        
        return $pages;
        
    } catch (Exception $e) {
        error_log("Error in get_available_pages: " . $e->getMessage());
        return [];
    }
}

/**
 * تسجيل عملية في سجل الصلاحيات
 * 
 * @param string $action نوع العملية
 * @param string $page_name اسم الصفحة
 * @param string $details تفاصيل العملية
 * @param int|null $target_user_id المستخدم المستهدف
 */
function log_permission_action($action, $page_name = null, $details = null, $target_user_id = null) {
    global $conn;
    
    try {
        $user_id = $_SESSION['user_id'] ?? 0;
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
        
        // إضافة معلومات المستخدم المستهدف إذا كان مختلفاً
        if ($target_user_id && $target_user_id != $user_id) {
            $details = "المستخدم المستهدف: $target_user_id - " . $details;
        }
        
        $stmt = $conn->prepare("
            INSERT INTO permissions_log 
            (user_id, action, page_name, details, ip_address) 
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->bind_param("issss", $user_id, $action, $page_name, $details, $ip_address);
        $stmt->execute();
        
    } catch (Exception $e) {
        error_log("Error in log_permission_action: " . $e->getMessage());
    }
}

/**
 * دالة مساعدة للتحقق من تسجيل الدخول
 */
function is_user_logged_in() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * دالة مساعدة للتحقق من كون المستخدم مدير
 */
function is_admin_user() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
}

/**
 * عرض رسالة خطأ الصلاحيات
 */
function show_permission_denied($page_name = '', $action = '') {
    $message = 'غير مسموح لك بالوصول لهذه الصفحة';
    if ($page_name) {
        $message .= ": $page_name";
    }
    if ($action) {
        $message .= " (العملية: $action)";
    }
    
    http_response_code(403);
    die('
    <div class="container mt-5">
        <div class="alert alert-danger text-center">
            <i class="fas fa-lock fa-3x mb-3"></i>
            <h4>وصول مرفوض</h4>
            <p>' . htmlspecialchars($message) . '</p>
            <a href="../dashboard/" class="btn btn-primary">العودة للرئيسية</a>
        </div>
    </div>
    ');
}

// تسجيل أن النظام البسيط تم تحميله
define('SIMPLE_PERMISSIONS_LOADED', true);
?>
