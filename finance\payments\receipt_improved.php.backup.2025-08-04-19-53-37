<?php
require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/security.php';

// التحقق من الجلسة والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

// جلب معرف المدفوعة
$payment_id = intval($_GET['id'] ?? 0);

if ($payment_id <= 0) {
    header('Location: index.php?error=' . urlencode('معرف المدفوعة غير صحيح'));
    exit();
}

// جلب بيانات المدفوعة مع تفاصيل الطالب والرسم
$stmt = $conn->prepare("
    SELECT 
        sp.*,
        u.full_name as student_name,
        s.student_id as student_number,
        s.parent_name,
        s.parent_phone,
        s.address,
        c.class_name,
        c.grade_level,
        sf.final_amount as fee_amount,
        sf.base_amount,
        sf.discount_amount,
        ft.type_name as fee_type_name,
        pu.full_name as processed_by_name
    FROM student_payments sp
    JOIN students s ON sp.student_id = s.id
    JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    LEFT JOIN student_fees sf ON sp.student_fee_id = sf.id
    LEFT JOIN fee_types ft ON sf.fee_type_id = ft.id
    LEFT JOIN users pu ON sp.processed_by = pu.id
    WHERE sp.id = ?
");

if (!$stmt) {
    die("خطأ في تحضير الاستعلام: " . $conn->error);
}

$stmt->bind_param("i", $payment_id);
$stmt->execute();
$payment = $stmt->get_result()->fetch_assoc();

if (!$payment) {
    header('Location: index.php?error=' . urlencode('المدفوعة غير موجودة'));
    exit();
}

// جلب بيانات المدرسة من الإعدادات
$school_name = get_system_setting('school_name', 'مدرسة النموذج');
$school_address = get_system_setting('school_address', '');
$school_phone = get_system_setting('school_phone', '');
$school_email = get_system_setting('school_email', '');
$school_logo = get_system_setting('school_logo', '');

$page_title = __('payment_receipt');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        @media print {
            .no-print { display: none !important; }
            .container { max-width: none !important; }
            body { font-size: 12px; }
        }
        .receipt-header {
            border-bottom: 3px solid #28a745;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .school-logo {
            max-height: 80px;
            max-width: 80px;
        }
        .receipt-title {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
        }
        .info-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .amount-highlight {
            background: #e8f5e8;
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }
        .payment-details {
            background: #f0f8f0;
            border: 1px solid #28a745;
            border-radius: 8px;
            padding: 15px;
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <!-- أزرار التحكم -->
        <div class="no-print mb-3">
            <div class="d-flex justify-content-between">
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i><?php echo __('back'); ?>
                </a>
                <button onclick="window.print()" class="btn btn-primary">
                    <i class="fas fa-print me-2"></i><?php echo __('print'); ?>
                </button>
            </div>
        </div>

        <!-- الإيصال -->
        <div class="card shadow-lg">
            <div class="card-body">
                <!-- هيدر المدرسة -->
                <div class="receipt-header">
                    <div class="row align-items-center">
                        <div class="col-md-2 text-center">
                            <?php if (!empty($school_logo)): ?>
                                <img src="../../uploads/<?php echo htmlspecialchars($school_logo); ?>" alt="شعار المدرسة" class="school-logo">
                            <?php else: ?>
                                <div class="bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                                    <i class="fas fa-school fa-2x"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-8 text-center">
                            <h2 class="text-success mb-1"><?php echo htmlspecialchars($school_name); ?></h2>
                            <?php if (!empty($school_address)): ?>
                                <p class="mb-1"><i class="fas fa-map-marker-alt me-2"></i><?php echo htmlspecialchars($school_address); ?></p>
                            <?php endif; ?>
                            <div class="row">
                                <?php if (!empty($school_phone)): ?>
                                <div class="col-md-6">
                                    <small><i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($school_phone); ?></small>
                                </div>
                                <?php endif; ?>
                                <?php if (!empty($school_email)): ?>
                                <div class="col-md-6">
                                    <small><i class="fas fa-envelope me-1"></i><?php echo htmlspecialchars($school_email); ?></small>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-2 text-center">
                            <div class="border rounded p-2">
                                <small class="text-muted"><?php echo __('receipt_number'); ?></small><br>
                                <strong><?php echo htmlspecialchars($payment['receipt_number'] ?? '#' . str_pad($payment['id'], 6, '0', STR_PAD_LEFT)); ?></strong>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- عنوان الإيصال -->
                <div class="receipt-title">
                    <h3 class="mb-0"><i class="fas fa-receipt me-2"></i><?php echo __('payment_receipt'); ?></h3>
                </div>

                <!-- معلومات الطالب -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-box">
                            <h6 class="text-success mb-3"><i class="fas fa-user-graduate me-2"></i><?php echo __('student_information'); ?></h6>
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td><strong><?php echo __('student_name'); ?>:</strong></td>
                                    <td><?php echo htmlspecialchars($payment['student_name']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong><?php echo __('student_number'); ?>:</strong></td>
                                    <td><?php echo htmlspecialchars($payment['student_number']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong><?php echo __('class'); ?>:</strong></td>
                                    <td><?php echo htmlspecialchars($payment['class_name'] ?? 'غير محدد'); ?> - <?php echo htmlspecialchars($payment['grade_level'] ?? ''); ?></td>
                                </tr>
                                <?php if (!empty($payment['parent_name'])): ?>
                                <tr>
                                    <td><strong><?php echo __('parent_name'); ?>:</strong></td>
                                    <td><?php echo htmlspecialchars($payment['parent_name']); ?></td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-box">
                            <h6 class="text-success mb-3"><i class="fas fa-file-invoice me-2"></i><?php echo __('payment_information'); ?></h6>
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td><strong><?php echo __('payment_date'); ?>:</strong></td>
                                    <td><?php echo date('Y-m-d', strtotime($payment['payment_date'])); ?></td>
                                </tr>
                                <tr>
                                    <td><strong><?php echo __('payment_method'); ?>:</strong></td>
                                    <td><?php echo __($payment['payment_method']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong><?php echo __('fee_type'); ?>:</strong></td>
                                    <td><?php echo htmlspecialchars($payment['fee_type_name'] ?? __('general_payment')); ?></td>
                                </tr>
                                <tr>
                                    <td><strong><?php echo __('processed_by'); ?>:</strong></td>
                                    <td><?php echo htmlspecialchars($payment['processed_by_name'] ?? 'النظام'); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- المبلغ المدفوع -->
                <div class="amount-highlight mb-4">
                    <div class="row text-center">
                        <div class="col-md-12">
                            <h2 class="text-success mb-2"><?php echo number_format($payment['amount'], 2); ?> <?php echo get_system_setting('currency_symbol', 'ر.س'); ?></h2>
                            <h5 class="text-muted"><?php echo __('paid_amount'); ?></h5>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل الدفع -->
                <div class="payment-details">
                    <h6 class="text-success mb-3"><i class="fas fa-info-circle me-2"></i><?php echo __('payment_details'); ?></h6>
                    <div class="row">
                        <?php if (!empty($payment['payment_reference'])): ?>
                        <div class="col-md-6">
                            <strong><?php echo __('payment_reference'); ?>:</strong> <?php echo htmlspecialchars($payment['payment_reference']); ?>
                        </div>
                        <?php endif; ?>
                        <?php if (!empty($payment['bank_name'])): ?>
                        <div class="col-md-6">
                            <strong><?php echo __('bank_name'); ?>:</strong> <?php echo htmlspecialchars($payment['bank_name']); ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php if (!empty($payment['notes'])): ?>
                    <div class="mt-2">
                        <strong><?php echo __('notes'); ?>:</strong><br>
                        <?php echo nl2br(htmlspecialchars($payment['notes'])); ?>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- فوتر الإيصال -->
                <div class="text-center mt-4 pt-3 border-top">
                    <small class="text-muted">
                        <?php echo __('receipt_generated_on'); ?>: <?php echo date('Y-m-d H:i:s'); ?><br>
                        <?php echo __('system_generated_receipt'); ?>
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
