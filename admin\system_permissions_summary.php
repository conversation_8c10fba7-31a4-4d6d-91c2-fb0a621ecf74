<?php
/**
 * ملخص شامل لنظام الصلاحيات
 * Comprehensive Permissions System Summary
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

$user_email = '<EMAIL>';

// جلب بيانات المستخدم
$user_query = "SELECT * FROM users WHERE email = ?";
$user_stmt = $conn->prepare($user_query);
$user_stmt->bind_param("s", $user_email);
$user_stmt->execute();
$user = $user_stmt->get_result()->fetch_assoc();

// فحص حالة النظام
$system_status = [
    'user_exists' => $user !== null,
    'user_role' => $user['role'] ?? 'غير محدد',
    'tables_exist' => false,
    'permissions_granted' => 0,
    'pages_fixed' => false
];

// فحص الجداول
try {
    $tables_check = $conn->query("SHOW TABLES LIKE 'system_resources'");
    $permissions_check = $conn->query("SHOW TABLES LIKE 'user_custom_permissions'");
    $system_status['tables_exist'] = ($tables_check->num_rows > 0 && $permissions_check->num_rows > 0);
} catch (Exception $e) {
    $system_status['tables_exist'] = false;
}

// عد الصلاحيات الممنوحة
if ($user && $system_status['tables_exist']) {
    try {
        $permissions_count = $conn->prepare("SELECT COUNT(*) as count FROM user_custom_permissions WHERE user_id = ? AND is_granted = 1");
        $permissions_count->bind_param("i", $user['id']);
        $permissions_count->execute();
        $result = $permissions_count->get_result()->fetch_assoc();
        $system_status['permissions_granted'] = $result['count'];
    } catch (Exception $e) {
        $system_status['permissions_granted'] = 0;
    }
}

// فحص عينة من الصفحات
$sample_pages = ['students/index.php', 'teachers/index.php', 'classes/index.php'];
$fixed_pages = 0;

foreach ($sample_pages as $page) {
    $file_path = "../$page";
    if (file_exists($file_path)) {
        $content = file_get_contents($file_path);
        if (strpos($content, "check_permission('staff')") !== false) {
            $fixed_pages++;
        }
    }
}

$system_status['pages_fixed'] = ($fixed_pages > 0);

$page_title = 'ملخص نظام الصلاحيات';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-chart-line me-2"></i><?php echo $page_title; ?></h2>
            <p class="text-muted">نظرة شاملة على حالة نظام الصلاحيات للمعلم والإداري</p>
        </div>
        <a href="../settings/permissions.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة
        </a>
    </div>

    <!-- حالة النظام العامة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-user fa-2x text-<?php echo $system_status['user_exists'] ? 'success' : 'danger'; ?> mb-2"></i>
                    <h5><?php echo $system_status['user_exists'] ? 'موجود' : 'مفقود'; ?></h5>
                    <p class="mb-0">المستخدم الإداري</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-database fa-2x text-<?php echo $system_status['tables_exist'] ? 'success' : 'danger'; ?> mb-2"></i>
                    <h5><?php echo $system_status['tables_exist'] ? 'جاهزة' : 'مفقودة'; ?></h5>
                    <p class="mb-0">جداول النظام</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-key fa-2x text-<?php echo $system_status['permissions_granted'] > 0 ? 'success' : 'warning'; ?> mb-2"></i>
                    <h5><?php echo $system_status['permissions_granted']; ?></h5>
                    <p class="mb-0">الصلاحيات الممنوحة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-file-code fa-2x text-<?php echo $system_status['pages_fixed'] ? 'success' : 'warning'; ?> mb-2"></i>
                    <h5><?php echo $system_status['pages_fixed'] ? 'محدثة' : 'تحتاج إصلاح'; ?></h5>
                    <p class="mb-0">صفحات النظام</p>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات المستخدم -->
    <?php if ($user): ?>
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5><i class="fas fa-user me-2"></i>معلومات المستخدم الإداري</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>الاسم:</strong><br>
                        <?php echo safe_html($user['full_name']); ?>
                    </div>
                    <div class="col-md-3">
                        <strong>البريد الإلكتروني:</strong><br>
                        <?php echo safe_html($user['email']); ?>
                    </div>
                    <div class="col-md-3">
                        <strong>الدور:</strong><br>
                        <span class="badge bg-<?php 
                            echo match($user['role']) {
                                'admin' => 'danger',
                                'teacher' => 'success',
                                'staff' => 'warning',
                                'student' => 'info',
                                default => 'secondary'
                            };
                        ?>">
                            <?php echo get_role_name_arabic($user['role']); ?>
                        </span>
                    </div>
                    <div class="col-md-3">
                        <strong>الحالة:</strong><br>
                        <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : 'danger'; ?>">
                            <?php echo $user['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- خطة العمل -->
    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5><i class="fas fa-tasks me-2"></i>خطة العمل الموصى بها</h5>
        </div>
        <div class="card-body">
            <?php
            $steps = [];
            
            if (!$system_status['user_exists']) {
                $steps[] = ['danger', 'إنشاء المستخدم الإداري', 'يجب إنشاء حساب للمستخدم <EMAIL> أولاً'];
            }
            
            if (!$system_status['tables_exist']) {
                $steps[] = ['warning', 'إنشاء جداول النظام', 'setup_staff_permissions.php'];
            }
            
            if ($system_status['permissions_granted'] == 0) {
                $steps[] = ['info', 'منح الصلاحيات', 'staff_permissions_manager.php'];
            }
            
            if (!$system_status['pages_fixed']) {
                $steps[] = ['primary', 'إصلاح صفحات النظام', 'fix_all_pages.php'];
            }
            
            if (empty($steps)) {
                $steps[] = ['success', 'النظام جاهز!', 'جميع المكونات تعمل بشكل صحيح'];
            }
            ?>
            
            <div class="row">
                <?php foreach ($steps as $index => $step): ?>
                    <div class="col-md-6 mb-3">
                        <div class="alert alert-<?php echo $step[0]; ?>">
                            <h6><i class="fas fa-<?php echo $index + 1; ?> me-2"></i><?php echo $step[1]; ?></h6>
                            <p class="mb-0"><?php echo $step[2]; ?></p>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- أدوات النظام -->
    <div class="card">
        <div class="card-header bg-secondary text-white">
            <h5><i class="fas fa-toolbox me-2"></i>أدوات النظام</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-search fa-2x text-info mb-3"></i>
                            <h6>فحص النظام</h6>
                            <p class="small text-muted">فحص شامل لجميع صفحات النظام</p>
                            <a href="scan_all_pages.php" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-search me-1"></i>فحص
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-tools fa-2x text-danger mb-3"></i>
                            <h6>إصلاح شامل</h6>
                            <p class="small text-muted">إصلاح جميع صفحات النظام تلقائياً</p>
                            <a href="fix_all_pages.php" class="btn btn-outline-danger btn-sm">
                                <i class="fas fa-tools me-1"></i>إصلاح
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-user-shield fa-2x text-success mb-3"></i>
                            <h6>إعداد الصلاحيات</h6>
                            <p class="small text-muted">إعداد صلاحيات الإداري من البداية</p>
                            <a href="setup_staff_permissions.php" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-cogs me-1"></i>إعداد
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-vial fa-2x text-warning mb-3"></i>
                            <h6>اختبار النظام</h6>
                            <p class="small text-muted">اختبار وصول الإداري للبيانات</p>
                            <a href="test_staff_access.php" class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-vial me-1"></i>اختبار
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- التقييم العام -->
    <div class="card mt-4">
        <div class="card-header bg-info text-white">
            <h5><i class="fas fa-chart-pie me-2"></i>التقييم العام للنظام</h5>
        </div>
        <div class="card-body">
            <?php
            $score = 0;
            $max_score = 4;
            
            if ($system_status['user_exists']) $score++;
            if ($system_status['tables_exist']) $score++;
            if ($system_status['permissions_granted'] > 0) $score++;
            if ($system_status['pages_fixed']) $score++;
            
            $percentage = ($score / $max_score) * 100;
            $status_class = $percentage >= 75 ? 'success' : ($percentage >= 50 ? 'warning' : 'danger');
            $status_text = $percentage >= 75 ? 'ممتاز' : ($percentage >= 50 ? 'جيد' : 'يحتاج عمل');
            ?>
            
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h4>حالة النظام: <span class="text-<?php echo $status_class; ?>"><?php echo $status_text; ?></span></h4>
                    <div class="progress mb-3" style="height: 20px;">
                        <div class="progress-bar bg-<?php echo $status_class; ?>" 
                             style="width: <?php echo $percentage; ?>%">
                            <?php echo round($percentage); ?>%
                        </div>
                    </div>
                    <p class="mb-0">
                        <?php if ($percentage >= 75): ?>
                            🎉 النظام يعمل بشكل ممتاز! يمكن للمعلم والإداري الوصول لجميع الصفحات المطلوبة.
                        <?php elseif ($percentage >= 50): ?>
                            ⚠️ النظام يعمل جزئياً. بعض المكونات تحتاج إصلاح أو تحديث.
                        <?php else: ?>
                            ❌ النظام يحتاج عمل كبير. يجب إكمال الإعداد قبل الاستخدام.
                        <?php endif; ?>
                    </p>
                </div>
                <div class="col-md-4 text-center">
                    <div class="display-1 text-<?php echo $status_class; ?>">
                        <?php echo $score; ?>/<?php echo $max_score; ?>
                    </div>
                    <p class="text-muted">المكونات الجاهزة</p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
