<?php
/**
 * إنشاء جداول النظام البسيط للصلاحيات
 * Create Simple Permissions System Tables
 */

require_once '../includes/config.php';
require_once '../includes/database.php';

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من الصلاحيات (مدير فقط)
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/?error=access_denied');
    exit();
}

$success_message = '';
$error_message = '';
$tables_created = [];

// إنشاء الجداول
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_tables'])) {
    try {
        // جدول الصلاحيات المخصصة للمستخدمين
        $sql1 = "CREATE TABLE IF NOT EXISTS `user_page_permissions` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `user_id` int(10) UNSIGNED NOT NULL,
          `page_name` varchar(100) NOT NULL COMMENT 'اسم الصفحة أو المسار',
          `can_view` tinyint(1) DEFAULT 0 COMMENT 'صلاحية المشاهدة',
          `can_add` tinyint(1) DEFAULT 0 COMMENT 'صلاحية الإضافة',
          `can_edit` tinyint(1) DEFAULT 0 COMMENT 'صلاحية التعديل',
          `can_delete` tinyint(1) DEFAULT 0 COMMENT 'صلاحية الحذف',
          `notes` text DEFAULT NULL COMMENT 'ملاحظات',
          `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          UNIQUE KEY `user_page_unique` (`user_id`, `page_name`),
          KEY `idx_user_id` (`user_id`),
          KEY `idx_page_name` (`page_name`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($conn->query($sql1)) {
            $tables_created[] = 'user_page_permissions';
        }

        // جدول الصفحات المتاحة
        $sql2 = "CREATE TABLE IF NOT EXISTS `available_pages` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `page_name` varchar(100) NOT NULL COMMENT 'اسم الصفحة',
          `page_title` varchar(200) NOT NULL COMMENT 'عنوان الصفحة',
          `page_path` varchar(255) NOT NULL COMMENT 'مسار الصفحة',
          `category` varchar(50) DEFAULT NULL COMMENT 'فئة الصفحة',
          `description` text DEFAULT NULL COMMENT 'وصف الصفحة',
          `requires_add` tinyint(1) DEFAULT 0 COMMENT 'تحتاج صلاحية إضافة',
          `requires_edit` tinyint(1) DEFAULT 0 COMMENT 'تحتاج صلاحية تعديل',
          `requires_delete` tinyint(1) DEFAULT 0 COMMENT 'تحتاج صلاحية حذف',
          `is_active` tinyint(1) DEFAULT 1,
          PRIMARY KEY (`id`),
          UNIQUE KEY `page_name_unique` (`page_name`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($conn->query($sql2)) {
            $tables_created[] = 'available_pages';
        }

        // جدول سجل العمليات البسيط
        $sql3 = "CREATE TABLE IF NOT EXISTS `permissions_log` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `user_id` int(10) UNSIGNED NOT NULL,
          `action` varchar(50) NOT NULL,
          `page_name` varchar(100) DEFAULT NULL,
          `details` text DEFAULT NULL,
          `ip_address` varchar(45) DEFAULT NULL,
          `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          KEY `idx_user_id` (`user_id`),
          KEY `idx_action` (`action`),
          KEY `idx_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($conn->query($sql3)) {
            $tables_created[] = 'permissions_log';
        }

        // إدراج البيانات الأساسية
        $pages_count = $conn->query("SELECT COUNT(*) as count FROM available_pages")->fetch_assoc()['count'];
        
        if ($pages_count == 0) {
            $basic_pages = [
                ['students', 'إدارة الطلاب', '/students/', 'الطلاب', 'عرض وإدارة بيانات الطلاب', 1, 1, 1],
                ['attendance', 'الحضور والغياب', '/attendance/', 'الحضور', 'تسجيل ومتابعة حضور الطلاب', 1, 1, 0],
                ['grades', 'الدرجات', '/grades/', 'الدرجات', 'إدارة درجات الطلاب', 0, 1, 0],
                ['finance', 'الشؤون المالية', '/finance/', 'المالية', 'إدارة الرسوم والمدفوعات', 1, 1, 1],
                ['reports', 'التقارير', '/reports/', 'التقارير', 'عرض وطباعة التقارير', 0, 0, 0],
                ['users', 'إدارة المستخدمين', '/admin/users/', 'الإدارة', 'إدارة حسابات المستخدمين', 1, 1, 1],
                ['settings', 'الإعدادات', '/settings/', 'الإدارة', 'إعدادات النظام', 0, 1, 0],
                ['exams', 'الامتحانات', '/exams/', 'الامتحانات', 'إدارة الامتحانات والنتائج', 1, 1, 1],
                ['library', 'المكتبة', '/library/', 'المكتبة', 'إدارة الكتب والمراجع', 1, 1, 1],
                ['communication', 'التواصل', '/communication/', 'التواصل', 'الرسائل والإشعارات', 1, 0, 1]
            ];
            
            $stmt = $conn->prepare("
                INSERT INTO available_pages 
                (page_name, page_title, page_path, category, description, requires_add, requires_edit, requires_delete) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            foreach ($basic_pages as $page) {
                $stmt->bind_param("sssssiiii", ...$page);
                $stmt->execute();
            }
            
            $tables_created[] = 'البيانات الأساسية للصفحات';
        }

        // إنشاء الفهارس الإضافية
        $conn->query("CREATE INDEX IF NOT EXISTS idx_user_permissions ON user_page_permissions(user_id, page_name)");
        $conn->query("CREATE INDEX IF NOT EXISTS idx_page_permissions ON user_page_permissions(page_name, can_view, can_add, can_edit, can_delete)");

        $success_message = 'تم إنشاء جميع الجداول والبيانات بنجاح!';
        
    } catch (Exception $e) {
        $error_message = 'خطأ في إنشاء الجداول: ' . $e->getMessage();
    }
}

// فحص حالة الجداول
$tables_status = [];
$required_tables = ['user_page_permissions', 'available_pages', 'permissions_log'];

foreach ($required_tables as $table) {
    try {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        $tables_status[$table] = $result->num_rows > 0;
        
        if ($tables_status[$table]) {
            $count_result = $conn->query("SELECT COUNT(*) as count FROM $table");
            $tables_status[$table . '_count'] = $count_result->fetch_assoc()['count'];
        }
    } catch (Exception $e) {
        $tables_status[$table] = false;
    }
}

$page_title = 'إنشاء جداول النظام البسيط';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <!-- رأس الصفحة -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="fas fa-database me-2 text-primary"></i><?php echo $page_title; ?></h2>
                <p class="text-muted">إنشاء الجداول المطلوبة للنظام البسيط للصلاحيات</p>
            </div>
        </div>

        <!-- رسائل التنبيه -->
        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (!empty($tables_created)): ?>
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle me-2"></i>الجداول المنشأة:</h6>
                <ul class="mb-0">
                    <?php foreach ($tables_created as $table): ?>
                        <li><?php echo $table; ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- حالة الجداول -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-table me-2"></i>حالة الجداول</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>اسم الجدول</th>
                                        <th>الحالة</th>
                                        <th>عدد السجلات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($required_tables as $table): ?>
                                        <tr>
                                            <td><code><?php echo $table; ?></code></td>
                                            <td>
                                                <?php if ($tables_status[$table]): ?>
                                                    <span class="badge bg-success">موجود</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">غير موجود</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (isset($tables_status[$table . '_count'])): ?>
                                                    <span class="badge bg-info"><?php echo $tables_status[$table . '_count']; ?></span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <?php 
                        $all_tables_exist = array_reduce($required_tables, function($carry, $table) use ($tables_status) {
                            return $carry && $tables_status[$table];
                        }, true);
                        ?>
                        
                        <?php if (!$all_tables_exist): ?>
                            <form method="POST" class="mt-3">
                                <input type="hidden" name="create_tables" value="1">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-plus me-2"></i>إنشاء الجداول المفقودة
                                </button>
                            </form>
                        <?php else: ?>
                            <div class="alert alert-success mt-3">
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>ممتاز!</strong> جميع الجداول موجودة ويمكن استخدام النظام.
                            </div>
                            
                            <div class="text-center mt-3">
                                <a href="simple_permissions_manager.php" class="btn btn-success btn-lg me-2">
                                    <i class="fas fa-shield-alt me-2"></i>بدء إدارة الصلاحيات
                                </a>
                                <a href="test_simple_permissions.php" class="btn btn-primary btn-lg">
                                    <i class="fas fa-vial me-2"></i>اختبار النظام
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات الجداول -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle me-2"></i>معلومات الجداول</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6><code>user_page_permissions</code></h6>
                                <p class="text-muted">يحتوي على الصلاحيات المخصصة لكل مستخدم على الصفحات المختلفة</p>
                            </div>
                            <div class="col-md-4">
                                <h6><code>available_pages</code></h6>
                                <p class="text-muted">يحتوي على قائمة الصفحات المتاحة في النظام وأنواع الصلاحيات المطلوبة</p>
                            </div>
                            <div class="col-md-4">
                                <h6><code>permissions_log</code></h6>
                                <p class="text-muted">يحتوي على سجل جميع عمليات الصلاحيات والوصول</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
