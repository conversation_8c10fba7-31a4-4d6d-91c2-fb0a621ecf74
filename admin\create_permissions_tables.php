<?php
/**
 * إنشاء جداول نظام الصلاحيات
 * Create Permissions System Tables
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

$success_message = '';
$error_message = '';
$results = [];

// معالجة إنشاء الجداول
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_tables'])) {
    $conn->begin_transaction();
    
    try {
        // 1. إنشاء جدول system_resources
        $create_resources_table = "
        CREATE TABLE IF NOT EXISTS `system_resources` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `resource_type` enum('page','action','data','report') NOT NULL,
            `resource_key` varchar(100) NOT NULL,
            `resource_name` varchar(255) NOT NULL,
            `resource_description` text,
            `resource_path` varchar(255) DEFAULT NULL,
            `parent_resource` varchar(100) DEFAULT NULL,
            `icon` varchar(100) DEFAULT NULL,
            `sort_order` int(11) DEFAULT 0,
            `is_active` tinyint(1) DEFAULT 1,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `resource_key` (`resource_key`),
            KEY `resource_type` (`resource_type`),
            KEY `is_active` (`is_active`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        if ($conn->query($create_resources_table)) {
            $results[] = "✅ تم إنشاء جدول system_resources";
        } else {
            throw new Exception("فشل في إنشاء جدول system_resources: " . $conn->error);
        }
        
        // 2. إنشاء جدول user_custom_permissions
        $create_permissions_table = "
        CREATE TABLE IF NOT EXISTS `user_custom_permissions` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) NOT NULL,
            `permission_key` varchar(100) NOT NULL,
            `is_granted` tinyint(1) DEFAULT 1,
            `granted_by` int(11) DEFAULT NULL,
            `granted_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `revoked_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `user_permission` (`user_id`,`permission_key`),
            KEY `user_id` (`user_id`),
            KEY `permission_key` (`permission_key`),
            KEY `is_granted` (`is_granted`),
            FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        if ($conn->query($create_permissions_table)) {
            $results[] = "✅ تم إنشاء جدول user_custom_permissions";
        } else {
            throw new Exception("فشل في إنشاء جدول user_custom_permissions: " . $conn->error);
        }
        
        // 3. إنشاء جدول permissions_audit_log
        $create_audit_table = "
        CREATE TABLE IF NOT EXISTS `permissions_audit_log` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) NOT NULL,
            `action_type` varchar(50) NOT NULL,
            `target_type` varchar(50) NOT NULL,
            `target_id` int(11) DEFAULT NULL,
            `old_value` text,
            `new_value` text,
            `ip_address` varchar(45) DEFAULT NULL,
            `user_agent` text,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `user_id` (`user_id`),
            KEY `action_type` (`action_type`),
            KEY `created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        if ($conn->query($create_audit_table)) {
            $results[] = "✅ تم إنشاء جدول permissions_audit_log";
        } else {
            throw new Exception("فشل في إنشاء جدول permissions_audit_log: " . $conn->error);
        }
        
        // 4. إضافة الموارد الأساسية
        $basic_resources = [
            ['page', 'classes_view', 'عرض الفصول', 'صفحة عرض قائمة الفصول الدراسية', '/classes/', '', 'fas fa-school', 100],
            ['page', 'subjects_view', 'عرض المواد', 'صفحة عرض قائمة المواد الدراسية', '/subjects/', '', 'fas fa-book', 110],
            ['page', 'exams_view', 'عرض الامتحانات', 'صفحة عرض قائمة الامتحانات', '/exams/', '', 'fas fa-file-alt', 120],
            ['page', 'reports_view', 'عرض التقارير', 'صفحة عرض التقارير العامة', '/reports/', '', 'fas fa-chart-bar', 130],
            ['report', 'student_reports', 'تقارير الطلاب', 'تقارير خاصة بالطلاب', '/reports/students.php', 'reports_view', 'fas fa-user-graduate', 131],
            ['report', 'attendance_reports', 'تقارير الحضور', 'تقارير الحضور والغياب', '/reports/attendance.php', 'reports_view', 'fas fa-calendar-check', 132],
            ['report', 'exam_reports', 'تقارير الامتحانات', 'تقارير الامتحانات والدرجات', '/reports/exams.php', 'reports_view', 'fas fa-file-alt', 133],
            ['report', 'financial_reports', 'التقارير المالية', 'التقارير المالية والمحاسبية', '/reports/financial.php', 'reports_view', 'fas fa-money-bill-wave', 134]
        ];
        
        $resource_stmt = $conn->prepare("
            INSERT IGNORE INTO system_resources 
            (resource_type, resource_key, resource_name, resource_description, resource_path, parent_resource, icon, sort_order, is_active) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)
        ");
        
        $added_resources = 0;
        foreach ($basic_resources as $resource) {
            $resource_stmt->bind_param("sssssssi", ...$resource);
            if ($resource_stmt->execute() && $resource_stmt->affected_rows > 0) {
                $added_resources++;
            }
        }
        
        $results[] = "📦 تم إضافة $added_resources مورد أساسي";
        
        // 5. منح الصلاحيات للمستخدم <EMAIL>
        $user_query = "SELECT id FROM users WHERE email = '<EMAIL>'";
        $user_result = $conn->query($user_query);
        
        if ($user_result && $user_result->num_rows > 0) {
            $user = $user_result->fetch_assoc();
            $user_id = $user['id'];
            
            $permissions_to_grant = ['classes_view', 'subjects_view', 'exams_view', 'reports_view', 'student_reports', 'attendance_reports', 'exam_reports'];
            
            // حذف الصلاحيات الموجودة أولاً
            $delete_stmt = $conn->prepare("DELETE FROM user_custom_permissions WHERE user_id = ?");
            $delete_stmt->bind_param("i", $user_id);
            $delete_stmt->execute();
            
            // إضافة الصلاحيات الجديدة
            $permission_stmt = $conn->prepare("
                INSERT INTO user_custom_permissions 
                (user_id, permission_key, is_granted, granted_by, granted_at) 
                VALUES (?, ?, 1, ?, NOW())
            ");
            
            $granted_permissions = 0;
            foreach ($permissions_to_grant as $permission) {
                $permission_stmt->bind_param("isi", $user_id, $permission, $_SESSION['user_id']);
                if ($permission_stmt->execute()) {
                    $granted_permissions++;
                }
            }
            
            $results[] = "🔑 تم منح $granted_permissions صلاحية للمستخدم <EMAIL>";
        } else {
            $results[] = "⚠️ المستخدم <EMAIL> غير موجود";
        }
        
        // 6. تسجيل في سجل المراجعة
        $audit_stmt = $conn->prepare("
            INSERT INTO permissions_audit_log 
            (user_id, action_type, target_type, target_id, old_value, new_value, ip_address, user_agent, created_at)
            VALUES (?, 'system_setup', 'permissions_system', NULL, 'no_system', 'full_system_created', ?, 'Admin Panel', NOW())
        ");
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
        $audit_stmt->bind_param("is", $_SESSION['user_id'], $ip_address);
        $audit_stmt->execute();
        
        $conn->commit();
        $success_message = "تم إنشاء نظام الصلاحيات بنجاح!";
        
    } catch (Exception $e) {
        $conn->rollback();
        $error_message = "خطأ في إنشاء النظام: " . $e->getMessage();
    }
}

// التحقق من حالة الجداول
$tables_status = [];
$required_tables = ['system_resources', 'user_custom_permissions', 'permissions_audit_log'];

foreach ($required_tables as $table) {
    $check = $conn->query("SHOW TABLES LIKE '$table'");
    $tables_status[$table] = $check->num_rows > 0;
}

$all_tables_exist = !in_array(false, $tables_status);

$page_title = 'إنشاء جداول نظام الصلاحيات';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-database me-2"></i><?php echo $page_title; ?></h2>
        </div>
        <a href="../settings/permissions.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة
        </a>
    </div>

    <!-- رسائل التنبيه -->
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- نتائج العملية -->
    <?php if (!empty($results)): ?>
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle me-2"></i>نتائج العملية:</h6>
            <ul class="mb-0">
                <?php foreach ($results as $result): ?>
                    <li><?php echo $result; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <?php if ($all_tables_exist): ?>
                <!-- جميع الجداول موجودة -->
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-check-circle me-2"></i>نظام الصلاحيات جاهز</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <h6><i class="fas fa-thumbs-up me-2"></i>ممتاز!</h6>
                            <p class="mb-0">جميع جداول نظام الصلاحيات موجودة ويمكن استخدام النظام الآن.</p>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-success">الجداول الموجودة:</h6>
                                <ul class="list-group">
                                    <?php foreach ($tables_status as $table => $exists): ?>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <?php echo $table; ?>
                                            <span class="badge bg-success">✓</span>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-info">الخطوات التالية:</h6>
                                <div class="d-grid gap-2">
                                    <a href="quick_fix_tamaly.php" class="btn btn-outline-primary">
                                        <i class="fas fa-tools me-2"></i>إصلاح سريع لـ tamaly
                                    </a>
                                    <a href="debug_user_permissions.php" class="btn btn-outline-info">
                                        <i class="fas fa-bug me-2"></i>تشخيص الصلاحيات
                                    </a>
                                    <a href="grant_tamaly_permissions.php" class="btn btn-outline-success">
                                        <i class="fas fa-user-shield me-2"></i>منح صلاحيات
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <!-- بعض الجداول مفقودة -->
                <div class="card border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>جداول مفقودة</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-info-circle me-2"></i>المشكلة:</h6>
                            <p class="mb-0">بعض جداول نظام الصلاحيات مفقودة. يجب إنشاؤها أولاً قبل استخدام النظام.</p>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-danger">الجداول المفقودة:</h6>
                                <ul class="list-group">
                                    <?php foreach ($tables_status as $table => $exists): ?>
                                        <?php if (!$exists): ?>
                                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                                <?php echo $table; ?>
                                                <span class="badge bg-danger">مفقود</span>
                                            </li>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success">الجداول الموجودة:</h6>
                                <ul class="list-group">
                                    <?php foreach ($tables_status as $table => $exists): ?>
                                        <?php if ($exists): ?>
                                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                                <?php echo $table; ?>
                                                <span class="badge bg-success">موجود</span>
                                            </li>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>

                        <form method="POST" class="mt-4">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="confirmCreate" required>
                                <label class="form-check-label" for="confirmCreate">
                                    <strong>أؤكد أنني أريد إنشاء جداول نظام الصلاحيات</strong>
                                </label>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="../settings/permissions.php" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>إلغاء
                                </a>
                                <button type="submit" name="create_tables" class="btn btn-warning" id="createButton" disabled>
                                    <i class="fas fa-database me-2"></i>إنشاء الجداول
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// تفعيل زر الإنشاء عند تأكيد الاختيار
document.getElementById('confirmCreate')?.addEventListener('change', function() {
    document.getElementById('createButton').disabled = !this.checked;
});
</script>

<?php include_once '../includes/footer.php'; ?>
