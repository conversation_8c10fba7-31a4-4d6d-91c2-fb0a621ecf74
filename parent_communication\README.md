# نظام إدارة التواصل مع أولياء الأمور

## نظرة عامة
نظام شامل لإدارة التواصل مع أولياء الأمور يتضمن واجهة تشبه واتساب، إدارة تقارير السلوك، والرسائل الجماعية.

## الميزات الرئيسية

### 1. واجهة واتساب (WhatsApp Interface)
- **الملف**: `whatsapp_interface.php`
- **الوصف**: واجهة محادثات تشبه واتساب لعرض وإدارة المحادثات مع أولياء الأمور
- **الميزات**:
  - قائمة جهات الاتصال مع البحث
  - عرض المحادثات في تصميم يشبه واتساب
  - إرسال سريع للرسائل
  - تحديث تلقائي للرسائل
  - حالات الرسائل (مرسل، تم التسليم، تم القراءة)

### 2. تقارير السلوك (Behavior Reports)
- **الملف**: `student_reports.php`
- **الوصف**: إضافة وإدارة تقارير سلوك الطلاب مع إشعار تلقائي لأولياء الأمور
- **الميزات**:
  - إضافة تقارير سلوك جديدة
  - تصنيف السلوك (ممتاز، جيد، يحتاج تحسين، مثير للقلق)
  - إشعار تلقائي لأولياء الأمور عبر واتساب
  - عرض التقارير الحديثة

### 3. الرسائل الجماعية (Bulk Messages)
- **الملف**: `bulk_message.php`
- **الوصف**: إرسال رسائل جماعية لمجموعات من أولياء الأمور
- **الميزات**:
  - إرسال لجميع الطلاب
  - إرسال حسب الفصول المحددة
  - إرسال حسب الصفوف المحددة
  - معاينة عدد المستقبلين
  - تتبع حالة الإرسال

### 4. لوحة التحكم الرئيسية
- **الملف**: `index.php`
- **الوصف**: لوحة تحكم شاملة مع إحصائيات ووصول سريع للميزات
- **الميزات**:
  - إحصائيات الرسائل والتقارير
  - أدوات سريعة للوصول للميزات
  - عرض الرسائل الحديثة

## الملفات المساعدة

### 1. خدمة واتساب
- **الملف**: `WhatsAppService.php`
- **الوصف**: فئة لإدارة إرسال الرسائل عبر واتساب

### 2. ملفات API
- **`get_messages.php`**: API لجلب الرسائل (AJAX)
- **`send_message_ajax.php`**: API لإرسال الرسائل (AJAX)

### 3. ملفات التصميم والتفاعل
- **`assets/whatsapp-style.css`**: أنماط CSS خاصة بواجهة واتساب
- **`assets/whatsapp-interface.js`**: JavaScript للتفاعل والميزات المتقدمة

## قاعدة البيانات

### الجداول المطلوبة:

#### 1. parent_communications
```sql
CREATE TABLE parent_communications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    parent_phone VARCHAR(20) NOT NULL,
    message_type ENUM('general', 'behavior', 'academic', 'attendance', 'emergency') DEFAULT 'general',
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    sent_via ENUM('whatsapp', 'sms', 'email') DEFAULT 'whatsapp',
    status ENUM('pending', 'sent', 'delivered', 'read', 'failed') DEFAULT 'pending',
    sent_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sent_at TIMESTAMP NULL,
    delivered_at TIMESTAMP NULL,
    read_at TIMESTAMP NULL,
    FOREIGN KEY (student_id) REFERENCES students(id),
    FOREIGN KEY (sent_by) REFERENCES users(id)
);
```

#### 2. student_behavior_reports
```sql
CREATE TABLE student_behavior_reports (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    report_date DATE NOT NULL,
    behavior_type ENUM('excellent', 'good', 'needs_improvement', 'concerning') NOT NULL,
    category ENUM('discipline', 'participation', 'homework', 'social', 'academic') NOT NULL,
    description TEXT NOT NULL,
    action_taken TEXT,
    teacher_id INT NOT NULL,
    notify_parent BOOLEAN DEFAULT FALSE,
    communication_id INT NULL,
    parent_notified_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id),
    FOREIGN KEY (teacher_id) REFERENCES teachers(id),
    FOREIGN KEY (communication_id) REFERENCES parent_communications(id)
);
```

#### 3. message_templates
```sql
CREATE TABLE message_templates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    template_name VARCHAR(255) NOT NULL,
    message_type ENUM('general', 'behavior', 'academic', 'attendance', 'emergency') NOT NULL,
    subject VARCHAR(255) NOT NULL,
    message_content TEXT NOT NULL,
    variables JSON,
    created_by INT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);
```

## التثبيت والإعداد

### 1. متطلبات النظام
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache/Nginx)
- مكتبة cURL لـ PHP

### 2. خطوات التثبيت
1. نسخ ملفات النظام إلى مجلد `parent_communication`
2. تشغيل استعلامات قاعدة البيانات لإنشاء الجداول
3. تحديث إعدادات قاعدة البيانات في `../includes/config.php`
4. إعداد خدمة واتساب في `WhatsAppService.php`
5. التأكد من صلاحيات الملفات والمجلدات

### 3. الإعدادات المطلوبة
- إعداد API واتساب (إذا كان متاحاً)
- إعداد خدمة الرسائل النصية (اختياري)
- إعداد خدمة البريد الإلكتروني (اختياري)

## الاستخدام

### 1. الوصول للنظام
- الرابط الرئيسي: `parent_communication/index.php`
- واجهة واتساب: `parent_communication/whatsapp_interface.php`
- تقارير السلوك: `parent_communication/student_reports.php`
- الرسائل الجماعية: `parent_communication/bulk_message.php`

### 2. الصلاحيات المطلوبة
- المدير: وصول كامل لجميع الميزات
- المعلم: وصول للميزات الأساسية

### 3. سير العمل
1. إضافة أرقام هواتف أولياء الأمور في بيانات الطلاب
2. استخدام واجهة واتساب للمحادثات الفردية
3. إضافة تقارير السلوك مع الإشعار التلقائي
4. استخدام الرسائل الجماعية للإعلانات العامة

## الميزات المتقدمة

### 1. التحديث التلقائي
- تحديث الرسائل كل 30 ثانية
- إشعارات فورية للرسائل الجديدة

### 2. البحث والتصفية
- البحث في جهات الاتصال
- تصفية الرسائل حسب النوع والحالة

### 3. التقارير والإحصائيات
- إحصائيات الرسائل المرسلة
- تقارير السلوك
- معدلات التسليم والقراءة

## الدعم والصيانة

### 1. ملفات السجل
- سجلات الأخطاء في ملفات PHP
- سجلات الرسائل في قاعدة البيانات

### 2. النسخ الاحتياطي
- نسخ احتياطي منتظم لقاعدة البيانات
- نسخ احتياطي لملفات النظام

### 3. التحديثات
- تحديث منتظم للنظام
- مراجعة الأمان والأداء

## المطورون
- تم تطوير النظام كجزء من نظام إدارة المدارس الشامل
- يدعم التوسعات والتخصيصات المستقبلية

## الترخيص
- النظام مطور للاستخدام التعليمي
- يمكن تخصيصه حسب احتياجات المدرسة
