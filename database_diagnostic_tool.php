<?php
/**
 * أداة تشخيص قاعدة البيانات
 * Database Diagnostic Tool
 */

define('SYSTEM_INIT', true);
require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/functions.php';

$page_title = 'أداة تشخيص قاعدة البيانات';

// بدء الجلسة للتحقق من الصلاحيات
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من الصلاحيات (السماح للمدير فقط)
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    echo '<div style="text-align: center; margin: 50px; font-family: Arial, sans-serif;">';
    echo '<h2>🔒 غير مسموح بالوصول</h2>';
    echo '<p>هذه الأداة متاحة للمديرين فقط</p>';
    echo '<a href="login.php" style="color: #007bff;">تسجيل الدخول</a>';
    echo '</div>';
    exit();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .diagnostic-card { margin-bottom: 20px; }
        .status-ok { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
        .code-block { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; }
    </style>
</head>
<body>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3><i class="fas fa-stethoscope"></i> أداة تشخيص قاعدة البيانات</h3>
                    <small>تشخيص شامل لمشاكل عدم عرض البيانات عند نقل المشروع</small>
                </div>
                <div class="card-body">

                    <?php
                    $diagnostics = [];
                    $overall_status = 'ok';

                    // 1. فحص إعدادات قاعدة البيانات
                    echo '<div class="diagnostic-card">';
                    echo '<h4><i class="fas fa-cog"></i> 1. إعدادات قاعدة البيانات</h4>';
                    echo '<div class="row">';
                    echo '<div class="col-md-6">';
                    echo '<table class="table table-sm">';
                    echo '<tr><td><strong>الخادم (Host):</strong></td><td>' . DB_HOST . '</td></tr>';
                    echo '<tr><td><strong>قاعدة البيانات:</strong></td><td>' . DB_NAME . '</td></tr>';
                    echo '<tr><td><strong>المستخدم:</strong></td><td>' . DB_USER . '</td></tr>';
                    echo '<tr><td><strong>الترميز:</strong></td><td>' . DB_CHARSET . '</td></tr>';
                    echo '</table>';
                    echo '</div>';
                    echo '<div class="col-md-6">';
                    
                    // فحص الاتصال
                    if (check_database_connection()) {
                        echo '<div class="alert alert-success"><i class="fas fa-check status-ok"></i> الاتصال بقاعدة البيانات ناجح</div>';
                        $diagnostics['connection'] = 'ok';
                    } else {
                        echo '<div class="alert alert-danger"><i class="fas fa-times status-error"></i> فشل الاتصال بقاعدة البيانات</div>';
                        $diagnostics['connection'] = 'error';
                        $overall_status = 'error';
                    }
                    
                    echo '</div>';
                    echo '</div>';
                    echo '</div>';

                    // 2. فحص وجود قاعدة البيانات
                    echo '<div class="diagnostic-card">';
                    echo '<h4><i class="fas fa-database"></i> 2. فحص قاعدة البيانات</h4>';
                    
                    if ($diagnostics['connection'] === 'ok') {
                        // فحص وجود قاعدة البيانات
                        $db_exists = $conn->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '" . DB_NAME . "'");
                        if ($db_exists && $db_exists->num_rows > 0) {
                            echo '<div class="alert alert-success"><i class="fas fa-check status-ok"></i> قاعدة البيانات <strong>' . DB_NAME . '</strong> موجودة</div>';
                            $diagnostics['database'] = 'ok';
                        } else {
                            echo '<div class="alert alert-danger"><i class="fas fa-times status-error"></i> قاعدة البيانات <strong>' . DB_NAME . '</strong> غير موجودة</div>';
                            $diagnostics['database'] = 'error';
                            $overall_status = 'error';
                        }
                    } else {
                        echo '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle status-warning"></i> لا يمكن فحص قاعدة البيانات بسبب فشل الاتصال</div>';
                        $diagnostics['database'] = 'error';
                    }
                    echo '</div>';

                    // 3. فحص الجداول المطلوبة
                    echo '<div class="diagnostic-card">';
                    echo '<h4><i class="fas fa-table"></i> 3. فحص الجداول المطلوبة</h4>';
                    
                    if ($diagnostics['connection'] === 'ok' && $diagnostics['database'] === 'ok') {
                        $required_tables = [
                            'users' => 'جدول المستخدمين',
                            'educational_stages' => 'جدول المراحل التعليمية',
                            'grades' => 'جدول الصفوف الدراسية',
                            'classes' => 'جدول الفصول',
                            'subjects' => 'جدول المواد',
                            'students' => 'جدول الطلاب',
                            'teachers' => 'جدول المعلمين',
                            'staff_absences_with_deduction' => 'جدول الغياب بالخصم',
                            'deduction_settings' => 'جدول إعدادات الخصم',
                            'attendance' => 'جدول الحضور العام',
                            'bank_accounts' => 'جدول الحسابات البنكية',
                            'bank_account_fee_types' => 'جدول ربط الحسابات بأنواع الرسوم',
                            'fee_types' => 'جدول أنواع الرسوم'
                        ];
                        
                        $missing_tables = [];
                        $tables_status = 'ok';
                        
                        echo '<div class="row">';
                        foreach ($required_tables as $table => $description) {
                            $check = $conn->query("SHOW TABLES LIKE '$table'");
                            $exists = $check && $check->num_rows > 0;
                            
                            if (!$exists) {
                                $missing_tables[] = $table;
                                $tables_status = 'error';
                            }
                            
                            echo '<div class="col-md-6 mb-2">';
                            echo '<div class="d-flex align-items-center">';
                            echo $exists ? '<i class="fas fa-check status-ok me-2"></i>' : '<i class="fas fa-times status-error me-2"></i>';
                            echo '<span>' . $description . ' (<code>' . $table . '</code>)</span>';
                            echo '</div>';
                            echo '</div>';
                        }
                        echo '</div>';
                        
                        if ($tables_status === 'ok') {
                            echo '<div class="alert alert-success mt-3"><i class="fas fa-check status-ok"></i> جميع الجداول المطلوبة موجودة</div>';
                            $diagnostics['tables'] = 'ok';
                        } else {
                            echo '<div class="alert alert-danger mt-3"><i class="fas fa-times status-error"></i> الجداول التالية مفقودة: <strong>' . implode(', ', $missing_tables) . '</strong></div>';
                            $diagnostics['tables'] = 'error';
                            $overall_status = 'error';
                        }
                    } else {
                        echo '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle status-warning"></i> لا يمكن فحص الجداول بسبب مشاكل في الاتصال أو قاعدة البيانات</div>';
                        $diagnostics['tables'] = 'error';
                    }
                    echo '</div>';

                    // 4. فحص البيانات في الجداول الأساسية
                    echo '<div class="diagnostic-card">';
                    echo '<h4><i class="fas fa-chart-bar"></i> 4. فحص البيانات</h4>';
                    
                    if ($diagnostics['tables'] === 'ok') {
                        $data_tables = [
                            'users' => 'المستخدمين',
                            'educational_stages' => 'المراحل التعليمية',
                            'grades' => 'الصفوف الدراسية',
                            'classes' => 'الفصول',
                            'staff_absences_with_deduction' => 'سجلات الغياب بالخصم'
                        ];
                        
                        echo '<div class="row">';
                        $empty_tables = [];
                        foreach ($data_tables as $table => $description) {
                            $count_result = $conn->query("SELECT COUNT(*) as count FROM $table");
                            $count = $count_result ? $count_result->fetch_assoc()['count'] : 0;
                            
                            if ($count == 0) {
                                $empty_tables[] = $table;
                            }
                            
                            echo '<div class="col-md-4 mb-2">';
                            echo '<div class="card">';
                            echo '<div class="card-body text-center">';
                            echo '<h5 class="card-title">' . $description . '</h5>';
                            echo '<h3 class="' . ($count > 0 ? 'status-ok' : 'status-warning') . '">' . $count . '</h3>';
                            echo '<small class="text-muted">سجل</small>';
                            echo '</div>';
                            echo '</div>';
                            echo '</div>';
                        }
                        echo '</div>';
                        
                        if (empty($empty_tables)) {
                            echo '<div class="alert alert-success mt-3"><i class="fas fa-check status-ok"></i> جميع الجداول تحتوي على بيانات</div>';
                            $diagnostics['data'] = 'ok';
                        } else {
                            echo '<div class="alert alert-warning mt-3"><i class="fas fa-exclamation-triangle status-warning"></i> الجداول التالية فارغة: <strong>' . implode(', ', $empty_tables) . '</strong></div>';
                            $diagnostics['data'] = 'warning';
                            if ($overall_status === 'ok') $overall_status = 'warning';
                        }
                    } else {
                        echo '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle status-warning"></i> لا يمكن فحص البيانات بسبب مشاكل في الجداول</div>';
                        $diagnostics['data'] = 'error';
                    }
                    echo '</div>';

                    // 5. التشخيص النهائي والحلول
                    echo '<div class="diagnostic-card">';
                    echo '<h4><i class="fas fa-clipboard-check"></i> 5. التشخيص النهائي والحلول</h4>';
                    
                    if ($overall_status === 'ok') {
                        echo '<div class="alert alert-success">';
                        echo '<h5><i class="fas fa-check-circle status-ok"></i> النظام يعمل بشكل صحيح</h5>';
                        echo '<p>جميع الفحوصات نجحت. إذا كنت لا تزال تواجه مشاكل في عرض البيانات، تحقق من:</p>';
                        echo '<ul>';
                        echo '<li>فلاتر البحث في الصفحات</li>';
                        echo '<li>صلاحيات المستخدم المسجل دخوله</li>';
                        echo '<li>إعدادات العرض والترقيم</li>';
                        echo '</ul>';
                        echo '</div>';
                    } else {
                        echo '<div class="alert alert-danger">';
                        echo '<h5><i class="fas fa-exclamation-triangle status-error"></i> تم اكتشاف مشاكل تحتاج إلى إصلاح</h5>';
                        echo '<p><strong>الحلول المقترحة:</strong></p>';
                        echo '<ol>';
                        
                        if ($diagnostics['connection'] === 'error') {
                            echo '<li><strong>مشكلة الاتصال:</strong> تحقق من إعدادات قاعدة البيانات في <code>config/database.php</code></li>';
                        }
                        
                        if ($diagnostics['database'] === 'error') {
                            echo '<li><strong>قاعدة البيانات مفقودة:</strong> أنشئ قاعدة بيانات باسم <code>' . DB_NAME . '</code> واستورد ملف <code>database/school_management.sql</code></li>';
                        }
                        
                        if ($diagnostics['tables'] === 'error') {
                            echo '<li><strong>جداول مفقودة:</strong> أعد استيراد ملف <code>database/school_management.sql</code> بالكامل</li>';
                        }
                        
                        if ($diagnostics['data'] === 'warning') {
                            echo '<li><strong>بيانات مفقودة:</strong> تأكد من استيراد البيانات مع الجداول، أو أضف البيانات يدوياً</li>';
                        }
                        
                        echo '</ol>';
                        echo '</div>';
                    }
                    
                    // عرض الأوامر المفيدة
                    echo '<div class="mt-4">';
                    echo '<h5><i class="fas fa-terminal"></i> أوامر مفيدة:</h5>';
                    echo '<div class="code-block">';
                    echo '<strong>إنشاء قاعدة البيانات:</strong><br>';
                    echo 'CREATE DATABASE ' . DB_NAME . ' CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;<br><br>';
                    echo '<strong>استيراد البيانات:</strong><br>';
                    echo 'mysql -u ' . DB_USER . ' -p ' . DB_NAME . ' < database/school_management.sql<br><br>';
                    echo '<strong>فحص الجداول:</strong><br>';
                    echo 'SHOW TABLES;<br>';
                    echo 'SELECT COUNT(*) FROM educational_stages;<br>';
                    echo '</div>';
                    echo '</div>';
                    
                    echo '</div>';
                    ?>

                    <div class="mt-4 text-center">
                        <a href="dashboard/" class="btn btn-primary">
                            <i class="fas fa-home"></i> العودة للوحة التحكم
                        </a>
                        <button onclick="location.reload()" class="btn btn-secondary">
                            <i class="fas fa-sync"></i> إعادة الفحص
                        </button>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
