<?php
/**
 * تحديث حالات الأقساط تلقائياً
 * Auto Update Installment Status
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

/**
 * تحديث حالات الأقساط بناءً على تاريخ الاستحقاق وحالة الدفع
 */
function updateInstallmentStatuses() {
    global $conn;
    
    $updated_count = 0;
    $errors = [];
    
    try {
        $conn->begin_transaction();
        
        // 1. تحديث الأقساط المتأخرة (تاريخ الاستحقاق مضى وحالة معلقة)
        $overdue_query = "
            UPDATE student_installments 
            SET status = 'overdue', updated_at = NOW() 
            WHERE due_date < CURDATE() 
            AND status = 'pending'
            AND paid_amount < total_amount
        ";
        
        $overdue_result = $conn->query($overdue_query);
        if ($overdue_result) {
            $overdue_count = $conn->affected_rows;
            $updated_count += $overdue_count;
            
            if ($overdue_count > 0) {
                log_activity(
                    $_SESSION['user_id'] ?? 1, 
                    'auto_update_overdue', 
                    'student_installments', 
                    null, 
                    null, 
                    ['count' => $overdue_count]
                );
            }
        }
        
        // 2. تحديث الأقساط المدفوعة بالكامل
        $paid_query = "
            UPDATE student_installments 
            SET status = 'paid', updated_at = NOW() 
            WHERE paid_amount >= total_amount 
            AND total_amount > 0
            AND status != 'paid'
        ";
        
        $paid_result = $conn->query($paid_query);
        if ($paid_result) {
            $paid_count = $conn->affected_rows;
            $updated_count += $paid_count;
            
            if ($paid_count > 0) {
                log_activity(
                    $_SESSION['user_id'] ?? 1, 
                    'auto_update_paid', 
                    'student_installments', 
                    null, 
                    null, 
                    ['count' => $paid_count]
                );
            }
        }
        
        // 3. تحديث الأقساط المدفوعة جزئياً
        $partial_query = "
            UPDATE student_installments 
            SET status = 'partial', updated_at = NOW() 
            WHERE paid_amount > 0 
            AND paid_amount < total_amount 
            AND status NOT IN ('paid', 'partial')
        ";
        
        $partial_result = $conn->query($partial_query);
        if ($partial_result) {
            $partial_count = $conn->affected_rows;
            $updated_count += $partial_count;
            
            if ($partial_count > 0) {
                log_activity(
                    $_SESSION['user_id'] ?? 1, 
                    'auto_update_partial', 
                    'student_installments', 
                    null, 
                    null, 
                    ['count' => $partial_count]
                );
            }
        }
        
        // 4. إعادة تعيين الأقساط المتأخرة التي تم دفعها
        $reset_overdue_query = "
            UPDATE student_installments 
            SET status = 'paid', updated_at = NOW() 
            WHERE status = 'overdue' 
            AND paid_amount >= total_amount 
            AND total_amount > 0
        ";
        
        $reset_result = $conn->query($reset_overdue_query);
        if ($reset_result) {
            $reset_count = $conn->affected_rows;
            $updated_count += $reset_count;
            
            if ($reset_count > 0) {
                log_activity(
                    $_SESSION['user_id'] ?? 1, 
                    'auto_reset_overdue_to_paid', 
                    'student_installments', 
                    null, 
                    null, 
                    ['count' => $reset_count]
                );
            }
        }
        
        // 5. إعادة تعيين الأقساط المتأخرة إلى معلقة إذا لم يحن موعد الاستحقاق بعد
        $reset_pending_query = "
            UPDATE student_installments 
            SET status = 'pending', updated_at = NOW() 
            WHERE status = 'overdue' 
            AND due_date >= CURDATE()
            AND paid_amount < total_amount
        ";
        
        $reset_pending_result = $conn->query($reset_pending_query);
        if ($reset_pending_result) {
            $reset_pending_count = $conn->affected_rows;
            $updated_count += $reset_pending_count;
            
            if ($reset_pending_count > 0) {
                log_activity(
                    $_SESSION['user_id'] ?? 1, 
                    'auto_reset_overdue_to_pending', 
                    'student_installments', 
                    null, 
                    null, 
                    ['count' => $reset_pending_count]
                );
            }
        }
        
        $conn->commit();
        
        return [
            'success' => true,
            'updated_count' => $updated_count,
            'details' => [
                'overdue' => $overdue_count ?? 0,
                'paid' => $paid_count ?? 0,
                'partial' => $partial_count ?? 0,
                'reset_paid' => $reset_count ?? 0,
                'reset_pending' => $reset_pending_count ?? 0
            ]
        ];
        
    } catch (Exception $e) {
        $conn->rollback();
        
        log_error("Error updating installment statuses: " . $e->getMessage());
        
        return [
            'success' => false,
            'error' => $e->getMessage(),
            'updated_count' => 0
        ];
    }
}

// تنفيذ التحديث
$result = updateInstallmentStatuses();

// إذا كان الطلب AJAX، إرجاع JSON
if (isset($_GET['ajax']) && $_GET['ajax'] == '1') {
    header('Content-Type: application/json');
    echo json_encode($result);
    exit();
}

// إذا كان طلب عادي، إعادة توجيه مع رسالة
if ($result['success']) {
    $message = "تم تحديث حالات {$result['updated_count']} قسط بنجاح";
    if ($result['updated_count'] > 0) {
        $details = $result['details'];
        $message .= " (متأخرة: {$details['overdue']}, مدفوعة: {$details['paid']}, جزئية: {$details['partial']})";
    }
    header('Location: index.php?success=' . urlencode($message));
} else {
    header('Location: index.php?error=' . urlencode('خطأ في تحديث حالات الأقساط: ' . $result['error']));
}
exit();
?>
