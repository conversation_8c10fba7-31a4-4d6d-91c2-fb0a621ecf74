<?php
/**
 * صفحة إضافة مدفوعة جديدة
 * Add New Payment Page
 */

require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/security.php';
require_once '../../includes/fee_functions.php';

// Start session and check permissions first
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

$error_message = '';
$success_message = '';

// Process form submission before any HTML output
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = __('invalid_request');
    } else {
        // جمع البيانات
        $student_id = intval($_POST['student_id'] ?? 0);
        $student_fee_id = intval($_POST['student_fee_id'] ?? 0);
        $amount = floatval($_POST['amount'] ?? 0);
        $payment_method = clean_input($_POST['payment_method'] ?? '');
        $payment_date = clean_input($_POST['payment_date'] ?? date('Y-m-d'));
        $bank_name = clean_input($_POST['bank_name'] ?? '');
        $check_number = clean_input($_POST['check_number'] ?? '');
        $transaction_id = clean_input($_POST['transaction_id'] ?? '');
        $receipt_number = clean_input($_POST['receipt_number'] ?? '');
        $notes = clean_input($_POST['notes'] ?? '');

        // إذا لم يتم اختيار رسم محدد، اجعل student_fee_id = null
        if ($student_fee_id == 0) {
            $student_fee_id = null;
        }

        // التحقق من صحة البيانات
        $errors = [];

        if (empty($student_id)) {
            $errors[] = __('student') . ' ' . __('required_field');
        }

        if (empty($amount) || $amount <= 0) {
            $errors[] = __('amount') . ' ' . __('required_field');
        }

        if (empty($payment_method)) {
            $errors[] = __('payment_method') . ' ' . __('required_field');
        }

        if (!validate_date($payment_date)) {
            $errors[] = __('invalid_payment_date');
        }

        // التحقق من المبلغ المتبقي للرسم
        if ($student_fee_id > 0) {
            $fee_stmt = $conn->prepare("
                SELECT 
                    sf.final_amount,
                    COALESCE(SUM(sp.amount), 0) as paid_amount
                FROM student_fees sf
                LEFT JOIN student_payments sp ON sf.id = sp.student_fee_id AND sp.status = 'confirmed'
                WHERE sf.id = ? AND sf.student_id = ?
                GROUP BY sf.id
            ");
            $fee_stmt->bind_param("ii", $student_fee_id, $student_id);
            $fee_stmt->execute();
            $fee_data = $fee_stmt->get_result()->fetch_assoc();

            if (!$fee_data) {
                $errors[] = __('invalid_fee_selected');
            } else {
                $remaining_amount = $fee_data['final_amount'] - $fee_data['paid_amount'];
                if ($amount > $remaining_amount) {
                    $errors[] = __('amount_exceeds_remaining') . ': ' . number_format($remaining_amount, 2);
                }
            }
        }

        if (empty($errors)) {
            global $conn;
            $conn->begin_transaction();

            try {
                // إنشاء رقم مرجعي فريد
                $payment_reference = 'PAY-' . date('Ymd') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);

                // التحقق من عدم تكرار الرقم المرجعي
                $check_ref = $conn->prepare("SELECT id FROM student_payments WHERE payment_reference = ?");
                $check_ref->bind_param("s", $payment_reference);
                $check_ref->execute();
                while ($check_ref->get_result()->num_rows > 0) {
                    $payment_reference = 'PAY-' . date('Ymd') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
                    $check_ref->bind_param("s", $payment_reference);
                    $check_ref->execute();
                }

                // إدراج المدفوعة
                $payment_stmt = $conn->prepare("
                    INSERT INTO student_payments (
                        student_id, student_fee_id, payment_reference, amount, payment_method,
                        payment_date, bank_name, check_number, transaction_id, receipt_number,
                        notes, status, processed_by, processed_at, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'confirmed', ?, NOW(), NOW())
                ");
                if ($payment_stmt === false) {
                    die('Prepare failed: ' . $conn->error);
                }
                $payment_stmt->bind_param(
                    "iisdsssssssi",
                    $student_id, $student_fee_id, $payment_reference, $amount, $payment_method,
                    $payment_date, $bank_name, $check_number, $transaction_id, $receipt_number,
                    $notes, $_SESSION['user_id']
                );
                if (!$payment_stmt->execute()) {
                    die('Execute failed: ' . $payment_stmt->error);
                }
                $payment_id = $conn->insert_id;

                // تحديث حالة الرسم إذا كانت مرتبطة برسم محدد
                if ($student_fee_id > 0) {
                    if (!update_fee_status($student_fee_id, $conn)) {
                        throw new Exception(__('failed_to_update_fee_status'));
                    }
                }

                // تحديث الأقساط المرتبطة
                if ($student_fee_id > 0) {
                    $installments_stmt = $conn->prepare("
                        SELECT id, amount FROM student_installments 
                        WHERE student_fee_id = ? AND status = 'pending'
                        ORDER BY installment_number ASC
                    ");
                    if ($installments_stmt === false) {
                        die('Prepare failed: ' . $conn->error);
                    }
                    $installments_stmt->bind_param("i", $student_fee_id);
                    $installments_stmt->execute();
                    $installments = $installments_stmt->get_result();

                    $remaining_payment = $amount;
                    while (($installment = $installments->fetch_assoc()) && $remaining_payment > 0) {
                        if ($remaining_payment >= $installment['amount']) {
                            $sql_update = "UPDATE student_installments SET paid_amount = amount, paid_date = ?, status = 'paid' WHERE id = ?";
                            $update_installment = $conn->prepare($sql_update);
                            if (!$update_installment) {
                                echo "Prepare failed: " . $conn->error . "<br>SQL: " . htmlspecialchars($sql_update);
                                exit;
                            }
                            $update_installment->bind_param("si", $payment_date, $installment['id']);
                            $update_installment->execute();
                            $remaining_payment -= $installment['amount'];
                        } else {
                            $sql_update = "UPDATE student_installments SET paid_amount = paid_amount + ?, paid_date = ? WHERE id = ?";
                            $update_installment = $conn->prepare($sql_update);
                            if (!$update_installment) {
                                echo "Prepare failed: " . $conn->error . "<br>SQL: " . htmlspecialchars($sql_update);
                                exit;
                            }
                            $update_installment->bind_param("dsi", $remaining_payment, $payment_date, $installment['id']);
                            $update_installment->execute();
                            $remaining_payment = 0;
                        }
                    }
                }

                $conn->commit();

                // تسجيل النشاط
                log_activity($_SESSION['user_id'], 'add_payment', 'student_payments', $payment_id, null, [
                    'student_id' => $student_id,
                    'amount' => $amount,
                    'payment_method' => $payment_method
                ]);

                // إرسال إشعار للطالب
                add_notification($student_id, __('payment_received'), 
                    __('payment_received_message') . ': ' . number_format($amount, 2) . ' ' . CURRENCY_SYMBOL, 
                    'success', 'finance/payments/view.php?id=' . $payment_id);

                // بعد نجاح عملية الحفظ
                if ($payment_id > 0) {
                    // 1. حفظ رسالة نجاح في الجلسة
                $_SESSION['success_message'] = __('payment_added_successfully');
                    // 2. التوجيه إلى صفحة العرض مع التحقق من headers_sent()
                    $redirect_url = 'view.php?id=' . $payment_id;
                    if (headers_sent()) {
                        echo '<script>window.location.href="' . $redirect_url . '";</script>';
                    } else {
                        header('Location: ' . $redirect_url);
                    }
                exit();
                } else {
                    // في حالة فشل عملية الحفظ
                    $error_message = __('failed_to_create_payment') . '<br>' . htmlspecialchars($conn->error);
                }
            } catch (Exception $e) {
                $conn->rollback();
                log_error("Error adding payment: " . $e->getMessage());
                $error_message = __('error_occurred');
            }
        } else {
            $error_message = implode('<br>', $errors);
        }
    }
}

// Set page title and load header AFTER processing POST
$page_title = __('add_payment');
require_once '../../includes/header.php';

// Fetch data for the form display
// جلب قائمة الطلاب
$students = $conn->query("
    SELECT s.id, u.full_name, s.national_id, c.class_name
    FROM students s
    LEFT JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    ORDER BY u.full_name
");

// جلب الطالب المحدد مسبقاً إذا كان موجوداً
$selected_student_id = intval($_GET['student_id'] ?? $_POST['student_id'] ?? 0);
$student_fees = [];
if ($selected_student_id > 0) {
    $fees_query = "
        SELECT 
            sf.id,
            sf.final_amount,
            ft.type_name,
            sf.academic_year,
            sf.semester,
            COALESCE(SUM(sp.amount), 0) as paid_amount,
            (sf.final_amount - COALESCE(SUM(sp.amount), 0)) as remaining_amount
        FROM student_fees sf
        JOIN fee_types ft ON sf.fee_type_id = ft.id
        LEFT JOIN student_payments sp ON sf.id = sp.student_fee_id AND sp.status = 'confirmed'
        WHERE sf.student_id = ? AND sf.status IN ('pending', 'partial')
        GROUP BY sf.id
        HAVING remaining_amount > 0
    ";
    $fees_stmt = $conn->prepare($fees_query);
    if (!$fees_stmt) {
        echo '<div class="alert alert-danger">'.__('sql_prepare_failed').': '.htmlspecialchars($conn->error).'<br><pre>'.htmlspecialchars($fees_query).'</pre></div>';
    } else {
    $fees_stmt->bind_param("i", $selected_student_id);
    $fees_stmt->execute();
    $student_fees = $fees_stmt->get_result();
    }
}
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('add_payment'); ?></h1>
            <p class="text-muted"><?php echo __('record_new_payment'); ?></p>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
            </a>
        </div>
    </div>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Payment Form -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-credit-card me-2"></i><?php echo __('payment_details'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">

                        <!-- Student Selection -->
                        <div class="mb-3">
                            <label for="student_id" class="form-label"><?php echo __('student'); ?> <span class="text-danger">*</span></label>
                            <select class="form-select" id="student_id" name="student_id" required onchange="loadStudentFees()">
                                <option value=""><?php echo __('choose_student'); ?></option>
                                <?php while ($student = $students->fetch_assoc()): ?>
                                    <option value="<?php echo $student['id']; ?>" 
                                            <?php echo ($selected_student_id == $student['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($student['full_name'] . ' (' . ($student['national_id'] ?? '') . ')'); ?>
                                        <?php if (!empty($student['class_name'])): ?>
                                            - <?php echo htmlspecialchars($student['class_name']); ?>
                                        <?php endif; ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                            <div class="invalid-feedback">
                                <?php echo __('required_field'); ?>
                            </div>
                        </div>

                        <!-- Fee Selection -->
                        <div class="mb-3" id="fee_selection" style="<?php echo $selected_student_id > 0 ? '' : 'display: none;'; ?>">
                            <label for="student_fee_id" class="form-label"><?php echo __('related_fee'); ?></label>
                            <select class="form-select" id="student_fee_id" name="student_fee_id" onchange="updateMaxAmount()">
                                <option value=""><?php echo __('general_payment'); ?></option>
                                <?php if ($student_fees): ?>
                                    <?php while ($fee = $student_fees->fetch_assoc()): ?>
                                        <option value="<?php echo $fee['id']; ?>" 
                                                data-remaining="<?php echo $fee['remaining_amount']; ?>"
                                                <?php echo (($_POST['student_fee_id'] ?? '') == $fee['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($fee['type_name']); ?> - 
                                            <?php echo $fee['academic_year']; ?> (<?php echo __(strtolower($fee['semester'])); ?>)
                                            - <?php echo __('remaining'); ?>: <?php echo number_format($fee['remaining_amount'], 2); ?>
                                        </option>
                                    <?php endwhile; ?>
                                <?php endif; ?>
                            </select>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="amount" class="form-label"><?php echo __('amount'); ?> <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" 
                                               class="form-control" 
                                               id="amount" 
                                               name="amount" 
                                               step="0.01"
                                               min="0.01"
                                               value="<?php echo htmlspecialchars($_POST['amount'] ?? ''); ?>"
                                               required>
                                        <span class="input-group-text"><?php echo get_system_setting('currency_symbol', 'ر.س'); ?></span>
                                    </div>
                                    <div class="invalid-feedback">
                                        <?php echo __('required_field'); ?>
                                    </div>
                                    <div id="amount_help" class="form-text"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="payment_date" class="form-label"><?php echo __('payment_date'); ?> <span class="text-danger">*</span></label>
                                    <input type="date" 
                                           class="form-control" 
                                           id="payment_date" 
                                           name="payment_date" 
                                           value="<?php echo htmlspecialchars($_POST['payment_date'] ?? date('Y-m-d')); ?>"
                                           required>
                                    <div class="invalid-feedback">
                                        <?php echo __('required_field'); ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="payment_method" class="form-label"><?php echo __('payment_method'); ?> <span class="text-danger">*</span></label>
                            <select class="form-select" id="payment_method" name="payment_method" required onchange="togglePaymentFields()">
                                <option value=""><?php echo __('choose_method'); ?></option>
                                <option value="cash" <?php echo (($_POST['payment_method'] ?? '') === 'cash') ? 'selected' : ''; ?>>
                                    <?php echo __('cash'); ?>
                                </option>
                                <option value="bank_transfer" <?php echo (($_POST['payment_method'] ?? '') === 'bank_transfer') ? 'selected' : ''; ?>>
                                    <?php echo __('bank_transfer'); ?>
                                </option>
                                <option value="check" <?php echo (($_POST['payment_method'] ?? '') === 'check') ? 'selected' : ''; ?>>
                                    <?php echo __('check'); ?>
                                </option>
                                <option value="card" <?php echo (($_POST['payment_method'] ?? '') === 'card') ? 'selected' : ''; ?>>
                                    <?php echo __('card'); ?>
                                </option>
                                <option value="online" <?php echo (($_POST['payment_method'] ?? '') === 'online') ? 'selected' : ''; ?>>
                                    <?php echo __('online'); ?>
                                </option>
                            </select>
                            <div class="invalid-feedback">
                                <?php echo __('required_field'); ?>
                            </div>
                        </div>

                        <!-- Payment Method Specific Fields -->
                        <div id="bank_fields" style="display: none;">
                            <div class="mb-3">
                                <label for="bank_name" class="form-label"><?php echo __('bank_name'); ?></label>
                                <input type="text" 
                                       class="form-control" 
                                       id="bank_name" 
                                       name="bank_name" 
                                       value="<?php echo htmlspecialchars($_POST['bank_name'] ?? ''); ?>">
                            </div>
                        </div>

                        <div id="check_fields" style="display: none;">
                            <div class="mb-3">
                                <label for="check_number" class="form-label"><?php echo __('check_number'); ?></label>
                                <input type="text" 
                                       class="form-control" 
                                       id="check_number" 
                                       name="check_number" 
                                       value="<?php echo htmlspecialchars($_POST['check_number'] ?? ''); ?>">
                            </div>
                        </div>

                        <div id="transaction_fields" style="display: none;">
                            <div class="mb-3">
                                <label for="transaction_id" class="form-label"><?php echo __('transaction_id'); ?></label>
                                <input type="text" 
                                       class="form-control" 
                                       id="transaction_id" 
                                       name="transaction_id" 
                                       value="<?php echo htmlspecialchars($_POST['transaction_id'] ?? ''); ?>">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="receipt_number" class="form-label"><?php echo __('receipt_number'); ?></label>
                            <input type="text" 
                                   class="form-control" 
                                   id="receipt_number" 
                                   name="receipt_number" 
                                   value="<?php echo htmlspecialchars($_POST['receipt_number'] ?? ''); ?>"
                                   placeholder="<?php echo __('optional'); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label"><?php echo __('notes'); ?></label>
                            <textarea class="form-control" 
                                      id="notes" 
                                      name="notes" 
                                      rows="3"
                                      placeholder="<?php echo __('additional_notes'); ?>"><?php echo htmlspecialchars($_POST['notes'] ?? ''); ?></textarea>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i><?php echo __('record_payment'); ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Student Info Sidebar -->
        <div class="col-lg-4">
            <div id="student_info" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-user me-2"></i><?php echo __('student_info'); ?>
                        </h6>
                    </div>
                    <div class="card-body" id="student_info_content">
                        <!-- Student info will be loaded here -->
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-money-bill me-2"></i><?php echo __('outstanding_fees'); ?>
                        </h6>
                    </div>
                    <div class="card-body" id="outstanding_fees">
                        <!-- Outstanding fees will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function loadStudentFees() {
        const studentId = document.getElementById('student_id').value;
        if (studentId) {
            // Show student info section
            document.getElementById('student_info').style.display = 'block';
            document.getElementById('fee_selection').style.display = 'block';
            
            // Load student fees via AJAX
            fetch(`get_student_fees.php?student_id=${studentId}`)
                .then(response => response.json())
                .then(data => {
                    // Update fee selection
                    const feeSelect = document.getElementById('student_fee_id');
                    feeSelect.innerHTML = '<option value=""><?php echo __('general_payment'); ?></option>';
                    
                    data.fees.forEach(fee => {
                        const option = document.createElement('option');
                        option.value = fee.id;
                        option.setAttribute('data-remaining', fee.remaining_amount);
                        option.textContent = `${fee.type_name} - ${fee.academic_year} (${fee.semester}) - ${fee.remaining_amount}`;
                        feeSelect.appendChild(option);
                    });
                    
                    // Update student info
                    document.getElementById('student_info_content').innerHTML = data.student_info;
                    document.getElementById('outstanding_fees').innerHTML = data.outstanding_fees;
                })
                .catch(error => console.error('Error:', error));
        } else {
            document.getElementById('student_info').style.display = 'none';
            document.getElementById('fee_selection').style.display = 'none';
        }
    }

    function updateMaxAmount() {
        const feeSelect = document.getElementById('student_fee_id');
        const selectedOption = feeSelect.options[feeSelect.selectedIndex];
        const remainingAmount = selectedOption.getAttribute('data-remaining');
        const amountInput = document.getElementById('amount');
        const amountHelp = document.getElementById('amount_help');
        
        if (remainingAmount && remainingAmount > 0) {
            amountInput.max = remainingAmount;
            amountInput.value = remainingAmount;
            amountHelp.textContent = `<?php echo __('maximum_amount'); ?>: ${remainingAmount} <?php echo CURRENCY_SYMBOL; ?>`;
        } else {
            amountInput.removeAttribute('max');
            amountHelp.textContent = '';
        }
    }

    function togglePaymentFields() {
        const method = document.getElementById('payment_method').value;
        
        // Hide all method-specific fields
        document.getElementById('bank_fields').style.display = 'none';
        document.getElementById('check_fields').style.display = 'none';
        document.getElementById('transaction_fields').style.display = 'none';
        
        // Show relevant fields
        if (method === 'bank_transfer') {
            document.getElementById('bank_fields').style.display = 'block';
            document.getElementById('transaction_fields').style.display = 'block';
        } else if (method === 'check') {
            document.getElementById('check_fields').style.display = 'block';
            document.getElementById('bank_fields').style.display = 'block';
        } else if (method === 'card' || method === 'online') {
            document.getElementById('transaction_fields').style.display = 'block';
        }
    }

    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        <?php if ($selected_student_id > 0): ?>
            loadStudentFees();
        <?php endif; ?>
        
        togglePaymentFields();
    });
</script>

<?php require_once '../../includes/footer.php'; ?>
