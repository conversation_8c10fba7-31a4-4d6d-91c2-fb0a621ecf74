<?php
session_start();
require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/functions.php';

// التحقق من تسجيل الدخول
if (!is_logged_in()) {
    redirect_to('../../auth/login.php');
}

// التحقق من الصلاحيات (مدير أو مالي)
if (!isset($_SESSION['role']) || !in_array($_SESSION['role'], ['admin', 'financial_manager'])) {
    die('غير مسموح لك بالوصول إلى هذه الصفحة');
}

$success_message = '';
$error_message = '';

// معالجة حذف البيانات التجريبية
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['confirm_delete'])) {
    try {
        $conn->begin_transaction();
        
        echo "<h3>بدء عملية حذف البيانات التجريبية...</h3>";
        
        // 1. حذف المدفوعات المرتبطة بالرسوم التجريبية
        echo "<p>1. حذف المدفوعات المرتبطة...</p>";
        $result1 = $conn->query("DELETE sp FROM student_payments sp 
                                JOIN student_fees sf ON sp.student_fee_id = sf.id 
                                WHERE sf.fee_type_id IN (3, 4, 5)");
        echo "<p>تم حذف " . $conn->affected_rows . " مدفوعة</p>";
        
        // 2. حذف الأقساط المرتبطة بالرسوم التجريبية
        echo "<p>2. حذف الأقساط المرتبطة...</p>";
        $result2 = $conn->query("DELETE si FROM student_installments si 
                                JOIN student_fees sf ON si.student_fee_id = sf.id 
                                WHERE sf.fee_type_id IN (3, 4, 5)");
        echo "<p>تم حذف " . $conn->affected_rows . " قسط</p>";
        
        // 3. حذف الرسوم التجريبية
        echo "<p>3. حذف الرسوم التجريبية...</p>";
        $result3 = $conn->query("DELETE FROM student_fees WHERE fee_type_id IN (3, 4, 5)");
        echo "<p>تم حذف " . $conn->affected_rows . " رسم</p>";
        
        // 4. حذف روابط الرسوم بالصفوف (إن وجدت)
        echo "<p>4. حذف روابط الرسوم بالصفوف...</p>";
        $result4 = $conn->query("DELETE FROM fee_type_classes WHERE fee_type_id IN (3, 4, 5)");
        echo "<p>تم حذف " . $conn->affected_rows . " ربط</p>";
        
        // 5. حذف روابط الرسوم بالصفوف الدراسية الجديدة (إن وجدت)
        echo "<p>5. حذف روابط الرسوم بالصفوف الدراسية...</p>";
        $result5 = $conn->query("DELETE FROM fee_type_grades WHERE fee_type_id IN (3, 4, 5)");
        echo "<p>تم حذف " . $conn->affected_rows . " ربط</p>";
        
        $conn->commit();
        
        echo "<div class='alert alert-success mt-4'>";
        echo "<h4>✅ تم حذف جميع البيانات التجريبية بنجاح!</h4>";
        echo "<p>يمكنك الآن حذف أنواع الرسوم التجريبية من الصفحة الرئيسية.</p>";
        echo "<a href='index.php' class='btn btn-primary'>العودة إلى قائمة أنواع الرسوم</a>";
        echo "</div>";
        
    } catch (Exception $e) {
        $conn->rollback();
        echo "<div class='alert alert-danger mt-4'>";
        echo "<h4>❌ خطأ في حذف البيانات التجريبية:</h4>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "<a href='index.php' class='btn btn-secondary'>العودة</a>";
        echo "</div>";
    }
    
    exit;
}

$page_title = 'حذف البيانات التجريبية';
include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        حذف البيانات التجريبية
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-warning me-2"></i>تحذير مهم!</h5>
                        <p>هذا الإجراء سيقوم بحذف جميع البيانات التجريبية من النظام نهائياً ولا يمكن التراجع عنه.</p>
                    </div>
                    
                    <h6>سيتم حذف البيانات التالية:</h6>
                    <ul class="list-group mb-4">
                        <li class="list-group-item">
                            <i class="fas fa-trash text-danger me-2"></i>
                            جميع رسوم الطلاب المرتبطة بأنواع الرسوم التجريبية
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-trash text-danger me-2"></i>
                            جميع المدفوعات المرتبطة بهذه الرسوم
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-trash text-danger me-2"></i>
                            جميع الأقساط المرتبطة بهذه الرسوم
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-trash text-danger me-2"></i>
                            جميع روابط أنواع الرسوم بالصفوف الدراسية
                        </li>
                    </ul>
                    
                    <h6>أنواع الرسوم التجريبية التي ستصبح قابلة للحذف:</h6>
                    <ul class="list-group mb-4">
                        <li class="list-group-item d-flex justify-content-between">
                            <span><strong>رسوم الكتب</strong> - رسوم الكتب والمواد التعليمية</span>
                            <span class="badge bg-warning">ID: 3</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            <span><strong>رسوم النشاطات</strong> - رسوم الأنشطة اللاصفية</span>
                            <span class="badge bg-warning">ID: 4</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            <span><strong>رسوم النقل</strong> - رسوم النقل المدرسي</span>
                            <span class="badge bg-warning">ID: 5</span>
                        </li>
                    </ul>
                    
                    <form method="POST" onsubmit="return confirm('هل أنت متأكد تماماً من حذف جميع البيانات التجريبية؟\n\nهذا الإجراء لا يمكن التراجع عنه!')">
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="understand" required>
                            <label class="form-check-label" for="understand">
                                أفهم أن هذا الإجراء سيحذف جميع البيانات التجريبية نهائياً
                            </label>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="submit" name="confirm_delete" class="btn btn-danger">
                                <i class="fas fa-trash-alt me-2"></i>
                                حذف جميع البيانات التجريبية
                            </button>
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>
                                إلغاء والعودة
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once '../../includes/footer.php'; ?>
