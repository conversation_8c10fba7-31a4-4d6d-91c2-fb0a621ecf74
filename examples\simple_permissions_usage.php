<?php
/**
 * مثال على استخدام النظام البسيط للصلاحيات
 * Example of Simple Permissions System Usage
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/simple_permissions.php';

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول
if (!is_user_logged_in()) {
    header('Location: ../login.php');
    exit();
}

// مثال 1: التحقق من صلاحية مشاهدة صفحة الطلاب
if (!check_page_permission('students', 'view')) {
    show_permission_denied('الطلاب', 'مشاهدة');
}

// مثال 2: التحقق من صلاحية إضافة طالب جديد
$can_add_student = check_page_permission('students', 'add');

// مثال 3: التحقق من صلاحية تعديل بيانات الطلاب
$can_edit_student = check_page_permission('students', 'edit');

// مثال 4: التحقق من صلاحية حذف الطلاب
$can_delete_student = check_page_permission('students', 'delete');

// مثال 5: استخدام require_page_permission للتحقق مع إعادة التوجيه
// require_page_permission('finance', 'view', '../dashboard/');

$page_title = 'مثال على استخدام النظام البسيط للصلاحيات';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2><?php echo $page_title; ?></h2>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>صلاحيات المستخدم الحالي</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>المستخدم:</strong> <?php echo htmlspecialchars($_SESSION['username'] ?? 'غير محدد'); ?></p>
                        <p><strong>الدور:</strong> <?php echo htmlspecialchars($_SESSION['role'] ?? 'غير محدد'); ?></p>
                        
                        <h6>صلاحيات صفحة الطلاب:</h6>
                        <ul class="list-group">
                            <li class="list-group-item d-flex justify-content-between">
                                مشاهدة
                                <?php if (check_page_permission('students', 'view')): ?>
                                    <span class="badge bg-success">مسموح</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">مرفوض</span>
                                <?php endif; ?>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                إضافة
                                <?php if ($can_add_student): ?>
                                    <span class="badge bg-success">مسموح</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">مرفوض</span>
                                <?php endif; ?>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                تعديل
                                <?php if ($can_edit_student): ?>
                                    <span class="badge bg-success">مسموح</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">مرفوض</span>
                                <?php endif; ?>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                حذف
                                <?php if ($can_delete_student): ?>
                                    <span class="badge bg-success">مسموح</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">مرفوض</span>
                                <?php endif; ?>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>أمثلة الاستخدام في الكود</h5>
                    </div>
                    <div class="card-body">
                        <h6>1. التحقق البسيط:</h6>
                        <pre><code>if (check_page_permission('students', 'view')) {
    // عرض قائمة الطلاب
}</code></pre>
                        
                        <h6>2. التحقق مع إعادة التوجيه:</h6>
                        <pre><code>require_page_permission('students', 'edit');</code></pre>
                        
                        <h6>3. إخفاء الأزرار حسب الصلاحية:</h6>
                        <pre><code><?php if ($can_add_student): ?>
    &lt;button&gt;إضافة طالب&lt;/button&gt;
<?php endif; ?></code></pre>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- مثال على الأزرار المشروطة -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>مثال: أزرار مشروطة حسب الصلاحيات</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex gap-2">
                            <?php if (check_page_permission('students', 'view')): ?>
                                <button class="btn btn-primary">
                                    <i class="fas fa-eye me-2"></i>عرض الطلاب
                                </button>
                            <?php endif; ?>
                            
                            <?php if ($can_add_student): ?>
                                <button class="btn btn-success">
                                    <i class="fas fa-plus me-2"></i>إضافة طالب
                                </button>
                            <?php endif; ?>
                            
                            <?php if ($can_edit_student): ?>
                                <button class="btn btn-warning">
                                    <i class="fas fa-edit me-2"></i>تعديل طالب
                                </button>
                            <?php endif; ?>
                            
                            <?php if ($can_delete_student): ?>
                                <button class="btn btn-danger">
                                    <i class="fas fa-trash me-2"></i>حذف طالب
                                </button>
                            <?php endif; ?>
                            
                            <?php if (!check_page_permission('students', 'view')): ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-lock me-2"></i>
                                    ليس لديك صلاحية للوصول لهذه الصفحة
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- جميع صلاحيات المستخدم -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>جميع صلاحيات المستخدم</h5>
                    </div>
                    <div class="card-body">
                        <?php 
                        $all_permissions = get_user_page_permissions();
                        
                        if ($all_permissions === 'all'): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-crown me-2"></i>
                                <strong>مدير النظام:</strong> لديك جميع الصلاحيات
                            </div>
                        <?php elseif (empty($all_permissions)): ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                لا توجد صلاحيات مخصصة لحسابك
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>الصفحة</th>
                                            <th>مشاهدة</th>
                                            <th>إضافة</th>
                                            <th>تعديل</th>
                                            <th>حذف</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($all_permissions as $page => $perms): ?>
                                            <tr>
                                                <td><strong><?php echo htmlspecialchars($page); ?></strong></td>
                                                <td>
                                                    <?php if ($perms['view']): ?>
                                                        <i class="fas fa-check text-success"></i>
                                                    <?php else: ?>
                                                        <i class="fas fa-times text-danger"></i>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($perms['add']): ?>
                                                        <i class="fas fa-check text-success"></i>
                                                    <?php else: ?>
                                                        <i class="fas fa-times text-danger"></i>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($perms['edit']): ?>
                                                        <i class="fas fa-check text-success"></i>
                                                    <?php else: ?>
                                                        <i class="fas fa-times text-danger"></i>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($perms['delete']): ?>
                                                        <i class="fas fa-check text-success"></i>
                                                    <?php else: ?>
                                                        <i class="fas fa-times text-danger"></i>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <a href="../dashboard/" class="btn btn-secondary">العودة للرئيسية</a>
            <?php if (is_admin_user()): ?>
                <a href="../admin/simple_permissions_manager.php" class="btn btn-primary">إدارة الصلاحيات</a>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
