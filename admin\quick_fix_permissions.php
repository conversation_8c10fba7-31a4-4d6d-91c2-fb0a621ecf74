<?php
/**
 * إصلاح سريع لمشكلة الصلاحيات
 * Quick Fix for Permissions Issue
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

$user_email = '<EMAIL>';
$results = [];
$success = true;

// معالجة الإصلاح السريع
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['quick_fix'])) {
    try {
        $results[] = "🚀 بدء الإصلاح السريع لمشكلة الصلاحيات";
        
        // 1. جلب بيانات المستخدم
        $user_query = "SELECT * FROM users WHERE email = ?";
        $user_stmt = $conn->prepare($user_query);
        $user_stmt->bind_param("s", $user_email);
        $user_stmt->execute();
        $user = $user_stmt->get_result()->fetch_assoc();
        
        if (!$user) {
            throw new Exception("المستخدم غير موجود!");
        }
        
        $results[] = "✅ المستخدم موجود: " . $user['full_name'];
        
        // 2. التأكد من وجود الجداول
        $tables_check = $conn->query("SHOW TABLES LIKE 'user_custom_permissions'");
        if ($tables_check->num_rows == 0) {
            // إنشاء الجدول
            $create_table = "
            CREATE TABLE IF NOT EXISTS `user_custom_permissions` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL,
                `permission_key` varchar(100) NOT NULL,
                `is_granted` tinyint(1) DEFAULT 1,
                `granted_by` int(11) DEFAULT NULL,
                `granted_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `user_permission` (`user_id`,`permission_key`),
                KEY `user_id` (`user_id`),
                KEY `permission_key` (`permission_key`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ";
            
            if ($conn->query($create_table)) {
                $results[] = "✅ تم إنشاء جدول user_custom_permissions";
            } else {
                throw new Exception("فشل في إنشاء الجدول");
            }
        } else {
            $results[] = "✅ جدول user_custom_permissions موجود";
        }
        
        // 3. حذف الصلاحيات الموجودة للمستخدم
        $delete_stmt = $conn->prepare("DELETE FROM user_custom_permissions WHERE user_id = ?");
        $delete_stmt->bind_param("i", $user['id']);
        $delete_stmt->execute();
        $results[] = "🧹 تم حذف الصلاحيات القديمة";
        
        // 4. إضافة صلاحيات شاملة للإداري
        $staff_permissions = [
            'students_view',
            'student_add', 
            'student_edit',
            'student_delete',
            'student_affairs',
            'teachers_view',
            'teacher_add',
            'teacher_edit', 
            'teacher_delete',
            'staff_affairs',
            'classes_view',
            'class_manage',
            'subjects_view',
            'subject_manage',
            'reports_view',
            'student_reports',
            'staff_reports',
            'attendance_reports',
            'permissions_management',
            'admin_access',
            'teacher_access'
        ];
        
        $insert_stmt = $conn->prepare("
            INSERT INTO user_custom_permissions 
            (user_id, permission_key, is_granted, granted_by, granted_at) 
            VALUES (?, ?, 1, ?, NOW())
        ");
        
        $granted_count = 0;
        foreach ($staff_permissions as $permission) {
            $insert_stmt->bind_param("isi", $user['id'], $permission, $_SESSION['user_id']);
            if ($insert_stmt->execute()) {
                $granted_count++;
            }
        }
        
        $results[] = "🔑 تم منح $granted_count صلاحية للمستخدم";
        
        // 5. اختبار الصلاحيات
        $results[] = "\n🧪 اختبار الصلاحيات:";
        
        // محاكاة جلسة المستخدم
        $original_session = $_SESSION;
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['role'] = $user['role'];
        $_SESSION['email'] = $user['email'];
        
        $test_permissions = [
            'students_view' => 'عرض الطلاب',
            'student_affairs' => 'شئون الطلاب',
            'teachers_view' => 'عرض المعلمين',
            'staff_affairs' => 'شئون العاملين',
            'classes_view' => 'عرض الفصول',
            'reports_view' => 'عرض التقارير'
        ];
        
        $working_permissions = 0;
        foreach ($test_permissions as $permission => $name) {
            $has_permission = has_permission($permission);
            $results[] = ($has_permission ? "✅" : "❌") . " $name: " . ($has_permission ? "متاح" : "غير متاح");
            if ($has_permission) $working_permissions++;
        }
        
        // استعادة الجلسة الأصلية
        $_SESSION = $original_session;
        
        $results[] = "\n📊 النتيجة: $working_permissions من " . count($test_permissions) . " صلاحيات تعمل";
        
        if ($working_permissions > 0) {
            $results[] = "\n🎉 تم الإصلاح بنجاح! الصلاحيات تعمل الآن";
        } else {
            $results[] = "\n⚠️ قد تحتاج لمزيد من التشخيص";
        }
        
    } catch (Exception $e) {
        $success = false;
        $results[] = "❌ خطأ: " . $e->getMessage();
    }
}

$page_title = 'إصلاح سريع لمشكلة الصلاحيات';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-bolt me-2"></i><?php echo $page_title; ?></h2>
            <p class="text-muted">حل سريع لمشكلة عدم ظهور الصلاحيات للمستخدم الإداري</p>
        </div>
        <div>
            <a href="debug_permissions_table.php" class="btn btn-outline-warning me-2">
                <i class="fas fa-bug me-2"></i>تشخيص مفصل
            </a>
            <a href="../settings/permissions.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة
            </a>
        </div>
    </div>

    <!-- شرح المشكلة -->
    <div class="alert alert-info">
        <h6><i class="fas fa-info-circle me-2"></i>المشكلة:</h6>
        <p class="mb-2">المستخدم الإداري لا يرى الصلاحيات التي تم منحها له من خلال صفحة إدارة الصلاحيات.</p>
        <p class="mb-0"><strong>الحل السريع:</strong> إعادة إنشاء الجدول وإضافة الصلاحيات بالتنسيق الصحيح.</p>
    </div>

    <!-- نتائج الإصلاح -->
    <?php if (!empty($results)): ?>
        <div class="card mb-4">
            <div class="card-header <?php echo $success ? 'bg-success' : 'bg-danger'; ?> text-white">
                <h5>
                    <i class="fas fa-<?php echo $success ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                    <?php echo $success ? 'تم الإصلاح بنجاح!' : 'فشل في الإصلاح'; ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="bg-dark text-light p-3 rounded" style="font-family: monospace; white-space: pre-line; max-height: 400px; overflow-y: auto;">
<?php echo implode("\n", $results); ?>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- نموذج الإصلاح -->
    <?php if (empty($results) || !$success): ?>
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5><i class="fas fa-bolt me-2"></i>إصلاح سريع للصلاحيات</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>ما سيتم عمله:</h6>
                    <ol>
                        <li><strong>التحقق من المستخدم:</strong> التأكد من وجود المستخدم <?php echo $user_email; ?></li>
                        <li><strong>إنشاء/فحص الجدول:</strong> التأكد من وجود جدول user_custom_permissions</li>
                        <li><strong>حذف الصلاحيات القديمة:</strong> إزالة أي صلاحيات تالفة</li>
                        <li><strong>إضافة صلاحيات جديدة:</strong> منح صلاحيات شاملة للإداري</li>
                        <li><strong>اختبار النظام:</strong> التأكد من عمل الصلاحيات</li>
                    </ol>
                </div>

                <div class="card mb-3">
                    <div class="card-header">
                        <h6><i class="fas fa-list me-2"></i>الصلاحيات التي سيتم منحها:</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6 class="text-primary">شئون الطلاب:</h6>
                                <ul class="small">
                                    <li>students_view</li>
                                    <li>student_add</li>
                                    <li>student_edit</li>
                                    <li>student_delete</li>
                                    <li>student_affairs</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <h6 class="text-success">شئون العاملين:</h6>
                                <ul class="small">
                                    <li>teachers_view</li>
                                    <li>teacher_add</li>
                                    <li>teacher_edit</li>
                                    <li>teacher_delete</li>
                                    <li>staff_affairs</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <h6 class="text-info">أخرى:</h6>
                                <ul class="small">
                                    <li>classes_view</li>
                                    <li>subjects_view</li>
                                    <li>reports_view</li>
                                    <li>permissions_management</li>
                                    <li>admin_access</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <form method="POST">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="confirmFix" required>
                        <label class="form-check-label" for="confirmFix">
                            <strong>أؤكد أنني أريد تطبيق الإصلاح السريع للصلاحيات</strong>
                        </label>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="../settings/permissions.php" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" name="quick_fix" class="btn btn-primary" id="fixButton" disabled>
                            <i class="fas fa-bolt me-2"></i>تطبيق الإصلاح السريع
                        </button>
                    </div>
                </form>
            </div>
        </div>
    <?php endif; ?>

    <!-- تعليمات الاختبار -->
    <?php if ($success && !empty($results)): ?>
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5><i class="fas fa-clipboard-check me-2"></i>اختبار النتيجة</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <h6><i class="fas fa-thumbs-up me-2"></i>تم الإصلاح!</h6>
                    <p class="mb-0">الآن يجب أن تعمل الصلاحيات بشكل صحيح.</p>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">خطوات الاختبار:</h6>
                        <ol>
                            <li><strong>سجل خروج</strong> من حسابك الحالي</li>
                            <li><strong>سجل دخول</strong> بحساب: <code><?php echo $user_email; ?></code></li>
                            <li><strong>تحقق من القائمة الجانبية</strong> - يجب أن تظهر الأقسام</li>
                            <li><strong>اختبر الوصول</strong> للطلاب والمعلمين</li>
                        </ol>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-success">يجب أن تظهر:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">👨‍🎓 شئون الطلاب</li>
                            <li class="list-group-item">👥 شئون العاملين</li>
                            <li class="list-group-item">🏫 الفصول</li>
                            <li class="list-group-item">📚 المواد</li>
                            <li class="list-group-item">📊 التقارير</li>
                        </ul>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <a href="permissions_manager.php" class="btn btn-primary me-2">
                        <i class="fas fa-key me-2"></i>إدارة الصلاحيات
                    </a>
                    <a href="test_staff_access.php" class="btn btn-info me-2">
                        <i class="fas fa-vial me-2"></i>اختبار الوصول
                    </a>
                    <button class="btn btn-secondary" onclick="window.location.reload()">
                        <i class="fas fa-redo me-2"></i>إعادة الإصلاح
                    </button>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
// تفعيل زر الإصلاح عند تأكيد الاختيار
document.getElementById('confirmFix')?.addEventListener('change', function() {
    document.getElementById('fixButton').disabled = !this.checked;
});
</script>

<?php include_once '../includes/footer.php'; ?>
