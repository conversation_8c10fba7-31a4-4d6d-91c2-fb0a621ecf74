<?php
/**
 * نظام الصلاحيات الموحد
 * Unified Permissions System
 */

// منع الوصول المباشر
if (!defined('SYSTEM_INIT') && !defined('FUNCTIONS_LOADED')) {
    die('Direct access not allowed');
}

// تعريف أن النظام الموحد محمل
define('UNIFIED_PERMISSIONS_LOADED', true);

// تحميل النظام المحسن إذا كان متوفراً
if (file_exists(__DIR__ . '/enhanced_permissions.php') && !defined('ENHANCED_PERMISSIONS_LOADED')) {
    require_once __DIR__ . '/enhanced_permissions.php';
}

/**
 * دالة التحقق من الصلاحيات الموحدة
 */
if (!function_exists('unified_has_permission')) {
    function unified_has_permission($permission_key, $required_level = 'read', $user_id = null) {
        // التحقق من تسجيل الدخول
        if (!isset($_SESSION['user_id'])) {
            return false;
        }
        
        // المدير العام له جميع الصلاحيات
        if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin') {
            return true;
        }
        
        // استخدام النظام المحسن إذا كان متوفراً
        if (defined('ENHANCED_PERMISSIONS_LOADED') && function_exists('enhanced_has_permission')) {
            return enhanced_has_permission($permission_key, $required_level, $user_id);
        }
        
        // العودة للنظام الأساسي
        return basic_permission_check($permission_key, $user_id);
    }
}

/**
 * دالة التحقق الأساسية
 */
if (!function_exists('basic_permission_check')) {
    function basic_permission_check($permission_key, $user_id = null) {
        global $conn;
        
        if ($user_id === null) {
            $user_id = $_SESSION['user_id'] ?? 0;
        }
        
        try {
            // التحقق من وجود الجدول أولاً
            $table_check = $conn->query("SHOW TABLES LIKE 'user_custom_permissions'");
            if ($table_check->num_rows == 0) {
                return false;
            }
            
            $stmt = $conn->prepare("
                SELECT is_granted 
                FROM user_custom_permissions 
                WHERE user_id = ? AND (resource_key = ? OR permission_key = ?) AND is_granted = 1
                AND (expires_at IS NULL OR expires_at > NOW())
                LIMIT 1
            ");
            $stmt->bind_param("iss", $user_id, $permission_key, $permission_key);
            $stmt->execute();
            $result = $stmt->get_result();
            
            return $result->num_rows > 0;
        } catch (Exception $e) {
            error_log("Error in basic_permission_check: " . $e->getMessage());
            return false;
        }
    }
}

/**
 * دالة التحقق مع إعادة التوجيه الموحدة
 */
if (!function_exists('unified_require_permission')) {
    function unified_require_permission($permission_key, $required_level = 'read', $redirect_url = '../dashboard/') {
        if (!unified_has_permission($permission_key, $required_level)) {
            header('Location: ' . $redirect_url . '?error=access_denied');
            exit();
        }
    }
}

/**
 * دوال التوافق - تستخدم النظام الموحد
 */
if (!function_exists('has_permission')) {
    function has_permission($permission_key, $required_level = 'read') {
        return unified_has_permission($permission_key, $required_level);
    }
}

if (!function_exists('require_permission')) {
    function require_permission($permission_key, $required_level = 'read', $redirect_url = '../dashboard/') {
        unified_require_permission($permission_key, $required_level, $redirect_url);
    }
}

/**
 * دالة فحص الصلاحيات للتوافق مع النظام القديم
 */
if (!function_exists('check_permission')) {
    function check_permission($role) {
        if (!isset($_SESSION['role'])) {
            return false;
        }
        
        if ($role === 'admin') {
            return $_SESSION['role'] === 'admin';
        }
        
        return $_SESSION['role'] === $role || $_SESSION['role'] === 'admin';
    }
}

/**
 * دوال إضافية للتوافق
 */
if (!function_exists('has_all_permissions')) {
    function has_all_permissions($permissions) {
        foreach ($permissions as $permission) {
            if (!has_permission($permission)) {
                return false;
            }
        }
        return true;
    }
}

if (!function_exists('has_any_permission')) {
    function has_any_permission($permissions) {
        foreach ($permissions as $permission) {
            if (has_permission($permission)) {
                return true;
            }
        }
        return false;
    }
}

if (!function_exists('get_user_permissions')) {
    function get_user_permissions() {
        if (!isset($_SESSION['role'])) {
            return [];
        }
        
        // إرجاع صلاحيات أساسية حسب الدور
        $role_permissions = [
            'admin' => ['*'],
            'teacher' => ['students.view', 'attendance.view', 'exams.view'],
            'student' => ['attendance.view', 'exams.view'],
            'staff' => ['students.view', 'attendance.view'],
            'parent' => ['students.view', 'attendance.view']
        ];
        
        return $role_permissions[$_SESSION['role']] ?? [];
    }
}

if (!function_exists('permission_denied')) {
    function permission_denied($message = 'غير مسموح لك بالوصول لهذه الصفحة') {
        http_response_code(403);
        die('<div class="alert alert-danger text-center mt-5">
                <i class="fas fa-lock fa-3x mb-3"></i>
                <h4>وصول مرفوض</h4>
                <p>' . htmlspecialchars($message) . '</p>
                <a href="../dashboard/" class="btn btn-primary">العودة للرئيسية</a>
             </div>');
    }
}

/**
 * دالة تسجيل العمليات (إذا كان النظام المحسن غير متوفر)
 */
if (!function_exists('log_permission_audit') && !defined('ENHANCED_PERMISSIONS_LOADED')) {
    function log_permission_audit($action_type, $resource_key = null, $old_value = null, $new_value = null, $changed_by = null, $notes = null) {
        // تسجيل بسيط في ملف log
        $log_entry = date('Y-m-d H:i:s') . " - User: " . ($_SESSION['user_id'] ?? 'unknown') . 
                    " - Action: $action_type - Resource: $resource_key - Notes: $notes\n";
        error_log($log_entry, 3, __DIR__ . '/../logs/permissions.log');
    }
}

/**
 * دالة التحقق من تسجيل الدخول
 */
if (!function_exists('is_logged_in')) {
    function is_logged_in() {
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }
}

/**
 * دالة الحصول على بيانات المستخدم الأساسية
 */
if (!function_exists('get_user_data') && !defined('ENHANCED_PERMISSIONS_LOADED')) {
    function get_user_data($user_id) {
        global $conn;
        
        try {
            $stmt = $conn->prepare("
                SELECT id, username, email, full_name, role, status 
                FROM users 
                WHERE id = ? 
                LIMIT 1
            ");
            $stmt->bind_param("i", $user_id);
            $stmt->execute();
            return $stmt->get_result()->fetch_assoc();
        } catch (Exception $e) {
            error_log("Error getting user data: " . $e->getMessage());
            return null;
        }
    }
}

/**
 * دالة تنظيف المدخلات
 */
if (!function_exists('clean_input')) {
    function clean_input($data) {
        $data = trim($data);
        $data = stripslashes($data);
        $data = htmlspecialchars($data);
        return $data;
    }
}

/**
 * دالة التحقق من الجلسة
 */
if (!function_exists('check_session')) {
    function check_session() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
            header('Location: ../login.php');
            exit();
        }
    }
}

// تسجيل أن النظام الموحد تم تحميله بنجاح
if (defined('DEBUG_PERMISSIONS') && DEBUG_PERMISSIONS) {
    error_log("Unified Permissions System loaded successfully");
}
?>
