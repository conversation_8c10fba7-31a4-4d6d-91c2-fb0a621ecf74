<?php
require_once '../includes/header.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
check_session();

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = trim($_POST['full_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $role = $_POST['role'] ?? 'student';
    $status = $_POST['status'] ?? 'active';
    $username = trim($_POST['username'] ?? '');

    if ($full_name && $email && $password && $username) {
        try {
            $conn->begin_transaction();

            $hashed = password_hash($password, PASSWORD_DEFAULT);
            $stmt = $conn->prepare("INSERT INTO users (username, full_name, email, password, role, status, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())");
            $stmt->bind_param("ssssss", $username, $full_name, $email, $hashed, $role, $status);

            if ($stmt->execute()) {
                $user_id = $conn->insert_id;

                // إذا كان الدور staff، أنشئ سجل في جدول الإداريين
                if ($role === 'staff') {
                    $admin_stmt = $conn->prepare("INSERT INTO staff (user_id, status, created_at) VALUES (?, 'active', NOW())");
                    $admin_stmt->bind_param("i", $user_id);
                    $admin_stmt->execute();
                }

                $conn->commit();
                $success = 'تمت إضافة المستخدم بنجاح';

                // إعادة توجيه حسب نوع المستخدم
                if ($role === 'staff') {
                    $success .= ' - <a href="../administrators/edit.php?id=' . $conn->insert_id . '">إكمال بيانات الإداري</a>';
                }
            } else {
                throw new Exception('فشل في إضافة المستخدم');
            }
        } catch (Exception $e) {
            $conn->rollback();
            $error = 'حدث خطأ أثناء الإضافة: ' . $e->getMessage();
        }
    } else {
        $error = 'يرجى تعبئة جميع الحقول المطلوبة';
    }
}
?>
<div class="container">
    <h2>إضافة مستخدم جديد</h2>
    <?php if ($error): ?><div class="alert alert-danger"><?= $error ?></div><?php endif; ?>
    <?php if ($success): ?><div class="alert alert-success"><?= $success ?></div><?php endif; ?>
    <form method="post">
        <div class="mb-3">
            <label>اسم المستخدم</label>
            <input type="text" name="username" class="form-control" required>
        </div>
        <div class="mb-3">
            <label>الاسم الكامل</label>
            <input type="text" name="full_name" class="form-control" required>
        </div>
        <div class="mb-3">
            <label>البريد الإلكتروني</label>
            <input type="email" name="email" class="form-control" required>
        </div>
        <div class="mb-3">
            <label>كلمة المرور</label>
            <input type="password" name="password" class="form-control" required>
        </div>
        <div class="mb-3">
            <label>الدور</label>
            <select name="role" class="form-control">
                <option value="admin">مدير</option>
                <option value="teacher">معلم</option>
                <option value="student">طالب</option>
                <option value="staff">موظف</option>
            </select>
        </div>
        <div class="mb-3">
            <label>الحالة</label>
            <select name="status" class="form-control">
                <option value="active">نشط</option>
                <option value="inactive">غير نشط</option>
            </select>
        </div>
        <button type="submit" class="btn btn-primary">حفظ</button>
        <a href="index.php" class="btn btn-secondary">رجوع</a>
    </form>
</div>
<?php require_once '../includes/footer.php'; ?> 