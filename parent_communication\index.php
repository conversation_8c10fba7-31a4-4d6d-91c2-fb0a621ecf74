<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('teacher')) {
    header('Location: ../dashboard/');
    exit();
}

$page_title = __('communication_dashboard');
require_once '../includes/header.php';

// جلب الإحصائيات
global $conn;

// إحصائيات الرسائل
$stats_query = "
    SELECT 
        COUNT(*) as total_messages,
        SUM(CASE WHEN status = 'sent' THEN 1 ELSE 0 END) as sent_messages,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_messages,
        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_messages,
        SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered_messages,
        SUM(CASE WHEN status = 'read' THEN 1 ELSE 0 END) as read_messages
    FROM parent_communications 
    WHERE DATE(created_at) = CURDATE()
";

$stats_result = $conn->query($stats_query);
$stats = $stats_result ? $stats_result->fetch_assoc() : [
    'total_messages' => 0,
    'sent_messages' => 0,
    'pending_messages' => 0,
    'failed_messages' => 0,
    'delivered_messages' => 0,
    'read_messages' => 0
];

// إحصائيات تقارير السلوك
$behavior_stats_query = "
    SELECT 
        COUNT(*) as total_reports,
        SUM(CASE WHEN behavior_type = 'excellent' THEN 1 ELSE 0 END) as excellent_reports,
        SUM(CASE WHEN behavior_type = 'good' THEN 1 ELSE 0 END) as good_reports,
        SUM(CASE WHEN behavior_type = 'needs_improvement' THEN 1 ELSE 0 END) as improvement_reports,
        SUM(CASE WHEN behavior_type = 'concerning' THEN 1 ELSE 0 END) as concerning_reports
    FROM student_behavior_reports 
    WHERE DATE(created_at) = CURDATE()
";

$behavior_stats_result = $conn->query($behavior_stats_query);
$behavior_stats = $behavior_stats_result ? $behavior_stats_result->fetch_assoc() : [
    'total_reports' => 0,
    'excellent_reports' => 0,
    'good_reports' => 0,
    'improvement_reports' => 0,
    'concerning_reports' => 0
];

// الرسائل الحديثة
$recent_messages_query = "
    SELECT 
        pc.*,
        s.student_id as student_number,
        u.full_name as student_name,
        sender.full_name as sender_name
    FROM parent_communications pc
    JOIN students s ON pc.student_id = s.id
    JOIN users u ON s.user_id = u.id
    JOIN users sender ON pc.sent_by = sender.id
    ORDER BY pc.created_at DESC
    LIMIT 10
";

$recent_messages_result = $conn->query($recent_messages_query);
$recent_messages = [];
if ($recent_messages_result) {
    while ($row = $recent_messages_result->fetch_assoc()) {
        $recent_messages[] = $row;
    }
}
?>

<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fab fa-whatsapp me-2 text-success"></i>
            <?php echo __('communication_dashboard'); ?>
        </h2>
        <div class="btn-group">
            <a href="send_message.php" class="btn btn-primary">
                <i class="fas fa-paper-plane me-2"></i><?php echo __('send_message'); ?>
            </a>
            <a href="student_reports.php" class="btn btn-success">
                <i class="fas fa-clipboard-list me-2"></i><?php echo __('behavior_reports'); ?>
            </a>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $stats['total_messages']; ?></h4>
                            <p class="mb-0"><?php echo __('total_messages'); ?></p>
                            <small class="opacity-75"><?php echo __('today'); ?></small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-envelope fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $stats['sent_messages']; ?></h4>
                            <p class="mb-0"><?php echo __('sent_messages'); ?></p>
                            <small class="opacity-75"><?php echo __('today'); ?></small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $stats['pending_messages']; ?></h4>
                            <p class="mb-0"><?php echo __('pending_messages'); ?></p>
                            <small class="opacity-75"><?php echo __('today'); ?></small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-danger text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $stats['failed_messages']; ?></h4>
                            <p class="mb-0"><?php echo __('failed_messages'); ?></p>
                            <small class="opacity-75"><?php echo __('today'); ?></small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات تقارير السلوك -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $behavior_stats['total_reports']; ?></h4>
                            <p class="mb-0"><?php echo __('behavior_reports'); ?></p>
                            <small class="opacity-75"><?php echo __('today'); ?></small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clipboard-list fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $behavior_stats['excellent_reports']; ?></h4>
                            <p class="mb-0"><?php echo __('excellent'); ?> 😊</p>
                            <small class="opacity-75"><?php echo __('today'); ?></small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-star fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $behavior_stats['improvement_reports']; ?></h4>
                            <p class="mb-0"><?php echo __('needs_improvement'); ?> ⚠️</p>
                            <small class="opacity-75"><?php echo __('today'); ?></small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-danger text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $behavior_stats['concerning_reports']; ?></h4>
                            <p class="mb-0"><?php echo __('concerning'); ?> 🚨</p>
                            <small class="opacity-75"><?php echo __('today'); ?></small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- أدوات سريعة -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tools me-2"></i><?php echo __('quick_actions'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="send_message.php" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-2"></i><?php echo __('quick_send'); ?>
                        </a>
                        <a href="whatsapp_interface.php" class="btn btn-success">
                            <i class="fab fa-whatsapp me-2"></i><?php echo __('whatsapp_interface'); ?>
                        </a>
                        <a href="bulk_message.php" class="btn btn-info">
                            <i class="fas fa-broadcast-tower me-2"></i><?php echo __('bulk_message'); ?>
                        </a>
                        <a href="student_reports.php" class="btn btn-success">
                            <i class="fas fa-clipboard-list me-2"></i><?php echo __('behavior_reports'); ?>
                        </a>
                        <a href="message_templates.php" class="btn btn-warning">
                            <i class="fas fa-file-alt me-2"></i><?php echo __('message_templates'); ?>
                        </a>
                        <a href="communication_reports.php" class="btn btn-secondary">
                            <i class="fas fa-chart-line me-2"></i><?php echo __('communication_reports'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- الرسائل الحديثة -->
        <div class="col-lg-8 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i><?php echo __('recent_messages'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($recent_messages)): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th><?php echo __('student_name'); ?></th>
                                        <th><?php echo __('subject'); ?></th>
                                        <th><?php echo __('status'); ?></th>
                                        <th><?php echo __('sent_by'); ?></th>
                                        <th><?php echo __('time'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_messages as $message): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo safe_html($message['student_name']); ?></strong>
                                                <br><small class="text-muted"><?php echo safe_html($message['student_number']); ?></small>
                                            </td>
                                            <td>
                                                <?php echo safe_html($message['subject']); ?>
                                                <br><small class="text-muted">
                                                    <i class="fas fa-tag me-1"></i><?php echo __($message['message_type']); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <?php
                                                $status_colors = [
                                                    'pending' => 'warning',
                                                    'sent' => 'primary',
                                                    'delivered' => 'success',
                                                    'read' => 'info',
                                                    'failed' => 'danger'
                                                ];
                                                $color = $status_colors[$message['status']] ?? 'secondary';
                                                ?>
                                                <span class="badge bg-<?php echo $color; ?>">
                                                    <?php echo __($message['status']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo safe_html($message['sender_name']); ?></td>
                                            <td>
                                                <small><?php echo date('H:i', strtotime($message['created_at'])); ?></small>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p class="text-muted"><?php echo __('no_recent_messages'); ?></p>
                            <a href="send_message.php" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i><?php echo __('send_first_message'); ?>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
