<?php
/**
 * صفحة حضور وغياب المعلمين
 * Teachers Attendance Page
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

$page_title = 'حضور وغياب المعلمين';
$user_id = $_SESSION['user_id'];

// معالجة تسجيل الحضور
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_attendance'])) {
    $attendance_date = clean_input($_POST['attendance_date']);
    $attendance_data = $_POST['attendance'] ?? [];
    
    if (!empty($attendance_date) && !empty($attendance_data)) {
        try {
            $conn->begin_transaction();
            
            $new_count = 0;
            $updated_count = 0;
            
            foreach ($attendance_data as $teacher_id => $data) {
                $status = clean_input($data['status']);
                $check_in_time = !empty($data['check_in_time']) ? clean_input($data['check_in_time']) : null;
                $check_out_time = !empty($data['check_out_time']) ? clean_input($data['check_out_time']) : null;
                $notes = clean_input($data['notes'] ?? '');
                
                // التحقق من وجود سجل موجود
                $check_stmt = $conn->prepare("
                    SELECT id FROM teacher_attendance 
                    WHERE teacher_id = ? AND attendance_date = ?
                ");
                $check_stmt->bind_param("is", $teacher_id, $attendance_date);
                $check_stmt->execute();
                $existing = $check_stmt->get_result()->fetch_assoc();
                
                if ($existing) {
                    // تحديث السجل الموجود
                    $update_stmt = $conn->prepare("
                        UPDATE teacher_attendance
                        SET status = ?, check_in_time = ?, check_out_time = ?, notes = ?, recorded_by = ?, updated_at = NOW()
                        WHERE teacher_id = ? AND attendance_date = ?
                    ");
                    $update_stmt->bind_param("ssssiss", $status, $check_in_time, $check_out_time, $notes, $user_id, $teacher_id, $attendance_date);
                    $update_stmt->execute();
                    $updated_count++;
                } else {
                    // إنشاء سجل جديد
                    $insert_stmt = $conn->prepare("
                        INSERT INTO teacher_attendance (teacher_id, attendance_date, status, check_in_time, check_out_time, notes, recorded_by)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ");
                    $insert_stmt->bind_param("isssssi", $teacher_id, $attendance_date, $status, $check_in_time, $check_out_time, $notes, $user_id);
                    $insert_stmt->execute();
                    $new_count++;
                }
            }
            
            $conn->commit();
            
            // تسجيل النشاط
            log_activity($user_id, 'take_teacher_attendance', 'teacher_attendance', null, null, [
                'date' => $attendance_date,
                'new_records' => $new_count,
                'updated_records' => $updated_count
            ]);
            
            $success_message = "تم حفظ الحضور بنجاح. سجلات جديدة: $new_count، سجلات محدثة: $updated_count";
            
        } catch (Exception $e) {
            $conn->rollback();
            $error_message = 'حدث خطأ: ' . $e->getMessage();
        }
    }
}

// تاريخ اليوم كافتراضي
$selected_date = $_GET['date'] ?? date('Y-m-d');

// جلب قائمة المعلمين
$teachers_query = "
    SELECT t.id as teacher_id, u.id as user_id, u.full_name, u.username, t.employee_id, t.department
    FROM teachers t
    JOIN users u ON t.user_id = u.id
    WHERE u.role = 'teacher' AND u.status = 'active'
    ORDER BY u.full_name
";
$teachers_result = $conn->query($teachers_query);

// جلب حالة الحضور الحالية للتاريخ المحدد
$attendance_status = [];
if ($teachers_result->num_rows > 0) {
    $teacher_ids = [];
    $teachers_result->data_seek(0);
    while ($teacher = $teachers_result->fetch_assoc()) {
        $teacher_ids[] = $teacher['teacher_id'];
    }
    
    if (!empty($teacher_ids)) {
        $ids_placeholder = str_repeat('?,', count($teacher_ids) - 1) . '?';
        $attendance_stmt = $conn->prepare("
            SELECT teacher_id, status, check_in_time, check_out_time, notes
            FROM teacher_attendance
            WHERE teacher_id IN ($ids_placeholder) AND attendance_date = ?
        ");
        $params = array_merge($teacher_ids, [$selected_date]);
        $types = str_repeat('i', count($teacher_ids)) . 's';
        $attendance_stmt->bind_param($types, ...$params);
        $attendance_stmt->execute();
        $attendance_result = $attendance_stmt->get_result();
        
        while ($row = $attendance_result->fetch_assoc()) {
            $attendance_status[$row['teacher_id']] = $row;
        }
    }
}

// جلب الغياب بالخصم للمعلمين
$teacher_absences = [];
$absence_stmt = $conn->prepare("
    SELECT sad.user_id, sad.absence_date, sad.deduction_amount, sad.absence_type, sad.status
    FROM staff_absences_with_deduction sad
    JOIN users u ON sad.user_id = u.id
    WHERE u.role = 'teacher' AND sad.absence_date = ?
    AND sad.status IN ('processed', 'approved')
");
$absence_stmt->bind_param("s", $selected_date);
$absence_stmt->execute();
$absence_result = $absence_stmt->get_result();
while ($row = $absence_result->fetch_assoc()) {
    $teacher_absences[$row['user_id']] = $row;
}

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-chalkboard-teacher text-primary me-2"></i>
                حضور وغياب المعلمين
            </h2>
            <p class="text-muted mb-0">إدارة حضور وغياب المعلمين</p>
        </div>
        <div>
            <a href="teachers_absence_with_deduction.php" class="btn btn-warning">
                <i class="fas fa-user-times me-2"></i>تسجيل غياب بالخصم
            </a>
            <a href="teachers_reports.php" class="btn btn-info">
                <i class="fas fa-chart-bar me-2"></i>تقارير المعلمين
            </a>
        </div>
    </div>

    <!-- Messages -->
    <?php if (isset($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Date Selection -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3 align-items-end">
                <div class="col-md-4">
                    <label for="date" class="form-label">التاريخ</label>
                    <input type="date" class="form-control" id="date" name="date" 
                           value="<?php echo htmlspecialchars($selected_date); ?>" required>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>عرض
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Attendance Form -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-calendar-check me-2"></i>
                تسجيل الحضور - <?php echo date('Y-m-d', strtotime($selected_date)); ?>
            </h5>
        </div>
        <div class="card-body">
            <?php if ($teachers_result->num_rows > 0): ?>
                <form method="POST" class="needs-validation" novalidate>
                    <input type="hidden" name="attendance_date" value="<?php echo htmlspecialchars($selected_date); ?>">
                    
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>المعلم</th>
                                    <th>رقم الموظف</th>
                                    <th>القسم</th>
                                    <th>الحالة</th>
                                    <th>وقت الدخول</th>
                                    <th>وقت الخروج</th>
                                    <th>ملاحظات</th>
                                    <th>غياب بالخصم</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                $teachers_result->data_seek(0);
                                while ($teacher = $teachers_result->fetch_assoc()): 
                                    $current_attendance = $attendance_status[$teacher['teacher_id']] ?? null;
                                    $has_deduction = isset($teacher_absences[$teacher['user_id']]);
                                ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($teacher['full_name']); ?></strong>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($teacher['username']); ?></small>
                                        </td>
                                        <td><?php echo htmlspecialchars($teacher['employee_id'] ?? '-'); ?></td>
                                        <td><?php echo htmlspecialchars($teacher['department'] ?? '-'); ?></td>
                                        <td>
                                            <select name="attendance[<?php echo $teacher['teacher_id']; ?>][status]" 
                                                    class="form-select form-select-sm" required>
                                                <option value="">اختر الحالة</option>
                                                <option value="present" <?php echo ($current_attendance['status'] ?? '') === 'present' ? 'selected' : ''; ?>>حاضر</option>
                                                <option value="absent" <?php echo ($current_attendance['status'] ?? '') === 'absent' ? 'selected' : ''; ?>>غائب</option>
                                                <option value="late" <?php echo ($current_attendance['status'] ?? '') === 'late' ? 'selected' : ''; ?>>متأخر</option>
                                                <option value="excused" <?php echo ($current_attendance['status'] ?? '') === 'excused' ? 'selected' : ''; ?>>غائب بعذر</option>
                                                <option value="sick_leave" <?php echo ($current_attendance['status'] ?? '') === 'sick_leave' ? 'selected' : ''; ?>>إجازة مرضية</option>
                                                <option value="regular_leave" <?php echo ($current_attendance['status'] ?? '') === 'regular_leave' ? 'selected' : ''; ?>>إجازة اعتيادية</option>
                                            </select>
                                        </td>
                                        <td>
                                            <input type="time" name="attendance[<?php echo $teacher['teacher_id']; ?>][check_in_time]" 
                                                   class="form-control form-control-sm" 
                                                   value="<?php echo htmlspecialchars($current_attendance['check_in_time'] ?? ''); ?>">
                                        </td>
                                        <td>
                                            <input type="time" name="attendance[<?php echo $teacher['teacher_id']; ?>][check_out_time]" 
                                                   class="form-control form-control-sm" 
                                                   value="<?php echo htmlspecialchars($current_attendance['check_out_time'] ?? ''); ?>">
                                        </td>
                                        <td>
                                            <input type="text" name="attendance[<?php echo $teacher['teacher_id']; ?>][notes]" 
                                                   class="form-control form-control-sm" 
                                                   value="<?php echo htmlspecialchars($current_attendance['notes'] ?? ''); ?>" 
                                                   placeholder="ملاحظات">
                                        </td>
                                        <td>
                                            <?php if ($has_deduction): ?>
                                                <span class="badge bg-warning text-dark">
                                                    <i class="fas fa-minus-circle me-1"></i>
                                                    <?php echo number_format($teacher_absences[$teacher['user_id']]['deduction_amount'], 2); ?> ج.م
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="text-end mt-3">
                        <button type="submit" name="save_attendance" class="btn btn-success btn-lg">
                            <i class="fas fa-save me-2"></i>حفظ الحضور
                        </button>
                    </div>
                </form>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-chalkboard-teacher fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا يوجد معلمون</h5>
                    <p class="text-muted">لم يتم العثور على معلمين نشطين في النظام</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?php require_once '../includes/footer.php'; ?>
