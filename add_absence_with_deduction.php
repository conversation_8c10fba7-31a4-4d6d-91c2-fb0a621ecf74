<?php
/**
 * إضافة نوع "غياب بالخصم" للنظام
 * Add "Absence with Deduction" Type to System
 */

require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/functions.php';

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>🔧 إضافة نوع 'غياب بالخصم' للنظام</h1>";

echo "<h3>📋 التحسينات المطبقة:</h3>";

echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>✅ 1. إضافة زر 'غياب بالخصم' في الحضور الذكي:</h4>";
echo "<ul>";
echo "<li>✅ تم إضافة الزر في صفحة الحضور الذكي للمعلمين والإداريين</li>";
echo "<li>✅ الزر يوجه إلى نموذج الإجازة مع نوع 'absence_with_deduction'</li>";
echo "<li>✅ أيقونة مناسبة: fas fa-minus-circle</li>";
echo "<li>✅ لون تحذيري: btn-warning</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>✅ 2. تحديث نموذج الإجازة (leave_form.php):</h4>";
echo "<ul>";
echo "<li>✅ دعم نوع 'absence_with_deduction' في قائمة الأنواع المسموحة</li>";
echo "<li>✅ عنوان مخصص: 'غياب بالخصم'</li>";
echo "<li>✅ أيقونة مخصصة: fas fa-minus-circle</li>";
echo "<li>✅ لون مخصص: bg-warning مع نص داكن</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>✅ 3. تحديث عرض الإجازات في قوائم الحضور:</h4>";
echo "<ul>";
echo "<li>✅ تحديث attendance/index.php لعرض 'غياب بالخصم'</li>";
echo "<li>✅ عرض صحيح في قسم المعلمين</li>";
echo "<li>✅ عرض صحيح في قسم الإداريين</li>";
echo "<li>✅ معالجة خاصة للنوع الجديد</li>";
echo "</ul>";
echo "</div>";

// التحقق من إعدادات الإجازات في قاعدة البيانات
echo "<h3>🔍 فحص إعدادات الإجازات:</h3>";

try {
    // التحقق من وجود جدول leave_settings
    $check_table = $conn->query("SHOW TABLES LIKE 'leave_settings'");
    if ($check_table && $check_table->num_rows > 0) {
        echo "<p>✅ جدول leave_settings موجود</p>";
        
        // التحقق من وجود نوع "غياب بالخصم"
        $check_absence = $conn->query("SELECT * FROM leave_settings WHERE leave_type = 'absence_with_deduction'");
        if ($check_absence && $check_absence->num_rows > 0) {
            echo "<p>✅ نوع 'غياب بالخصم' موجود في إعدادات الإجازات</p>";
        } else {
            echo "<p>❌ نوع 'غياب بالخصم' غير موجود في إعدادات الإجازات</p>";
            
            // إضافة النوع الجديد
            $insert_setting = $conn->prepare("
                INSERT INTO leave_settings (
                    leave_type, 
                    max_days_per_year, 
                    max_consecutive_days, 
                    requires_approval, 
                    deduct_from_salary, 
                    is_active, 
                    description
                ) VALUES (
                    'absence_with_deduction', 
                    365, 
                    30, 
                    1, 
                    1, 
                    1, 
                    'غياب بالخصم من الراتب'
                )
            ");
            
            if ($insert_setting && $insert_setting->execute()) {
                echo "<p>✅ تم إضافة نوع 'غياب بالخصم' إلى إعدادات الإجازات</p>";
            } else {
                echo "<p>❌ فشل في إضافة النوع الجديد: " . $conn->error . "</p>";
            }
        }
        
        // عرض جميع أنواع الإجازات
        $all_types = $conn->query("SELECT * FROM leave_settings ORDER BY leave_type");
        if ($all_types && $all_types->num_rows > 0) {
            echo "<h4>📋 أنواع الإجازات المتاحة:</h4>";
            echo "<table style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='border: 1px solid #ddd; padding: 8px;'>نوع الإجازة</th>";
            echo "<th style='border: 1px solid #ddd; padding: 8px;'>الحد الأقصى سنوياً</th>";
            echo "<th style='border: 1px solid #ddd; padding: 8px;'>خصم من الراتب</th>";
            echo "<th style='border: 1px solid #ddd; padding: 8px;'>الحالة</th>";
            echo "</tr>";
            
            while ($type = $all_types->fetch_assoc()) {
                echo "<tr>";
                echo "<td style='border: 1px solid #ddd; padding: 8px;'>";
                if ($type['leave_type'] === 'absence_with_deduction') {
                    echo "<strong>غياب بالخصم</strong>";
                } else {
                    echo $type['leave_type'];
                }
                echo "</td>";
                echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $type['max_days_per_year'] . " يوم</td>";
                echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . ($type['deduct_from_salary'] ? 'نعم' : 'لا') . "</td>";
                echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . ($type['is_active'] ? 'نشط' : 'معطل') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } else {
        echo "<p>❌ جدول leave_settings غير موجود</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "<h3>🧪 اختبار الميزات الجديدة:</h3>";

echo "<div style='background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>🔗 روابط الاختبار:</h4>";
echo "<ul>";
echo "<li><a href='attendance/smart_attendance.php?tab=teachers' target='_blank'>الحضور الذكي للمعلمين</a></li>";
echo "<li><a href='attendance/smart_attendance.php?tab=admins' target='_blank'>الحضور الذكي للإداريين</a></li>";
echo "<li><a href='attendance/leave_form.php?type=absence_with_deduction' target='_blank'>نموذج غياب بالخصم</a></li>";
echo "<li><a href='attendance/index.php?tab=teachers' target='_blank'>قائمة حضور المعلمين</a></li>";
echo "<li><a href='attendance/index.php?tab=admins' target='_blank'>قائمة حضور الإداريين</a></li>";
echo "</ul>";
echo "</div>";

echo "<h3>📝 خطوات الاختبار:</h3>";

echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>🧪 للتأكد من عمل الميزة:</h4>";
echo "<ol>";
echo "<li>اذهب إلى الحضور الذكي للمعلمين أو الإداريين</li>";
echo "<li>ابحث عن زر 'غياب بالخصم' (لون أصفر تحذيري)</li>";
echo "<li>انقر على الزر للانتقال إلى نموذج الإجازة</li>";
echo "<li>تأكد من ظهور العنوان 'غياب بالخصم' باللون الأصفر</li>";
echo "<li>املأ النموذج واحفظ</li>";
echo "<li>تحقق من ظهور الإجازة في قوائم الحضور</li>";
echo "<li>تأكد من عرض 'غياب بالخصم' بدلاً من النصوص الافتراضية</li>";
echo "</ol>";
echo "</div>";

echo "<h3>⚙️ الميزات المضافة:</h3>";

echo "<div style='background: #e2e3e5; color: #383d41; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>🎯 الوظائف الجديدة:</h4>";
echo "<ul>";
echo "<li><strong>زر غياب بالخصم:</strong> في صفحة الحضور الذكي</li>";
echo "<li><strong>نموذج مخصص:</strong> لتسجيل غياب بالخصم</li>";
echo "<li><strong>عرض مميز:</strong> في قوائم الحضور</li>";
echo "<li><strong>إعدادات قاعدة البيانات:</strong> لنوع الإجازة الجديد</li>";
echo "<li><strong>تكامل كامل:</strong> مع النظام الموجود</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🔄 التحديثات المطلوبة لاحقاً:</h3>";

echo "<div style='background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>📋 تحسينات مستقبلية:</h4>";
echo "<ul>";
echo "<li>إضافة فلتر بحث حسب نوع الإجازة في قوائم الحضور</li>";
echo "<li>إضافة تقارير خاصة بالغياب بالخصم</li>";
echo "<li>ربط النوع بنظام الرواتب للخصم التلقائي</li>";
echo "<li>إضافة إشعارات للإدارة عند تسجيل غياب بالخصم</li>";
echo "<li>إضافة إحصائيات خاصة بهذا النوع</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>🎉 تم إضافة 'غياب بالخصم' بنجاح!</h4>";
echo "<p>النظام الآن يدعم نوع إجازة جديد 'غياب بالخصم' مع جميع الوظائف المطلوبة.</p>";
echo "<p>يمكن للإداريين الآن تسجيل هذا النوع من الغياب للمعلمين والموظفين.</p>";
echo "</div>";
?>
