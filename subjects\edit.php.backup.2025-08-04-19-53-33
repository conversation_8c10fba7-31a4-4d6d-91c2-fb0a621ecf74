<?php
/**
 * تعديل المادة الدراسية
 * Edit Subject
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

// التحقق من وجود معرف المادة
$subject_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
if (!$subject_id) {
    $_SESSION['error_message'] = 'معرف المادة غير صحيح';
    header('Location: index.php');
    exit();
}

// جلب بيانات المادة
$subject_query = "SELECT * FROM subjects WHERE id = ?";
$subject_stmt = $conn->prepare($subject_query);
$subject_stmt->bind_param("i", $subject_id);
$subject_stmt->execute();
$subject_result = $subject_stmt->get_result();

if ($subject_result->num_rows === 0) {
    $_SESSION['error_message'] = 'المادة غير موجودة';
    header('Location: index.php');
    exit();
}

$subject = $subject_result->fetch_assoc();

// الحصول على المراحل الدراسية
$stages_query = "SELECT id, stage_name, stage_code FROM educational_stages WHERE status = 'active' ORDER BY sort_order";
$stages_result = $conn->query($stages_query);
$stages = [];
if ($stages_result) {
    while ($row = $stages_result->fetch_assoc()) {
        $stages[] = $row;
    }
}

// الحصول على الصفوف الدراسية
$grades_query = "
    SELECT g.id, g.grade_name, g.grade_code, g.stage_id, es.stage_name
    FROM grades g
    INNER JOIN educational_stages es ON g.stage_id = es.id
    WHERE g.status = 'active'
    ORDER BY es.sort_order, g.sort_order
";
$grades_result = $conn->query($grades_query);
$grades = [];
if ($grades_result) {
    while ($row = $grades_result->fetch_assoc()) {
        $grades[] = $row;
    }
}

$error = '';
$success = '';
$validation_errors = [];

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'])) {
        $error = 'رمز الأمان غير صحيح';
    } else {
        // جمع البيانات من النموذج
        $subject_name = clean_input($_POST['subject_name'] ?? '');
        $subject_name_en = clean_input($_POST['subject_name_en'] ?? '');
        $subject_code = clean_input($_POST['subject_code'] ?? '');
        $stage_id = intval($_POST['stage_id'] ?? 0);
        $grade_id = intval($_POST['grade_id'] ?? 0);
        $credit_hours = intval($_POST['credit_hours'] ?? 1);
        $department = clean_input($_POST['department'] ?? '');
        $description = clean_input($_POST['description'] ?? '');
        $status = clean_input($_POST['status'] ?? 'active');

        // التحقق من صحة البيانات
        if (empty($subject_name)) {
            $validation_errors['subject_name'] = 'اسم المادة مطلوب';
        }

        if (empty($subject_code)) {
            $validation_errors['subject_code'] = 'كود المادة مطلوب';
        } else {
            // التحقق من عدم تكرار كود المادة
            $check_code_query = "SELECT id FROM subjects WHERE subject_code = ? AND id != ?";
            $check_stmt = $conn->prepare($check_code_query);
            $check_stmt->bind_param("si", $subject_code, $subject_id);
            $check_stmt->execute();
            $result = $check_stmt->get_result();
            if ($result->num_rows > 0) {
                $validation_errors['subject_code'] = 'كود المادة موجود مسبقاً';
            }
            $check_stmt->close();
        }

        if ($stage_id <= 0) {
            $validation_errors['stage_id'] = 'المرحلة الدراسية مطلوبة';
        }

        if ($grade_id <= 0) {
            $validation_errors['grade_id'] = 'الصف الدراسي مطلوب';
        }

        if ($credit_hours < 1 || $credit_hours > 10) {
            $validation_errors['credit_hours'] = 'الساعات المعتمدة يجب أن تكون بين 1 و 10';
        }

        if (!in_array($status, ['active', 'inactive'])) {
            $validation_errors['status'] = 'حالة المادة غير صحيحة';
        }

        // إذا لم توجد أخطاء، قم بتحديث البيانات
        if (empty($validation_errors)) {
            $update_query = "
                UPDATE subjects SET
                    subject_name = ?, subject_name_en = ?, subject_code = ?,
                    stage_id = ?, grade_id = ?, description = ?,
                    credit_hours = ?, department = ?, status = ?,
                    updated_at = NOW()
                WHERE id = ?
            ";

            $stmt = $conn->prepare($update_query);
            if ($stmt) {
                $stmt->bind_param('sssiissssi',
                    $subject_name, $subject_name_en, $subject_code, $stage_id, $grade_id,
                    $description, $credit_hours, $department, $status, $subject_id
                );

                if ($stmt->execute()) {
                    // تسجيل النشاط
                    log_activity($_SESSION['user_id'], 'edit_subject', 'subjects', $subject_id, [
                        'old_name' => $subject['subject_name'],
                        'old_code' => $subject['subject_code']
                    ], [
                        'subject_name' => $subject_name,
                        'subject_code' => $subject_code
                    ]);

                    $_SESSION['success_message'] = 'تم تحديث المادة بنجاح';
                    header('Location: view.php?id=' . $subject_id);
                    exit();
                } else {
                    $error = 'خطأ في قاعدة البيانات: ' . $conn->error;
                }
                $stmt->close();
            } else {
                $error = 'خطأ في قاعدة البيانات: ' . $conn->error;
            }
        } else {
            $error = 'يرجى إصلاح الأخطاء أدناه';
        }
    }
} else {
    // تعيين القيم الافتراضية من بيانات المادة الموجودة
    $subject_name = $subject['subject_name'];
    $subject_name_en = $subject['subject_name_en'];
    $subject_code = $subject['subject_code'];
    $stage_id = $subject['stage_id'];
    $grade_id = $subject['grade_id'];
    $credit_hours = $subject['credit_hours'];
    $department = $subject['department'];
    $description = $subject['description'];
    $status = $subject['status'];
}

$page_title = 'تعديل المادة - ' . $subject['subject_name'];
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-edit me-2 text-warning"></i>تعديل المادة
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="index.php">المواد الدراسية</a></li>
                    <li class="breadcrumb-item"><a href="view.php?id=<?php echo $subject_id; ?>">عرض المادة</a></li>
                    <li class="breadcrumb-item active">تعديل</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="view.php?id=<?php echo $subject_id; ?>" class="btn btn-outline-info me-2">
                <i class="fas fa-eye me-2"></i>عرض المادة
            </a>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
            </a>
        </div>
    </div>

    <!-- عرض الرسائل -->
    <?php if (!empty($success)): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- النموذج الرئيسي -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header bg-warning text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>معلومات المادة
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <?php echo csrf_token_field(); ?>

                        <div class="row">
                            <!-- اسم المادة بالعربية -->
                            <div class="col-md-6 mb-3">
                                <label for="subject_name" class="form-label">
                                    اسم المادة <span class="text-danger">*</span>
                                </label>
                                <input type="text"
                                       class="form-control <?php echo isset($validation_errors['subject_name']) ? 'is-invalid' : ''; ?>"
                                       id="subject_name"
                                       name="subject_name"
                                       value="<?php echo htmlspecialchars($subject_name ?? ''); ?>"
                                       placeholder="أدخل اسم المادة"
                                       required>
                                <?php if (isset($validation_errors['subject_name'])): ?>
                                    <div class="invalid-feedback">
                                        <?php echo $validation_errors['subject_name']; ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- اسم المادة بالإنجليزية -->
                            <div class="col-md-6 mb-3">
                                <label for="subject_name_en" class="form-label">
                                    اسم المادة بالإنجليزية
                                </label>
                                <input type="text"
                                       class="form-control"
                                       id="subject_name_en"
                                       name="subject_name_en"
                                       value="<?php echo htmlspecialchars($subject_name_en ?? ''); ?>"
                                       placeholder="Enter subject name in English">
                            </div>
                        </div>

                        <div class="row">
                            <!-- رمز المادة -->
                            <div class="col-md-4 mb-3">
                                <label for="subject_code" class="form-label">
                                    كود المادة <span class="text-danger">*</span>
                                </label>
                                <input type="text"
                                       class="form-control <?php echo isset($validation_errors['subject_code']) ? 'is-invalid' : ''; ?>"
                                       id="subject_code"
                                       name="subject_code"
                                       value="<?php echo htmlspecialchars($subject_code ?? ''); ?>"
                                       placeholder="مثال: MATH101"
                                       style="text-transform: uppercase;"
                                       pattern="[A-Z0-9_]+"
                                       required>
                                <div class="form-text">استخدم أحرف إنجليزية كبيرة وأرقام فقط</div>
                                <?php if (isset($validation_errors['subject_code'])): ?>
                                    <div class="invalid-feedback">
                                        <?php echo $validation_errors['subject_code']; ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- المرحلة الدراسية -->
                            <div class="col-md-4 mb-3">
                                <label for="stage_id" class="form-label">
                                    المرحلة الدراسية <span class="text-danger">*</span>
                                </label>
                                <select class="form-select <?php echo isset($validation_errors['stage_id']) ? 'is-invalid' : ''; ?>"
                                        id="stage_id" name="stage_id" required onchange="updateGrades()">
                                    <option value="">اختر المرحلة</option>
                                    <?php foreach ($stages as $stage): ?>
                                        <option value="<?php echo $stage['id']; ?>"
                                                <?php echo $stage_id == $stage['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($stage['stage_name']); ?> (<?php echo htmlspecialchars($stage['stage_code']); ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (isset($validation_errors['stage_id'])): ?>
                                    <div class="invalid-feedback">
                                        <?php echo $validation_errors['stage_id']; ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- الصف الدراسي -->
                            <div class="col-md-4 mb-3">
                                <label for="grade_id" class="form-label">
                                    الصف الدراسي <span class="text-danger">*</span>
                                </label>
                                <select class="form-select <?php echo isset($validation_errors['grade_id']) ? 'is-invalid' : ''; ?>"
                                        id="grade_id" name="grade_id" required>
                                    <option value="">اختر الصف أولاً</option>
                                    <?php foreach ($grades as $grade): ?>
                                        <option value="<?php echo $grade['id']; ?>"
                                                data-stage="<?php echo $grade['stage_id']; ?>"
                                                <?php echo $grade_id == $grade['id'] ? 'selected' : ''; ?>
                                                style="display: none;">
                                            <?php echo htmlspecialchars($grade['grade_name']); ?> (<?php echo htmlspecialchars($grade['grade_code']); ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (isset($validation_errors['grade_id'])): ?>
                                    <div class="invalid-feedback">
                                        <?php echo $validation_errors['grade_id']; ?>
                                    </div>
                                <?php endif; ?>
                                <div class="form-text">اختر المرحلة أولاً</div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- الساعات المعتمدة -->
                            <div class="col-md-4 mb-3">
                                <label for="credit_hours" class="form-label">
                                    الساعات المعتمدة <span class="text-danger">*</span>
                                </label>
                                <input type="number"
                                       class="form-control <?php echo isset($validation_errors['credit_hours']) ? 'is-invalid' : ''; ?>"
                                       id="credit_hours"
                                       name="credit_hours"
                                       value="<?php echo $credit_hours; ?>"
                                       min="1"
                                       max="10"
                                       required>
                                <?php if (isset($validation_errors['credit_hours'])): ?>
                                    <div class="invalid-feedback">
                                        <?php echo $validation_errors['credit_hours']; ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- القسم -->
                            <div class="col-md-4 mb-3">
                                <label for="department" class="form-label">
                                    القسم
                                </label>
                                <input type="text"
                                       class="form-control"
                                       id="department"
                                       name="department"
                                       value="<?php echo htmlspecialchars($department ?? ''); ?>"
                                       placeholder="مثال: رياضيات، علوم، لغة عربية">
                            </div>

                            <!-- حالة المادة -->
                            <div class="col-md-4 mb-3">
                                <label for="status" class="form-label">
                                    الحالة <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="active" <?php echo $status === 'active' ? 'selected' : ''; ?>>
                                        نشط
                                    </option>
                                    <option value="inactive" <?php echo $status === 'inactive' ? 'selected' : ''; ?>>
                                        غير نشط
                                    </option>
                                </select>
                            </div>
                        </div>

                        <!-- الوصف -->
                        <div class="mb-4">
                            <label for="description" class="form-label">
                                الوصف
                            </label>
                            <textarea class="form-control"
                                      id="description"
                                      name="description"
                                      rows="4"
                                      placeholder="أدخل وصف المادة (اختياري)"><?php echo htmlspecialchars($description ?? ''); ?></textarea>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="d-flex justify-content-between">
                            <div>
                                <a href="index.php" class="btn btn-outline-secondary me-2">
                                    <i class="fas fa-times me-2"></i>إلغاء
                                </a>
                                <a href="view.php?id=<?php echo $subject_id; ?>" class="btn btn-outline-info">
                                    <i class="fas fa-eye me-2"></i>عرض المادة
                                </a>
                            </div>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-2"></i>حفظ التعديلات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- الشريط الجانبي -->
        <div class="col-lg-4">
            <!-- معلومات المادة الحالية -->
            <div class="card shadow mb-4">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>المعلومات الحالية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">اسم المادة الحالي:</small>
                        <div class="fw-bold"><?php echo htmlspecialchars($subject['subject_name']); ?></div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">كود المادة الحالي:</small>
                        <div class="fw-bold"><?php echo htmlspecialchars($subject['subject_code']); ?></div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">تاريخ الإنشاء:</small>
                        <div><?php echo date('d/m/Y H:i', strtotime($subject['created_at'])); ?></div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">آخر تحديث:</small>
                        <div><?php echo date('d/m/Y H:i', strtotime($subject['updated_at'])); ?></div>
                    </div>
                </div>
            </div>

            <!-- نصائح وإرشادات -->
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>نصائح وإرشادات
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-success">كود المادة</h6>
                        <p class="small text-muted">
                            استخدم كود واضح ومميز للمادة مثل MATH101 للرياضيات أو SCI201 للعلوم.
                        </p>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-success">المرحلة والصف</h6>
                        <p class="small text-muted">
                            تأكد من اختيار المرحلة الدراسية أولاً ثم الصف المناسب للمادة.
                        </p>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-success">الساعات المعتمدة</h6>
                        <p class="small text-muted">
                            حدد عدد الساعات المعتمدة للمادة حسب المنهج الدراسي المعتمد.
                        </p>
                    </div>

                    <div class="alert alert-warning">
                        <small>
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            <strong>تنبيه:</strong> تأكد من صحة البيانات قبل الحفظ حيث أن تغيير كود المادة قد يؤثر على البيانات المرتبطة.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحديث قائمة الصفوف عند تغيير المرحلة
function updateGrades() {
    const stageSelect = document.getElementById('stage_id');
    const gradeSelect = document.getElementById('grade_id');
    const selectedStage = stageSelect.value;

    // إخفاء جميع الصفوف
    Array.from(gradeSelect.options).forEach(option => {
        if (option.value === '') return; // تجاهل الخيار الافتراضي

        if (selectedStage === '' || option.dataset.stage === selectedStage) {
            option.style.display = 'block';
        } else {
            option.style.display = 'none';
        }
    });

    // إعادة تعيين الصف المحدد إذا لم يعد متاحاً
    if (gradeSelect.value && gradeSelect.selectedOptions[0].style.display === 'none') {
        gradeSelect.value = '';
    }
}

// تحويل كود المادة إلى أحرف كبيرة
document.getElementById('subject_code').addEventListener('input', function() {
    this.value = this.value.toUpperCase();
});

// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// تحميل الصفوف عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateGrades();
});
</script>

<?php include_once '../includes/footer.php'; ?>