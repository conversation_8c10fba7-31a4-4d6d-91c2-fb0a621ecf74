<?php
/**
 * عرض تفاصيل الإداري
 * View Administrator Details
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    redirect_to('../dashboard/');
}

// التحقق من وجود معرف الإداري
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error_message'] = 'معرف الإداري غير صحيح';
    redirect_to('index.php');
}

$administrator_id = intval($_GET['id']);

// جلب بيانات الإداري
$admin_query = "
    SELECT
        a.*,
        u.username,
        u.email,
        u.status as user_status,
        u.last_login,
        u.created_at as user_created_at,
        COALESCE(u.full_name, u.username) as full_name
    FROM staff a
    INNER JOIN users u ON a.user_id = u.id
    WHERE a.id = ? AND u.role = 'staff'
";

$stmt = $conn->prepare($admin_query);
if (!$stmt) {
    $_SESSION['error_message'] = 'خطأ في قاعدة البيانات';
    redirect_to('index.php');
}

$stmt->bind_param('i', $administrator_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    $_SESSION['error_message'] = 'الإداري غير موجود';
    redirect_to('index.php');
}

$administrator = $result->fetch_assoc();
$stmt->close();

// جلب إحصائيات الحضور (سيتم إضافة هذه الوظيفة لاحقاً)
$attendance_stats_query = "
    SELECT
        0 as total_days,
        0 as present_days,
        0 as absent_days,
        0 as late_days
";

$attendance_stats = ['total_days' => 0, 'present_days' => 0, 'absent_days' => 0, 'late_days' => 0];
$stmt = $conn->prepare($attendance_stats_query);
if ($stmt) {
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result->num_rows > 0) {
        $attendance_stats = $result->fetch_assoc();
    }
    $stmt->close();
}

// جلب آخر سجلات الحضور (سيتم إضافة هذه الوظيفة لاحقاً)
$recent_attendance = [];

$page_title = 'تفاصيل الإداري: ' . ($administrator['full_name'] ?? $administrator['username']);
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-user-tie text-primary me-2"></i>
                تفاصيل الإداري
            </h2>
            <p class="text-muted mb-0"><?php echo htmlspecialchars($administrator['full_name'] ?? $administrator['username']); ?></p>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة للإداريين
            </a>
            <a href="edit.php?id=<?php echo $administrator_id; ?>" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>تعديل البيانات
            </a>
        </div>
    </div>

    <div class="row">
        <!-- معلومات أساسية -->
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-body text-center">
                    <?php if (!empty($administrator['profile_picture'])): ?>
                        <img src="../uploads/profiles/<?php echo $administrator['profile_picture']; ?>" 
                             alt="<?php echo htmlspecialchars($administrator['full_name']); ?>"
                             class="rounded-circle mb-3" 
                             width="120" 
                             height="120">
                    <?php else: ?>
                        <div class="bg-secondary rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" 
                             style="width: 120px; height: 120px;">
                            <i class="fas fa-user fa-3x text-white"></i>
                        </div>
                    <?php endif; ?>
                    
                    <h4 class="mb-1"><?php echo htmlspecialchars($administrator['full_name']); ?></h4>
                    <p class="text-muted mb-2"><?php echo htmlspecialchars($administrator['position'] ?? 'غير محدد'); ?></p>
                    
                    <?php
                    $status_class = match($administrator['user_status']) {
                        'active' => 'success',
                        'inactive' => 'secondary',
                        'suspended' => 'danger',
                        default => 'secondary'
                    };
                    ?>
                    <span class="badge bg-<?php echo $status_class; ?> mb-3">
                        <?php echo __($administrator['user_status']); ?>
                    </span>

                    <div class="row text-center">
                        <div class="col-4">
                            <div class="border-end">
                                <h5 class="mb-0"><?php echo $administrator['experience_years'] ?? 0; ?></h5>
                                <small class="text-muted">سنوات الخبرة</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="border-end">
                                <h5 class="mb-0"><?php echo $attendance_stats['present_days']; ?></h5>
                                <small class="text-muted">أيام الحضور</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <h5 class="mb-0"><?php echo $attendance_stats['absent_days']; ?></h5>
                            <small class="text-muted">أيام الغياب</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات الاتصال -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-address-book me-2"></i>معلومات الاتصال
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">البريد الإلكتروني</label>
                        <p class="mb-0"><?php echo htmlspecialchars($administrator['email']); ?></p>
                    </div>
                    
                    <?php if (!empty($administrator['phone'])): ?>
                    <div class="mb-3">
                        <label class="form-label text-muted">رقم الهاتف</label>
                        <p class="mb-0"><?php echo htmlspecialchars($administrator['phone']); ?></p>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($administrator['address'])): ?>
                    <div class="mb-3">
                        <label class="form-label text-muted">العنوان</label>
                        <p class="mb-0"><?php echo htmlspecialchars($administrator['address']); ?></p>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($administrator['emergency_contact_name'])): ?>
                    <div class="mb-3">
                        <label class="form-label text-muted">جهة الاتصال في الطوارئ</label>
                        <p class="mb-0"><?php echo htmlspecialchars($administrator['emergency_contact_name']); ?></p>
                        <?php if (!empty($administrator['emergency_contact_phone'])): ?>
                            <small class="text-muted"><?php echo htmlspecialchars($administrator['emergency_contact_phone']); ?></small>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- التفاصيل الرئيسية -->
        <div class="col-md-8">
            <!-- المعلومات الشخصية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-id-card me-2"></i>المعلومات الشخصية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">اسم المستخدم</label>
                                <p class="mb-0"><?php echo htmlspecialchars($administrator['username']); ?></p>
                            </div>
                            
                            <?php if (!empty($administrator['employee_id'])): ?>
                            <div class="mb-3">
                                <label class="form-label text-muted">رقم الموظف</label>
                                <p class="mb-0"><?php echo htmlspecialchars($administrator['employee_id']); ?></p>
                            </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($administrator['national_id'])): ?>
                            <div class="mb-3">
                                <label class="form-label text-muted">الرقم القومي</label>
                                <p class="mb-0"><?php echo htmlspecialchars($administrator['national_id']); ?></p>
                            </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($administrator['date_of_birth'])): ?>
                            <div class="mb-3">
                                <label class="form-label text-muted">تاريخ الميلاد</label>
                                <p class="mb-0"><?php echo date('Y-m-d', strtotime($administrator['date_of_birth'])); ?></p>
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="col-md-6">
                            <?php if (!empty($administrator['gender'])): ?>
                            <div class="mb-3">
                                <label class="form-label text-muted">الجنس</label>
                                <p class="mb-0"><?php echo __($administrator['gender']); ?></p>
                            </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($administrator['nationality'])): ?>
                            <div class="mb-3">
                                <label class="form-label text-muted">الجنسية</label>
                                <p class="mb-0"><?php echo htmlspecialchars($administrator['nationality']); ?></p>
                            </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($administrator['last_login'])): ?>
                            <div class="mb-3">
                                <label class="form-label text-muted">آخر تسجيل دخول</label>
                                <p class="mb-0"><?php echo date('Y-m-d H:i', strtotime($administrator['last_login'])); ?></p>
                            </div>
                            <?php endif; ?>
                            
                            <div class="mb-3">
                                <label class="form-label text-muted">تاريخ إنشاء الحساب</label>
                                <p class="mb-0"><?php echo date('Y-m-d', strtotime($administrator['user_created_at'])); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- المعلومات الوظيفية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-briefcase me-2"></i>المعلومات الوظيفية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <?php if (!empty($administrator['department'])): ?>
                            <div class="mb-3">
                                <label class="form-label text-muted">القسم</label>
                                <p class="mb-0"><?php echo htmlspecialchars($administrator['department']); ?></p>
                            </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($administrator['qualification'])): ?>
                            <div class="mb-3">
                                <label class="form-label text-muted">المؤهل</label>
                                <p class="mb-0"><?php echo htmlspecialchars($administrator['qualification']); ?></p>
                            </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($administrator['hire_date'])): ?>
                            <div class="mb-3">
                                <label class="form-label text-muted">تاريخ التعيين</label>
                                <p class="mb-0"><?php echo date('Y-m-d', strtotime($administrator['hire_date'])); ?></p>
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="col-md-6">
                            <?php if (!empty($administrator['salary'])): ?>
                            <div class="mb-3">
                                <label class="form-label text-muted">الراتب</label>
                                <p class="mb-0"><?php echo $administrator['salary'] ? number_format($administrator['salary'], 2) . ' ج.م' : '-'; ?></p>
                            </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($administrator['bank_account'])): ?>
                            <div class="mb-3">
                                <label class="form-label text-muted">رقم الحساب البنكي</label>
                                <p class="mb-0"><?php echo htmlspecialchars($administrator['bank_account']); ?></p>
                            </div>
                            <?php endif; ?>
                            
                            <div class="mb-3">
                                <label class="form-label text-muted">حالة الإداري</label>
                                <p class="mb-0">
                                    <span class="badge bg-<?php echo $status_class; ?>">
                                        <?php echo __($administrator['status']); ?>
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- سجل الحضور الأخير -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-check me-2"></i>سجل الحضور الأخير
                        <span class="badge bg-primary"><?php echo count($recent_attendance); ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_attendance)): ?>
                        <div class="text-center py-3">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد سجلات حضور</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>الحالة</th>
                                        <th>وقت الدخول</th>
                                        <th>وقت الخروج</th>
                                        <th>ملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_attendance as $attendance): ?>
                                        <tr>
                                            <td><?php echo date('Y-m-d', strtotime($attendance['attendance_date'])); ?></td>
                                            <td>
                                                <?php
                                                $status_class = match($attendance['status']) {
                                                    'present' => 'success',
                                                    'absent' => 'danger',
                                                    'late' => 'warning',
                                                    'excused' => 'info',
                                                    'sick_leave' => 'secondary',
                                                    'regular_leave' => 'primary',
                                                    default => 'secondary'
                                                };
                                                ?>
                                                <span class="badge bg-<?php echo $status_class; ?>">
                                                    <?php echo __($attendance['status']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo $attendance['check_in_time'] ? date('H:i', strtotime($attendance['check_in_time'])) : '-'; ?></td>
                                            <td><?php echo $attendance['check_out_time'] ? date('H:i', strtotime($attendance['check_out_time'])) : '-'; ?></td>
                                            <td><?php echo htmlspecialchars($attendance['notes'] ?? '-'); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
