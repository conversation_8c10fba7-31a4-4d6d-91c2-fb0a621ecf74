<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('teacher')) {
    header('Location: ../dashboard/');
    exit();
}

global $conn;

// معالجة إضافة قالب جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $template_name = clean_input($_POST['template_name'] ?? '');
    $message_type = clean_input($_POST['message_type'] ?? 'general');
    $subject = clean_input($_POST['subject'] ?? '');
    $message_content = clean_input($_POST['message_content'] ?? '');
    $variables = clean_input($_POST['variables'] ?? '');
    
    $errors = [];
    
    // التحقق من البيانات
    if (empty($template_name)) {
        $errors[] = 'اسم القالب مطلوب';
    }
    if (empty($subject)) {
        $errors[] = 'موضوع الرسالة مطلوب';
    }
    if (empty($message_content)) {
        $errors[] = 'محتوى الرسالة مطلوب';
    }
    
    // التحقق من عدم تكرار اسم القالب
    if (!empty($template_name)) {
        $check_query = "SELECT id FROM message_templates WHERE template_name = ?";
        $stmt = $conn->prepare($check_query);
        $stmt->bind_param('s', $template_name);
        $stmt->execute();
        $existing = $stmt->get_result()->fetch_assoc();
        $stmt->close();
        
        if ($existing) {
            $errors[] = 'اسم القالب موجود بالفعل، يرجى اختيار اسم آخر';
        }
    }
    
    if (empty($errors)) {
        // تحويل المتغيرات إلى JSON
        $variables_json = null;
        if (!empty($variables)) {
            $vars_array = array_map('trim', explode(',', $variables));
            $variables_json = json_encode($vars_array);
        }
        
        // إضافة قالب جديد
        $insert_query = "
            INSERT INTO message_templates 
            (template_name, message_type, subject, message_content, variables, created_by, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ";
        
        $stmt = $conn->prepare($insert_query);
        $stmt->bind_param('sssssi', 
            $template_name, 
            $message_type, 
            $subject, 
            $message_content, 
            $variables_json, 
            $_SESSION['user_id']
        );
        
        if ($stmt->execute()) {
            $_SESSION['success_message'] = 'تم إضافة القالب بنجاح';
            $stmt->close();
            header('Location: message_templates.php');
            exit();
        } else {
            $errors[] = 'فشل في إضافة القالب: ' . $stmt->error;
            $stmt->close();
        }
    }
    
    if (!empty($errors)) {
        $_SESSION['error_message'] = implode('<br>', $errors);
    }
}

$page_title = 'إضافة قالب جديد';
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-plus me-2"></i>
            إضافة قالب رسالة جديد
        </h2>
        <div class="btn-group">
            <a href="message_templates.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back'); ?>
            </a>
        </div>
    </div>

    <!-- رسائل النجاح والخطأ -->
    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php elseif (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file-alt me-2"></i>بيانات القالب الجديد
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <div class="row">
                            <!-- اسم القالب -->
                            <div class="col-md-6 mb-3">
                                <label for="template_name" class="form-label">
                                    <i class="fas fa-tag me-2"></i>اسم القالب
                                    <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="template_name" name="template_name" 
                                       value="<?php echo isset($_POST['template_name']) ? safe_html($_POST['template_name']) : ''; ?>"
                                       placeholder="أدخل اسم القالب..." required>
                                <div class="form-text">اختر اسماً وصفياً يسهل التعرف على القالب</div>
                                <div class="invalid-feedback">اسم القالب مطلوب</div>
                            </div>

                            <!-- نوع الرسالة -->
                            <div class="col-md-6 mb-3">
                                <label for="message_type" class="form-label">
                                    <i class="fas fa-category me-2"></i>نوع الرسالة
                                </label>
                                <select class="form-select" id="message_type" name="message_type">
                                    <option value="general" <?php echo (isset($_POST['message_type']) && $_POST['message_type'] === 'general') ? 'selected' : ''; ?>>
                                        <?php echo __('general'); ?>
                                    </option>
                                    <option value="behavior" <?php echo (isset($_POST['message_type']) && $_POST['message_type'] === 'behavior') ? 'selected' : ''; ?>>
                                        <?php echo __('student_behavior'); ?>
                                    </option>
                                    <option value="academic" <?php echo (isset($_POST['message_type']) && $_POST['message_type'] === 'academic') ? 'selected' : ''; ?>>
                                        <?php echo __('academic'); ?>
                                    </option>
                                    <option value="attendance" <?php echo (isset($_POST['message_type']) && $_POST['message_type'] === 'attendance') ? 'selected' : ''; ?>>
                                        <?php echo __('attendance'); ?>
                                    </option>
                                    <option value="emergency" <?php echo (isset($_POST['message_type']) && $_POST['message_type'] === 'emergency') ? 'selected' : ''; ?>>
                                        <?php echo __('emergency'); ?>
                                    </option>
                                </select>
                                <div class="form-text">اختر نوع الرسالة المناسب للقالب</div>
                            </div>
                        </div>

                        <!-- موضوع الرسالة -->
                        <div class="mb-3">
                            <label for="subject" class="form-label">
                                <i class="fas fa-heading me-2"></i>موضوع الرسالة
                                <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="subject" name="subject" 
                                   value="<?php echo isset($_POST['subject']) ? safe_html($_POST['subject']) : ''; ?>"
                                   placeholder="أدخل موضوع الرسالة..." required>
                            <div class="form-text">يمكنك استخدام المتغيرات مثل {student_name} في الموضوع</div>
                            <div class="invalid-feedback">موضوع الرسالة مطلوب</div>
                        </div>

                        <!-- محتوى الرسالة -->
                        <div class="mb-3">
                            <label for="message_content" class="form-label">
                                <i class="fas fa-comment me-2"></i>محتوى الرسالة
                                <span class="text-danger">*</span>
                            </label>
                            <textarea class="form-control" id="message_content" name="message_content" rows="8" 
                                      placeholder="اكتب محتوى الرسالة هنا..." required><?php echo isset($_POST['message_content']) ? safe_html($_POST['message_content']) : ''; ?></textarea>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                يمكنك استخدام المتغيرات مثل {student_name}, {class_name}, {date} في المحتوى
                            </div>
                            <div class="invalid-feedback">محتوى الرسالة مطلوب</div>
                        </div>

                        <!-- المتغيرات -->
                        <div class="mb-4">
                            <label for="variables" class="form-label">
                                <i class="fas fa-code me-2"></i>المتغيرات (اختياري)
                            </label>
                            <input type="text" class="form-control" id="variables" name="variables" 
                                   value="<?php echo isset($_POST['variables']) ? safe_html($_POST['variables']) : ''; ?>"
                                   placeholder="student_name, class_name, date">
                            <div class="form-text">
                                <i class="fas fa-lightbulb me-1"></i>
                                أدخل أسماء المتغيرات مفصولة بفواصل. مثال: student_name, class_name, date
                            </div>
                        </div>

                        <!-- أمثلة على القوالب -->
                        <div class="alert alert-info mb-4">
                            <h6><i class="fas fa-lightbulb me-2"></i>أمثلة على القوالب:</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>تقرير سلوك إيجابي:</strong>
                                    <div class="small text-muted mb-2">
                                        الموضوع: تهنئة - سلوك ممتاز<br>
                                        المحتوى: نود إعلامكم بأن الطالب/ة {student_name} أظهر سلوكاً ممتازاً اليوم في {class_name}.
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <strong>تنبيه غياب:</strong>
                                    <div class="small text-muted mb-2">
                                        الموضوع: تنبيه غياب<br>
                                        المحتوى: الطالب/ة {student_name} غائب اليوم {date}. يرجى التواصل مع المدرسة.
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>إعلان عام:</strong>
                                    <div class="small text-muted">
                                        الموضوع: إعلان مهم<br>
                                        المحتوى: إعلان مهم لأولياء أمور طلاب {class_name} بخصوص الأنشطة المدرسية.
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <strong>تقرير أكاديمي:</strong>
                                    <div class="small text-muted">
                                        الموضوع: تقرير أكاديمي<br>
                                        المحتوى: تقرير عن الأداء الأكاديمي للطالب/ة {student_name} في {class_name}.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراء -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="message_templates.php" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="button" class="btn btn-outline-primary" onclick="previewTemplate()">
                                <i class="fas fa-eye me-2"></i>معاينة
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ القالب
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة المعاينة -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-eye me-2"></i>معاينة القالب
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="card">
                    <div class="card-header bg-light">
                        <strong>الموضوع:</strong> <span id="preview-subject"></span>
                    </div>
                    <div class="card-body">
                        <div id="preview-content"></div>
                    </div>
                    <div class="card-footer bg-light">
                        <small class="text-muted">
                            <strong>النوع:</strong> <span id="preview-type"></span> |
                            <strong>المتغيرات:</strong> <span id="preview-variables"></span>
                        </small>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<script>
// معاينة القالب
function previewTemplate() {
    const subject = document.getElementById('subject').value || 'بدون موضوع';
    const content = document.getElementById('message_content').value || 'بدون محتوى';
    const type = document.getElementById('message_type').options[document.getElementById('message_type').selectedIndex].text;
    const variables = document.getElementById('variables').value || 'لا توجد متغيرات';
    
    document.getElementById('preview-subject').textContent = subject;
    document.getElementById('preview-content').innerHTML = content.replace(/\n/g, '<br>');
    document.getElementById('preview-type').textContent = type;
    document.getElementById('preview-variables').textContent = variables;
    
    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    modal.show();
}

// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        Array.prototype.forEach.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// تحديث عداد الأحرف
document.getElementById('message_content').addEventListener('input', function() {
    const maxLength = 1000;
    const currentLength = this.value.length;
    const remaining = maxLength - currentLength;
    
    // إضافة عداد إذا لم يكن موجوداً
    let counter = document.getElementById('char-counter');
    if (!counter) {
        counter = document.createElement('div');
        counter.id = 'char-counter';
        counter.className = 'form-text text-end';
        this.parentNode.appendChild(counter);
    }
    
    counter.innerHTML = `عدد الأحرف: ${currentLength} / ${maxLength}`;
    counter.className = `form-text text-end ${remaining < 100 ? 'text-warning' : remaining < 50 ? 'text-danger' : ''}`;
});
</script>

<?php require_once '../includes/footer.php'; ?>
