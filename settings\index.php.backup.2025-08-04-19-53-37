<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';
/**
 * صفحة إعدادات النظام
 * System Settings Page
 */

$page_title = __('system_settings');
require_once '../includes/header.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

$error_message = '';
$success_message = '';

// معالجة حفظ الإعدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = __('invalid_request');
    } else {
        // توحيد max_file_size بالميجابايت
        $max_file_size_mb = intval($_POST['max_file_size'] ?? 10);
        $settings_to_update = [
            'school_name' => clean_input($_POST['school_name'] ?? ''),
            'school_name_en' => clean_input($_POST['school_name_en'] ?? ''),
            'school_description' => clean_input($_POST['school_description'] ?? ''),
            'school_description_en' => clean_input($_POST['school_description_en'] ?? ''),
            'school_address' => clean_input($_POST['school_address'] ?? ''),
            'school_phone' => clean_input($_POST['school_phone'] ?? ''),
            'school_email' => clean_input($_POST['school_email'] ?? ''),
            'school_website' => clean_input($_POST['school_website'] ?? ''),
            'academic_year' => clean_input($_POST['academic_year'] ?? ''),
            'semester' => clean_input($_POST['semester'] ?? ''),
            'language' => clean_input($_POST['language'] ?? 'ar'),
            'timezone' => clean_input($_POST['timezone'] ?? 'Asia/Riyadh'),
            'date_format' => clean_input($_POST['date_format'] ?? 'Y-m-d'),
            'time_format' => clean_input($_POST['time_format'] ?? 'H:i'),
            'currency_symbol' => clean_input($_POST['currency_symbol'] ?? 'ر.س'),
            'items_per_page' => intval($_POST['items_per_page'] ?? 20),
            'max_file_size' => $max_file_size_mb * 1048576,
            'allowed_file_types' => clean_input($_POST['allowed_file_types'] ?? 'jpg,jpeg,png,pdf,doc,docx'),
            'email_notifications' => isset($_POST['email_notifications']) ? '1' : '0',
            'sms_notifications' => isset($_POST['sms_notifications']) ? '1' : '0',
            'maintenance_mode' => isset($_POST['maintenance_mode']) ? '1' : '0',
            'registration_enabled' => isset($_POST['registration_enabled']) ? '1' : '0',
            'backup_enabled' => isset($_POST['backup_enabled']) ? '1' : '0',
            'backup_frequency' => clean_input($_POST['backup_frequency'] ?? 'daily'),
            'session_timeout' => intval($_POST['session_timeout'] ?? 3600),
            'password_min_length' => intval($_POST['password_min_length'] ?? 8),
            'login_attempts' => intval($_POST['login_attempts'] ?? 5),
            'lockout_duration' => intval($_POST['lockout_duration'] ?? 900)
        ];

        // معالجة سلم الدرجات
        if (isset($_POST['grade_scale']) && is_array($_POST['grade_scale'])) {
            $grades = [];
            foreach ($_POST['grade_scale'] as $k => $v) {
                $grades[$k] = intval($v);
            }
            $settings_to_update['grade_scale'] = json_encode($grades, JSON_UNESCAPED_UNICODE);
        }

        // التحقق من صحة البيانات
        $errors = [];

        if (empty($settings_to_update['school_name'])) {
            $errors[] = __('school_name') . ' ' . __('required_field');
        }

        if (!empty($settings_to_update['school_email']) && !filter_var($settings_to_update['school_email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = __('invalid_email_format');
        }

        if (!empty($settings_to_update['school_website']) && !filter_var($settings_to_update['school_website'], FILTER_VALIDATE_URL)) {
            $errors[] = __('invalid_website_format');
        }

        if ($settings_to_update['items_per_page'] < 5 || $settings_to_update['items_per_page'] > 100) {
            $errors[] = __('items_per_page_range_error');
        }

        if ($max_file_size_mb < 1 || $max_file_size_mb > 50) {
            $errors[] = __('max_file_size_range_error');
        }

        if (empty($errors)) {
            global $conn;
            $conn->begin_transaction();

            try {
                foreach ($settings_to_update as $key => $value) {
                    $stmt = $conn->prepare("
                        INSERT INTO system_settings (setting_key, setting_value, updated_by, updated_at)
                        VALUES (?, ?, ?, NOW())
                        ON DUPLICATE KEY UPDATE
                        setting_value = VALUES(setting_value),
                        updated_by = VALUES(updated_by),
                        updated_at = NOW()
                    ");
                    $stmt->bind_param("ssi", $key, $value, $_SESSION['user_id']);
                    $stmt->execute();
                }

                $conn->commit();

                // تسجيل النشاط
                log_activity($_SESSION['user_id'], 'update_settings', 'system_settings', null);

                $success_message = __('settings_updated_successfully');

                // إعادة تحميل الإعدادات في الجلسة
                apply_system_settings();
                // إذا تم تغيير اللغة من النموذج، حدث الجلسة فوراً
                if (isset($_POST['language']) && in_array($_POST['language'], ['ar', 'en'])) {
                    $_SESSION['system_language'] = $_POST['language'];
                }

            } catch (Exception $e) {
                $conn->rollback();
                log_error("Error updating settings: " . $e->getMessage());
                $error_message = __('error_occurred');
            }
        } else {
            $error_message = implode('<br>', $errors);
        }
    }
}

// جلب الإعدادات الحالية
$current_settings = [];
if (!isset($conn) || !$conn) {
    die('<div style="background:#ffe0e0;color:#900;padding:10px;margin-bottom:10px;">خطأ: الاتصال بقاعدة البيانات غير متوفر!</div>');
}
$settings_result = $conn->query("SELECT setting_key, setting_value FROM system_settings");
if ($conn->error) {
    die('<div style="background:#ffe0e0;color:#900;padding:10px;margin-bottom:10px;">DB ERROR: ' . $conn->error . '</div>');
}
if ($settings_result && $settings_result->num_rows > 0) {
    while ($setting = $settings_result->fetch_assoc()) {
        $current_settings[$setting['setting_key']] = $setting['setting_value'];
    }
}
// إذا لم توجد إعدادات، جلب جميع الإعدادات الافتراضية (لضمان ظهور الحقول)
if (empty($current_settings) && function_exists('get_all_system_settings')) {
    $all_settings = get_all_system_settings();
    foreach ($all_settings as $key => $arr) {
        $current_settings[$key] = $arr['value'] ?? '';
    }
}
?>
<!-- Page Header -->
<?php if (!empty($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo $success_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>
<?php if (!empty($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?php echo $error_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0 fw-bold"><?php echo __('system_settings'); ?></h1>
        <p class="text-muted mb-0"><?php echo __('configure_system_settings'); ?></p>
    </div>
    <div>
        <button type="button" class="btn btn-outline-secondary" onclick="resetToDefaults()">
            <i class="fas fa-undo me-2"></i><?php echo __('reset_defaults'); ?>
        </button>
    </div>
</div>
<form method="POST" class="needs-validation" novalidate>
    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
    <div class="row">
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-school me-2"></i><?php echo __('school_information'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="school_name" class="form-label"><?php echo __('school_name'); ?> <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="school_name" name="school_name" value="<?php echo htmlspecialchars($current_settings['school_name'] ?? ''); ?>" required>
                                <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="school_name_en" class="form-label"><?php echo __('school_name_english'); ?></label>
                                <input type="text" class="form-control" id="school_name_en" name="school_name_en" value="<?php echo htmlspecialchars($current_settings['school_name_en'] ?? ''); ?>">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="school_address" class="form-label"><?php echo __('school_address'); ?></label>
                        <input type="text" class="form-control" id="school_address" name="school_address" value="<?php echo htmlspecialchars($current_settings['school_address'] ?? ''); ?>">
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="school_phone" class="form-label"><?php echo __('school_phone'); ?></label>
                                <input type="text" class="form-control" id="school_phone" name="school_phone" value="<?php echo htmlspecialchars($current_settings['school_phone'] ?? ''); ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="school_email" class="form-label"><?php echo __('school_email'); ?></label>
                                <input type="email" class="form-control" id="school_email" name="school_email" value="<?php echo htmlspecialchars($current_settings['school_email'] ?? ''); ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="currency_symbol" class="form-label"><?php echo __('currency_symbol'); ?></label>
                                <input type="text" class="form-control" id="currency_symbol" name="currency_symbol" value="<?php echo htmlspecialchars($current_settings['currency_symbol'] ?? 'ر.س'); ?>">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i><?php echo __('system_preferences'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="language" class="form-label"><?php echo __('language'); ?></label>
                                <select class="form-select" id="language" name="language">
                                    <option value="ar" <?php echo ($current_settings['language'] ?? 'ar') === 'ar' ? 'selected' : ''; ?>>العربية</option>
                                    <option value="en" <?php echo ($current_settings['language'] ?? 'ar') === 'en' ? 'selected' : ''; ?>>English</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="timezone" class="form-label"><?php echo __('timezone'); ?></label>
                                <input type="text" class="form-control" id="timezone" name="timezone" value="<?php echo htmlspecialchars($current_settings['timezone'] ?? 'Asia/Riyadh'); ?>">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="date_format" class="form-label"><?php echo __('date_format'); ?></label>
                                <input type="text" class="form-control" id="date_format" name="date_format" value="<?php echo htmlspecialchars($current_settings['date_format'] ?? 'Y-m-d'); ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="time_format" class="form-label"><?php echo __('time_format'); ?></label>
                                <input type="text" class="form-control" id="time_format" name="time_format" value="<?php echo htmlspecialchars($current_settings['time_format'] ?? 'H:i'); ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="items_per_page" class="form-label"><?php echo __('items_per_page'); ?></label>
                                <input type="number" class="form-control" id="items_per_page" name="items_per_page" value="<?php echo htmlspecialchars($current_settings['items_per_page'] ?? '20'); ?>">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="max_file_size" class="form-label"><?php echo __('max_file_size'); ?></label>
                                <input type="number" class="form-control" id="max_file_size" name="max_file_size" min="1" max="50" value="<?php echo htmlspecialchars(isset($current_settings['max_file_size']) ? intval($current_settings['max_file_size']) / 1048576 : '10'); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="allowed_file_types" class="form-label"><?php echo __('allowed_file_types'); ?></label>
                                <input type="text" class="form-control" id="allowed_file_types" name="allowed_file_types" value="<?php echo htmlspecialchars($current_settings['allowed_file_types'] ?? 'jpg,jpeg,png,pdf,doc,docx'); ?>">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="backup_enabled" name="backup_enabled" value="1" <?php echo ($current_settings['backup_enabled'] ?? 'false') === 'true' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="backup_enabled"><?php echo __('backup_enabled'); ?></label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="backup_frequency" class="form-label"><?php echo __('backup_frequency'); ?></label>
                                <input type="text" class="form-control" id="backup_frequency" name="backup_frequency" value="<?php echo htmlspecialchars($current_settings['backup_frequency'] ?? 'daily'); ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="email_notifications" name="email_notifications" value="1" <?php echo ($current_settings['email_notifications'] ?? 'false') === 'true' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="email_notifications"><?php echo __('email_notifications'); ?></label>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="sms_notifications" name="sms_notifications" value="1" <?php echo ($current_settings['sms_notifications'] ?? 'false') === 'true' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="sms_notifications"><?php echo __('sms_notifications'); ?></label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="maintenance_mode" name="maintenance_mode" value="1" <?php echo ($current_settings['maintenance_mode'] ?? 'false') === 'true' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="maintenance_mode"><?php echo __('maintenance_mode'); ?></label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="registration_open" name="registration_open" value="1" <?php echo ($current_settings['registration_open'] ?? 'false') === 'true' ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="registration_open"><?php echo __('registration_open'); ?></label>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="grade_scale" class="form-label"><?php echo __('grade_scale'); ?></label>
                        <div class="table-responsive">
                            <table class="table table-bordered align-middle mb-0" style="max-width:400px;">
                                <thead>
                                    <tr>
                                        <th style="width: 80px;">الدرجة</th>
                                        <th>الحد الأدنى</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $default_grades = [
                                        'A+' => 95,
                                        'A'  => 90,
                                        'B+' => 85,
                                        'B'  => 80,
                                        'C+' => 75,
                                        'C'  => 70,
                                        'D+' => 65,
                                        'D'  => 60,
                                        'F'  => 0
                                    ];
                                    $grade_scale = $default_grades;
                                    if (!empty($current_settings['grade_scale'])) {
                                        $decoded = json_decode($current_settings['grade_scale'], true);
                                        if (is_array($decoded)) {
                                            $grade_scale = array_merge($default_grades, $decoded);
                                        }
                                    }
                                    foreach ($default_grades as $grade => $min) {
                                        $value = isset($grade_scale[$grade]) ? $grade_scale[$grade] : $min;
                                        echo '<tr>';
                                        echo '<td><label for="grade_'.$grade.'" class="form-label mb-0">'.$grade.'</label></td>';
                                        echo '<td><input type="number" class="form-control" id="grade_'.$grade.'" name="grade_scale['.$grade.']" value="'.htmlspecialchars($value).'" min="0" max="100" required></td>';
                                        echo '</tr>';
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary w-100 mt-3">
                        <i class="fas fa-save me-2"></i><?php echo __('save'); ?>
                    </button>
                </div>
            </div>
        </div>

        <!-- العمود الجانبي -->
        <div class="col-lg-4">
            <!-- قسم إدارة الصلاحيات والأدوار -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-shield-alt me-2"></i>إدارة الصلاحيات والأدوار
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted small mb-3">
                        تحكم في صلاحيات المستخدمين وإدارة الأدوار في النظام
                    </p>

                    <?php
                    // جلب إحصائيات سريعة للصلاحيات
                    $permissions_stats = [];
                    try {
                        $stats_query = "
                            SELECT
                                (SELECT COUNT(*) FROM users WHERE status = 'active') as total_users,
                                (SELECT COUNT(*) FROM user_custom_permissions WHERE is_granted = 1) as custom_permissions,
                                (SELECT COUNT(*) FROM system_resources WHERE is_active = 1) as active_resources
                        ";
                        $stats_result = $conn->query($stats_query);
                        if ($stats_result) {
                            $permissions_stats = $stats_result->fetch_assoc();
                        }
                    } catch (Exception $e) {
                        // في حالة عدم وجود الجداول بعد
                        $permissions_stats = [
                            'total_users' => 0,
                            'custom_permissions' => 0,
                            'active_resources' => 0
                        ];
                    }
                    ?>

                    <!-- إحصائيات سريعة -->
                    <div class="row text-center mb-3">
                        <div class="col-4">
                            <div class="bg-light p-2 rounded">
                                <h6 class="mb-0"><?php echo $permissions_stats['total_users'] ?? 0; ?></h6>
                                <small class="text-muted">مستخدم</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="bg-light p-2 rounded">
                                <h6 class="mb-0"><?php echo $permissions_stats['custom_permissions'] ?? 0; ?></h6>
                                <small class="text-muted">صلاحية</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="bg-light p-2 rounded">
                                <h6 class="mb-0"><?php echo $permissions_stats['active_resources'] ?? 0; ?></h6>
                                <small class="text-muted">مورد</small>
                            </div>
                        </div>
                    </div>

                    <!-- روابط إدارة الصلاحيات -->
                    <div class="d-grid gap-2">
                        <a href="permissions.php" class="btn btn-primary btn-sm">
                            <i class="fas fa-shield-alt me-2"></i>إدارة الصلاحيات والأدوار
                        </a>
                        <a href="../admin/permissions_manager.php" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-users me-2"></i>صلاحيات المستخدمين
                        </a>
                        <a href="../admin/system_resources.php" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-sitemap me-2"></i>موارد النظام
                        </a>
                        <a href="../admin/permissions_audit.php" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-history me-2"></i>سجل التغييرات
                        </a>
                    </div>

                    <hr class="my-3">

                    <!-- معلومات سريعة -->
                    <div class="alert alert-info alert-sm">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> نظام الصلاحيات يتيح لك التحكم الدقيق في ما يمكن لكل مستخدم الوصول إليه في النظام.
                    </div>
                </div>
            </div>

            <!-- قسم الأمان والحماية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-lock me-2"></i>الأمان والحماية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="session_timeout" class="form-label">مدة انتهاء الجلسة (ثانية)</label>
                        <input type="number" class="form-control" id="session_timeout" name="session_timeout"
                               value="<?php echo htmlspecialchars($current_settings['session_timeout'] ?? '3600'); ?>"
                               min="300" max="86400">
                        <div class="form-text">الافتراضي: 3600 ثانية (ساعة واحدة)</div>
                    </div>

                    <div class="mb-3">
                        <label for="password_min_length" class="form-label">الحد الأدنى لطول كلمة المرور</label>
                        <input type="number" class="form-control" id="password_min_length" name="password_min_length"
                               value="<?php echo htmlspecialchars($current_settings['password_min_length'] ?? '8'); ?>"
                               min="6" max="20">
                    </div>

                    <div class="mb-3">
                        <label for="login_attempts" class="form-label">عدد محاولات تسجيل الدخول</label>
                        <input type="number" class="form-control" id="login_attempts" name="login_attempts"
                               value="<?php echo htmlspecialchars($current_settings['login_attempts'] ?? '5'); ?>"
                               min="3" max="10">
                    </div>

                    <div class="mb-3">
                        <label for="lockout_duration" class="form-label">مدة الحظر (ثانية)</label>
                        <input type="number" class="form-control" id="lockout_duration" name="lockout_duration"
                               value="<?php echo htmlspecialchars($current_settings['lockout_duration'] ?? '900'); ?>"
                               min="300" max="3600">
                        <div class="form-text">الافتراضي: 900 ثانية (15 دقيقة)</div>
                    </div>
                </div>
            </div>

            <!-- قسم النسخ الاحتياطي -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-database me-2"></i>النسخ الاحتياطي
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning alert-sm">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>مهم:</strong> تأكد من إجراء نسخ احتياطية دورية لحماية بياناتك.
                    </div>

                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="createBackup()">
                            <i class="fas fa-download me-2"></i>إنشاء نسخة احتياطية
                        </button>
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="viewBackups()">
                            <i class="fas fa-list me-2"></i>عرض النسخ الاحتياطية
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<script>
// وظائف إضافية لصفحة الإعدادات
function resetToDefaults() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟')) {
        // يمكن إضافة منطق إعادة التعيين هنا
        alert('سيتم تطبيق هذه الميزة قريباً');
    }
}

function createBackup() {
    if (confirm('هل تريد إنشاء نسخة احتياطية من قاعدة البيانات؟')) {
        // يمكن إضافة منطق النسخ الاحتياطي هنا
        alert('سيتم تطبيق هذه الميزة قريباً');
    }
}

function viewBackups() {
    // يمكن إضافة منطق عرض النسخ الاحتياطية هنا
    alert('سيتم تطبيق هذه الميزة قريباً');
}

// التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?php
require_once '../includes/footer.php';
?>