# 🔧 دليل استكشاف أخطاء نظام الصلاحيات المحسن
## Enhanced Permissions System Troubleshooting Guide

---

## ❗ المشاكل الشائعة والحلول

### 🚨 **خطأ: Cannot redeclare has_permission()**

#### **الوصف:**
```
Fatal error: Cannot redeclare has_permission() (previously declared in...)
```

#### **السبب:**
تضارب في تعريف دالة `has_permission()` بين الملفات.

#### **الحل:**
1. **تأكد من التحديث الصحيح:**
   ```bash
   # تحقق من أن ملف functions.php محدث
   grep -n "enhanced_has_permission" includes/functions.php
   ```

2. **تأكد من عدم وجود تعريفات مكررة:**
   ```bash
   # ابحث عن جميع تعريفات الدالة
   grep -r "function has_permission" includes/
   ```

3. **إعادة تحميل الملفات:**
   - احذف ملف `includes/enhanced_permissions.php`
   - أعد إنشاؤه من النسخة المحدثة
   - تأكد من أن `includes/functions.php` يستخدم `enhanced_has_permission()`

---

### 🗄️ **خطأ: جداول قاعدة البيانات غير موجودة**

#### **الوصف:**
```
Table 'database.custom_roles' doesn't exist
```

#### **السبب:**
لم يتم تشغيل ملف تحديث قاعدة البيانات.

#### **الحل:**
1. **تشغيل ملف SQL يدوياً:**
   ```sql
   SOURCE database/update_permissions_system.sql;
   ```

2. **أو استخدام phpMyAdmin:**
   - افتح phpMyAdmin
   - اختر قاعدة البيانات
   - انتقل لتبويب "SQL"
   - انسخ محتوى `database/update_permissions_system.sql`
   - اضغط "تنفيذ"

3. **أو استخدام معالج التطبيق:**
   ```
   انتقل إلى: /admin/apply_enhanced_permissions.php
   ```

---

### 🔐 **خطأ: وصول مرفوض للمدير**

#### **الوصف:**
المدير لا يستطيع الوصول لصفحات الإدارة.

#### **السبب:**
مشكلة في التحقق من دور المدير.

#### **الحل:**
1. **تحقق من دور المدير في قاعدة البيانات:**
   ```sql
   SELECT id, username, role FROM users WHERE role = 'admin';
   ```

2. **تأكد من وجود دور المدير في الجدول الجديد:**
   ```sql
   SELECT * FROM custom_roles WHERE role_name = 'admin';
   ```

3. **إضافة دور المدير إذا لم يكن موجوداً:**
   ```sql
   INSERT INTO custom_roles (role_name, role_display_name, role_description, is_system_role, created_by) 
   VALUES ('admin', 'مدير النظام', 'مدير عام للنظام مع جميع الصلاحيات', 1, 1);
   ```

---

### 📄 **خطأ: الصفحات لا تظهر بشكل صحيح**

#### **الوصف:**
صفحات إدارة الصلاحيات تظهر فارغة أو بأخطاء.

#### **السبب:**
مشاكل في تضمين الملفات أو الأذونات.

#### **الحل:**
1. **تحقق من أذونات الملفات:**
   ```bash
   chmod 644 includes/enhanced_permissions.php
   chmod 644 admin/enhanced_permissions_manager.php
   ```

2. **تحقق من مسارات الملفات:**
   ```php
   // في بداية كل ملف إدارة
   require_once '../includes/config.php';
   require_once '../includes/database.php';
   require_once '../includes/functions.php';
   ```

3. **تفعيل عرض الأخطاء للتشخيص:**
   ```php
   // أضف في بداية الملف مؤقتاً
   error_reporting(E_ALL);
   ini_set('display_errors', 1);
   ```

---

### 🔄 **خطأ: الصلاحيات المخصصة لا تعمل**

#### **الوصف:**
الصلاحيات المخصصة لا تؤثر على المستخدمين.

#### **السبب:**
مشكلة في ترتيب أولوية الصلاحيات.

#### **الحل:**
1. **تحقق من البيانات في الجدول:**
   ```sql
   SELECT * FROM user_custom_permissions WHERE user_id = [USER_ID];
   ```

2. **تأكد من تاريخ الانتهاء:**
   ```sql
   SELECT * FROM user_custom_permissions 
   WHERE user_id = [USER_ID] 
   AND (expires_at IS NULL OR expires_at > NOW());
   ```

3. **اختبر الدالة مباشرة:**
   ```php
   var_dump(enhanced_has_permission('resource_key', 'read', $user_id));
   ```

---

### 📊 **خطأ: سجل التدقيق فارغ**

#### **الوصف:**
لا تظهر أي سجلات في صفحة التدقيق.

#### **السبب:**
جدول السجل غير موجود أو لا يتم الكتابة فيه.

#### **الحل:**
1. **تحقق من وجود الجدول:**
   ```sql
   SHOW TABLES LIKE 'permissions_audit_log';
   ```

2. **إنشاء الجدول يدوياً:**
   ```sql
   CREATE TABLE `permissions_audit_log` (
       `id` int(11) NOT NULL AUTO_INCREMENT,
       `user_id` int(10) UNSIGNED NOT NULL,
       `action_type` enum('role_changed','permission_granted','permission_revoked','login','logout','access_denied') NOT NULL,
       `resource_key` varchar(100) DEFAULT NULL,
       `old_value` varchar(255) DEFAULT NULL,
       `new_value` varchar(255) DEFAULT NULL,
       `changed_by` int(10) UNSIGNED DEFAULT NULL,
       `ip_address` varchar(45) DEFAULT NULL,
       `user_agent` text DEFAULT NULL,
       `notes` text DEFAULT NULL,
       `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
       PRIMARY KEY (`id`)
   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
   ```

3. **اختبر تسجيل العمليات:**
   ```php
   log_permission_audit('test', 'test_resource', 'old', 'new', $_SESSION['user_id'], 'اختبار');
   ```

---

## 🛠️ أدوات التشخيص

### 📋 **صفحة الاختبار**
```
/admin/test_enhanced_permissions.php
```
تعرض حالة جميع مكونات النظام.

### 🔍 **فحص قاعدة البيانات**
```sql
-- فحص الجداول
SHOW TABLES LIKE '%permissions%';
SHOW TABLES LIKE '%roles%';

-- فحص البيانات
SELECT COUNT(*) FROM custom_roles;
SELECT COUNT(*) FROM system_resources;
SELECT COUNT(*) FROM user_custom_permissions;
```

### 📝 **فحص الملفات**
```bash
# تحقق من وجود الملفات
ls -la includes/enhanced_permissions.php
ls -la admin/enhanced_permissions_manager.php
ls -la admin/roles_manager.php

# تحقق من الأذونات
ls -la includes/
ls -la admin/
```

---

## 🚀 خطوات الإصلاح السريع

### 1. **إعادة تعيين كاملة**
```bash
# 1. احذف الملفات المشكوك فيها
rm includes/enhanced_permissions.php
rm admin/enhanced_permissions_manager.php

# 2. أعد إنشاؤها من النسخ الأصلية
# 3. شغل ملف قاعدة البيانات مرة أخرى
```

### 2. **إصلاح سريع للصلاحيات**
```sql
-- امنح المدير جميع الصلاحيات
UPDATE users SET role = 'admin' WHERE id = 1;

-- تأكد من وجود دور المدير
INSERT IGNORE INTO custom_roles (role_name, role_display_name, is_system_role, created_by) 
VALUES ('admin', 'مدير النظام', 1, 1);
```

### 3. **اختبار سريع**
```php
// أضف في أي صفحة للاختبار
if (function_exists('enhanced_has_permission')) {
    echo "النظام المحسن يعمل!";
} else {
    echo "النظام المحسن غير محمل!";
}
```

---

## 📞 طلب المساعدة

### 🔍 **معلومات مطلوبة عند طلب المساعدة:**
1. رسالة الخطأ الكاملة
2. إصدار PHP و MySQL
3. نتائج صفحة الاختبار
4. محتوى ملف error.log

### 📧 **كيفية الإبلاغ عن خطأ:**
1. انسخ رسالة الخطأ كاملة
2. اذكر الخطوات التي أدت للخطأ
3. أرفق لقطة شاشة إن أمكن
4. اذكر متصفحك ونظام التشغيل

---

## ✅ قائمة التحقق النهائية

- [ ] جميع الجداول موجودة في قاعدة البيانات
- [ ] ملف `enhanced_permissions.php` محمل بدون أخطاء
- [ ] دالة `has_permission()` تعمل بشكل صحيح
- [ ] صفحات الإدارة تظهر بدون أخطاء
- [ ] الصلاحيات المخصصة تعمل
- [ ] سجل التدقيق يسجل العمليات
- [ ] جميع الأدوار الأساسية موجودة
- [ ] المدير يستطيع الوصول لجميع الصفحات

---

**إذا استمرت المشاكل، تأكد من أخذ نسخة احتياطية واتصل بالدعم الفني** 🆘
