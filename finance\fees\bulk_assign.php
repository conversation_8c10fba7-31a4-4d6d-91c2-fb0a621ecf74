<?php
/**
 * صفحة تعيين الرسوم بالجملة
 * Bulk Assign Fees Page
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    header('Location: ../../dashboard/');
    exit();
}

$errors = [];
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'])) {
        die('CSRF token validation failed.');
    }

    $fee_type_id = filter_input(INPUT_POST, 'fee_type_id', FILTER_VALIDATE_INT);
    $class_id = filter_input(INPUT_POST, 'class_id', FILTER_VALIDATE_INT);
    $academic_year = clean_input($_POST['academic_year']);

    if (!$fee_type_id || !$class_id || empty($academic_year)) {
        $errors[] = __('please_fill_all_required_fields');
    } else {
        // Get fee details
        $fee_type_stmt = $conn->prepare("SELECT amount FROM fee_types WHERE id = ?");
        $fee_type_stmt->bind_param('i', $fee_type_id);
        $fee_type_stmt->execute();
        $fee_type = $fee_type_stmt->get_result()->fetch_assoc();

        // Get students in the selected class
        $students_stmt = $conn->prepare("SELECT id FROM students WHERE class_id = ?");
        $students_stmt->bind_param('i', $class_id);
        $students_stmt->execute();
        $students_result = $students_stmt->get_result();

        if ($fee_type && $students_result->num_rows > 0) {
            $conn->begin_transaction();
            try {
                $insert_stmt = $conn->prepare("INSERT INTO student_fees (student_id, fee_type_id, academic_year, amount, due_date, status) VALUES (?, ?, ?, ?, ?, 'unpaid')");
                $check_stmt = $conn->prepare("SELECT id FROM student_fees WHERE student_id = ? AND fee_type_id = ? AND academic_year = ?");

                $assigned_count = 0;
                while ($student = $students_result->fetch_assoc()) {
                    // Check if fee is already assigned
                    $check_stmt->bind_param('iis', $student['id'], $fee_type_id, $academic_year);
                    $check_stmt->execute();
                    if ($check_stmt->get_result()->num_rows === 0) {
                        $due_date = date('Y-m-d', strtotime('+30 days')); // Example due date
                        $insert_stmt->bind_param('iisd', $student['id'], $fee_type_id, $academic_year, $fee_type['amount'], $due_date);
                        $insert_stmt->execute();
                        $assigned_count++;
                    }
                }
                $conn->commit();
                $_SESSION['success_message'] = sprintf(__('%d_students_assigned_fee'), $assigned_count);
                header('Location: index.php');
                exit();
            } catch (Exception $e) {
                $conn->rollback();
                $errors[] = __('error_during_assignment') . ': ' . $e->getMessage();
            }
        } else {
            $errors[] = __('no_students_in_class_or_invalid_fee');
        }
    }
}

// Fetch data for dropdowns
$fee_types = $conn->query("SELECT id, fee_name, amount FROM fee_types WHERE status = 'active' ORDER BY fee_name");
$classes = $conn->query("SELECT id, class_name, grade_level FROM classes ORDER BY grade_level, class_name");

include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('bulk_assign_fees'); ?></h1>
            <p class="text-muted"><?php echo __('assign_fee_to_class'); ?></p>
        </div>
        <div>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_fees'); ?>
            </a>
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-body">
            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <?php foreach ($errors as $error): ?>
                        <p class="mb-0"><?php echo $error; ?></p>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>

            <form action="bulk_assign.php" method="POST">
                <?php echo generate_csrf_token(); ?>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="fee_type_id" class="form-label"><?php echo __('fee_type'); ?> <span class="text-danger">*</span></label>
                        <select class="form-select" id="fee_type_id" name="fee_type_id" required>
                            <option value=""><?php echo __('select_fee_type'); ?></option>
                            <?php while($row = $fee_types->fetch_assoc()): ?>
                                <option value="<?php echo $row['id']; ?>"><?php echo htmlspecialchars($row['fee_name']) . ' (' . number_format($row['amount'], 2) . ')'; ?></option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="class_id" class="form-label"><?php echo __('class'); ?> <span class="text-danger">*</span></label>
                        <select class="form-select" id="class_id" name="class_id" required>
                            <option value=""><?php echo __('select_class'); ?></option>
                            <?php while($row = $classes->fetch_assoc()): ?>
                                <option value="<?php echo $row['id']; ?>"><?php echo htmlspecialchars($row['class_name']) . ' (' . $row['grade_level'] . ')'; ?></option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="academic_year" class="form-label"><?php echo __('academic_year'); ?> <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="academic_year" name="academic_year" value="<?php echo get_current_academic_year(); ?>" required>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary"><i class="fas fa-users me-2"></i><?php echo __('assign_fee'); ?></button>
            </form>
        </div>
    </div>
</div>

<?php include_once '../../includes/footer.php'; ?>
