<?php
/**
 * صفحة تعديل الطالب
 * Edit Student Page
 */

if (!function_exists('validate_date')) {
    function validate_date($date, $format = 'Y-m-d') {
        $d = DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }
}

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// الاتصال عبر PDO
$db = Database::getInstance();
$pdo = $db->getConnection();

$page_title = __('edit_student');

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

$student_id = intval($_GET['id'] ?? 0);
$error_message = '';
$success_message = '';

if (empty($student_id)) {
    $_SESSION['error_message'] = __('invalid_student_id');
    header('Location: index.php');
    exit();
}

// جلب بيانات الطالب
$query = "
    SELECT 
        s.*,
        u.full_name,
        u.email,
        u.phone,
        u.status as user_status,
        c.class_name,
        c.grade_level
    FROM students s
    JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    WHERE s.id = ?
";
$stmt = $pdo->prepare($query);
$stmt->execute([$student_id]);
$student = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$student) {
    $_SESSION['error_message'] = __('student_not_found');
    header('Location: index.php');
    exit();
}

// معالجة تحديث البيانات (قبل تضمين الهيدر)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = __('invalid_request');
    } else {
        // جمع البيانات
        $full_name = clean_input($_POST['full_name'] ?? '');
        $email = clean_input($_POST['email'] ?? '');
        $phone = clean_input($_POST['phone'] ?? '');
        $address = clean_input($_POST['address'] ?? '');
        $date_of_birth = clean_input($_POST['date_of_birth'] ?? '');
        $gender = clean_input($_POST['gender'] ?? '');
        $national_id = clean_input($_POST['national_id'] ?? '');
        $student_id_val = clean_input($_POST['student_id'] ?? '');
        $class_id = intval($_POST['class_id'] ?? 0);
        $enrollment_date = clean_input($_POST['enrollment_date'] ?? '');
        $parent_name = clean_input($_POST['parent_name'] ?? '');
        $parent_phone = clean_input($_POST['parent_phone'] ?? '');
        $parent_email = clean_input($_POST['parent_email'] ?? '');
        $parent_national_id = clean_input($_POST['parent_national_id'] ?? '');
        $emergency_contact_name = clean_input($_POST['emergency_contact_name'] ?? '');
        $emergency_contact_phone = clean_input($_POST['emergency_contact_phone'] ?? '');
        $medical_conditions = clean_input($_POST['medical_conditions'] ?? '');
        $notes = clean_input($_POST['notes'] ?? '');
        $status = clean_input($_POST['status'] ?? 'active');

        // التحقق من صحة البيانات
        $errors = [];

        if (empty($full_name)) {
            $errors[] = __('full_name') . ' ' . __('required_field');
        }

        if (empty($email)) {
            $errors[] = __('email') . ' ' . __('required_field');
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = __('invalid_email_format');
        }

        if (empty($student_id_val)) {
            $errors[] = __('student_id') . ' ' . __('required_field');
        }

        if (!empty($date_of_birth) && !validate_date($date_of_birth)) {
            $errors[] = __('invalid_date_of_birth');
        }

        if (!empty($enrollment_date) && !validate_date($enrollment_date)) {
            $errors[] = __('invalid_enrollment_date');
        }

        if (!empty($national_id) && !validate_national_id($national_id)) {
            $errors[] = __('invalid_national_id');
        }
        if (!empty($phone) && !validate_phone($phone)) {
            $errors[] = __('invalid_phone');
        }
        if (!empty($parent_phone) && !validate_phone($parent_phone)) {
            $errors[] = __('invalid_parent_phone');
        }

        // التحقق من عدم تكرار البريد الإلكتروني
        if (!empty($email)) {
            $email_check = $pdo->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
            $email_check->execute([$email, $student['user_id']]);
            if ($email_check->fetch(PDO::FETCH_ASSOC)) {
                $errors[] = __('email_already_exists');
            }
        }

        // تحقق من عدم تكرار student_id
        if (!empty($student_id_val)) {
            $number_check = $pdo->prepare("SELECT id FROM students WHERE student_id = ? AND id != ?");
            $number_check->execute([$student_id_val, $student_id]);
            if ($number_check->fetch(PDO::FETCH_ASSOC)) {
                $errors[] = __('student_id_already_exists');
            }
        }

        if (empty($errors)) {
            $pdo->beginTransaction();

            try {
                // تحديث بيانات المستخدم
                $update_user_stmt = $pdo->prepare("
                    UPDATE users SET 
                        full_name = ?, email = ?, phone = ?, status = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $update_user_stmt->execute([
                    $full_name, $email, $phone, $status, $student['user_id']
                ]);

                // تحديث بيانات الطالب
                $update_student_stmt = $pdo->prepare("
                    UPDATE students SET 
                        student_id = ?, class_id = ?, enrollment_date = ?, 
                        parent_name = ?, parent_phone = ?, parent_email = ?, parent_national_id = ?,
                        emergency_contact_name = ?, emergency_contact_phone = ?, medical_conditions = ?, notes = ?, status = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $update_student_stmt->execute([
                    $student_id_val, $class_id, $enrollment_date,
                    $parent_name, $parent_phone, $parent_email, $parent_national_id,
                    $emergency_contact_name, $emergency_contact_phone, $medical_conditions, $notes, $status, $student_id
                ]);

                $pdo->commit();

                // تسجيل النشاط
                log_activity($_SESSION['user_id'], 'edit_student', 'students', $student_id, 
                    ['old_name' => $student['full_name']], 
                    ['new_name' => $full_name, 'class_id' => $class_id]
                );

                // إرسال إشعار للطالب
                add_notification($student['user_id'], __('profile_updated'), 
                    __('your_profile_has_been_updated'), 'info', 'students/view.php?id=' . $student_id);

                $_SESSION['success_message'] = __('student_updated_successfully');
                header('Location: view.php?id=' . $student_id);
                exit();

            } catch (Exception $e) {
                $pdo->rollBack();
                log_error("Error updating student: " . $e->getMessage());
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    $error_message = __('error_occurred') . '<br>' . htmlspecialchars($e->getMessage());
                } else {
                    $error_message = __('error_occurred');
                }
            }
        } else {
            $error_message = implode('<br>', $errors);
        }
    }
}

// بعد معالجة POST والتوجيه، ضمّن الهيدر
require_once '../includes/header.php';

// جلب قائمة الفصول
$classes = $pdo->query("SELECT id, class_name, grade_level FROM classes WHERE status = 'active' ORDER BY grade_level, class_name")->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('edit_student'); ?></h1>
            <p class="text-muted"><?php echo __('update_student_information'); ?></p>
        </div>
        <div>
            <a href="view.php?id=<?php echo $student_id; ?>" class="btn btn-outline-info">
                <i class="fas fa-eye me-2"></i><?php echo __('view_profile'); ?>
            </a>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
            </a>
        </div>
    </div>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Edit Form -->
    <form method="POST" class="needs-validation" novalidate>
        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">

        <div class="row">
            <!-- Personal Information -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-user me-2"></i><?php echo __('personal_information'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="full_name" class="form-label"><?php echo __('full_name'); ?> <span class="text-danger">*</span></label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="full_name" 
                                           name="full_name" 
                                           value="<?php echo htmlspecialchars($student['full_name']); ?>"
                                           required>
                                    <div class="invalid-feedback">
                                        <?php echo __('required_field'); ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="student_id" class="form-label"><?php echo __('student_id'); ?> <span class="text-danger">*</span></label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="student_id" 
                                           name="student_id" 
                                           value="<?php echo htmlspecialchars($student['student_id']); ?>"
                                           required>
                                    <div class="invalid-feedback">
                                        <?php echo __('required_field'); ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label"><?php echo __('email'); ?> <span class="text-danger">*</span></label>
                                    <input type="email" 
                                           class="form-control" 
                                           id="email" 
                                           name="email" 
                                           value="<?php echo htmlspecialchars($student['email']); ?>"
                                           required>
                                    <div class="invalid-feedback">
                                        <?php echo __('required_field'); ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label"><?php echo __('phone'); ?></label>
                                    <input type="tel" 
                                           class="form-control" 
                                           id="phone" 
                                           name="phone" 
                                           value="<?php echo htmlspecialchars($student['phone']); ?>">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="date_of_birth" class="form-label"><?php echo __('date_of_birth'); ?></label>
                                    <input type="date" 
                                           class="form-control" 
                                           id="date_of_birth" 
                                           name="date_of_birth" 
                                           value="<?php echo htmlspecialchars($student['date_of_birth']); ?>">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="gender" class="form-label"><?php echo __('gender'); ?></label>
                                    <select class="form-select" id="gender" name="gender">
                                        <option value=""><?php echo __('choose_gender'); ?></option>
                                        <option value="male" <?php echo ($student['gender'] === 'male') ? 'selected' : ''; ?>>
                                            <?php echo __('male'); ?>
                                        </option>
                                        <option value="female" <?php echo ($student['gender'] === 'female') ? 'selected' : ''; ?>>
                                            <?php echo __('female'); ?>
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="national_id" class="form-label"><?php echo __('national_id'); ?></label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="national_id" 
                                           name="national_id" 
                                           value="<?php echo htmlspecialchars($student['national_id']); ?>">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label"><?php echo __('address'); ?></label>
                            <textarea class="form-control" 
                                      id="address" 
                                      name="address" 
                                      rows="3"><?php echo htmlspecialchars($student['address']); ?></textarea>
                        </div>
                    </div>
                </div>

                <!-- Academic Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-graduation-cap me-2"></i><?php echo __('academic_information'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="class_id" class="form-label"><?php echo __('class'); ?></label>
                                    <select class="form-select" id="class_id" name="class_id">
                                        <option value=""><?php echo __('choose_class'); ?></option>
                                        <?php foreach ($classes as $class): ?>
                                            <option value="<?php echo $class['id']; ?>" 
                                                    <?php echo ($student['class_id'] == $class['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($class['class_name'] . ' - ' . $class['grade_level']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="enrollment_date" class="form-label"><?php echo __('enrollment_date'); ?></label>
                                    <input type="date" 
                                           class="form-control" 
                                           id="enrollment_date" 
                                           name="enrollment_date" 
                                           value="<?php echo htmlspecialchars($student['enrollment_date']); ?>">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label"><?php echo __('status'); ?></label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="active" <?php echo ($student['user_status'] === 'active') ? 'selected' : ''; ?>>
                                            <?php echo __('active'); ?>
                                        </option>
                                        <option value="inactive" <?php echo ($student['user_status'] === 'inactive') ? 'selected' : ''; ?>>
                                            <?php echo __('inactive'); ?>
                                        </option>
                                        <option value="suspended" <?php echo ($student['user_status'] === 'suspended') ? 'selected' : ''; ?>>
                                            <?php echo __('suspended'); ?>
                                        </option>
                                        <option value="graduated" <?php echo ($student['user_status'] === 'graduated') ? 'selected' : ''; ?>>
                                            <?php echo __('graduated'); ?>
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Guardian & Emergency Information -->
            <div class="col-lg-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-users me-2"></i><?php echo __('guardian_information'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="parent_name" class="form-label"><?php echo __('parent_name'); ?></label>
                            <input type="text" 
                                   class="form-control" 
                                   id="parent_name" 
                                   name="parent_name" 
                                   value="<?php echo htmlspecialchars($student['parent_name']); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="parent_phone" class="form-label"><?php echo __('parent_phone'); ?></label>
                            <input type="tel" 
                                   class="form-control" 
                                   id="parent_phone" 
                                   name="parent_phone" 
                                   value="<?php echo htmlspecialchars($student['parent_phone']); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="parent_email" class="form-label"><?php echo __('parent_email'); ?></label>
                            <input type="email" 
                                   class="form-control" 
                                   id="parent_email" 
                                   name="parent_email" 
                                   value="<?php echo htmlspecialchars($student['parent_email']); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="parent_national_id" class="form-label"><?php echo __('parent_national_id'); ?></label>
                            <input type="text" 
                                   class="form-control" 
                                   id="parent_national_id" 
                                   name="parent_national_id" 
                                   value="<?php echo htmlspecialchars($student['parent_national_id']); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="emergency_contact_name" class="form-label"><?php echo __('emergency_contact_name'); ?></label>
                            <input type="text" 
                                   class="form-control" 
                                   id="emergency_contact_name" 
                                   name="emergency_contact_name" 
                                   value="<?php echo htmlspecialchars($student['emergency_contact_name']); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="emergency_contact_phone" class="form-label"><?php echo __('emergency_contact_phone'); ?></label>
                            <input type="tel" 
                                   class="form-control" 
                                   id="emergency_contact_phone" 
                                   name="emergency_contact_phone" 
                                   value="<?php echo htmlspecialchars($student['emergency_contact_phone']); ?>">
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i><?php echo __('additional_information'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="medical_conditions" class="form-label"><?php echo __('medical_conditions'); ?></label>
                            <textarea class="form-control" 
                                      id="medical_conditions" 
                                      name="medical_conditions" 
                                      rows="3"
                                      placeholder="<?php echo __('allergies_medications_conditions'); ?>"><?php echo htmlspecialchars($student['medical_conditions']); ?></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label"><?php echo __('notes'); ?></label>
                            <textarea class="form-control" 
                                      id="notes" 
                                      name="notes" 
                                      rows="3"><?php echo htmlspecialchars($student['notes'] ?? ''); ?></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-end gap-2">
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i><?php echo __('update_student'); ?>
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
    // Form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();
</script>

<?php require_once '../includes/footer.php'; ?>
