<?php
/**
 * نظام الصلاحيات المحسن والمتكامل
 * Enhanced and Integrated Permissions System
 *
 * <AUTHOR> Management System
 * @version 2.0
 * @date 2025-08-05
 */

// السماح بالوصول إذا كان النظام مهيأ أو إذا تم استدعاؤه من ملف functions.php
if (!defined('SYSTEM_INIT') && !defined('FUNCTIONS_LOADED')) {
    // التحقق من وجود اتصال قاعدة البيانات
    if (!isset($conn)) {
        die('Database connection required');
    }
}

// تعريف أن النظام المحسن محمل
define('ENHANCED_PERMISSIONS_LOADED', true);

/**
 * تعريف مستويات الصلاحيات
 */
define('PERMISSION_LEVELS', [
    'none' => 0,    // لا يوجد صلاحية
    'read' => 1,    // قراءة فقط
    'write' => 2,   // قراءة وكتابة
    'full' => 3     // صلاحية كاملة
]);

/**
 * تعريف الأدوار الأساسية وصلاحياتها الافتراضية
 */
define('DEFAULT_ROLE_PERMISSIONS', [
    'admin' => [
        'level' => 'full',
        'resources' => ['*'] // جميع الموارد
    ],
    'teacher' => [
        'level' => 'write',
        'resources' => [
            'students_view', 'students_edit',
            'classes_view', 'subjects_view',
            'attendance_view', 'attendance_take',
            'exams_view', 'exams_add', 'exams_edit', 'exams_grade',
            'reports_view', 'reports_students', 'reports_attendance'
        ]
    ],
    'student' => [
        'level' => 'read',
        'resources' => [
            'attendance_view', 'exams_view', 'finance_view'
        ]
    ],
    'staff' => [
        'level' => 'read',
        'resources' => [
            'students_view', 'teachers_view', 'classes_view',
            'attendance_view', 'reports_view'
        ]
    ],
    'financial_manager' => [
        'level' => 'full',
        'resources' => [
            'finance_view', 'finance_fees', 'finance_payments', 'finance_reports',
            'students_view', 'reports_view', 'reports_financial'
        ]
    ],
    'academic_supervisor' => [
        'level' => 'write',
        'resources' => [
            'students_view', 'students_edit', 'teachers_view', 'teachers_edit',
            'classes_view', 'classes_edit', 'subjects_view', 'subjects_edit',
            'exams_view', 'exams_edit', 'reports_view'
        ]
    ],
    'librarian' => [
        'level' => 'write',
        'resources' => [
            'students_view', 'teachers_view', 'reports_view'
        ]
    ],
    'parent' => [
        'level' => 'read',
        'resources' => [
            'students_view', 'attendance_view', 'exams_view', 'finance_view'
        ]
    ]
]);

/**
 * التحقق من صلاحية المستخدم لمورد معين (النسخة المحسنة)
 *
 * @param string $resource_key مفتاح المورد
 * @param string $required_level المستوى المطلوب (read, write, full)
 * @param int|null $user_id معرف المستخدم (افتراضي: المستخدم الحالي)
 * @return bool
 */
function enhanced_has_permission($resource_key, $required_level = 'read', $user_id = null) {
    global $conn;

    // التحقق من تسجيل الدخول
    if (!is_logged_in()) {
        return false;
    }

    // استخدام المستخدم الحالي إذا لم يتم تحديد معرف
    if ($user_id === null) {
        $user_id = $_SESSION['user_id'] ?? 0;
    }

    // الحصول على بيانات المستخدم
    $user = get_user_data($user_id);
    if (!$user || $user['status'] !== 'active') {
        return false;
    }

    // المدير العام له جميع الصلاحيات
    if ($user['role'] === 'admin') {
        return true;
    }

    // التحقق من الصلاحيات المخصصة أولاً (أولوية عالية)
    $custom_permission = get_custom_permission($user_id, $resource_key);
    if ($custom_permission !== null) {
        return check_permission_level($custom_permission, $required_level);
    }

    // التحقق من صلاحيات الدور
    $role_permission = get_role_permission($user['role'], $resource_key);
    if ($role_permission !== null) {
        return check_permission_level($role_permission, $required_level);
    }

    // التحقق من الصلاحيات الافتراضية
    return check_default_permission($user['role'], $resource_key, $required_level);
}

/**
 * الحصول على الصلاحية المخصصة للمستخدم
 * 
 * @param int $user_id معرف المستخدم
 * @param string $resource_key مفتاح المورد
 * @return string|null مستوى الصلاحية أو null إذا لم توجد
 */
function get_custom_permission($user_id, $resource_key) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            SELECT permission_level 
            FROM user_custom_permissions 
            WHERE user_id = ? AND resource_key = ? AND is_granted = 1
            AND (expires_at IS NULL OR expires_at > NOW())
            LIMIT 1
        ");
        $stmt->bind_param("is", $user_id, $resource_key);
        $stmt->execute();
        $result = $stmt->get_result()->fetch_assoc();
        
        return $result ? $result['permission_level'] : null;
    } catch (Exception $e) {
        error_log("Error getting custom permission: " . $e->getMessage());
        return null;
    }
}

/**
 * الحصول على صلاحية الدور
 * 
 * @param string $role_name اسم الدور
 * @param string $resource_key مفتاح المورد
 * @return string|null مستوى الصلاحية أو null إذا لم توجد
 */
function get_role_permission($role_name, $resource_key) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            SELECT permission_level 
            FROM role_permissions 
            WHERE role_name = ? AND resource_key = ? AND is_granted = 1
            LIMIT 1
        ");
        $stmt->bind_param("ss", $role_name, $resource_key);
        $stmt->execute();
        $result = $stmt->get_result()->fetch_assoc();
        
        return $result ? $result['permission_level'] : null;
    } catch (Exception $e) {
        error_log("Error getting role permission: " . $e->getMessage());
        return null;
    }
}

/**
 * التحقق من الصلاحيات الافتراضية
 * 
 * @param string $role_name اسم الدور
 * @param string $resource_key مفتاح المورد
 * @param string $required_level المستوى المطلوب
 * @return bool
 */
function check_default_permission($role_name, $resource_key, $required_level) {
    $role_config = DEFAULT_ROLE_PERMISSIONS[$role_name] ?? null;
    
    if (!$role_config) {
        return false;
    }
    
    // التحقق من وجود المورد في قائمة الموارد المسموحة
    if (!in_array($resource_key, $role_config['resources']) && !in_array('*', $role_config['resources'])) {
        return false;
    }
    
    // التحقق من مستوى الصلاحية
    return check_permission_level($role_config['level'], $required_level);
}

/**
 * التحقق من مستوى الصلاحية
 * 
 * @param string $user_level مستوى صلاحية المستخدم
 * @param string $required_level المستوى المطلوب
 * @return bool
 */
function check_permission_level($user_level, $required_level) {
    $user_level_value = PERMISSION_LEVELS[$user_level] ?? 0;
    $required_level_value = PERMISSION_LEVELS[$required_level] ?? 0;
    
    return $user_level_value >= $required_level_value;
}

/**
 * الحصول على بيانات المستخدم
 * 
 * @param int $user_id معرف المستخدم
 * @return array|null بيانات المستخدم أو null إذا لم يوجد
 */
function get_user_data($user_id) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            SELECT id, username, email, full_name, role, custom_role_id, status 
            FROM users 
            WHERE id = ? 
            LIMIT 1
        ");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        return $stmt->get_result()->fetch_assoc();
    } catch (Exception $e) {
        error_log("Error getting user data: " . $e->getMessage());
        return null;
    }
}

/**
 * التحقق من الصلاحية مع إعادة توجيه في حالة عدم التوفر (النسخة المحسنة)
 *
 * @param string $resource_key مفتاح المورد
 * @param string $required_level المستوى المطلوب
 * @param string $redirect_url رابط إعادة التوجيه
 */
function enhanced_require_permission($resource_key, $required_level = 'read', $redirect_url = '../dashboard/') {
    if (!enhanced_has_permission($resource_key, $required_level)) {
        // تسجيل محاولة الوصول غير المصرح بها
        log_permission_audit('access_denied', $resource_key);

        // إعادة التوجيه
        header('Location: ' . $redirect_url . '?error=access_denied');
        exit();
    }
}

/**
 * تسجيل عملية في سجل تدقيق الصلاحيات
 * 
 * @param string $action_type نوع العملية
 * @param string $resource_key مفتاح المورد
 * @param string $old_value القيمة القديمة
 * @param string $new_value القيمة الجديدة
 * @param int|null $changed_by من قام بالتغيير
 * @param string $notes ملاحظات
 */
function log_permission_audit($action_type, $resource_key = null, $old_value = null, $new_value = null, $changed_by = null, $notes = null) {
    global $conn;
    
    if (!isset($_SESSION['user_id'])) {
        return;
    }
    
    $user_id = $_SESSION['user_id'];
    $changed_by = $changed_by ?? $user_id;
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    
    try {
        $stmt = $conn->prepare("
            INSERT INTO permissions_audit_log 
            (user_id, action_type, resource_key, old_value, new_value, changed_by, ip_address, user_agent, notes) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->bind_param("issssisss", $user_id, $action_type, $resource_key, $old_value, $new_value, $changed_by, $ip_address, $user_agent, $notes);
        $stmt->execute();
    } catch (Exception $e) {
        error_log("Error logging permission audit: " . $e->getMessage());
    }
}
