<?php
/**
 * صفحة إدارة رسوم الطلاب
 * Student Fees Management Page
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// معالجة تغيير اللغة من URL
if (isset($_GET['lang']) && in_array($_GET['lang'], ['ar', 'en'])) {
    $_SESSION['system_language'] = $_GET['lang'];
}


// التحقق من الصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    header('Location: ../../dashboard/');
    exit();
}

// معالجة الحذف
if (isset($_POST['delete_fee'])) {
    $fee_id = intval($_POST['fee_id']);

    global $conn;
    $conn->begin_transaction();

    try {
        // التحقق من وجود مدفوعات مرتبطة
        $check_payments = $conn->prepare("SELECT COUNT(*) as count FROM student_payments WHERE student_fee_id = ?");
        $check_payments->bind_param("i", $fee_id);
        $check_payments->execute();
        $payment_count = $check_payments->get_result()->fetch_assoc()['count'];

        if ($payment_count > 0) {
            throw new Exception("Cannot delete fee with existing payments");
        }

        // حذف الأقساط المرتبطة
        $delete_installments = $conn->prepare("DELETE FROM student_installments WHERE student_fee_id = ?");
        $delete_installments->bind_param("i", $fee_id);
        $delete_installments->execute();

        // حذف الرسم
        $delete_fee = $conn->prepare("DELETE FROM student_fees WHERE id = ?");
        $delete_fee->bind_param("i", $fee_id);
        $delete_fee->execute();

        $conn->commit();

        // تسجيل النشاط
        log_activity($_SESSION['user_id'], 'delete_student_fee', 'student_fees', $fee_id);

        $_SESSION['success_message'] = __('deleted_successfully');
    } catch (Exception $e) {
        $conn->rollback();
        if (strpos($e->getMessage(), "Cannot delete fee with existing payments") !== false) {
            $_SESSION['error_message'] = __('cannot_delete_fee_with_payments');
        } else {
            $_SESSION['error_message'] = __('error_occurred');
        }
        log_error("Error deleting student fee: " . $e->getMessage());
    }

    header('Location: index.php');
    exit();
}

// معالجة البحث والفلترة
$search = clean_input($_GET['search'] ?? '');
$student_filter = intval($_GET['student_id'] ?? 0);
$fee_type_filter = intval($_GET['fee_type_id'] ?? 0);
$status_filter = clean_input($_GET['status'] ?? '');
$academic_year_filter = intval($_GET['academic_year'] ?? date('Y'));
$class_filter = intval($_GET['class_id'] ?? 0);

// بناء استعلام البحث المبسط
$where_conditions = ["1=1"];
$bind_params = [];
$bind_types = "";

if (!empty($search)) {
    $where_conditions[] = "(u.full_name LIKE ? OR s.student_id LIKE ?)";
    $search_param = "%$search%";
    $bind_params[] = $search_param;
    $bind_params[] = $search_param;
    $bind_types .= "ss";
}

if ($student_filter > 0) {
    $where_conditions[] = "sf.student_id = ?";
    $bind_params[] = $student_filter;
    $bind_types .= "i";
}

if ($fee_type_filter > 0) {
    $where_conditions[] = "sf.fee_type_id = ?";
    $bind_params[] = $fee_type_filter;
    $bind_types .= "i";
}

if (!empty($status_filter)) {
    $where_conditions[] = "sf.status = ?";
    $bind_params[] = $status_filter;
    $bind_types .= "s";
}

if ($academic_year_filter > 0) {
    $where_conditions[] = "YEAR(sf.created_at) = ?";
    $bind_params[] = $academic_year_filter;
    $bind_types .= "i";
}

if ($class_filter > 0) {
    $where_conditions[] = "s.class_id = ?";
    $bind_params[] = $class_filter;
    $bind_types .= "i";
}

$where_clause = implode(" AND ", $where_conditions);

// الحصول على عدد الصفحات
$count_query = "
    SELECT COUNT(*) as total
    FROM student_fees sf
    JOIN students s ON sf.student_id = s.id
    JOIN users u ON s.user_id = u.id
    LEFT JOIN fee_types ft ON sf.fee_type_id = ft.id
    LEFT JOIN classes c ON s.class_id = c.id
    WHERE $where_clause
";

$count_stmt = $conn->prepare($count_query);
if (!empty($bind_types)) {
    $count_stmt->bind_param($bind_types, ...$bind_params);
}
$count_stmt->execute();
$total_records = $count_stmt->get_result()->fetch_assoc()['total'];

// إعدادات الترقيم
$page = intval($_GET['page'] ?? 1);
$records_per_page = 20;
$total_pages = ceil($total_records / $records_per_page);
$offset = ($page - 1) * $records_per_page;

// جلب الرسوم
$query = "
    SELECT
        sf.id,
        sf.student_id,
        sf.fee_type_id,
        sf.base_amount,
        sf.discount_amount,
        sf.final_amount,
        sf.paid_amount,
        sf.remaining_amount,
        sf.status,
        sf.due_date,
        sf.created_at,
        u.full_name as student_name,
        s.student_id as student_number,
        ft.type_name as fee_type_name,
        c.class_name,
        c.grade_level
    FROM student_fees sf
    JOIN students s ON sf.student_id = s.id
    JOIN users u ON s.user_id = u.id
    LEFT JOIN fee_types ft ON sf.fee_type_id = ft.id
    LEFT JOIN classes c ON s.class_id = c.id
    WHERE $where_clause
    ORDER BY sf.created_at DESC
    LIMIT ? OFFSET ?
";

$stmt = $conn->prepare($query);
if (!$stmt) {
    die("خطأ في تحضير الاستعلام: " . $conn->error);
}

$final_params = $bind_params;
$final_params[] = $records_per_page;
$final_params[] = $offset;
$final_types = $bind_types . "ii";

if (!empty($final_types)) {
    $stmt->bind_param($final_types, ...$final_params);
}
$stmt->execute();
$fees = $stmt->get_result();

// جلب قوائم الفلترة
$students = $conn->query("
    SELECT s.id, u.full_name, s.student_id as student_number, c.class_name
    FROM students s
    JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    WHERE s.status = 'active'
    ORDER BY u.full_name
");

$fee_types = $conn->query("SELECT id, type_name FROM fee_types WHERE status = 'active' ORDER BY type_name");

$classes = $conn->query("SELECT id, class_name, grade_level FROM classes WHERE status = 'active' ORDER BY grade_level, class_name");

$academic_years = $conn->query("SELECT DISTINCT YEAR(created_at) as academic_year FROM student_fees ORDER BY academic_year DESC");

// إحصائيات سريعة
$stats_query = "
    SELECT
        COUNT(*) as total_fees,
        SUM(sf.final_amount) as total_amount,
        SUM(CASE WHEN sf.status = 'paid' THEN sf.final_amount ELSE 0 END) as paid_amount,
        SUM(CASE WHEN sf.status IN ('pending', 'overdue') THEN sf.final_amount ELSE 0 END) as pending_amount,
        COUNT(CASE WHEN sf.status = 'paid' THEN 1 END) as paid_count,
        COUNT(CASE WHEN sf.status = 'pending' THEN 1 END) as pending_count,
        COUNT(CASE WHEN sf.status = 'overdue' THEN 1 END) as overdue_count
    FROM student_fees sf
    WHERE YEAR(sf.created_at) = ?
";

$stats_stmt = $conn->prepare($stats_query);
$year_to_filter = $academic_year_filter ?: date('Y');
$stats_stmt->bind_param("i", $year_to_filter);
$stats_stmt->execute();
$stats_result = $stats_stmt->get_result()->fetch_assoc();

// تنظيف البيانات وضمان عدم وجود قيم null
$stats = [
    'total_fees' => intval($stats_result['total_fees'] ?? 0),
    'total_amount' => floatval($stats_result['total_amount'] ?? 0),
    'paid_amount' => floatval($stats_result['paid_amount'] ?? 0),
    'pending_amount' => floatval($stats_result['pending_amount'] ?? 0),
    'paid_count' => intval($stats_result['paid_count'] ?? 0),
    'pending_count' => intval($stats_result['pending_count'] ?? 0),
    'overdue_count' => intval($stats_result['overdue_count'] ?? 0)
];

include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('student_fees'); ?></h1>
            <p class="text-muted"><?php echo __('manage_student_fees_info'); ?></p>
        </div>
        <div>
            <a href="add_simple_working.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i><?php echo __('add_fee'); ?>
            </a>
            <a href="bulk_assign.php" class="btn btn-success">
                <i class="fas fa-users me-2"></i><?php echo __('bulk_assign'); ?>
            </a>
            <a href="import.php" class="btn btn-info">
                <i class="fas fa-upload me-2"></i><?php echo __('import_fees'); ?>
            </a>
        </div>
    </div>

    <!-- رسائل النجاح والخطأ -->
    <?php if (isset($_SESSION['success_message'])): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i><?php echo $_SESSION['success_message']; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php unset($_SESSION['success_message']); endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i><?php echo $_SESSION['error_message']; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php unset($_SESSION['error_message']); endif; ?>

    <?php if (isset($_GET['success']) && $_GET['success'] == 1): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i><?php echo __('fee_added_successfully'); ?>
        <?php if (isset($_GET['fee_type']) && isset($_GET['amount'])): ?>
            <br><strong><?php echo __('fee_type'); ?>:</strong> <?php echo htmlspecialchars($_GET['fee_type']); ?>
            - <strong><?php echo __('amount'); ?>:</strong> <?php echo format_currency($_GET['amount']); ?>
        <?php endif; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if (isset($_GET['delete_success']) && $_GET['delete_success'] == 1): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-trash me-2"></i><?php echo __('fee_deleted_successfully'); ?>
        <?php if (isset($_GET['student_name']) && isset($_GET['fee_type'])): ?>
            - <?php echo __('student'); ?>: <?php echo htmlspecialchars($_GET['student_name']); ?>
            - <?php echo __('fee_type'); ?>: <?php echo htmlspecialchars($_GET['fee_type']); ?>
        <?php endif; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-gradient p-3 rounded-3">
                                <i class="fas fa-money-bill-wave text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($stats['total_fees']); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('total_fees'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-gradient p-3 rounded-3">
                                <i class="fas fa-check-circle text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo format_currency($stats['paid_amount']); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('paid_fees_amount'); ?></p>
                            <small class="text-success"><?php echo $stats['paid_count']; ?> <?php echo __('fees'); ?></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-gradient p-3 rounded-3">
                                <i class="fas fa-clock text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo format_currency($stats['pending_amount']); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('unpaid_fees_amount'); ?></p>
                            <small class="text-warning"><?php echo ($stats['pending_count'] + $stats['overdue_count']); ?> <?php echo __('fees'); ?></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-danger bg-gradient p-3 rounded-3">
                                <i class="fas fa-exclamation-triangle text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo $stats['overdue_count']; ?></h3>
                            <p class="text-muted mb-0"><?php echo __('overdue_fees'); ?></p>
                            <small class="text-danger"><?php echo __('fees_past_due'); ?></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-2">
                    <label for="search" class="form-label"><?php echo __('search'); ?></label>
                    <input type="text"
                           class="form-control"
                           id="search"
                           name="search"
                           value="<?php echo htmlspecialchars($search); ?>"
                           placeholder="<?php echo __('search_student'); ?>">
                </div>

                <div class="col-md-2">
                    <label for="student_id" class="form-label"><?php echo __('student'); ?></label>
                    <select class="form-select" id="student_id" name="student_id">
                        <option value=""><?php echo __('all_students'); ?></option>
                        <?php while ($student = $students->fetch_assoc()): ?>
                            <option value="<?php echo $student['id']; ?>"
                                    <?php echo ($student_filter == $student['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($student['full_name'] . ' (ID: ' . $student['student_number'] . ')'); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="fee_type_id" class="form-label"><?php echo __('fee_type'); ?></label>
                    <select class="form-select" id="fee_type_id" name="fee_type_id">
                        <option value=""><?php echo __('all_types'); ?></option>
                        <?php while ($fee_type = $fee_types->fetch_assoc()): ?>
                            <option value="<?php echo $fee_type['id']; ?>"
                                    <?php echo ($fee_type_filter == $fee_type['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($fee_type['type_name']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="class_id" class="form-label"><?php echo __('class'); ?></label>
                    <select class="form-select" id="class_id" name="class_id">
                        <option value=""><?php echo __('all_classes'); ?></option>
                        <?php while ($class = $classes->fetch_assoc()): ?>
                            <option value="<?php echo $class['id']; ?>"
                                    <?php echo ($class_filter == $class['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($class['class_name'] . ' - ' . $class['grade_level']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="status" class="form-label"><?php echo __('status'); ?></label>
                    <select class="form-select" id="status" name="status">
                        <option value=""><?php echo __('all_statuses'); ?></option>
                        <option value="pending" <?php echo ($status_filter == 'pending') ? 'selected' : ''; ?>>
                            <?php echo __('pending'); ?>
                        </option>
                        <option value="partial" <?php echo ($status_filter == 'partial') ? 'selected' : ''; ?>>
                            <?php echo __('partial'); ?>
                        </option>
                        <option value="paid" <?php echo ($status_filter == 'paid') ? 'selected' : ''; ?>>
                            <?php echo __('paid'); ?>
                        </option>
                        <option value="overdue" <?php echo ($status_filter == 'overdue') ? 'selected' : ''; ?>>
                            <?php echo __('overdue'); ?>
                        </option>
                        <option value="cancelled" <?php echo ($status_filter == 'cancelled') ? 'selected' : ''; ?>>
                            <?php echo __('cancelled'); ?>
                        </option>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="academic_year" class="form-label"><?php echo __('academic_year'); ?></label>
                    <select class="form-select" id="academic_year" name="academic_year">
                        <?php while ($year = $academic_years->fetch_assoc()): ?>
                            <option value="<?php echo $year['academic_year']; ?>"
                                    <?php echo ($academic_year_filter == $year['academic_year']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($year['academic_year']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>

                <div class="col-md-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i><?php echo __('search'); ?>
                    </button>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i><?php echo __('clear'); ?>
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول النتائج -->
    <?php if ($fees && $fees->num_rows > 0): ?>
    <div class="card">
        <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i><?php echo __('fees_list'); ?>
                    <span class="badge bg-primary ms-2"><?php echo $total_records; ?></span>
                </h5>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-success btn-sm" onclick="exportToExcel()">
                        <i class="fas fa-file-excel me-1"></i><?php echo __('export_excel'); ?>
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="printTable()">
                        <i class="fas fa-print me-1"></i><?php echo __('print'); ?>
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="feesTable">
                    <thead class="table-dark">
                        <tr>
                            <th><?php echo __('student'); ?></th>
                            <th><?php echo __('fee_type'); ?></th>
                            <th><?php echo __('amounts'); ?></th>
                            <th><?php echo __('status'); ?></th>
                            <th><?php echo __('due_date'); ?></th>
                            <th><?php echo __('created_date'); ?></th>
                            <th class="no-print"><?php echo __('actions'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while ($fee = $fees->fetch_assoc()): ?>
                        <tr>
                            <td>
                                <div>
                                    <strong><?php echo htmlspecialchars($fee['student_name']); ?></strong>
                                    <br>
                                    <small class="text-muted">ID: <?php echo htmlspecialchars($fee['student_number']); ?></small>
                                    <?php if (!empty($fee['class_name'])): ?>
                                        <br>
                                        <small class="text-info"><?php echo htmlspecialchars($fee['class_name']); ?></small>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <span class="fw-bold"><?php echo htmlspecialchars($fee['fee_type_name'] ?? __('general_fee')); ?></span>
                            </td>
                            <td>
                                <div class="small">
                                    <?php if ($fee['discount_amount'] > 0): ?>
                                    <div class="d-flex justify-content-between">
                                        <span><?php echo __('base_amount'); ?>:</span>
                                        <span><?php echo format_currency($fee['base_amount']); ?></span>
                                    </div>
                                    <div class="d-flex justify-content-between text-success">
                                        <span><?php echo __('discount'); ?>:</span>
                                        <span>-<?php echo format_currency($fee['discount_amount']); ?></span>
                                    </div>
                                    <div class="d-flex justify-content-between text-primary border-top pt-1">
                                        <span><strong><?php echo __('fee_amount'); ?>:</strong></span>
                                        <span class="fw-bold"><?php echo format_currency($fee['final_amount']); ?></span>
                                    </div>
                                    <?php else: ?>
                                    <div class="text-center">
                                        <span class="fw-bold fs-5 text-primary"><?php echo format_currency($fee['final_amount']); ?></span>
                                        <br>
                                        <small class="text-muted"><?php echo __('fee_amount'); ?></small>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <?php
                                $status_classes = [
                                    'pending' => 'bg-warning',
                                    'partial' => 'bg-info',
                                    'paid' => 'bg-success',
                                    'overdue' => 'bg-danger',
                                    'cancelled' => 'bg-secondary'
                                ];
                                $status_class = $status_classes[$fee['status']] ?? 'bg-secondary';
                                ?>
                                <span class="badge <?php echo $status_class; ?>">
                                    <?php echo __($fee['status']); ?>
                                </span>
                            </td>
                            <td>
                                <?php echo date('Y-m-d', strtotime($fee['due_date'])); ?>
                                <?php if ($fee['status'] === 'overdue'): ?>
                                    <br>
                                    <small class="text-danger">
                                        <i class="fas fa-exclamation-triangle me-1"></i><?php echo __('overdue'); ?>
                                    </small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php echo date('Y-m-d', strtotime($fee['created_at'])); ?>
                                <br>
                                <small class="text-muted"><?php echo date('H:i', strtotime($fee['created_at'])); ?></small>
                            </td>
                            <td class="no-print">
                                <div class="btn-group" role="group">
                                    <a href="view.php?id=<?php echo $fee['id']; ?>" class="btn btn-sm btn-info" title="<?php echo __('view_details'); ?>">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="edit.php?id=<?php echo $fee['id']; ?>" class="btn btn-sm btn-warning" title="<?php echo __('edit'); ?>">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="delete.php?id=<?php echo $fee['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('<?php echo __('confirm_delete'); ?>');" title="<?php echo __('delete'); ?>">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- الترقيم -->
        <?php if ($total_pages > 1): ?>
        <div class="card-footer">
            <nav aria-label="<?php echo __('pagination'); ?>">
                <ul class="pagination justify-content-center mb-0">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                    <?php endfor; ?>

                    <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>

            <div class="text-center mt-2">
                <small class="text-muted">
                    <?php echo __('showing'); ?> <?php echo (($page - 1) * $records_per_page) + 1; ?> -
                    <?php echo min($page * $records_per_page, $total_records); ?>
                    <?php echo __('of'); ?> <?php echo $total_records; ?> <?php echo __('records'); ?>
                </small>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <?php elseif (isset($_GET['search']) || isset($_GET['student_id']) || isset($_GET['fee_type_id']) || isset($_GET['status']) || isset($_GET['academic_year']) || isset($_GET['class_id'])): ?>
    <!-- رسالة عدم وجود نتائج -->
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <h5 class="text-muted"><?php echo __('no_results_found'); ?></h5>
            <p class="text-muted"><?php echo __('try_different_search_criteria'); ?></p>
            <a href="index.php" class="btn btn-outline-primary">
                <i class="fas fa-refresh me-2"></i><?php echo __('clear_filters'); ?>
            </a>
        </div>
    </div>
    <?php endif; ?>

</div>

<style>
@media print {
    .no-print { display: none !important; }
    .container { max-width: none !important; }
    body { font-size: 12px; }
    .card { border: none !important; box-shadow: none !important; }
    .table { font-size: 11px; }
}
</style>

<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script>
function exportToExcel() {
    // تصدير الجدول إلى Excel
    const table = document.getElementById('feesTable');
    if (table) {
        const wb = XLSX.utils.table_to_book(table, {sheet: "الرسوم"});
        XLSX.writeFile(wb, 'fees_report_' + new Date().toISOString().slice(0,10) + '.xlsx');
    } else {
        alert('لا توجد بيانات للتصدير');
    }
}

function printTable() {
    // طباعة الجدول
    window.print();
}
</script>

<?php include_once '../../includes/footer.php'; ?>