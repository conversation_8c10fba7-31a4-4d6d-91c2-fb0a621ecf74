<?php
/**
 * صفحة إيصال الدفع
 * Payment Receipt Page
 */

define('SYSTEM_INIT', true);
require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/security.php';

// التحقق من الجلسة والصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    header('Location: ../../dashboard/');
    exit();
}

$payment_id = intval($_GET['id'] ?? 0);

if (empty($payment_id)) {
    $_SESSION['error_message'] = __('invalid_payment_id');
    header('Location: index.php');
    exit();
}

// جلب بيانات المدفوعة
$payment_stmt = $conn->prepare("
    SELECT
        sp.*,
        u.full_name as student_name,
        s.student_id as student_number,
        c.class_name,
        c.grade_level,
        sf.final_amount as fee_amount,
        ft.type_name as fee_type_name,
        pu.full_name as processed_by_name
    FROM student_payments sp
    JOIN students s ON sp.student_id = s.id
    JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    LEFT JOIN student_fees sf ON sp.student_fee_id = sf.id
    LEFT JOIN fee_types ft ON sf.fee_type_id = ft.id
    LEFT JOIN users pu ON sp.processed_by = pu.id
    WHERE sp.id = ?
");

if (!$payment_stmt) {
    die("خطأ في تحضير الاستعلام: " . $conn->error);
}

$payment_stmt->bind_param("i", $payment_id);
$payment_stmt->execute();
$payment = $payment_stmt->get_result()->fetch_assoc();

if (!$payment) {
    $_SESSION['error_message'] = __('payment_not_found');
    header('Location: index.php');
    exit();
}

// التحقق من الصلاحيات
if (!check_permission('admin') && $_SESSION['user_id'] != $payment['student_id']) {
    $_SESSION['error_message'] = __('access_denied');
    header('Location: index.php');
    exit();
}

// جلب إعدادات المدرسة
$school_name = get_system_setting('school_name', 'School Management System');
$school_name_en = get_system_setting('school_name_en', 'School Management System');
$school_address = get_system_setting('school_address', '');
$school_phone = get_system_setting('school_phone', '');
$school_email = get_system_setting('school_email', '');
$currency_symbol = get_system_setting('currency_symbol', 'ر.س');

// تحديد اللغة
$current_language = get_current_language();
$is_rtl = $current_language === 'ar';
?>

<!DOCTYPE html>
<html lang="<?php echo $current_language; ?>" dir="<?php echo $is_rtl ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo __('payment_receipt'); ?> - <?php echo $payment['payment_reference']; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS for Receipt -->
    <style>
        @media print {
            .no-print { display: none !important; }
            .container { max-width: none !important; }
            body { font-size: 12px; }
        }
        
        .receipt-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .receipt-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .receipt-body {
            padding: 30px;
        }
        
        .receipt-footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #dee2e6;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px dotted #ddd;
        }
        
        .info-label {
            font-weight: 600;
            color: #495057;
        }
        
        .info-value {
            color: #212529;
        }
        
        .amount-highlight {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
            margin: 20px 0;
        }
        
        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.85em;
        }
        
        .status-confirmed {
            background: #d4edda;
            color: #155724;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .qr-code {
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Print Button -->
        <div class="text-center mb-4 no-print">
            <button onclick="window.print()" class="btn btn-primary me-2">
                <i class="fas fa-print me-2"></i><?php echo __('print_receipt'); ?>
            </button>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_payments'); ?>
            </a>
        </div>

        <!-- Receipt Container -->
        <div class="receipt-container">
            <!-- Receipt Header -->
            <div class="receipt-header">
                <h1 class="mb-2"><?php echo $is_rtl ? $school_name : $school_name_en; ?></h1>
                <?php if (!empty($school_address)): ?>
                    <p class="mb-1"><?php echo htmlspecialchars($school_address); ?></p>
                <?php endif; ?>
                <div class="d-flex justify-content-center gap-3">
                    <?php if (!empty($school_phone)): ?>
                        <span><i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($school_phone); ?></span>
                    <?php endif; ?>
                    <?php if (!empty($school_email)): ?>
                        <span><i class="fas fa-envelope me-1"></i><?php echo htmlspecialchars($school_email); ?></span>
                    <?php endif; ?>
                </div>
                <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">
                <h3 class="mb-0"><?php echo __('payment_receipt'); ?></h3>
            </div>

            <!-- Receipt Body -->
            <div class="receipt-body">
                <!-- Receipt Info -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5 class="text-primary mb-3"><?php echo __('receipt_information'); ?></h5>
                        <div class="info-row">
                            <span class="info-label"><?php echo __('receipt_number'); ?>:</span>
                            <span class="info-value"><?php echo htmlspecialchars($payment['payment_reference']); ?></span>
                        </div>
                        <div class="info-row">
                            <span class="info-label"><?php echo __('payment_date'); ?>:</span>
                            <span class="info-value"><?php echo format_date($payment['payment_date']); ?></span>
                        </div>
                        <div class="info-row">
                            <span class="info-label"><?php echo __('processed_at'); ?>:</span>
                            <span class="info-value"><?php echo format_datetime($payment['processed_at']); ?></span>
                        </div>
                        <div class="info-row">
                            <span class="info-label"><?php echo __('status'); ?>:</span>
                            <span class="info-value">
                                <span class="status-badge status-<?php echo $payment['status']; ?>">
                                    <?php echo __($payment['status']); ?>
                                </span>
                            </span>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h5 class="text-primary mb-3"><?php echo __('student_information'); ?></h5>
                        <div class="info-row">
                            <span class="info-label"><?php echo __('student_name'); ?>:</span>
                            <span class="info-value"><?php echo htmlspecialchars($payment['student_name']); ?></span>
                        </div>
                        <div class="info-row">
                            <span class="info-label"><?php echo __('student_number'); ?>:</span>
                            <span class="info-value"><?php echo htmlspecialchars($payment['student_number']); ?></span>
                        </div>
                        <?php if (!empty($payment['class_name'])): ?>
                        <div class="info-row">
                            <span class="info-label"><?php echo __('class'); ?>:</span>
                            <span class="info-value"><?php echo htmlspecialchars($payment['class_name'] . ' - ' . $payment['grade_level']); ?></span>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Payment Details -->
                <div class="mb-4">
                    <h5 class="text-primary mb-3"><?php echo __('payment_details'); ?></h5>
                    
                    <?php if (!empty($payment['fee_type_name'])): ?>
                    <div class="info-row">
                        <span class="info-label"><?php echo __('fee_type'); ?>:</span>
                        <span class="info-value">
                            <?php 
                            $fee_type_display = $is_rtl ? $payment['fee_type_name'] : 
                                (!empty($payment['fee_type_name_en']) ? $payment['fee_type_name_en'] : $payment['fee_type_name']);
                            echo htmlspecialchars($fee_type_display); 
                            ?>
                        </span>
                    </div>
                    <?php endif; ?>
                    
                    <div class="info-row">
                        <span class="info-label"><?php echo __('payment_method'); ?>:</span>
                        <span class="info-value">
                            <?php
                            $method_icons = [
                                'cash' => 'fas fa-money-bill text-success',
                                'bank_transfer' => 'fas fa-university text-primary',
                                'check' => 'fas fa-money-check text-info',
                                'card' => 'fas fa-credit-card text-warning',
                                'online' => 'fas fa-globe text-secondary'
                            ];
                            $icon = $method_icons[$payment['payment_method']] ?? 'fas fa-question';
                            ?>
                            <i class="<?php echo $icon; ?> me-2"></i>
                            <?php echo __($payment['payment_method']); ?>
                        </span>
                    </div>
                    
                    <?php if (!empty($payment['bank_name'])): ?>
                    <div class="info-row">
                        <span class="info-label"><?php echo __('bank_name'); ?>:</span>
                        <span class="info-value"><?php echo htmlspecialchars($payment['bank_name']); ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($payment['check_number'])): ?>
                    <div class="info-row">
                        <span class="info-label"><?php echo __('check_number'); ?>:</span>
                        <span class="info-value"><?php echo htmlspecialchars($payment['check_number']); ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($payment['transaction_id'])): ?>
                    <div class="info-row">
                        <span class="info-label"><?php echo __('transaction_id'); ?>:</span>
                        <span class="info-value"><?php echo htmlspecialchars($payment['transaction_id']); ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($payment['receipt_number'])): ?>
                    <div class="info-row">
                        <span class="info-label"><?php echo __('receipt_number'); ?>:</span>
                        <span class="info-value"><?php echo htmlspecialchars($payment['receipt_number']); ?></span>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Amount Highlight -->
                <div class="amount-highlight">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mb-0"><?php echo __('amount_paid'); ?></h4>
                            <?php if (!empty($payment['notes'])): ?>
                                <p class="mb-0 text-muted"><?php echo htmlspecialchars($payment['notes']); ?></p>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-4 text-end">
                            <h2 class="mb-0 text-primary">
                                <?php echo number_format($payment['amount'], 2); ?> <?php echo $currency_symbol; ?>
                            </h2>
                        </div>
                    </div>
                </div>

                <!-- Amount in Words -->
                <div class="text-center mb-4">
                    <p class="text-muted">
                        <strong><?php echo __('amount_in_words'); ?>:</strong>
                        <?php echo number_to_words($payment['amount'], $current_language); ?> <?php echo __('only'); ?>
                    </p>
                </div>

                <!-- QR Code (if available) -->
                <div class="qr-code">
                    <div id="qrcode"></div>
                    <small class="text-muted"><?php echo __('scan_for_verification'); ?></small>
                </div>
            </div>

            <!-- Receipt Footer -->
            <div class="receipt-footer">
                <div class="row">
                    <div class="col-md-6 text-start">
                        <?php if (!empty($payment['processed_by_name'])): ?>
                            <p class="mb-1"><strong><?php echo __('processed_by'); ?>:</strong></p>
                            <p class="mb-0"><?php echo htmlspecialchars($payment['processed_by_name']); ?></p>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-6 text-end">
                        <p class="mb-1"><strong><?php echo __('signature'); ?>:</strong></p>
                        <div style="height: 40px; border-bottom: 1px solid #000; width: 200px; margin-left: auto;"></div>
                    </div>
                </div>
                <hr>
                <p class="mb-0 text-muted">
                    <?php echo __('receipt_generated_on'); ?>: <?php echo format_datetime(date('Y-m-d H:i:s')); ?>
                </p>
                <p class="mb-0 text-muted">
                    <?php echo __('thank_you_for_payment'); ?>
                </p>
            </div>
        </div>
    </div>

    <!-- QR Code Library -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    
    <script>
        // Generate QR Code
        const qrData = {
            receipt: '<?php echo $payment['payment_reference']; ?>',
            amount: '<?php echo $payment['amount']; ?>',
            date: '<?php echo $payment['payment_date']; ?>',
            student: '<?php echo addslashes($payment['student_name']); ?>',
            verify_url: '<?php echo get_base_url(); ?>/finance/payments/verify.php?ref=<?php echo $payment['payment_reference']; ?>'
        };
        
        QRCode.toCanvas(document.getElementById('qrcode'), JSON.stringify(qrData), {
            width: 150,
            margin: 2,
            color: {
                dark: '#000000',
                light: '#FFFFFF'
            }
        }, function (error) {
            if (error) console.error(error);
        });
        
        // Auto print if requested
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('print') === '1') {
            window.onload = function() {
                setTimeout(function() {
                    window.print();
                }, 1000);
            };
        }
    </script>
</body>
</html>

<?php
/**
 * تحويل الرقم إلى كلمات
 * Convert number to words
 */
function number_to_words($number, $language = 'ar') {
    // هذه دالة مبسطة - يمكن تطويرها لاحقاً
    if ($language === 'ar') {
        $ones = ['', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة'];
        $tens = ['', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'];
        $hundreds = ['', 'مائة', 'مائتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة', 'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'];
    } else {
        $ones = ['', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine'];
        $tens = ['', '', 'twenty', 'thirty', 'forty', 'fifty', 'sixty', 'seventy', 'eighty', 'ninety'];
        $hundreds = ['', 'one hundred', 'two hundred', 'three hundred', 'four hundred', 'five hundred', 'six hundred', 'seven hundred', 'eight hundred', 'nine hundred'];
    }
    
    // تحويل مبسط للأرقام الصحيحة
    $integer_part = intval($number);
    $decimal_part = round(($number - $integer_part) * 100);
    
    if ($integer_part == 0) {
        return $language === 'ar' ? 'صفر' : 'zero';
    }
    
    // تحويل مبسط - يمكن تحسينه
    if ($integer_part < 10) {
        $result = $ones[$integer_part];
    } elseif ($integer_part < 100) {
        $result = $tens[intval($integer_part / 10)] . ' ' . $ones[$integer_part % 10];
    } else {
        $result = $hundreds[intval($integer_part / 100)] . ' ' . $tens[intval(($integer_part % 100) / 10)] . ' ' . $ones[$integer_part % 10];
    }
    
    if ($decimal_part > 0) {
        $currency_fraction = $language === 'ar' ? 'هللة' : 'cents';
        $result .= ' ' . ($language === 'ar' ? 'و' : 'and') . ' ' . $decimal_part . ' ' . $currency_fraction;
    }
    
    return trim($result);
}
?>
