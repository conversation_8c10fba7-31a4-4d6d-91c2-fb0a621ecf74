<?php
/**
 * نموذج تسجيل الغياب بالخصم
 * Absence with Deduction Form
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';
require_once '../includes/header.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

$user_id = $_SESSION['user_id'];
$tab = $_GET['tab'] ?? 'all';

// تحديد العنوان والرابط حسب التبويب
$page_title = 'نموذج تسجيل الغياب بالخصم';
$back_link = 'smart_attendance.php';

switch ($tab) {
    case 'teachers':
        $page_title = 'نموذج تسجيل غياب المعلمين بالخصم';
        $back_link = 'smart_attendance.php?tab=teachers&date=' . date('Y-m-d');
        break;
    case 'admins':
        $page_title = 'نموذج تسجيل غياب الإداريين بالخصم';
        $back_link = 'smart_attendance.php?tab=admins&date=' . date('Y-m-d');
        break;
    default:
        $back_link = 'smart_attendance.php?date=' . date('Y-m-d');
        break;
}

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'طلب غير صالح';
    } else {
        $staff_id = intval($_POST['staff_id']);
        $absence_date = clean_input($_POST['absence_date']);
        $reason = clean_input($_POST['reason']);
        $deduction_amount = floatval($_POST['deduction_amount']);
        $deduction_type = clean_input($_POST['deduction_type']);

        if (empty($staff_id) || empty($absence_date)) {
            $error_message = 'الحقول المطلوبة: الموظف وتاريخ الغياب';
        } elseif ($absence_date > date('Y-m-d')) {
            $error_message = 'لا يمكن تسجيل غياب لتاريخ مستقبلي';
        } else {
            try {
                $conn->begin_transaction();

                // التحقق من عدم وجود تسجيل مسبق لنفس التاريخ
                $check_stmt = $conn->prepare("
                    SELECT id FROM staff_absences_with_deduction
                    WHERE user_id = ? AND absence_date = ?
                ");
                $check_stmt->bind_param("is", $staff_id, $absence_date);
                $check_stmt->execute();
                $existing = $check_stmt->get_result();

                if ($existing->num_rows > 0) {
                    throw new Exception('يوجد تسجيل غياب بالخصم مسبق لهذا التاريخ');
                }

                // الحصول على نوع المستخدم
                $user_role_stmt = $conn->prepare("SELECT role FROM users WHERE id = ?");
                $user_role_stmt->bind_param("i", $staff_id);
                $user_role_stmt->execute();
                $user_role_result = $user_role_stmt->get_result();
                $user_data = $user_role_result->fetch_assoc();

                if (!$user_data) {
                    throw new Exception('المستخدم غير موجود');
                }

                $user_role = $user_data['role'];

                // التحقق من وجود الجداول المطلوبة
                $required_tables = ['staff_absences_with_deduction'];
                if ($user_role === 'teacher') {
                    $required_tables[] = 'teacher_attendance';
                } else {
                    $required_tables[] = 'admin_attendance';
                }

                foreach ($required_tables as $table) {
                    $table_check = $conn->query("SHOW TABLES LIKE '$table'");
                    if ($table_check->num_rows === 0) {
                        throw new Exception("جدول $table غير موجود. يرجى تشغيل ملف الإعداد أولاً.");
                    }
                }

                // إدراج الغياب بالخصم (مفعل مباشرة)
                $absence_stmt = $conn->prepare("
                    INSERT INTO staff_absences_with_deduction
                    (user_id, absence_date, reason, deduction_amount, absence_type, status, recorded_by, processed_by, processed_at)
                    VALUES (?, ?, ?, ?, 'unauthorized', 'processed', ?, ?, NOW())
                ");
                $absence_stmt->bind_param("issdii", $staff_id, $absence_date, $reason, $deduction_amount, $user_id, $user_id);
                $absence_stmt->execute();

                $absence_id = $conn->insert_id;

                // تحديث سجل الحضور إلى غياب بالخصم
                $note = 'غياب بالخصم - مبلغ الخصم: ' . $deduction_amount . ' ج.م';
                if (!empty($reason)) {
                    $note .= ' - السبب: ' . $reason;
                }

                if ($user_role === 'teacher') {
                    // للمعلمين: استخدام جدول teacher_attendance
                    $teacher_stmt = $conn->prepare("SELECT id FROM teachers WHERE user_id = ?");
                    $teacher_stmt->bind_param("i", $staff_id);
                    $teacher_stmt->execute();
                    $teacher_result = $teacher_stmt->get_result()->fetch_assoc();

                    if ($teacher_result) {
                        $update_attendance_stmt = $conn->prepare("
                            INSERT INTO teacher_attendance (teacher_id, attendance_date, status, notes, recorded_by, created_at)
                            VALUES (?, ?, 'absence_with_deduction', ?, ?, NOW())
                            ON DUPLICATE KEY UPDATE
                            status = 'absence_with_deduction',
                            notes = ?,
                            check_in_time = NULL,
                            check_out_time = NULL,
                            recorded_by = ?,
                            updated_at = NOW()
                        ");
                        $update_attendance_stmt->bind_param("issisi", $teacher_result['id'], $absence_date, $note, $user_id, $note, $user_id);
                        if (!$update_attendance_stmt->execute()) {
                            throw new Exception('فشل في تحديث سجل حضور المعلم');
                        }
                    } else {
                        throw new Exception('المعلم غير موجود في جدول المعلمين');
                    }
                } elseif ($user_role === 'admin' || $user_role === 'staff') {
                    // للإداريين: استخدام جدول admin_attendance
                    $update_attendance_stmt = $conn->prepare("
                        INSERT INTO admin_attendance (admin_id, attendance_date, status, notes, recorded_by, created_at)
                        VALUES (?, ?, 'absence_with_deduction', ?, ?, NOW())
                        ON DUPLICATE KEY UPDATE
                        status = 'absence_with_deduction',
                        notes = ?,
                        check_in_time = NULL,
                        check_out_time = NULL,
                        recorded_by = ?,
                        updated_at = NOW()
                    ");
                    $update_attendance_stmt->bind_param("issisi", $staff_id, $absence_date, $note, $user_id, $note, $user_id);
                    if (!$update_attendance_stmt->execute()) {
                        throw new Exception('فشل في تحديث سجل حضور الإداري');
                    }
                } else {
                    throw new Exception('نوع المستخدم غير مدعوم: ' . $user_role);
                }

                $conn->commit();

                // تسجيل النشاط
                log_activity($user_id, 'record_absence_with_deduction', 'staff_absences_with_deduction', $absence_id);

                $success_message = 'تم تسجيل الغياب بالخصم وتفعيله مباشرة. مبلغ الخصم: ' . $deduction_amount . ' ج.م';

            } catch (Exception $e) {
                $conn->rollback();
                $error_message = 'حدث خطأ: ' . $e->getMessage();
            }
        }
    }
}

// جلب قائمة الموظفين حسب التبويب
$role_condition = '';
switch ($tab) {
    case 'teachers':
        $role_condition = "AND u.role = 'teacher'";
        break;
    case 'admins':
        $role_condition = "AND u.role = 'staff'";
        break;
    default:
        $role_condition = "AND u.role IN ('teacher', 'staff')";
        break;
}

$staff_query = "
    SELECT u.id, u.username, u.full_name, u.role
    FROM users u
    WHERE u.status = 'active'
    $role_condition
    ORDER BY u.role, u.full_name
";
$staff_result = $conn->query($staff_query);

// جلب إعدادات الخصم
$deduction_settings = [];
try {
    $deduction_settings_query = "SELECT * FROM deduction_settings WHERE is_active = 1 ORDER BY absence_type";
    $deduction_settings_result = $conn->query($deduction_settings_query);
    if ($deduction_settings_result) {
        while ($row = $deduction_settings_result->fetch_assoc()) {
            $deduction_settings[$row['absence_type']] = [
                'deduction_type' => $row['deduction_type'],
                'deduction_value' => $row['deduction_value'],
                'max_allowed_per_month' => $row['max_allowed_per_month'],
                'requires_approval' => $row['requires_approval'],
                'description' => $row['description']
            ];
        }
    }
} catch (mysqli_sql_exception $e) {
    // إذا لم يكن الجدول موجود، استخدم القيم الافتراضية
    error_log("Deduction settings table not found: " . $e->getMessage());
}

// القيم الافتراضية للخصم
$default_daily = $deduction_settings['unauthorized']['deduction_value'] ?? 100.00;
$default_hourly = 15.00; // قيمة افتراضية للخصم بالساعة

// التحقق من وجود الجداول المطلوبة
$tables_exist = true;
$missing_tables = [];

$required_tables = ['staff_absences_with_deduction', 'deduction_settings'];
foreach ($required_tables as $table) {
    $check = $conn->query("SHOW TABLES LIKE '$table'");
    if ($check->num_rows === 0) {
        $tables_exist = false;
        $missing_tables[] = $table;
    }
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-user-times me-2"></i>
                        <?php echo $page_title; ?>
                    </h5>

                </div>
                <div class="card-body">
                    <?php if (isset($success_message)): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($error_message)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                        </div>
                    <?php endif; ?>

                    <?php if (!$tables_exist): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-database me-2"></i>
                            <strong>خطأ في قاعدة البيانات:</strong> الجداول المطلوبة غير موجودة.
                            <br>الجداول المفقودة: <?php echo implode(', ', $missing_tables); ?>
                            <br><a href="../setup_absence_system.php" class="btn btn-warning btn-sm mt-2">
                                <i class="fas fa-tools me-2"></i>تشغيل ملف الإعداد
                            </a>
                        </div>
                    <?php endif; ?>

                    <!-- تحذير مهم -->
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> تسجيل الغياب بالخصم سيؤثر على راتب الموظف ويتم تفعيله مباشرة. يرجى التأكد من صحة البيانات قبل الحفظ.
                    </div>

                    <form method="POST" class="needs-validation" novalidate>
                        <?php echo csrf_token_field(); ?>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="staff_id" class="form-label">الموظف *</label>
                                    <select class="form-select" id="staff_id" name="staff_id" required>
                                        <option value="">اختر الموظف</option>
                                        <?php while ($staff = $staff_result->fetch_assoc()): ?>
                                            <option value="<?php echo $staff['id']; ?>">
                                                <?php echo htmlspecialchars($staff['full_name'] . ' (' . $staff['role'] . ')'); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="absence_date" class="form-label">تاريخ الغياب *</label>
                                    <input type="date" class="form-control" id="absence_date" name="absence_date" 
                                           max="<?php echo date('Y-m-d'); ?>" required>
                                    <small class="text-muted">لا يمكن تسجيل غياب لتاريخ مستقبلي</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="deduction_type" class="form-label">نوع الخصم *</label>
                                    <select class="form-select" id="deduction_type" name="deduction_type" required>
                                        <option value="daily_wage">خصم يومي كامل</option>
                                        <option value="hourly_wage">خصم بالساعة</option>
                                        <option value="fixed_amount">مبلغ ثابت</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="deduction_amount" class="form-label">مبلغ الخصم (ج.م) *</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="deduction_amount" name="deduction_amount"
                                               step="0.01" min="0" value="<?php echo $default_daily; ?>" required>
                                        <span class="input-group-text">ج.م</span>
                                    </div>
                                    <small class="text-muted">سيتم تحديث المبلغ تلقائياً حسب نوع الخصم</small>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="reason" class="form-label">سبب الغياب (اختياري)</label>
                            <textarea class="form-control" id="reason" name="reason" rows="3"
                                      placeholder="اكتب سبب الغياب بالخصم (اختياري)..."></textarea>
                            <small class="text-muted">يمكنك ترك هذا الحقل فارغاً إذا لم يكن هناك سبب محدد</small>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-secondary" onclick="goBackToAttendance()">
                                <i class="fas fa-arrow-left me-2"></i>رجوع
                            </button>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-save me-2"></i>تسجيل الغياب بالخصم
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const deductionType = document.getElementById('deduction_type');
    const deductionAmount = document.getElementById('deduction_amount');
    
    // القيم الافتراضية
    const defaultAmounts = {
        'daily_wage': <?php echo $default_daily; ?>,
        'hourly_wage': <?php echo $default_hourly; ?>,
        'fixed_amount': 50.00
    };
    
    // تحديث مبلغ الخصم عند تغيير النوع
    deductionType.addEventListener('change', function() {
        const selectedType = this.value;
        if (defaultAmounts[selectedType]) {
            deductionAmount.value = defaultAmounts[selectedType];
        }
    });
    
    // التحقق من صحة النموذج
    const form = document.querySelector('.needs-validation');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        } else {
            // تأكيد إضافي
            const staffSelect = document.getElementById('staff_id');
            const staffName = staffSelect.options[staffSelect.selectedIndex].text;
            const amount = deductionAmount.value;
            const date = document.getElementById('absence_date').value;
            
            if (!confirm(`هل أنت متأكد من تسجيل غياب بالخصم؟\n\nالموظف: ${staffName}\nالتاريخ: ${date}\nمبلغ الخصم: ${amount} ج.م\n\nهذا الإجراء سيؤثر على راتب الموظف.`)) {
                event.preventDefault();
                event.stopPropagation();
            }
        }
        form.classList.add('was-validated');
    });
});

// دالة الرجوع للحضور الذكي
function goBackToAttendance() {
    const urlParams = new URLSearchParams(window.location.search);
    const tab = urlParams.get('tab');
    const currentDate = new Date().toISOString().split('T')[0];

    let backUrl = 'smart_attendance.php?date=' + currentDate;

    if (tab === 'teachers') {
        backUrl = 'smart_attendance.php?tab=teachers&date=' + currentDate;
    } else if (tab === 'admins') {
        backUrl = 'smart_attendance.php?tab=admins&date=' + currentDate;
    }

    window.location.href = backUrl;
}
</script>

<?php require_once '../includes/footer.php'; ?>
