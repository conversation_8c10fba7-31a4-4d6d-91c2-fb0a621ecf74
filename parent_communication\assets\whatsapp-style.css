/* واجهة واتساب - تحسينات إضافية */

/* تحسين الألوان */
:root {
    --whatsapp-green: #00a884;
    --whatsapp-green-dark: #008069;
    --whatsapp-bg: #efeae2;
    --whatsapp-chat-bg: #f0f2f5;
    --message-sent: #d9fdd3;
    --message-received: #ffffff;
    --border-color: #e9ecef;
    --text-muted: #667781;
    --text-primary: #111b21;
}

/* تحسين الخطوط */
.whatsapp-container {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* تحسين الشريط الجانبي */
.chat-sidebar {
    background: var(--whatsapp-chat-bg);
    border-right: 1px solid var(--border-color);
}

.chat-header {
    background: var(--whatsapp-green);
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-green-dark) 100%);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* تحسين عناصر جهات الاتصال */
.contact-item {
    transition: all 0.2s ease;
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.contact-item:hover {
    background-color: #f5f6fa;
    transform: translateX(2px);
}

.contact-item.active {
    background-color: #e3f2fd;
    border-left: 3px solid var(--whatsapp-green);
}

.contact-avatar {
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-green-dark) 100%);
    box-shadow: 0 2px 8px rgba(0,168,132,0.3);
    transition: transform 0.2s ease;
}

.contact-item:hover .contact-avatar {
    transform: scale(1.05);
}

.contact-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 16px;
}

.contact-last-message {
    color: var(--text-muted);
    font-size: 14px;
    line-height: 1.3;
}

.contact-time {
    color: var(--text-muted);
    font-size: 12px;
    font-weight: 500;
}

.message-count {
    background: var(--whatsapp-green);
    color: white;
    border-radius: 50%;
    font-size: 11px;
    font-weight: 600;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

/* تحسين منطقة المحادثة */
.chat-main {
    background: var(--whatsapp-bg);
}

.chat-main-header {
    background: white;
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.chat-messages {
    background-image: 
        radial-gradient(circle at 20% 50%, rgba(120, 119, 116, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(120, 119, 116, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(120, 119, 116, 0.3) 0%, transparent 50%);
    background-size: 100px 100px, 80px 80px, 120px 120px;
    background-position: 0 0, 40px 60px, 130px 270px;
}

/* تحسين فقاعات الرسائل */
.message {
    margin-bottom: 12px;
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-bubble {
    max-width: 75%;
    padding: 10px 14px;
    border-radius: 12px;
    position: relative;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    word-wrap: break-word;
}

.message.sent .message-bubble {
    background: var(--message-sent);
    border-bottom-right-radius: 4px;
    margin-left: auto;
}

.message.received .message-bubble {
    background: var(--message-received);
    border-bottom-left-radius: 4px;
}

.message-content {
    margin-bottom: 6px;
    line-height: 1.4;
    color: var(--text-primary);
}

.message-time {
    font-size: 11px;
    color: var(--text-muted);
    text-align: right;
    margin-top: 4px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 4px;
}

.message-status {
    display: inline-flex;
    align-items: center;
}

.status-icon {
    font-size: 12px;
    color: #53bdeb;
}

.status-icon.delivered {
    color: #53bdeb;
}

.status-icon.read {
    color: #53bdeb;
}

.status-icon.failed {
    color: #f15c6d;
}

/* تحسين منطقة الإدخال */
.chat-input {
    background: white;
    border-top: 1px solid var(--border-color);
    padding: 12px 20px;
    box-shadow: 0 -1px 3px rgba(0,0,0,0.1);
}

.chat-input .form-control {
    border-radius: 20px;
    border: 1px solid var(--border-color);
    padding: 10px 16px;
    transition: all 0.2s ease;
}

.chat-input .form-control:focus {
    border-color: var(--whatsapp-green);
    box-shadow: 0 0 0 0.2rem rgba(0, 168, 132, 0.25);
}

.chat-input .btn {
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.chat-input .btn:hover {
    transform: scale(1.05);
}

/* تحسين مربع البحث */
.search-box .form-control {
    border-radius: 20px;
    border: none;
    background: #f0f2f5;
    padding: 10px 16px;
    transition: all 0.2s ease;
}

.search-box .form-control:focus {
    background: white;
    box-shadow: 0 0 0 0.2rem rgba(0, 168, 132, 0.25);
}

.search-box .input-group-text {
    border-radius: 20px 0 0 20px;
    border: none;
    background: #f0f2f5;
}

/* تحسين الحالة الفارغة */
.empty-chat {
    background: var(--whatsapp-bg);
    color: var(--text-muted);
}

.empty-chat i {
    color: var(--whatsapp-green);
    opacity: 0.3;
    margin-bottom: 20px;
}

/* تحسين التمرير */
.chat-sidebar::-webkit-scrollbar,
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-sidebar::-webkit-scrollbar-track,
.chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chat-sidebar::-webkit-scrollbar-thumb,
.chat-messages::-webkit-scrollbar-thumb {
    background: rgba(0,0,0,0.2);
    border-radius: 3px;
}

.chat-sidebar::-webkit-scrollbar-thumb:hover,
.chat-messages::-webkit-scrollbar-thumb:hover {
    background: rgba(0,0,0,0.3);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .whatsapp-container {
        height: calc(100vh - 80px);
    }
    
    .contact-item {
        padding: 10px 12px;
    }
    
    .contact-avatar {
        width: 45px;
        height: 45px;
        font-size: 16px;
    }
    
    .message-bubble {
        max-width: 85%;
        padding: 8px 12px;
    }
    
    .chat-input {
        padding: 10px 15px;
    }
}

/* تأثيرات إضافية */
.contact-item {
    position: relative;
    overflow: hidden;
}

.contact-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s;
}

.contact-item:hover::before {
    left: 100%;
}

/* تحسين الأزرار */
.btn-success {
    background: var(--whatsapp-green);
    border-color: var(--whatsapp-green);
}

.btn-success:hover {
    background: var(--whatsapp-green-dark);
    border-color: var(--whatsapp-green-dark);
}

/* تحسين الشارات */
.badge {
    font-size: 11px;
    font-weight: 500;
}

/* تحسين القوائم المنسدلة */
.dropdown-menu {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border: none;
}

.dropdown-item {
    padding: 8px 16px;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    transform: translateX(2px);
}

/* تحسين الأيقونات */
.fab.fa-whatsapp {
    color: var(--whatsapp-green);
}

/* تحسين النصوص */
.text-muted {
    color: var(--text-muted) !important;
}

/* تحسين الحدود */
.border {
    border-color: var(--border-color) !important;
}
