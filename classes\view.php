<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

$page_title = __('view_class');
require_once '../includes/header.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('teacher') && !check_permission('staff') && !has_permission('teacher_access')) {
    header('Location: ../dashboard/');
    exit();
}

$class_id = intval($_GET['id'] ?? 0);
if (!$class_id) {
    $_SESSION['error_message'] = __('invalid_class_id');
    header('Location: index.php');
    exit();
}

// جلب بيانات الفصل
$class = null;
global $conn;
$stmt = $conn->prepare("SELECT * FROM classes WHERE id = ?");
$stmt->bind_param("i", $class_id);
$stmt->execute();
$result = $stmt->get_result();
if ($result->num_rows === 0) {
    $_SESSION['error_message'] = __('class_not_found');
    header('Location: index.php');
    exit();
}
$class = $result->fetch_assoc();

// جلب المواد المرتبطة بالفصل
$subjects_query = "
    SELECT DISTINCT
        s.id,
        s.subject_name,
        s.subject_code,
        COALESCE(g.grade_name, 'غير محدد') as grade_name,
        COALESCE(es.stage_name, 'غير محدد') as stage_name,
        COUNT(DISTINCT ta.teacher_id) as teachers_count,
        SUM(ta.weekly_hours) as total_hours
    FROM teacher_assignments ta
    JOIN subjects s ON ta.subject_id = s.id
    LEFT JOIN grades g ON s.grade_id = g.id
    LEFT JOIN educational_stages es ON s.stage_id = es.id
    WHERE ta.class_id = ? AND ta.status = 'active'
    GROUP BY s.id, s.subject_name, s.subject_code, g.grade_name, es.stage_name
    ORDER BY s.subject_name
";

$subjects_stmt = $conn->prepare($subjects_query);
$subjects_stmt->bind_param('i', $class_id);
$subjects_stmt->execute();
$subjects_result = $subjects_stmt->get_result();
$related_subjects = [];
while ($subject = $subjects_result->fetch_assoc()) {
    $related_subjects[] = $subject;
}
$subjects_stmt->close();

// جلب المعلمين المرتبطين بالفصل
$teachers_query = "
    SELECT DISTINCT
        t.id,
        u.full_name,
        COALESCE(u.email, 'غير محدد') as email,
        COALESCE(u.phone, 'غير محدد') as phone,
        COUNT(DISTINCT ta.subject_id) as subjects_taught,
        SUM(ta.weekly_hours) as total_hours
    FROM teacher_assignments ta
    JOIN teachers t ON ta.teacher_id = t.id
    JOIN users u ON t.user_id = u.id
    WHERE ta.class_id = ? AND ta.status = 'active'
    GROUP BY t.id, u.full_name, u.email, u.phone
    ORDER BY u.full_name
";

$teachers_stmt = $conn->prepare($teachers_query);
$teachers_stmt->bind_param('i', $class_id);
$teachers_stmt->execute();
$teachers_result = $teachers_stmt->get_result();
$related_teachers = [];
while ($teacher = $teachers_result->fetch_assoc()) {
    $related_teachers[] = $teacher;
}
$teachers_stmt->close();

// جلب الطلاب المرتبطين بالفصل
$students_query = "
    SELECT
        s.id,
        u.full_name,
        s.student_id,
        COALESCE(u.email, 'غير محدد') as email,
        COALESCE(u.phone, 'غير محدد') as phone,
        s.status
    FROM students s
    JOIN users u ON s.user_id = u.id
    WHERE s.class_id = ?
    ORDER BY u.full_name
";

$students_stmt = $conn->prepare($students_query);
$students_stmt->bind_param('i', $class_id);
$students_stmt->execute();
$students_result = $students_stmt->get_result();
$related_students = [];
while ($student = $students_result->fetch_assoc()) {
    $related_students[] = $student;
}
$students_stmt->close();

// تسجيل للتشخيص
error_log("=== CLASS VIEW DEBUG ===");
error_log("Class ID: $class_id");
error_log("Found subjects: " . count($related_subjects));
error_log("Found teachers: " . count($related_teachers));
error_log("Found students: " . count($related_students));
error_log("=== END CLASS VIEW DEBUG ===");

// حذف الفصل
if (isset($_POST['delete_class']) && check_permission('admin')) {
    // تحقق من عدم وجود طلاب في الفصل
    $check_stmt = $conn->prepare("SELECT COUNT(*) as count FROM students WHERE class_id = ?");
    $check_stmt->bind_param("i", $class_id);
    $check_stmt->execute();
    $student_count = $check_stmt->get_result()->fetch_assoc()['count'];
    if ($student_count > 0) {
        $_SESSION['error_message'] = __('cannot_delete_class_with_students');
        header('Location: view.php?id=' . $class_id);
        exit();
    }
    $stmt = $conn->prepare("DELETE FROM classes WHERE id = ?");
    $stmt->bind_param("i", $class_id);
    if ($stmt->execute()) {
        $_SESSION['success_message'] = __('deleted_successfully');
        header('Location: index.php');
        exit();
    } else {
        $_SESSION['error_message'] = __('error_occurred');
        header('Location: view.php?id=' . $class_id);
        exit();
    }
}
?>
<link rel="stylesheet" href="../assets/css/modern-style.css">
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('view_class'); ?></h1>
            <p class="text-muted"><?php echo htmlspecialchars($class['class_name']); ?></p>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
            </a>
            <?php if (check_permission('admin')): ?>
            <a href="edit.php?id=<?php echo $class_id; ?>" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i><?php echo __('edit'); ?>
            </a>
            <form method="POST" style="display:inline;" onsubmit="return confirm('<?php echo __('are_you_sure_delete_class'); ?>');">
                <button type="submit" name="delete_class" class="btn btn-danger">
                    <i class="fas fa-trash me-2"></i><?php echo __('delete'); ?>
                </button>
            </form>
            <?php endif; ?>
        </div>
    </div>
    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php elseif (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-school me-2"></i><?php echo __('class_information'); ?>
            </h5>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-6">
                    <strong><?php echo __('class_name'); ?>:</strong> <?php echo htmlspecialchars($class['class_name']); ?>
                </div>
                <div class="col-md-6">
                    <strong><?php echo __('grade_level'); ?>:</strong> <?php echo htmlspecialchars($class['grade_level']); ?>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-md-6">
                    <strong><?php echo __('capacity'); ?>:</strong> <?php echo htmlspecialchars($class['capacity']); ?>
                </div>
                <div class="col-md-6">
                    <strong><?php echo __('status'); ?>:</strong> <?php echo ($class['status'] == 'active') ? __('active') : __('inactive'); ?>
                </div>
            </div>
            <?php if (isset($class['description']) && !empty($class['description'])): ?>
                <div class="mb-3">
                    <strong><?php echo __('description'); ?>:</strong><br>
                    <span><?php echo nl2br(htmlspecialchars($class['description'])); ?></span>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- المواد المرتبطة بالفصل -->
    <div class="card mt-4">
        <div class="card-header bg-success text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-book me-2"></i>المواد المرتبطة بالفصل
                <span class="badge bg-light text-success ms-2"><?php echo count($related_subjects); ?></span>
            </h5>
        </div>
        <div class="card-body">
            <?php if (!empty($related_subjects)): ?>
                <div class="row">
                    <?php foreach ($related_subjects as $subject): ?>
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="card h-100 border-success">
                                <div class="card-body">
                                    <h6 class="card-title text-success">
                                        <i class="fas fa-book me-2"></i>
                                        <?php echo htmlspecialchars($subject['subject_name']); ?>
                                    </h6>
                                    <?php if (!empty($subject['subject_code'])): ?>
                                        <p class="card-text">
                                            <small class="text-muted">
                                                <i class="fas fa-code me-1"></i>
                                                <?php echo htmlspecialchars($subject['subject_code']); ?>
                                            </small>
                                        </p>
                                    <?php endif; ?>
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-layer-group me-1"></i>
                                            <?php echo htmlspecialchars($subject['stage_name']); ?> - <?php echo htmlspecialchars($subject['grade_name']); ?>
                                        </small>
                                    </div>
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <small class="text-primary">
                                                <i class="fas fa-chalkboard-teacher me-1"></i>
                                                <?php echo $subject['teachers_count']; ?> معلم
                                            </small>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-warning">
                                                <i class="fas fa-clock me-1"></i>
                                                <?php echo $subject['total_hours']; ?> ساعة
                                            </small>
                                        </div>
                                    </div>
                                    <div class="text-center mt-2">
                                        <a href="../subjects/view.php?id=<?php echo $subject['id']; ?>"
                                           class="btn btn-sm btn-outline-success">
                                            <i class="fas fa-eye me-1"></i>عرض التفاصيل
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-book fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد مواد مرتبطة بهذا الفصل حالياً</p>
                    <?php if (check_permission('admin')): ?>
                        <a href="edit.php?id=<?php echo $class_id; ?>" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>ربط مواد بالفصل
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- المعلمين المختصين بالفصل -->
    <div class="card mt-4">
        <div class="card-header bg-warning text-dark">
            <h5 class="card-title mb-0">
                <i class="fas fa-chalkboard-teacher me-2"></i>المعلمين المختصين بالفصل
                <span class="badge bg-dark ms-2"><?php echo count($related_teachers); ?></span>
            </h5>
        </div>
        <div class="card-body">
            <?php if (!empty($related_teachers)): ?>
                <div class="row">
                    <?php foreach ($related_teachers as $teacher): ?>
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="card h-100 border-warning">
                                <div class="card-body">
                                    <h6 class="card-title text-warning">
                                        <i class="fas fa-user-tie me-2"></i>
                                        <?php echo htmlspecialchars($teacher['full_name']); ?>
                                    </h6>
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-envelope me-1"></i>
                                            <?php echo htmlspecialchars($teacher['email']); ?>
                                        </small>
                                    </div>
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-phone me-1"></i>
                                            <?php echo htmlspecialchars($teacher['phone']); ?>
                                        </small>
                                    </div>
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <small class="text-success">
                                                <i class="fas fa-book me-1"></i>
                                                <?php echo $teacher['subjects_taught']; ?> مادة
                                            </small>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-primary">
                                                <i class="fas fa-clock me-1"></i>
                                                <?php echo $teacher['total_hours']; ?> ساعة
                                            </small>
                                        </div>
                                    </div>
                                    <div class="text-center mt-2">
                                        <a href="../teachers/view.php?id=<?php echo $teacher['id']; ?>"
                                           class="btn btn-sm btn-outline-warning">
                                            <i class="fas fa-eye me-1"></i>عرض التفاصيل
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-chalkboard-teacher fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا يوجد معلمين مختصين بهذا الفصل حالياً</p>
                    <?php if (check_permission('admin')): ?>
                        <a href="edit.php?id=<?php echo $class_id; ?>" class="btn btn-warning">
                            <i class="fas fa-plus me-2"></i>تعيين معلمين للفصل
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- جدول الطلاب المسجلين في الفصل -->
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-users me-2"></i><?php echo __('students'); ?>
            </h5>
        </div>
        <div class="card-body">
            <?php
            // جلب الطلاب المسجلين في هذا الفصل
            $students = [];
            $stmt = $conn->prepare("SELECT s.id, u.full_name, u.email, u.status, s.student_id FROM students s JOIN users u ON s.user_id = u.id WHERE s.class_id = ? ORDER BY u.full_name ASC");
            $stmt->bind_param("i", $class_id);
            $stmt->execute();
            $result = $stmt->get_result();
            while ($row = $result->fetch_assoc()) {
                $students[] = $row;
            }
            ?>
            <?php if (count($students) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-striped table-bordered align-middle">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th><?php echo __('student_id'); ?></th>
                                <th><?php echo __('full_name'); ?></th>
                                <th><?php echo __('email'); ?></th>
                                <th><?php echo __('status'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($students as $i => $student): ?>
                                <tr>
                                    <td><?php echo $i + 1; ?></td>
                                    <td><?php echo htmlspecialchars($student['student_id']); ?></td>
                                    <td><?php echo htmlspecialchars($student['full_name']); ?></td>
                                    <td><?php echo htmlspecialchars($student['email']); ?></td>
                                    <td><?php echo ($student['status'] == 'active') ? __('active') : __('inactive'); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="alert alert-info text-center mb-0">
                    <i class="fas fa-info-circle me-2"></i><?php echo __('no_students_found'); ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php require_once '../includes/footer.php'; ?> 