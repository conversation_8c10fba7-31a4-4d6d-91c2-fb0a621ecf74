<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('teacher')) {
    header('Location: ../dashboard/');
    exit();
}

global $conn;

// تحديد الفترة الزمنية
$date_from = isset($_GET['date_from']) ? clean_input($_GET['date_from']) : date('Y-m-01');
$date_to = isset($_GET['date_to']) ? clean_input($_GET['date_to']) : date('Y-m-d');
$message_type = isset($_GET['message_type']) ? clean_input($_GET['message_type']) : '';
$status = isset($_GET['status']) ? clean_input($_GET['status']) : '';

// إحصائيات عامة
$stats_query = "
    SELECT
        COUNT(*) as total_messages,
        COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent_messages,
        COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered_messages,
        COUNT(CASE WHEN status = 'read' THEN 1 END) as read_messages,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_messages,
        COUNT(CASE WHEN message_type = 'behavior' THEN 1 END) as behavior_messages,
        COUNT(CASE WHEN message_type = 'academic' THEN 1 END) as academic_messages,
        COUNT(CASE WHEN message_type = 'attendance' THEN 1 END) as attendance_messages,
        COUNT(CASE WHEN message_type = 'emergency' THEN 1 END) as emergency_messages
    FROM parent_communications
    WHERE created_at BETWEEN ? AND ?
";

$date_from_full = $date_from . ' 00:00:00';
$date_to_full = $date_to . ' 23:59:59';

if (!empty($message_type) && !empty($status)) {
    $stats_query .= " AND message_type = ? AND status = ?";
    $stmt = $conn->prepare($stats_query);
    $stmt->bind_param('ssss', $date_from_full, $date_to_full, $message_type, $status);
} elseif (!empty($message_type)) {
    $stats_query .= " AND message_type = ?";
    $stmt = $conn->prepare($stats_query);
    $stmt->bind_param('sss', $date_from_full, $date_to_full, $message_type);
} elseif (!empty($status)) {
    $stats_query .= " AND status = ?";
    $stmt = $conn->prepare($stats_query);
    $stmt->bind_param('sss', $date_from_full, $date_to_full, $status);
} else {
    $stmt = $conn->prepare($stats_query);
    $stmt->bind_param('ss', $date_from_full, $date_to_full);
}

$stmt->execute();
$stats = $stmt->get_result()->fetch_assoc();
$stmt->close();

// التأكد من وجود البيانات
if (!$stats) {
    $stats = [
        'total_messages' => 0,
        'sent_messages' => 0,
        'delivered_messages' => 0,
        'read_messages' => 0,
        'failed_messages' => 0,
        'behavior_messages' => 0,
        'academic_messages' => 0,
        'attendance_messages' => 0,
        'emergency_messages' => 0
    ];
}

// إحصائيات يومية
$daily_stats_query = "
    SELECT
        DATE(created_at) as message_date,
        COUNT(*) as total_count,
        COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent_count,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count
    FROM parent_communications
    WHERE created_at BETWEEN ? AND ?
    GROUP BY DATE(created_at)
    ORDER BY message_date DESC
    LIMIT 30
";

$stmt = $conn->prepare($daily_stats_query);
$stmt->bind_param('ss', $date_from_full, $date_to_full);
$stmt->execute();
$daily_stats_result = $stmt->get_result();
$daily_stats = [];
if ($daily_stats_result) {
    while ($row = $daily_stats_result->fetch_assoc()) {
        $daily_stats[] = $row;
    }
}
$stmt->close();

// أكثر المرسلين نشاطاً
$top_senders_query = "
    SELECT
        u.full_name as sender_name,
        COUNT(*) as message_count,
        COUNT(CASE WHEN pc.status = 'sent' THEN 1 END) as sent_count,
        COUNT(CASE WHEN pc.status = 'failed' THEN 1 END) as failed_count
    FROM parent_communications pc
    JOIN users u ON pc.sent_by = u.id
    WHERE pc.created_at BETWEEN ? AND ?
    GROUP BY pc.sent_by, u.full_name
    ORDER BY message_count DESC
    LIMIT 10
";

$stmt = $conn->prepare($top_senders_query);
$stmt->bind_param('ss', $date_from_full, $date_to_full);
$stmt->execute();
$top_senders_result = $stmt->get_result();
$top_senders = [];
if ($top_senders_result) {
    while ($row = $top_senders_result->fetch_assoc()) {
        $top_senders[] = $row;
    }
}
$stmt->close();

$page_title = __('communication_reports');
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-chart-line me-2"></i>
            <?php echo __('communication_reports'); ?>
        </h2>
        <div class="btn-group">
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back'); ?>
            </a>
            <button type="button" class="btn btn-primary" onclick="window.print()">
                <i class="fas fa-print me-2"></i>طباعة التقرير
            </button>
        </div>
    </div>

    <!-- فلاتر التقرير -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-filter me-2"></i>فلاتر التقرير
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" 
                           value="<?php echo $date_from; ?>">
                </div>
                <div class="col-md-3">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" 
                           value="<?php echo $date_to; ?>">
                </div>
                <div class="col-md-3">
                    <label for="message_type" class="form-label">نوع الرسالة</label>
                    <select class="form-select" id="message_type" name="message_type">
                        <option value="">جميع الأنواع</option>
                        <option value="general" <?php echo $message_type === 'general' ? 'selected' : ''; ?>>عام</option>
                        <option value="behavior" <?php echo $message_type === 'behavior' ? 'selected' : ''; ?>>سلوك</option>
                        <option value="academic" <?php echo $message_type === 'academic' ? 'selected' : ''; ?>>أكاديمي</option>
                        <option value="attendance" <?php echo $message_type === 'attendance' ? 'selected' : ''; ?>>حضور</option>
                        <option value="emergency" <?php echo $message_type === 'emergency' ? 'selected' : ''; ?>>طارئ</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="pending" <?php echo $status === 'pending' ? 'selected' : ''; ?>>في الانتظار</option>
                        <option value="sent" <?php echo $status === 'sent' ? 'selected' : ''; ?>>مرسل</option>
                        <option value="delivered" <?php echo $status === 'delivered' ? 'selected' : ''; ?>>تم التسليم</option>
                        <option value="read" <?php echo $status === 'read' ? 'selected' : ''; ?>>مقروء</option>
                        <option value="failed" <?php echo $status === 'failed' ? 'selected' : ''; ?>>فشل</option>
                    </select>
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>تطبيق الفلاتر
                    </button>
                    <a href="communication_reports.php" class="btn btn-secondary">
                        <i class="fas fa-undo me-2"></i>إعادة تعيين
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- الإحصائيات العامة -->
    <div class="row mb-4">
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-envelope fa-2x mb-2"></i>
                    <h4 class="mb-0"><?php echo number_format($stats['total_messages']); ?></h4>
                    <small>إجمالي الرسائل</small>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-check fa-2x mb-2"></i>
                    <h4 class="mb-0"><?php echo number_format($stats['sent_messages']); ?></h4>
                    <small>مرسلة</small>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-check-double fa-2x mb-2"></i>
                    <h4 class="mb-0"><?php echo number_format($stats['delivered_messages']); ?></h4>
                    <small>تم التسليم</small>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-eye fa-2x mb-2"></i>
                    <h4 class="mb-0"><?php echo number_format($stats['read_messages']); ?></h4>
                    <small>مقروءة</small>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-danger text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-times fa-2x mb-2"></i>
                    <h4 class="mb-0"><?php echo number_format($stats['failed_messages']); ?></h4>
                    <small>فشلت</small>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-secondary text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-percentage fa-2x mb-2"></i>
                    <h4 class="mb-0">
                        <?php 
                        $success_rate = $stats['total_messages'] > 0 ? 
                            round(($stats['sent_messages'] / $stats['total_messages']) * 100, 1) : 0;
                        echo $success_rate . '%';
                        ?>
                    </h4>
                    <small>معدل النجاح</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- الإحصائيات اليومية -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>الإحصائيات اليومية
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($daily_stats)): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>إجمالي الرسائل</th>
                                        <th>مرسلة</th>
                                        <th>فشلت</th>
                                        <th>معدل النجاح</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($daily_stats as $day): ?>
                                        <tr>
                                            <td><?php echo date('Y-m-d', strtotime($day['message_date'])); ?></td>
                                            <td>
                                                <span class="badge bg-primary"><?php echo $day['total_count']; ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-success"><?php echo $day['sent_count']; ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-danger"><?php echo $day['failed_count']; ?></span>
                                            </td>
                                            <td>
                                                <?php 
                                                $daily_success_rate = $day['total_count'] > 0 ? 
                                                    round(($day['sent_count'] / $day['total_count']) * 100, 1) : 0;
                                                ?>
                                                <span class="badge bg-<?php echo $daily_success_rate >= 90 ? 'success' : ($daily_success_rate >= 70 ? 'warning' : 'danger'); ?>">
                                                    <?php echo $daily_success_rate; ?>%
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد بيانات للفترة المحددة</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- أكثر المرسلين نشاطاً -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-friends me-2"></i>أكثر المرسلين نشاطاً
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($top_senders)): ?>
                        <?php foreach ($top_senders as $sender): ?>
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div>
                                    <strong><?php echo safe_html($sender['sender_name']); ?></strong>
                                    <br><small class="text-muted">
                                        <?php echo $sender['sent_count']; ?> مرسلة، 
                                        <?php echo $sender['failed_count']; ?> فشلت
                                    </small>
                                </div>
                                <span class="badge bg-primary fs-6">
                                    <?php echo $sender['message_count']; ?>
                                </span>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-user-friends fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد بيانات</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات أنواع الرسائل -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-pie-chart me-2"></i>توزيع أنواع الرسائل
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3 mb-3">
                            <div class="border rounded p-3">
                                <i class="fas fa-comment fa-2x text-secondary mb-2"></i>
                                <h4><?php echo number_format($stats['total_messages'] - $stats['behavior_messages'] - $stats['academic_messages'] - $stats['attendance_messages'] - $stats['emergency_messages']); ?></h4>
                                <small class="text-muted">رسائل عامة</small>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="border rounded p-3">
                                <i class="fas fa-clipboard-list fa-2x text-warning mb-2"></i>
                                <h4><?php echo number_format($stats['behavior_messages']); ?></h4>
                                <small class="text-muted">تقارير سلوك</small>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="border rounded p-3">
                                <i class="fas fa-graduation-cap fa-2x text-info mb-2"></i>
                                <h4><?php echo number_format($stats['academic_messages']); ?></h4>
                                <small class="text-muted">أكاديمية</small>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="border rounded p-3">
                                <i class="fas fa-calendar-check fa-2x text-primary mb-2"></i>
                                <h4><?php echo number_format($stats['attendance_messages']); ?></h4>
                                <small class="text-muted">حضور وغياب</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .btn, .card-header, .navbar, .sidebar {
        display: none !important;
    }
    
    .container-fluid {
        margin: 0;
        padding: 0;
    }
    
    .card {
        border: none;
        box-shadow: none;
    }
    
    .card-body {
        padding: 0;
    }
}
</style>

<?php require_once '../includes/footer.php'; ?>
