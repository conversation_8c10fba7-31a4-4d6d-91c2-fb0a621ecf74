<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';
require_once 'WhatsAppService.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('teacher')) {
    header('Location: ../dashboard/');
    exit();
}

global $conn;

// معالجة إرسال الرسالة الجماعية
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $recipient_type = clean_input($_POST['recipient_type'] ?? '');
    $class_ids = isset($_POST['class_ids']) ? $_POST['class_ids'] : [];
    $grade_ids = isset($_POST['grade_ids']) ? $_POST['grade_ids'] : [];
    $message_type = clean_input($_POST['message_type'] ?? 'general');
    $subject = clean_input($_POST['subject'] ?? '');
    $message = clean_input($_POST['message'] ?? '');
    $priority = clean_input($_POST['priority'] ?? 'medium');
    $send_via = clean_input($_POST['send_via'] ?? 'whatsapp');
    
    $errors = [];
    
    // التحقق من البيانات
    if (empty($recipient_type)) {
        $errors[] = 'يرجى اختيار نوع المستقبلين';
    }
    if ($recipient_type === 'by_class' && empty($class_ids)) {
        $errors[] = 'يرجى اختيار فصل واحد على الأقل';
    }
    if ($recipient_type === 'by_grade' && empty($grade_ids)) {
        $errors[] = 'يرجى اختيار صف واحد على الأقل';
    }
    if (empty($subject)) {
        $errors[] = __('message_subject') . ' ' . __('required_field');
    }
    if (empty($message)) {
        $errors[] = __('message_content') . ' ' . __('required_field');
    }
    
    if (empty($errors)) {
        // بناء استعلام جلب الطلاب حسب نوع المستقبلين
        $students_query = "
            SELECT DISTINCT
                s.id,
                s.student_id as student_number,
                u.full_name as student_name,
                s.parent_phone,
                c.class_name
            FROM students s
            JOIN users u ON s.user_id = u.id
            LEFT JOIN classes c ON s.class_id = c.id
            WHERE s.status = 'active' 
            AND s.parent_phone IS NOT NULL 
            AND s.parent_phone != ''
        ";
        
        $params = [];
        $types = '';
        
        if ($recipient_type === 'by_class') {
            $placeholders = str_repeat('?,', count($class_ids) - 1) . '?';
            $students_query .= " AND s.class_id IN ($placeholders)";
            $params = $class_ids;
            $types = str_repeat('i', count($class_ids));
        } elseif ($recipient_type === 'by_grade') {
            $placeholders = str_repeat('?,', count($grade_ids) - 1) . '?';
            $students_query .= " AND c.grade_id IN ($placeholders)";
            $params = $grade_ids;
            $types = str_repeat('i', count($grade_ids));
        }
        
        $students_query .= " ORDER BY u.full_name";
        
        $stmt = $conn->prepare($students_query);
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        $stmt->execute();
        $students_result = $stmt->get_result();
        
        $recipients = [];
        while ($row = $students_result->fetch_assoc()) {
            $recipients[] = $row;
        }
        $stmt->close();
        
        if (!empty($recipients)) {
            $success_count = 0;
            $failed_count = 0;
            $whatsapp = new WhatsAppService($conn);
            
            foreach ($recipients as $recipient) {
                // إضافة اسم الطالب للرسالة
                $full_message = "السلام عليكم ولي أمر الطالب/ة " . $recipient['student_name'] . "\n\n" . $message . "\n\nمع تحيات إدارة المدرسة";
                
                // حفظ الرسالة في قاعدة البيانات
                $insert_query = "
                    INSERT INTO parent_communications 
                    (student_id, parent_phone, message_type, subject, message, priority, sent_via, status, sent_by, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', ?, NOW())
                ";
                
                $stmt = $conn->prepare($insert_query);
                $stmt->bind_param('issssssi', 
                    $recipient['id'], 
                    $recipient['parent_phone'], 
                    $message_type, 
                    $subject, 
                    $full_message, 
                    $priority, 
                    $send_via, 
                    $_SESSION['user_id']
                );
                
                if ($stmt->execute()) {
                    $message_id = $conn->insert_id;
                    $stmt->close();
                    
                    // إرسال الرسالة عبر واتساب
                    if ($send_via === 'whatsapp') {
                        $result = $whatsapp->sendMessage($recipient['parent_phone'], $full_message, $message_id);
                        
                        if ($result['success']) {
                            $success_count++;
                        } else {
                            $failed_count++;
                        }
                    } else {
                        $success_count++;
                    }
                } else {
                    $failed_count++;
                    $stmt->close();
                }
                
                // تأخير قصير لتجنب إرهاق الخادم
                usleep(500000); // 0.5 ثانية
            }
            
            $total_recipients = count($recipients);
            $_SESSION['success_message'] = "تم إرسال الرسائل إلى $success_count من أصل $total_recipients مستقبل";
            
            if ($failed_count > 0) {
                $_SESSION['success_message'] .= ". فشل في إرسال $failed_count رسالة";
            }
        } else {
            $errors[] = 'لم يتم العثور على مستقبلين مناسبين';
        }
    }
    
    if (!empty($errors)) {
        $_SESSION['error_message'] = implode('<br>', $errors);
    } else {
        header('Location: bulk_message.php');
        exit();
    }
}

// جلب قائمة الفصول
$classes_query = "
    SELECT 
        c.id,
        c.class_name,
        g.grade_name,
        es.stage_name,
        COUNT(s.id) as student_count,
        COUNT(CASE WHEN s.parent_phone IS NOT NULL AND s.parent_phone != '' THEN 1 END) as parents_with_phone
    FROM classes c
    LEFT JOIN grades g ON c.grade_id = g.id
    LEFT JOIN educational_stages es ON c.stage_id = es.id
    LEFT JOIN students s ON c.id = s.class_id AND s.status = 'active'
    GROUP BY c.id, c.class_name, g.grade_name, es.stage_name
    ORDER BY es.sort_order, g.sort_order, c.class_name
";

$classes_result = $conn->query($classes_query);
$classes = [];
if ($classes_result) {
    while ($row = $classes_result->fetch_assoc()) {
        $classes[] = $row;
    }
}

// جلب قائمة الصفوف
$grades_query = "
    SELECT 
        g.id,
        g.grade_name,
        es.stage_name,
        COUNT(DISTINCT c.id) as classes_count,
        COUNT(s.id) as student_count,
        COUNT(CASE WHEN s.parent_phone IS NOT NULL AND s.parent_phone != '' THEN 1 END) as parents_with_phone
    FROM grades g
    LEFT JOIN educational_stages es ON g.stage_id = es.id
    LEFT JOIN classes c ON g.id = c.grade_id
    LEFT JOIN students s ON c.id = s.class_id AND s.status = 'active'
    GROUP BY g.id, g.grade_name, es.stage_name
    ORDER BY es.sort_order, g.sort_order
";

$grades_result = $conn->query($grades_query);
$grades = [];
if ($grades_result) {
    while ($row = $grades_result->fetch_assoc()) {
        $grades[] = $row;
    }
}

$page_title = __('bulk_message');
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-broadcast-tower me-2"></i>
            <?php echo __('bulk_message'); ?>
        </h2>
        <div class="btn-group">
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back'); ?>
            </a>
            <a href="send_message.php" class="btn btn-primary">
                <i class="fas fa-paper-plane me-2"></i><?php echo __('send_message'); ?>
            </a>
        </div>
    </div>

    <!-- رسائل النجاح والخطأ -->
    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php elseif (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- نموذج الرسالة الجماعية -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-envelope me-2"></i>إرسال رسالة جماعية
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <!-- اختيار نوع المستقبلين -->
                        <div class="mb-4">
                            <label class="form-label">
                                <i class="fas fa-users me-2"></i><?php echo __('recipients'); ?>
                                <span class="text-danger">*</span>
                            </label>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="recipient_type" 
                                               id="all_students" value="all_students" required>
                                        <label class="form-check-label" for="all_students">
                                            <strong><?php echo __('all_students'); ?></strong>
                                            <br><small class="text-muted">جميع أولياء الأمور</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="recipient_type" 
                                               id="by_class" value="by_class" required>
                                        <label class="form-check-label" for="by_class">
                                            <strong><?php echo __('by_class'); ?></strong>
                                            <br><small class="text-muted">حسب الفصول المحددة</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="recipient_type" 
                                               id="by_grade" value="by_grade" required>
                                        <label class="form-check-label" for="by_grade">
                                            <strong><?php echo __('by_grade'); ?></strong>
                                            <br><small class="text-muted">حسب الصفوف المحددة</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- اختيار الفصول -->
                        <div class="mb-3" id="class_selector" style="display: none;">
                            <label class="form-label">
                                <i class="fas fa-school me-2"></i><?php echo __('select_class'); ?>
                            </label>
                            <div class="row">
                                <?php foreach ($classes as $class): ?>
                                    <div class="col-md-6 col-lg-4 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="class_ids[]" 
                                                   value="<?php echo $class['id']; ?>" 
                                                   id="class_<?php echo $class['id']; ?>">
                                            <label class="form-check-label" for="class_<?php echo $class['id']; ?>">
                                                <strong><?php echo safe_html($class['class_name']); ?></strong>
                                                <?php if ($class['grade_name']): ?>
                                                    <br><small class="text-muted">
                                                        <?php echo safe_html($class['grade_name']); ?>
                                                        • <?php echo $class['parents_with_phone']; ?> ولي أمر
                                                    </small>
                                                <?php endif; ?>
                                            </label>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- اختيار الصفوف -->
                        <div class="mb-3" id="grade_selector" style="display: none;">
                            <label class="form-label">
                                <i class="fas fa-layer-group me-2"></i><?php echo __('select_grade'); ?>
                            </label>
                            <div class="row">
                                <?php foreach ($grades as $grade): ?>
                                    <div class="col-md-6 col-lg-4 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="grade_ids[]" 
                                                   value="<?php echo $grade['id']; ?>" 
                                                   id="grade_<?php echo $grade['id']; ?>">
                                            <label class="form-check-label" for="grade_<?php echo $grade['id']; ?>">
                                                <strong><?php echo safe_html($grade['grade_name']); ?></strong>
                                                <?php if ($grade['stage_name']): ?>
                                                    <br><small class="text-muted">
                                                        <?php echo safe_html($grade['stage_name']); ?>
                                                        • <?php echo $grade['parents_with_phone']; ?> ولي أمر
                                                    </small>
                                                <?php endif; ?>
                                            </label>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <div class="row">
                            <!-- نوع الرسالة -->
                            <div class="col-md-6 mb-3">
                                <label for="message_type" class="form-label">
                                    <i class="fas fa-tag me-2"></i><?php echo __('message_type'); ?>
                                </label>
                                <select class="form-select" id="message_type" name="message_type">
                                    <option value="general"><?php echo __('general'); ?></option>
                                    <option value="behavior"><?php echo __('student_behavior'); ?></option>
                                    <option value="academic"><?php echo __('academic'); ?></option>
                                    <option value="attendance"><?php echo __('attendance'); ?></option>
                                    <option value="emergency"><?php echo __('emergency'); ?></option>
                                </select>
                            </div>

                            <!-- الأولوية -->
                            <div class="col-md-6 mb-3">
                                <label for="priority" class="form-label">
                                    <i class="fas fa-exclamation-circle me-2"></i><?php echo __('message_priority'); ?>
                                </label>
                                <select class="form-select" id="priority" name="priority">
                                    <option value="low"><?php echo __('low'); ?></option>
                                    <option value="medium" selected><?php echo __('medium'); ?></option>
                                    <option value="high"><?php echo __('high'); ?></option>
                                    <option value="urgent"><?php echo __('urgent'); ?></option>
                                </select>
                            </div>
                        </div>

                        <!-- موضوع الرسالة -->
                        <div class="mb-3">
                            <label for="subject" class="form-label">
                                <i class="fas fa-heading me-2"></i><?php echo __('message_subject'); ?>
                                <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="subject" name="subject" 
                                   placeholder="أدخل موضوع الرسالة..." required>
                            <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                        </div>

                        <!-- محتوى الرسالة -->
                        <div class="mb-3">
                            <label for="message" class="form-label">
                                <i class="fas fa-comment me-2"></i><?php echo __('message_content'); ?>
                                <span class="text-danger">*</span>
                            </label>
                            <textarea class="form-control" id="message" name="message" rows="6" 
                                      placeholder="اكتب محتوى الرسالة هنا..." required></textarea>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                سيتم إضافة اسم كل طالب وتحية المدرسة تلقائياً لكل رسالة
                            </div>
                            <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                        </div>

                        <!-- طريقة الإرسال -->
                        <div class="mb-3">
                            <label for="send_via" class="form-label">
                                <i class="fas fa-share me-2"></i><?php echo __('send_via'); ?>
                            </label>
                            <select class="form-select" id="send_via" name="send_via">
                                <option value="whatsapp">
                                    <i class="fab fa-whatsapp"></i> <?php echo __('whatsapp'); ?>
                                </option>
                                <option value="sms"><?php echo __('sms'); ?></option>
                                <option value="email"><?php echo __('email'); ?></option>
                            </select>
                        </div>

                        <!-- تحذير -->
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>تنبيه:</strong> سيتم إرسال الرسالة إلى جميع أولياء الأمور المحددين. 
                            تأكد من محتوى الرسالة قبل الإرسال.
                        </div>

                        <!-- أزرار الإجراء -->
                        <div class="d-flex justify-content-end gap-2">
                            <button type="button" class="btn btn-secondary" onclick="window.history.back()">
                                <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                            </button>
                            <button type="submit" class="btn btn-primary" onclick="return confirm('هل أنت متأكد من إرسال هذه الرسالة الجماعية؟')">
                                <i class="fas fa-broadcast-tower me-2"></i>إرسال الرسالة الجماعية
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>معلومات الإرسال
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6>إحصائيات سريعة:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-school me-2 text-primary"></i>عدد الفصول: <?php echo count($classes); ?></li>
                            <li><i class="fas fa-layer-group me-2 text-success"></i>عدد الصفوف: <?php echo count($grades); ?></li>
                            <li><i class="fas fa-users me-2 text-info"></i>إجمالي أولياء الأمور: 
                                <?php
                                $total_parents = 0;
                                foreach ($classes as $class) {
                                    $total_parents += $class['parents_with_phone'];
                                }
                                echo $total_parents;
                                ?>
                            </li>
                        </ul>
                    </div>

                    <div class="mb-3">
                        <h6>نصائح للإرسال الجماعي:</h6>
                        <ul class="small">
                            <li>تأكد من وضوح ودقة محتوى الرسالة</li>
                            <li>استخدم لغة مهذبة ومناسبة</li>
                            <li>تجنب الإرسال في أوقات متأخرة</li>
                            <li>راجع قائمة المستقبلين قبل الإرسال</li>
                        </ul>
                    </div>

                    <div class="alert alert-info small">
                        <i class="fas fa-clock me-2"></i>
                        <strong>وقت الإرسال المتوقع:</strong> 
                        يتم إرسال الرسائل تدريجياً لتجنب إرهاق الخادم.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// إظهار/إخفاء خيارات المستقبلين
document.querySelectorAll('input[name="recipient_type"]').forEach(radio => {
    radio.addEventListener('change', function() {
        const classSelector = document.getElementById('class_selector');
        const gradeSelector = document.getElementById('grade_selector');
        
        // إخفاء جميع الخيارات
        classSelector.style.display = 'none';
        gradeSelector.style.display = 'none';
        
        // إظهار الخيار المناسب
        if (this.value === 'by_class') {
            classSelector.style.display = 'block';
        } else if (this.value === 'by_grade') {
            gradeSelector.style.display = 'block';
        }
    });
});

// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        Array.prototype.forEach.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?php require_once '../includes/footer.php'; ?>
