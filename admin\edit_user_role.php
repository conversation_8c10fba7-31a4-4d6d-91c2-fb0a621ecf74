<?php
/**
 * تعديل دور المستخدم - صفحة مستقلة
 * Edit User Role - Standalone Page
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

// التحقق من معرف المستخدم
if (!isset($_GET['user_id']) || !is_numeric($_GET['user_id'])) {
    header('Location: permissions_manager.php');
    exit();
}

$user_id = intval($_GET['user_id']);
$success_message = '';
$error_message = '';

// جلب بيانات المستخدم
$user_stmt = $conn->prepare("SELECT id, full_name, username, email, role, status FROM users WHERE id = ?");
$user_stmt->bind_param("i", $user_id);
$user_stmt->execute();
$user = $user_stmt->get_result()->fetch_assoc();

if (!$user) {
    header('Location: permissions_manager.php');
    exit();
}

// معالجة تحديث الدور
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_role'])) {
    $new_role = clean_input($_POST['role']);
    $notes = clean_input($_POST['notes'] ?? '');
    
    $valid_roles = ['admin', 'teacher', 'student', 'staff', 'parent', 'financial_manager', 'librarian', 'nurse', 'security', 'maintenance'];
    
    if (!empty($new_role) && in_array($new_role, $valid_roles)) {
        $conn->begin_transaction();
        try {
            // تحديث الدور
            $stmt = $conn->prepare("UPDATE users SET role = ? WHERE id = ?");
            $stmt->bind_param("si", $new_role, $user_id);
            $stmt->execute();
            
            // تسجيل التغيير في سجل المراجعة
            $audit_stmt = $conn->prepare("
                INSERT INTO permissions_audit_log 
                (user_id, action_type, old_value, new_value, changed_by, ip_address, notes) 
                VALUES (?, 'role_changed', ?, ?, ?, ?, ?)
            ");
            $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            $audit_stmt->bind_param("isssss", $user_id, $user['role'], $new_role, $_SESSION['user_id'], $ip, $notes);
            $audit_stmt->execute();
            
            $conn->commit();
            $success_message = "تم تحديث دور المستخدم بنجاح";
            
            // تحديث بيانات المستخدم المعروضة
            $user['role'] = $new_role;
            
        } catch (Exception $e) {
            $conn->rollback();
            $error_message = "خطأ في تحديث الدور: " . $e->getMessage();
        }
    } else {
        $error_message = "الدور المحدد غير صحيح";
    }
}

$page_title = 'تعديل دور المستخدم: ' . $user['full_name'];
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-user-edit me-2"></i><?php echo $page_title; ?></h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="../admin/">الإدارة</a></li>
                    <li class="breadcrumb-item"><a href="permissions_manager.php">إدارة الصلاحيات</a></li>
                    <li class="breadcrumb-item active">تعديل الدور</li>
                </ol>
            </nav>
        </div>
        <a href="permissions_manager.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
        </a>
    </div>

    <!-- رسائل التنبيه -->
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-user-cog me-2"></i>تعديل دور المستخدم</h5>
                </div>
                <div class="card-body">
                    <!-- معلومات المستخدم -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <div class="avatar-lg bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3">
                                        <i class="fas fa-user fa-2x"></i>
                                    </div>
                                    <h5><?php echo htmlspecialchars($user['full_name']); ?></h5>
                                    <p class="text-muted mb-2">@<?php echo htmlspecialchars($user['username']); ?></p>
                                    <p class="text-muted mb-2"><?php echo htmlspecialchars($user['email']); ?></p>
                                    <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                        <?php echo $user['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">الدور الحالي</h6>
                                    <div class="text-center">
                                        <span class="badge bg-primary fs-6 p-3">
                                            <?php 
                                            $role_names = [
                                                'admin' => 'مدير النظام',
                                                'financial_manager' => 'مدير مالي',
                                                'teacher' => 'معلم',
                                                'staff' => 'موظف',
                                                'student' => 'طالب',
                                                'parent' => 'ولي أمر',
                                                'librarian' => 'أمين مكتبة',
                                                'nurse' => 'ممرضة',
                                                'security' => 'أمن',
                                                'maintenance' => 'صيانة'
                                            ];
                                            echo $role_names[$user['role']] ?? $user['role'];
                                            ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- نموذج تعديل الدور -->
                    <form method="POST">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>تحذير:</strong> تغيير دور المستخدم سيؤثر على صلاحياته في النظام. 
                            تأكد من اختيار الدور المناسب.
                        </div>

                        <div class="mb-4">
                            <label class="form-label">الدور الجديد *</label>
                            <select class="form-select form-select-lg" name="role" required>
                                <option value="">اختر الدور الجديد</option>
                                <?php foreach ($role_names as $role_key => $role_name): ?>
                                    <option value="<?php echo $role_key; ?>" 
                                            <?php echo $user['role'] === $role_key ? 'selected' : ''; ?>>
                                        <?php echo $role_name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">
                                اختر الدور المناسب للمستخدم. سيحصل على الصلاحيات المرتبطة بهذا الدور.
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="form-label">ملاحظات التغيير</label>
                            <textarea class="form-control" name="notes" rows="3" 
                                      placeholder="اكتب سبب تغيير الدور (اختياري)"></textarea>
                            <div class="form-text">
                                هذه الملاحظات ستُسجل في سجل تغييرات الصلاحيات.
                            </div>
                        </div>

                        <!-- معاينة الصلاحيات -->
                        <div class="mb-4">
                            <h6>معاينة الصلاحيات الأساسية للأدوار:</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card border-success">
                                        <div class="card-header bg-success text-white">
                                            <small>مدير النظام</small>
                                        </div>
                                        <div class="card-body">
                                            <small>• وصول كامل لجميع أجزاء النظام<br>• إدارة المستخدمين والصلاحيات<br>• جميع التقارير والإعدادات</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-info">
                                        <div class="card-header bg-info text-white">
                                            <small>مدير مالي</small>
                                        </div>
                                        <div class="card-body">
                                            <small>• النظام المالي الكامل<br>• إدارة الرسوم والمصروفات<br>• التقارير المالية</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <div class="card border-primary">
                                        <div class="card-header bg-primary text-white">
                                            <small>معلم</small>
                                        </div>
                                        <div class="card-body">
                                            <small>• إدارة الطلاب والحضور<br>• الامتحانات والدرجات<br>• التواصل مع أولياء الأمور</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-secondary">
                                        <div class="card-header bg-secondary text-white">
                                            <small>موظف</small>
                                        </div>
                                        <div class="card-body">
                                            <small>• عرض بيانات الطلاب<br>• تسجيل الحضور<br>• التقارير الأساسية</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="permissions_manager.php" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" name="update_role" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- روابط سريعة -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6><i class="fas fa-link me-2"></i>إجراءات أخرى</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2 d-md-flex">
                        <a href="user_permissions_detail.php?user_id=<?php echo $user_id; ?>" class="btn btn-outline-success">
                            <i class="fas fa-key me-2"></i>إدارة الصلاحيات المخصصة
                        </a>
                        <a href="permissions_audit.php?search_user=<?php echo urlencode($user['username']); ?>" class="btn btn-outline-info">
                            <i class="fas fa-history me-2"></i>سجل تغييرات هذا المستخدم
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
