<?php
/**
 * إصلاح هيكل جداول الأقساط والمالية
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    die('غير مسموح لك بالوصول لهذه الصفحة');
}

echo "<h2>إصلاح هيكل جداول الأقساط والمالية</h2>";

try {
    $fixes_applied = [];
    
    // 1. التحقق من جدول student_installments وإصلاحه
    echo "<h3>1. فحص جدول student_installments:</h3>";
    
    $table_check = $conn->query("SHOW TABLES LIKE 'student_installments'");
    if ($table_check->num_rows == 0) {
        echo "<p>جدول student_installments غير موجود. سيتم إنشاؤه...</p>";
        
        $create_installments_sql = "
        CREATE TABLE `student_installments` (
            `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
            `student_id` int(10) UNSIGNED NOT NULL,
            `fee_type_id` int(10) UNSIGNED DEFAULT NULL,
            `installment_number` int(11) NOT NULL,
            `amount` decimal(10,2) NOT NULL,
            `due_date` date NOT NULL,
            `paid_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `payment_date` date DEFAULT NULL,
            `status` enum('pending','paid','overdue','cancelled') NOT NULL DEFAULT 'pending',
            `payment_method` enum('cash','bank_transfer','check','online') DEFAULT NULL,
            `bank_account_id` int(10) UNSIGNED DEFAULT NULL,
            `transaction_reference` varchar(255) DEFAULT NULL,
            `notes` text DEFAULT NULL,
            `created_by` int(10) UNSIGNED DEFAULT NULL,
            `updated_by` int(10) UNSIGNED DEFAULT NULL,
            `created_at` datetime DEFAULT current_timestamp(),
            `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_student_installment_fee` (`student_id`, `installment_number`, `fee_type_id`),
            KEY `idx_student_id` (`student_id`),
            KEY `idx_fee_type_id` (`fee_type_id`),
            KEY `idx_due_date` (`due_date`),
            KEY `idx_status` (`status`),
            KEY `idx_bank_account_id` (`bank_account_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='أقساط الطلاب'
        ";
        
        if ($conn->query($create_installments_sql)) {
            echo "<div class='alert alert-success'>✅ تم إنشاء جدول student_installments بنجاح</div>";
            $fixes_applied[] = "إنشاء جدول student_installments";
        }
    } else {
        echo "<p>جدول student_installments موجود. فحص الأعمدة...</p>";
        
        // فحص الأعمدة المطلوبة
        $columns_check = $conn->query("DESCRIBE student_installments");
        $existing_columns = [];
        while ($row = $columns_check->fetch_assoc()) {
            $existing_columns[] = $row['Field'];
        }
        
        $required_columns = [
            'fee_type_id' => "ADD COLUMN `fee_type_id` int(10) UNSIGNED DEFAULT NULL AFTER `student_id`",
            'bank_account_id' => "ADD COLUMN `bank_account_id` int(10) UNSIGNED DEFAULT NULL AFTER `payment_method`",
            'transaction_reference' => "ADD COLUMN `transaction_reference` varchar(255) DEFAULT NULL AFTER `bank_account_id`",
            'created_by' => "ADD COLUMN `created_by` int(10) UNSIGNED DEFAULT NULL AFTER `notes`",
            'updated_by' => "ADD COLUMN `updated_by` int(10) UNSIGNED DEFAULT NULL AFTER `created_by`"
        ];
        
        foreach ($required_columns as $column => $alter_sql) {
            if (!in_array($column, $existing_columns)) {
                echo "<p>إضافة العمود المفقود: $column</p>";
                if ($conn->query("ALTER TABLE student_installments $alter_sql")) {
                    echo "<div class='alert alert-success'>✅ تم إضافة العمود $column</div>";
                    $fixes_applied[] = "إضافة العمود $column";
                } else {
                    echo "<div class='alert alert-warning'>⚠️ فشل في إضافة العمود $column: " . $conn->error . "</div>";
                }
            } else {
                echo "<p>✅ العمود $column موجود</p>";
            }
        }
        
        // إضافة فهارس مفقودة
        $indexes_to_add = [
            "ADD INDEX `idx_fee_type_id` (`fee_type_id`)",
            "ADD INDEX `idx_bank_account_id` (`bank_account_id`)"
        ];
        
        foreach ($indexes_to_add as $index_sql) {
            try {
                $conn->query("ALTER TABLE student_installments $index_sql");
                echo "<p>✅ تم إضافة فهرس</p>";
            } catch (mysqli_sql_exception $e) {
                // تجاهل خطأ الفهرس الموجود
                if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                    echo "<p>⚠️ تحذير في الفهرس: " . $e->getMessage() . "</p>";
                }
            }
        }
    }
    
    // 2. التحقق من جدول fee_types
    echo "<h3>2. فحص جدول fee_types:</h3>";
    $fee_types_check = $conn->query("SHOW TABLES LIKE 'fee_types'");
    if ($fee_types_check->num_rows == 0) {
        echo "<p>جدول fee_types غير موجود. سيتم إنشاؤه...</p>";
        
        $create_fee_types_sql = "
        CREATE TABLE `fee_types` (
            `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
            `type_name` varchar(255) NOT NULL,
            `description` text DEFAULT NULL,
            `amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `is_recurring` tinyint(1) NOT NULL DEFAULT 0,
            `recurring_period` enum('monthly','quarterly','semester','yearly') DEFAULT NULL,
            `is_active` tinyint(1) NOT NULL DEFAULT 1,
            `created_at` datetime DEFAULT current_timestamp(),
            `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_type_name` (`type_name`),
            KEY `idx_is_active` (`is_active`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='أنواع الرسوم'
        ";
        
        if ($conn->query($create_fee_types_sql)) {
            echo "<div class='alert alert-success'>✅ تم إنشاء جدول fee_types بنجاح</div>";
            $fixes_applied[] = "إنشاء جدول fee_types";
            
            // إدراج أنواع رسوم تجريبية
            $sample_fee_types = [
                ['الرسوم الدراسية', 'الرسوم الدراسية الأساسية', 5000.00, 1, 'semester'],
                ['رسوم الكتب', 'رسوم الكتب والمواد التعليمية', 500.00, 1, 'yearly'],
                ['رسوم النشاطات', 'رسوم الأنشطة الطلابية', 300.00, 1, 'semester'],
                ['رسوم الامتحانات', 'رسوم الامتحانات النهائية', 200.00, 1, 'semester'],
                ['رسوم التأخير', 'رسوم التأخير في السداد', 100.00, 0, null]
            ];
            
            $insert_fee_sql = "INSERT INTO fee_types (type_name, description, amount, is_recurring, recurring_period) VALUES (?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($insert_fee_sql);
            
            foreach ($sample_fee_types as $fee) {
                $stmt->bind_param("ssdis", $fee[0], $fee[1], $fee[2], $fee[3], $fee[4]);
                $stmt->execute();
            }
            
            echo "<div class='alert alert-info'>✅ تم إدراج " . count($sample_fee_types) . " أنواع رسوم تجريبية</div>";
        }
    } else {
        echo "<p>✅ جدول fee_types موجود</p>";
        
        // التحقق من العمود type_name
        $columns_check = $conn->query("DESCRIBE fee_types");
        $existing_columns = [];
        while ($row = $columns_check->fetch_assoc()) {
            $existing_columns[] = $row['Field'];
        }
        
        if (!in_array('type_name', $existing_columns) && in_array('name', $existing_columns)) {
            echo "<p>تحديث اسم العمود من 'name' إلى 'type_name'...</p>";
            if ($conn->query("ALTER TABLE fee_types CHANGE `name` `type_name` varchar(255) NOT NULL")) {
                echo "<div class='alert alert-success'>✅ تم تحديث اسم العمود</div>";
                $fixes_applied[] = "تحديث اسم العمود في fee_types";
            }
        }
    }
    
    // 3. التحقق من جدول bank_accounts
    echo "<h3>3. فحص جدول bank_accounts:</h3>";
    $bank_accounts_check = $conn->query("SHOW TABLES LIKE 'bank_accounts'");
    if ($bank_accounts_check->num_rows == 0) {
        echo "<p>جدول bank_accounts غير موجود. سيتم إنشاؤه...</p>";
        
        $create_bank_accounts_sql = "
        CREATE TABLE `bank_accounts` (
            `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
            `account_name` varchar(255) NOT NULL,
            `account_number` varchar(100) NOT NULL,
            `bank_name` varchar(255) NOT NULL,
            `branch_name` varchar(255) DEFAULT NULL,
            `account_type` enum('current','savings','business') NOT NULL DEFAULT 'current',
            `currency` varchar(10) NOT NULL DEFAULT 'EGP',
            `balance` decimal(15,2) NOT NULL DEFAULT 0.00,
            `is_active` tinyint(1) NOT NULL DEFAULT 1,
            `notes` text DEFAULT NULL,
            `created_at` datetime DEFAULT current_timestamp(),
            `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_account_number` (`account_number`, `bank_name`),
            KEY `idx_account_name` (`account_name`),
            KEY `idx_is_active` (`is_active`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='الحسابات البنكية'
        ";
        
        if ($conn->query($create_bank_accounts_sql)) {
            echo "<div class='alert alert-success'>✅ تم إنشاء جدول bank_accounts بنجاح</div>";
            $fixes_applied[] = "إنشاء جدول bank_accounts";
            
            // إدراج حسابات بنكية تجريبية
            $sample_accounts = [
                ['الحساب الرئيسي', '*********', 'البنك الأهلي المصري', 'فرع المعادي', 'current', 'EGP', 50000.00],
                ['حساب الرسوم الدراسية', '*********', 'بنك مصر', 'فرع وسط البلد', 'business', 'EGP', 25000.00],
                ['حساب الطوارئ', '*********', 'بنك القاهرة', 'فرع النزهة', 'savings', 'EGP', 10000.00]
            ];
            
            $insert_account_sql = "INSERT INTO bank_accounts (account_name, account_number, bank_name, branch_name, account_type, currency, balance) VALUES (?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($insert_account_sql);
            
            foreach ($sample_accounts as $account) {
                $stmt->bind_param("ssssssd", $account[0], $account[1], $account[2], $account[3], $account[4], $account[5], $account[6]);
                $stmt->execute();
            }
            
            echo "<div class='alert alert-info'>✅ تم إدراج " . count($sample_accounts) . " حسابات بنكية تجريبية</div>";
        }
    } else {
        echo "<p>✅ جدول bank_accounts موجود</p>";
    }
    
    // 4. إنشاء جدول bank_account_fee_types إذا لم يكن موجوداً
    echo "<h3>4. فحص جدول bank_account_fee_types:</h3>";
    $bank_fee_check = $conn->query("SHOW TABLES LIKE 'bank_account_fee_types'");
    if ($bank_fee_check->num_rows == 0) {
        echo "<p>جدول bank_account_fee_types غير موجود. سيتم إنشاؤه...</p>";
        
        $create_bank_fee_sql = "
        CREATE TABLE `bank_account_fee_types` (
            `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
            `bank_account_id` int(10) UNSIGNED NOT NULL,
            `fee_type_id` int(10) UNSIGNED NOT NULL,
            `is_active` tinyint(1) NOT NULL DEFAULT 1,
            `created_at` datetime DEFAULT current_timestamp(),
            `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_bank_fee_type` (`bank_account_id`, `fee_type_id`),
            KEY `idx_bank_account_id` (`bank_account_id`),
            KEY `idx_fee_type_id` (`fee_type_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='ربط الحسابات البنكية بأنواع الرسوم'
        ";
        
        if ($conn->query($create_bank_fee_sql)) {
            echo "<div class='alert alert-success'>✅ تم إنشاء جدول bank_account_fee_types بنجاح</div>";
            $fixes_applied[] = "إنشاء جدول bank_account_fee_types";
        }
    } else {
        echo "<p>✅ جدول bank_account_fee_types موجود</p>";
    }
    
    // عرض ملخص الإصلاحات
    echo "<hr>";
    echo "<h3>ملخص الإصلاحات المطبقة:</h3>";
    if (!empty($fixes_applied)) {
        echo "<div class='alert alert-success'>";
        echo "<ul>";
        foreach ($fixes_applied as $fix) {
            echo "<li>✅ $fix</li>";
        }
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-info'>✅ جميع الجداول والأعمدة موجودة ولا تحتاج إصلاح</div>";
    }
    
    // عرض إحصائيات نهائية
    echo "<h3>إحصائيات الجداول:</h3>";
    $tables_stats = [
        'student_installments' => 'أقساط الطلاب',
        'fee_types' => 'أنواع الرسوم',
        'bank_accounts' => 'الحسابات البنكية',
        'bank_account_fee_types' => 'ربط الحسابات بالرسوم'
    ];
    
    foreach ($tables_stats as $table => $description) {
        $count_result = $conn->query("SELECT COUNT(*) as count FROM $table");
        if ($count_result) {
            $count = $count_result->fetch_assoc()['count'];
            echo "<p><strong>$description ($table):</strong> $count سجل</p>";
        }
    }
    
    echo "<hr>";
    echo "<div class='alert alert-success'>";
    echo "<h4>✅ تم إصلاح جميع مشاكل هيكل قاعدة البيانات!</h4>";
    echo "<p>يمكنك الآن الوصول لصفحات الأقساط والمالية بدون أخطاء.</p>";
    echo "</div>";
    
    echo "<div class='mt-3'>";
    echo "<a href='installments/add.php' class='btn btn-primary me-2'>اختبار إضافة قسط</a>";
    echo "<a href='installments/bank_accounts.php' class='btn btn-success me-2'>اختبار الحسابات البنكية</a>";
    echo "<a href='../dashboard/' class='btn btn-secondary'>العودة للوحة التحكم</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ حدث خطأ:</h4>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>
