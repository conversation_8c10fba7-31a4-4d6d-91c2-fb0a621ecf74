<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('teacher')) {
    header('Location: ../dashboard/');
    exit();
}

global $conn;



// معالجة حذف قالب
if (isset($_GET['delete']) && intval($_GET['delete']) > 0) {
    $template_id = intval($_GET['delete']);
    
    $delete_query = "DELETE FROM message_templates WHERE id = ?";
    $stmt = $conn->prepare($delete_query);
    $stmt->bind_param('i', $template_id);
    
    if ($stmt->execute()) {
        $_SESSION['success_message'] = 'تم حذف القالب بنجاح';
    } else {
        $_SESSION['error_message'] = 'فشل في حذف القالب';
    }
    $stmt->close();
    
    header('Location: message_templates.php');
    exit();
}

// معالجة تغيير حالة القالب
if (isset($_GET['toggle']) && intval($_GET['toggle']) > 0) {
    $template_id = intval($_GET['toggle']);
    
    $toggle_query = "UPDATE message_templates SET is_active = NOT is_active WHERE id = ?";
    $stmt = $conn->prepare($toggle_query);
    $stmt->bind_param('i', $template_id);
    
    if ($stmt->execute()) {
        $_SESSION['success_message'] = 'تم تغيير حالة القالب بنجاح';
    } else {
        $_SESSION['error_message'] = 'فشل في تغيير حالة القالب';
    }
    $stmt->close();
    
    header('Location: message_templates.php');
    exit();
}

// جلب القوالب
$templates_query = "
    SELECT 
        mt.*,
        u.full_name as creator_name
    FROM message_templates mt
    JOIN users u ON mt.created_by = u.id
    ORDER BY mt.created_at DESC
";

$templates_result = $conn->query($templates_query);
$templates = [];
if ($templates_result) {
    while ($row = $templates_result->fetch_assoc()) {
        $templates[] = $row;
    }
}



$page_title = __('message_templates');
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-file-alt me-2"></i>
            <?php echo __('message_templates'); ?>
        </h2>
        <div class="btn-group">
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back'); ?>
            </a>
            <a href="add_template.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة قالب جديد
            </a>
        </div>
    </div>

    <!-- رسائل النجاح والخطأ -->
    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php elseif (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- قائمة القوالب -->
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>قوالب الرسائل المحفوظة
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($templates)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>اسم القالب</th>
                                        <th>نوع الرسالة</th>
                                        <th>الموضوع</th>
                                        <th>المحتوى</th>
                                        <th>المتغيرات</th>
                                        <th>الحالة</th>
                                        <th>المنشئ</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($templates as $template): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo safe_html($template['template_name']); ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo match($template['message_type']) {
                                                        'behavior' => 'warning',
                                                        'academic' => 'info',
                                                        'attendance' => 'primary',
                                                        'emergency' => 'danger',
                                                        default => 'secondary'
                                                    };
                                                ?>">
                                                    <?php echo __($template['message_type']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo safe_html(mb_substr($template['subject'], 0, 30)) . '...'; ?></td>
                                            <td>
                                                <small class="text-muted">
                                                    <?php echo safe_html(mb_substr($template['message_content'], 0, 50)) . '...'; ?>
                                                </small>
                                            </td>
                                            <td>
                                                <?php if ($template['variables']): ?>
                                                    <?php $vars = json_decode($template['variables'], true); ?>
                                                    <small class="text-info">
                                                        <?php echo implode(', ', array_map('trim', $vars)); ?>
                                                    </small>
                                                <?php else: ?>
                                                    <small class="text-muted">لا توجد</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $template['is_active'] ? 'success' : 'secondary'; ?>">
                                                    <?php echo $template['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                                </span>
                                            </td>
                                            <td><?php echo safe_html($template['creator_name']); ?></td>
                                            <td>
                                                <small><?php echo date('Y-m-d H:i', strtotime($template['created_at'])); ?></small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="edit_template.php?id=<?php echo $template['id']; ?>"
                                                       class="btn btn-outline-primary" title="تحرير">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="send_message.php?template_id=<?php echo $template['id']; ?>"
                                                       class="btn btn-outline-info" title="استخدام">
                                                        <i class="fas fa-paper-plane"></i>
                                                    </a>
                                                    <a href="?toggle=<?php echo $template['id']; ?>"
                                                       class="btn btn-outline-<?php echo $template['is_active'] ? 'warning' : 'success'; ?>"
                                                       onclick="return confirm('هل تريد تغيير حالة هذا القالب؟')"
                                                       title="<?php echo $template['is_active'] ? 'إيقاف' : 'تفعيل'; ?>">
                                                        <i class="fas fa-<?php echo $template['is_active'] ? 'pause' : 'play'; ?>"></i>
                                                    </a>
                                                    <a href="?delete=<?php echo $template['id']; ?>"
                                                       class="btn btn-outline-danger"
                                                       onclick="return confirm('هل أنت متأكد من حذف هذا القالب؟')"
                                                       title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد قوالب رسائل</h5>
                            <p class="text-muted">ابدأ بإنشاء قالب رسالة جديد</p>
                            <a href="add_template.php" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>إضافة قالب جديد
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>



<?php require_once '../includes/footer.php'; ?>
