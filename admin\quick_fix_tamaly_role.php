<?php
/**
 * إصلاح سريع لدور المستخدم <EMAIL>
 * Quick <NAME_EMAIL> User Role
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

$user_email = '<EMAIL>';
$results = [];
$success = true;

try {
    $results[] = "🚀 بدء الإصلاح السريع لدور المستخدم $user_email";
    
    // 1. التحقق من وجود المستخدم
    $user_query = "SELECT * FROM users WHERE email = ?";
    $user_stmt = $conn->prepare($user_query);
    $user_stmt->bind_param("s", $user_email);
    $user_stmt->execute();
    $user = $user_stmt->get_result()->fetch_assoc();

    if (!$user) {
        throw new Exception("❌ المستخدم $user_email غير موجود!");
    }

    $results[] = "✅ المستخدم موجود: " . $user['full_name'];
    $results[] = "📋 الدور الحالي: " . $user['role'];

    // 2. تحديث دور المستخدم إلى teacher
    $new_role = 'teacher';
    $update_query = "UPDATE users SET role = ? WHERE email = ?";
    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bind_param("ss", $new_role, $user_email);
    
    if ($update_stmt->execute()) {
        $results[] = "✅ تم تحديث الدور من '{$user['role']}' إلى '$new_role'";
    } else {
        throw new Exception("❌ فشل في تحديث الدور");
    }

    // 3. إنشاء جداول الصلاحيات إذا لم تكن موجودة
    $create_resources = "
    CREATE TABLE IF NOT EXISTS `system_resources` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `resource_type` enum('page','action','data','report') NOT NULL,
        `resource_key` varchar(100) NOT NULL,
        `resource_name` varchar(255) NOT NULL,
        `resource_description` text,
        `resource_path` varchar(255) DEFAULT NULL,
        `parent_resource` varchar(100) DEFAULT NULL,
        `icon` varchar(100) DEFAULT NULL,
        `sort_order` int(11) DEFAULT 0,
        `is_active` tinyint(1) DEFAULT 1,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `resource_key` (`resource_key`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ";

    $create_permissions = "
    CREATE TABLE IF NOT EXISTS `user_custom_permissions` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `permission_key` varchar(100) NOT NULL,
        `is_granted` tinyint(1) DEFAULT 1,
        `granted_by` int(11) DEFAULT NULL,
        `granted_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `user_permission` (`user_id`,`permission_key`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ";

    if ($conn->query($create_resources) && $conn->query($create_permissions)) {
        $results[] = "✅ جداول الصلاحيات جاهزة";
    } else {
        $results[] = "⚠️ تحذير: مشكلة في جداول الصلاحيات";
    }

    // 4. إضافة صلاحيات إضافية للمستخدم
    $additional_permissions = [
        'classes_view', 'classes_manage',
        'subjects_view', 'subjects_manage',
        'students_view', 'students_manage',
        'exams_view', 'exams_manage',
        'reports_view'
    ];

    // إعادة جلب بيانات المستخدم المحدثة
    $user_stmt->execute();
    $updated_user = $user_stmt->get_result()->fetch_assoc();

    // حذف الصلاحيات الموجودة
    $delete_stmt = $conn->prepare("DELETE FROM user_custom_permissions WHERE user_id = ?");
    $delete_stmt->bind_param("i", $updated_user['id']);
    $delete_stmt->execute();

    // إضافة الصلاحيات الجديدة
    $permission_stmt = $conn->prepare("
        INSERT INTO user_custom_permissions 
        (user_id, permission_key, is_granted, granted_by, granted_at) 
        VALUES (?, ?, 1, ?, NOW())
    ");

    $granted_count = 0;
    foreach ($additional_permissions as $permission) {
        $permission_stmt->bind_param("isi", $updated_user['id'], $permission, $_SESSION['user_id']);
        if ($permission_stmt->execute()) {
            $granted_count++;
        }
    }

    $results[] = "🔑 تم منح $granted_count صلاحية إضافية";

    // 5. اختبار النتيجة
    $results[] = "\n🧪 اختبار النتيجة:";
    
    // محاكاة جلسة المستخدم
    $original_session = $_SESSION;
    $_SESSION['user_id'] = $updated_user['id'];
    $_SESSION['role'] = $updated_user['role'];
    $_SESSION['email'] = $updated_user['email'];

    // اختبار الصلاحيات الأساسية
    $basic_tests = [
        'admin' => check_permission('admin'),
        'teacher' => check_permission('teacher'),
        'staff' => check_permission('staff')
    ];

    foreach ($basic_tests as $role => $has_permission) {
        $results[] = ($has_permission ? "✅" : "❌") . " صلاحية $role: " . ($has_permission ? "متاح" : "غير متاح");
    }

    // اختبار الصلاحيات المخصصة
    $custom_tests = ['classes_view', 'subjects_view', 'students_view'];
    foreach ($custom_tests as $permission) {
        $has_permission = has_permission($permission);
        $results[] = ($has_permission ? "✅" : "❌") . " $permission: " . ($has_permission ? "متاح" : "غير متاح");
    }

    // استعادة الجلسة الأصلية
    $_SESSION = $original_session;

    $results[] = "\n🎉 تم الإصلاح بنجاح!";
    $results[] = "📋 الآن يجب أن تظهر القائمة الجانبية للمستخدم";
    $results[] = "🔄 يجب على المستخدم تسجيل الدخول مرة أخرى لرؤية التغييرات";

} catch (Exception $e) {
    $success = false;
    $results[] = "❌ خطأ: " . $e->getMessage();
}

$page_title = 'إصلاح سريع لدور المستخدم <EMAIL>';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-user-shield me-2"></i><?php echo $page_title; ?></h2>
            <p class="text-muted">حل مشكلة: القائمة الجانبية لا تظهر</p>
        </div>
        <a href="../settings/permissions.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة
        </a>
    </div>

    <!-- نتائج الإصلاح -->
    <div class="card">
        <div class="card-header <?php echo $success ? 'bg-success' : 'bg-danger'; ?> text-white">
            <h5>
                <i class="fas fa-<?php echo $success ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                <?php echo $success ? 'تم الإصلاح بنجاح!' : 'فشل في الإصلاح'; ?>
            </h5>
        </div>
        <div class="card-body">
            <div class="bg-dark text-light p-3 rounded" style="font-family: monospace; white-space: pre-line;">
<?php echo implode("\n", $results); ?>
            </div>
        </div>
    </div>

    <?php if ($success): ?>
        <!-- تعليمات الاختبار -->
        <div class="card mt-4">
            <div class="card-header bg-info text-white">
                <h5><i class="fas fa-clipboard-check me-2"></i>خطوات الاختبار</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <h6><i class="fas fa-thumbs-up me-2"></i>تم تحديث دور المستخدم!</h6>
                    <p class="mb-0">تم تغيير دور المستخدم إلى "معلم" وإضافة صلاحيات إضافية.</p>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">خطوات الاختبار:</h6>
                        <ol>
                            <li><strong>أخبر المستخدم</strong> بتسجيل الخروج من حسابه</li>
                            <li><strong>تسجيل دخول جديد</strong> بحساب: <code><?php echo $user_email; ?></code></li>
                            <li><strong>التحقق من القائمة الجانبية</strong> - يجب أن تظهر الآن</li>
                            <li><strong>اختبار الوصول</strong> للصفحات المختلفة</li>
                        </ol>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-success">يجب أن تظهر الآن:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-user-graduate me-2"></i>الطلاب
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-school me-2"></i>الفصول
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-book me-2"></i>المواد
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-file-alt me-2"></i>الامتحانات
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>مهم:</h6>
                        <p class="mb-0">
                            يجب على المستخدم <strong>تسجيل الخروج وإعادة تسجيل الدخول</strong> 
                            لرؤية التغييرات في القائمة الجانبية.
                        </p>
                    </div>
                    
                    <a href="check_tamaly_role.php" class="btn btn-info me-2">
                        <i class="fas fa-user-cog me-2"></i>فحص الدور مرة أخرى
                    </a>
                    <button class="btn btn-secondary" onclick="window.location.reload()">
                        <i class="fas fa-redo me-2"></i>إعادة تشغيل الإصلاح
                    </button>
                </div>
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="card mt-4">
            <div class="card-header bg-secondary text-white">
                <h6><i class="fas fa-info-circle me-2"></i>ما تم عمله</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">التغييرات:</h6>
                        <ul class="small">
                            <li>تم تغيير دور المستخدم إلى "teacher"</li>
                            <li>تم إنشاء جداول الصلاحيات</li>
                            <li>تم منح صلاحيات إضافية</li>
                            <li>تم اختبار النظام</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-success">النتيجة:</h6>
                        <ul class="small">
                            <li>القائمة الجانبية ستظهر الآن</li>
                            <li>يمكن الوصول للفصول والمواد</li>
                            <li>يمكن عرض الطلاب والامتحانات</li>
                            <li>صلاحيات معلم كاملة</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    <?php else: ?>
        <!-- في حالة الفشل -->
        <div class="card mt-4">
            <div class="card-header bg-warning text-dark">
                <h5><i class="fas fa-tools me-2"></i>خيارات أخرى</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <a href="check_tamaly_role.php" class="btn btn-outline-primary w-100">
                            <i class="fas fa-user-cog me-2"></i>فحص الدور يدوياً
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="fix_data_permissions.php" class="btn btn-outline-info w-100">
                            <i class="fas fa-database me-2"></i>إصلاح الصلاحيات
                        </a>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-outline-secondary w-100" onclick="window.location.reload()">
                            <i class="fas fa-redo me-2"></i>إعادة المحاولة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php include_once '../includes/footer.php'; ?>
