<?php
/**
 * صفحة تأكيد حذف المادة الدراسية
 * Subject Delete Confirmation Page
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول';
    header('Location: ../dashboard/');
    exit();
}

// الحصول على معرف المادة
$subject_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($subject_id <= 0) {
    $_SESSION['error_message'] = 'معرف المادة غير صحيح';
    header('Location: index.php');
    exit();
}

// جلب بيانات المادة
$subject_query = "SELECT id, subject_name, subject_code FROM subjects WHERE id = ?";
$stmt = $conn->prepare($subject_query);
if (!$stmt) {
    $_SESSION['error_message'] = 'خطأ في قاعدة البيانات';
    header('Location: index.php');
    exit();
}

$stmt->bind_param('i', $subject_id);
$stmt->execute();
$result = $stmt->get_result();
$subject = $result->fetch_assoc();
$stmt->close();

if (!$subject) {
    $_SESSION['error_message'] = 'المادة غير موجودة';
    header('Location: index.php');
    exit();
}

// فحص التبعيات
$dependencies = [];

// فحص المعلمين المرتبطين
$teachers_query = "SELECT COUNT(*) as count FROM teacher_assignments WHERE subject_id = ? AND status = 'active'";
$stmt = $conn->prepare($teachers_query);
$stmt->bind_param('i', $subject_id);
$stmt->execute();
$teachers_count = $stmt->get_result()->fetch_assoc()['count'];
$stmt->close();

if ($teachers_count > 0) {
    $dependencies[] = "عدد $teachers_count من تكليفات المعلمين";
}

// فحص الفصول المرتبطة
$classes_query = "SELECT COUNT(DISTINCT class_id) as count FROM teacher_assignments WHERE subject_id = ? AND status = 'active'";
$stmt = $conn->prepare($classes_query);
$stmt->bind_param('i', $subject_id);
$stmt->execute();
$classes_count = $stmt->get_result()->fetch_assoc()['count'];
$stmt->close();

if ($classes_count > 0) {
    $dependencies[] = "عدد $classes_count من الفصول الدراسية";
}

$page_title = 'تأكيد حذف المادة: ' . $subject['subject_name'];
require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- بطاقة التحذير -->
            <div class="card border-danger shadow">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تأكيد حذف المادة الدراسية
                    </h4>
                </div>
                <div class="card-body">
                    <!-- معلومات المادة -->
                    <div class="alert alert-warning">
                        <h5 class="alert-heading">
                            <i class="fas fa-book me-2"></i>
                            معلومات المادة المراد حذفها:
                        </h5>
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-1"><strong>اسم المادة:</strong></p>
                                <p class="h5 text-primary"><?php echo htmlspecialchars($subject['subject_name']); ?></p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-1"><strong>كود المادة:</strong></p>
                                <p class="h5 text-secondary"><?php echo htmlspecialchars($subject['subject_code'] ?? 'غير محدد'); ?></p>
                            </div>
                        </div>
                        <p class="mb-1"><strong>معرف المادة:</strong> <?php echo $subject['id']; ?></p>
                    </div>

                    <!-- التبعيات -->
                    <?php if (!empty($dependencies)): ?>
                        <div class="alert alert-danger">
                            <h5 class="alert-heading">
                                <i class="fas fa-link me-2"></i>
                                تحذير: هذه المادة مرتبطة بـ:
                            </h5>
                            <ul class="mb-0">
                                <?php foreach ($dependencies as $dependency): ?>
                                    <li><?php echo $dependency; ?></li>
                                <?php endforeach; ?>
                            </ul>
                            <hr>
                            <p class="mb-0">
                                <i class="fas fa-info-circle me-1"></i>
                                <strong>ملاحظة:</strong> سيتم حذف جميع الارتباطات المتعلقة بهذه المادة.
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <h5 class="alert-heading">
                                <i class="fas fa-check-circle me-2"></i>
                                حالة المادة:
                            </h5>
                            <p class="mb-0">هذه المادة غير مرتبطة بأي بيانات أخرى. يمكن حذفها بأمان.</p>
                        </div>
                    <?php endif; ?>

                    <!-- رسالة التحذير النهائية -->
                    <div class="alert alert-danger border-danger">
                        <h5 class="text-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            تحذير مهم:
                        </h5>
                        <ul class="mb-0">
                            <li><strong>لا يمكن التراجع عن هذا الإجراء</strong></li>
                            <li>سيتم حذف المادة نهائياً من النظام</li>
                            <li>سيتم حذف جميع الارتباطات المتعلقة بها</li>
                            <li>تأكد من أنك تريد المتابعة</li>
                        </ul>
                    </div>

                    <!-- أزرار الإجراء -->
                    <div class="row">
                        <div class="col-md-6">
                            <form method="POST" action="delete.php?id=<?php echo $subject_id; ?>&confirm=1" class="d-inline">
                                <input type="hidden" name="confirm_delete" value="1">
                                <input type="hidden" name="subject_name" value="<?php echo htmlspecialchars($subject['subject_name']); ?>">
                                <button type="submit" class="btn btn-danger btn-lg w-100">
                                    <i class="fas fa-trash me-2"></i>
                                    نعم، احذف المادة نهائياً
                                </button>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <a href="index.php" class="btn btn-secondary btn-lg w-100">
                                <i class="fas fa-times me-2"></i>
                                إلغاء - العودة للقائمة
                            </a>
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="mt-4">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            إذا كنت غير متأكد، يمكنك إلغاء العملية والعودة لقائمة المواد.
                            يمكنك أيضاً تعديل المادة بدلاً من حذفها.
                        </small>
                    </div>

                    <!-- روابط سريعة -->
                    <div class="mt-3">
                        <div class="btn-group btn-group-sm">
                            <a href="view.php?id=<?php echo $subject_id; ?>" class="btn btn-outline-info">
                                <i class="fas fa-eye me-1"></i>عرض تفاصيل المادة
                            </a>
                            <a href="edit.php?id=<?php echo $subject_id; ?>" class="btn btn-outline-warning">
                                <i class="fas fa-edit me-1"></i>تعديل المادة
                            </a>
                            <a href="index.php" class="btn btn-outline-secondary">
                                <i class="fas fa-list me-1"></i>قائمة المواد
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تأكيد إضافي عند الإرسال
document.querySelector('form').addEventListener('submit', function(e) {
    const subjectName = '<?php echo addslashes($subject['subject_name']); ?>';
    const confirmed = confirm(`تأكيد أخير: هل أنت متأكد من حذف المادة "${subjectName}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`);
    
    if (!confirmed) {
        e.preventDefault();
        return false;
    }
    
    // إظهار رسالة تحميل
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحذف...';
    submitBtn.disabled = true;
    
    return true;
});
</script>

<?php require_once '../includes/footer.php'; ?>
