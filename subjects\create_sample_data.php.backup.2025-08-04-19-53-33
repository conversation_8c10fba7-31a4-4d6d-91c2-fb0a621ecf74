<?php
/**
 * إنشاء بيانات تجريبية لربط المواد بالفصول
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

check_session();
if (!check_permission('admin')) {
    die('ليس لديك صلاحية للوصول');
}

echo "<h2>إنشاء بيانات تجريبية لربط المواد بالفصول</h2>";

// 1. فحص البيانات الموجودة
echo "<h3>1. فحص البيانات الموجودة:</h3>";

$subjects_count = $conn->query("SELECT COUNT(*) as count FROM subjects")->fetch_assoc()['count'];
$classes_count = $conn->query("SELECT COUNT(*) as count FROM classes")->fetch_assoc()['count'];
$teachers_count = $conn->query("SELECT COUNT(*) as count FROM teachers")->fetch_assoc()['count'];
$assignments_count = $conn->query("SELECT COUNT(*) as count FROM teacher_assignments")->fetch_assoc()['count'];

echo "<div style='background: #e8f5e8; padding: 10px; border-radius: 5px;'>";
echo "<p>✅ المواد: <strong>$subjects_count</strong></p>";
echo "<p>✅ الفصول: <strong>$classes_count</strong></p>";
echo "<p>✅ المعلمين: <strong>$teachers_count</strong></p>";
echo "<p>✅ التكليفات: <strong>$assignments_count</strong></p>";
echo "</div>";

// 2. إضافة فصول إذا لم تكن موجودة
if ($classes_count == 0) {
    echo "<h3>2. إضافة فصول تجريبية:</h3>";
    
    $sample_classes = [
        [1, 'الصف الأول أ', 'أ', 1, 1],
        [2, 'الصف الأول ب', 'ب', 1, 1],
        [3, 'الصف الثاني أ', 'أ', 2, 1],
        [4, 'الصف الثاني ب', 'ب', 2, 1],
    ];
    
    foreach ($sample_classes as $class_data) {
        $insert_class = "INSERT INTO classes (id, class_name, section, grade_id, stage_id, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, 'active', NOW(), NOW())";
        $stmt = $conn->prepare($insert_class);
        $stmt->bind_param('issii', $class_data[0], $class_data[1], $class_data[2], $class_data[3], $class_data[4]);
        
        if ($stmt->execute()) {
            echo "<p style='color: green;'>✅ تم إضافة الفصل: {$class_data[1]}</p>";
        } else {
            echo "<p style='color: red;'>❌ خطأ في إضافة الفصل: " . $stmt->error . "</p>";
        }
        $stmt->close();
    }
}

// 3. إضافة معلمين إذا لم يكونوا موجودين
if ($teachers_count == 0) {
    echo "<h3>3. إضافة معلمين تجريبيين:</h3>";
    
    // إضافة مستخدمين أولاً
    $sample_users = [
        [1, 'أحمد محمد', '<EMAIL>', '01234567890', 'teacher'],
        [2, 'فاطمة علي', '<EMAIL>', '01234567891', 'teacher'],
    ];
    
    foreach ($sample_users as $user_data) {
        $insert_user = "INSERT INTO users (id, full_name, email, phone, role, password, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, 'active', NOW(), NOW())";
        $stmt = $conn->prepare($insert_user);
        $password = password_hash('123456', PASSWORD_DEFAULT);
        $stmt->bind_param('isssss', $user_data[0], $user_data[1], $user_data[2], $user_data[3], $user_data[4], $password);
        
        if ($stmt->execute()) {
            echo "<p style='color: green;'>✅ تم إضافة المستخدم: {$user_data[1]}</p>";
            
            // إضافة المعلم
            $insert_teacher = "INSERT INTO teachers (id, user_id, employee_id, hire_date, status, created_at, updated_at) VALUES (?, ?, ?, NOW(), 'active', NOW(), NOW())";
            $stmt2 = $conn->prepare($insert_teacher);
            $employee_id = 'T' . str_pad($user_data[0], 4, '0', STR_PAD_LEFT);
            $stmt2->bind_param('iis', $user_data[0], $user_data[0], $employee_id);
            
            if ($stmt2->execute()) {
                echo "<p style='color: green;'>✅ تم إضافة المعلم: {$user_data[1]}</p>";
            } else {
                echo "<p style='color: red;'>❌ خطأ في إضافة المعلم: " . $stmt2->error . "</p>";
            }
            $stmt2->close();
            
        } else {
            echo "<p style='color: red;'>❌ خطأ في إضافة المستخدم: " . $stmt->error . "</p>";
        }
        $stmt->close();
    }
}

// 4. إضافة مواد إذا لم تكن موجودة
if ($subjects_count == 0) {
    echo "<h3>4. إضافة مواد تجريبية:</h3>";
    
    $sample_subjects = [
        [7, 'الرياضيات', 'MATH101', 'مادة الرياضيات للمرحلة الابتدائية', 1, 1],
        [8, 'العلوم', 'SCI101', 'مادة العلوم للمرحلة الابتدائية', 1, 1],
        [9, 'اللغة العربية', 'AR101', 'مادة اللغة العربية', 1, 1],
        [10, 'اللغة الإنجليزية', 'EN101', 'مادة اللغة الإنجليزية', 1, 1],
    ];
    
    foreach ($sample_subjects as $subject_data) {
        $insert_subject = "INSERT INTO subjects (id, subject_name, subject_code, description, stage_id, grade_id, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, 'active', NOW(), NOW())";
        $stmt = $conn->prepare($insert_subject);
        $stmt->bind_param('isssii', $subject_data[0], $subject_data[1], $subject_data[2], $subject_data[3], $subject_data[4], $subject_data[5]);
        
        if ($stmt->execute()) {
            echo "<p style='color: green;'>✅ تم إضافة المادة: {$subject_data[1]}</p>";
        } else {
            echo "<p style='color: red;'>❌ خطأ في إضافة المادة: " . $stmt->error . "</p>";
        }
        $stmt->close();
    }
}

// 5. إضافة التكليفات (الربط الأساسي)
echo "<h3>5. إضافة تكليفات المعلمين:</h3>";

// حذف التكليفات الموجودة أولاً
$conn->query("DELETE FROM teacher_assignments");
echo "<p>تم حذف التكليفات القديمة</p>";

$sample_assignments = [
    [1, 1, 7, 'active', 4], // معلم 1، فصل 1، مادة الرياضيات
    [1, 2, 7, 'active', 4], // معلم 1، فصل 2، مادة الرياضيات
    [1, 3, 7, 'active', 4], // معلم 1، فصل 3، مادة الرياضيات
    [2, 1, 8, 'active', 3], // معلم 2، فصل 1، مادة العلوم
    [2, 2, 8, 'active', 3], // معلم 2، فصل 2، مادة العلوم
    [2, 3, 8, 'active', 3], // معلم 2، فصل 3، مادة العلوم
    [1, 4, 9, 'active', 5], // معلم 1، فصل 4، مادة العربية
    [2, 4, 10, 'active', 3], // معلم 2، فصل 4، مادة الإنجليزية
];

$insert_assignment = "INSERT INTO teacher_assignments (teacher_id, class_id, subject_id, status, weekly_hours, academic_year_id, semester, assigned_at, created_at, updated_at) VALUES (?, ?, ?, ?, ?, 1, 'first', NOW(), NOW(), NOW())";
$stmt = $conn->prepare($insert_assignment);

$success_count = 0;
foreach ($sample_assignments as $assignment) {
    $stmt->bind_param('iiisi', $assignment[0], $assignment[1], $assignment[2], $assignment[3], $assignment[4]);
    
    if ($stmt->execute()) {
        echo "<p style='color: green;'>✅ تكليف: معلم {$assignment[0]} → فصل {$assignment[1]} → مادة {$assignment[2]}</p>";
        $success_count++;
    } else {
        echo "<p style='color: red;'>❌ خطأ في التكليف: " . $stmt->error . "</p>";
    }
}
$stmt->close();

echo "<p><strong>تم إضافة $success_count تكليف بنجاح</strong></p>";

// 6. اختبار النتائج
echo "<h3>6. اختبار النتائج:</h3>";

$test_subjects = [7, 8, 9, 10];
foreach ($test_subjects as $subject_id) {
    $classes_count = $conn->query("SELECT COUNT(DISTINCT ta.class_id) as count FROM teacher_assignments ta WHERE ta.subject_id = $subject_id AND ta.status = 'active'")->fetch_assoc()['count'];
    $teachers_count = $conn->query("SELECT COUNT(DISTINCT ta.teacher_id) as count FROM teacher_assignments ta WHERE ta.subject_id = $subject_id AND ta.status = 'active'")->fetch_assoc()['count'];
    
    $subject_result = $conn->query("SELECT subject_name FROM subjects WHERE id = $subject_id");
    $subject_name = $subject_result ? $subject_result->fetch_assoc()['subject_name'] : "مادة غير موجودة";

    echo "<p><strong>$subject_name (ID: $subject_id):</strong> $classes_count فصل، $teachers_count معلم</p>";
}

// 7. روابط الاختبار
echo "<h3>7. روابط الاختبار:</h3>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px;'>";
echo "<p><a href='view.php?id=7' target='_blank'>🔍 عرض مادة الرياضيات</a></p>";
echo "<p><a href='view.php?id=8' target='_blank'>🔍 عرض مادة العلوم</a></p>";
echo "<p><a href='view.php?id=9' target='_blank'>🔍 عرض مادة العربية</a></p>";
echo "<p><a href='view.php?id=10' target='_blank'>🔍 عرض مادة الإنجليزية</a></p>";
echo "<p><a href='index.php' target='_blank'>📋 قائمة المواد</a></p>";
echo "<p><a href='check_database.php' target='_blank'>🔧 فحص قاعدة البيانات</a></p>";
echo "</div>";

echo "<h3>8. تعليمات:</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;'>";
echo "<ol>";
echo "<li><strong>شغل هذا الملف مرة واحدة فقط</strong> لإنشاء البيانات التجريبية</li>";
echo "<li><strong>اختبر روابط العرض</strong> للتأكد من ظهور الفصول</li>";
echo "<li><strong>إذا ظهرت الفصول</strong> فالمشكلة كانت في عدم وجود بيانات</li>";
echo "<li><strong>إذا لم تظهر</strong> فهناك مشكلة في الاستعلام نفسه</li>";
echo "</ol>";
echo "</div>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h3 { color: #333; border-bottom: 2px solid #ddd; padding-bottom: 5px; margin-top: 30px; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
