<?php
/**
 * تقارير الغياب بالخصم المحسنة
 * Enhanced Absence with Deduction Reports
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';
require_once '../includes/header.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

$user_id = $_SESSION['user_id'];

// فلاتر التقرير
$from_date = $_GET['from_date'] ?? date('Y-m-01');
$to_date = $_GET['to_date'] ?? date('Y-m-t');
$employee_search = $_GET['employee_search'] ?? '';
$report_type = $_GET['report_type'] ?? 'summary';
$role_filter = $_GET['role_filter'] ?? 'all';

$where_conditions = [];
$params = [];
$types = "";

// فلتر التاريخ
if (!empty($from_date)) {
    $where_conditions[] = "sad.absence_date >= ?";
    $params[] = $from_date;
    $types .= "s";
}

if (!empty($to_date)) {
    $where_conditions[] = "sad.absence_date <= ?";
    $params[] = $to_date;
    $types .= "s";
}

// فلتر الموظف
if (!empty($employee_search)) {
    $where_conditions[] = "u.full_name LIKE ?";
    $params[] = '%' . $employee_search . '%';
    $types .= "s";
}

// فلتر الدور
if ($role_filter !== 'all') {
    $where_conditions[] = "u.role = ?";
    $params[] = $role_filter;
    $types .= "s";
}

// إضافة شرط استبعاد مديري النظام
$where_conditions[] = "u.role NOT IN ('admin', 'system_admin')";

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

// تقرير ملخص
if ($report_type === 'summary') {
    $summary_query = "
        SELECT 
            u.full_name,
            u.role,
            COUNT(*) as total_absence_days,
            SUM(sad.deduction_amount) as total_deduction,
            AVG(sad.deduction_amount) as avg_deduction,
            MIN(sad.absence_date) as first_absence,
            MAX(sad.absence_date) as last_absence,
            GROUP_CONCAT(DISTINCT sad.absence_type) as absence_types
        FROM staff_absences_with_deduction sad
        JOIN users u ON sad.user_id = u.id
        $where_clause
        GROUP BY sad.user_id, u.full_name, u.role
        ORDER BY total_deduction DESC, total_absence_days DESC
    ";
    
    $summary_stmt = $conn->prepare($summary_query);
    if (!empty($params)) {
        $summary_stmt->bind_param($types, ...$params);
    }
    $summary_stmt->execute();
    $summary_result = $summary_stmt->get_result();
}

// تقرير تفصيلي
if ($report_type === 'detailed') {
    $detailed_query = "
        SELECT 
            sad.*,
            u.full_name,
            u.role,
            recorder.full_name as recorded_by_name
        FROM staff_absences_with_deduction sad
        JOIN users u ON sad.user_id = u.id
        LEFT JOIN users recorder ON sad.recorded_by = recorder.id
        $where_clause
        ORDER BY sad.absence_date DESC, u.full_name ASC
    ";
    
    $detailed_stmt = $conn->prepare($detailed_query);
    if (!empty($params)) {
        $detailed_stmt->bind_param($types, ...$params);
    }
    $detailed_stmt->execute();
    $detailed_result = $detailed_stmt->get_result();
}

// إحصائيات عامة
$stats_query = "
    SELECT 
        COUNT(*) as total_records,
        COUNT(DISTINCT sad.user_id) as unique_employees,
        SUM(sad.deduction_amount) as total_deductions,
        AVG(sad.deduction_amount) as avg_deduction,
        MIN(sad.absence_date) as earliest_date,
        MAX(sad.absence_date) as latest_date
    FROM staff_absences_with_deduction sad
    JOIN users u ON sad.user_id = u.id
    $where_clause
";

$stats_stmt = $conn->prepare($stats_query);
if (!empty($params)) {
    $stats_stmt->bind_param($types, ...$params);
}
$stats_stmt->execute();
$stats_result = $stats_stmt->get_result();
$stats = $stats_result->fetch_assoc();

// حساب عدد الأيام في الفترة
$period_days = 0;
if (!empty($from_date) && !empty($to_date)) {
    $start = new DateTime($from_date);
    $end = new DateTime($to_date);
    $period_days = $end->diff($start)->days + 1;
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-chart-line text-primary me-2"></i>
                    تقارير الغياب بالخصم
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="manage_absences.php">إدارة الغياب</a></li>
                        <li class="breadcrumb-item active">التقارير</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body text-center">
                    <h3><?php echo $stats['total_records'] ?? 0; ?></h3>
                    <p class="mb-0 small">إجمالي السجلات</p>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body text-center">
                    <h3><?php echo $stats['unique_employees'] ?? 0; ?></h3>
                    <p class="mb-0 small">عدد الموظفين</p>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-danger text-white h-100">
                <div class="card-body text-center">
                    <h3><?php echo number_format($stats['total_deductions'] ?? 0, 2); ?></h3>
                    <p class="mb-0 small">إجمالي الخصومات</p>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body text-center">
                    <h3><?php echo number_format($stats['avg_deduction'] ?? 0, 2); ?></h3>
                    <p class="mb-0 small">متوسط الخصم</p>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body text-center">
                    <h3><?php echo $period_days; ?></h3>
                    <p class="mb-0 small">أيام الفترة</p>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-dark text-white h-100">
                <div class="card-body text-center">
                    <h3><?php echo $stats['total_records'] > 0 && $period_days > 0 ? number_format(($stats['total_records'] / $period_days) * 100, 1) : 0; ?>%</h3>
                    <p class="mb-0 small">معدل الغياب</p>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر التقرير -->
    <div class="card mb-4">
        <div class="card-header">
            <h6 class="card-title mb-0">
                <i class="fas fa-filter me-2"></i>
                فلاتر التقرير
            </h6>
        </div>
        <div class="card-body">
            <form method="GET">
                <div class="row">
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <label for="from_date" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="from_date" name="from_date" value="<?php echo $from_date; ?>">
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <label for="to_date" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="to_date" name="to_date" value="<?php echo $to_date; ?>">
                    </div>
                    <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                        <label for="employee_search" class="form-label">البحث في الموظفين</label>
                        <input type="text" class="form-control" id="employee_search" name="employee_search" 
                               value="<?php echo htmlspecialchars($employee_search); ?>" placeholder="اسم الموظف...">
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <label for="role_filter" class="form-label">الدور</label>
                        <select class="form-select" id="role_filter" name="role_filter">
                            <option value="all" <?php echo $role_filter === 'all' ? 'selected' : ''; ?>>جميع الأدوار</option>
                            <option value="teacher" <?php echo $role_filter === 'teacher' ? 'selected' : ''; ?>>معلم</option>
                            <option value="staff" <?php echo $role_filter === 'staff' ? 'selected' : ''; ?>>موظف</option>
                        </select>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <label for="report_type" class="form-label">نوع التقرير</label>
                        <select class="form-select" id="report_type" name="report_type">
                            <option value="summary" <?php echo $report_type === 'summary' ? 'selected' : ''; ?>>ملخص</option>
                            <option value="detailed" <?php echo $report_type === 'detailed' ? 'selected' : ''; ?>>تفصيلي</option>
                        </select>
                    </div>
                    <div class="col-lg-1 col-md-4 col-sm-6 mb-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- التقرير -->
    <?php if ($report_type === 'summary' && isset($summary_result)): ?>
        <!-- تقرير ملخص -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    تقرير ملخص الغياب بالخصم
                    <?php if (!empty($from_date) && !empty($to_date)): ?>
                        (من <?php echo $from_date; ?> إلى <?php echo $to_date; ?>)
                    <?php endif; ?>
                </h6>
            </div>
            <div class="card-body">
                <?php if ($summary_result->num_rows > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الموظف</th>
                                    <th>الدور</th>
                                    <th class="text-center">عدد أيام الغياب</th>
                                    <th class="text-center">إجمالي الخصم</th>
                                    <th class="text-center">متوسط الخصم</th>
                                    <th class="text-center">أول غياب</th>
                                    <th class="text-center">آخر غياب</th>
                                    <th class="text-center">أنواع الخصم</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($row = $summary_result->fetch_assoc()): ?>
                                    <tr>
                                        <td><strong><?php echo htmlspecialchars($row['full_name']); ?></strong></td>
                                        <td>
                                            <span class="badge bg-<?php echo $row['role'] === 'teacher' ? 'primary' : 'secondary'; ?>">
                                                <?php echo $row['role'] === 'teacher' ? 'معلم' : 'إداري'; ?>
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-danger fs-6"><?php echo $row['total_absence_days']; ?></span>
                                        </td>
                                        <td class="text-center">
                                            <strong class="text-danger"><?php echo number_format($row['total_deduction'], 2); ?> ج.م</strong>
                                        </td>
                                        <td class="text-center">
                                            <span class="text-muted"><?php echo number_format($row['avg_deduction'], 2); ?> ج.م</span>
                                        </td>
                                        <td class="text-center"><?php echo $row['first_absence']; ?></td>
                                        <td class="text-center"><?php echo $row['last_absence']; ?></td>
                                        <td class="text-center">
                                            <small><?php echo str_replace(',', ', ', $row['absence_types']); ?></small>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle me-2"></i>
                        لا توجد بيانات للفترة المحددة
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>

    <?php if ($report_type === 'detailed' && isset($detailed_result)): ?>
        <!-- تقرير تفصيلي -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    تقرير تفصيلي للغياب بالخصم
                </h6>
            </div>
            <div class="card-body">
                <?php if ($detailed_result->num_rows > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>التاريخ</th>
                                    <th>الموظف</th>
                                    <th>الدور</th>
                                    <th>السبب</th>
                                    <th class="text-center">مبلغ الخصم</th>
                                    <th class="text-center">نوع الخصم</th>
                                    <th>مسجل بواسطة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($row = $detailed_result->fetch_assoc()): ?>
                                    <tr>
                                        <td><?php echo $row['absence_date']; ?></td>
                                        <td><strong><?php echo htmlspecialchars($row['full_name']); ?></strong></td>
                                        <td>
                                            <span class="badge bg-<?php echo $row['role'] === 'teacher' ? 'primary' : 'secondary'; ?>">
                                                <?php echo $row['role'] === 'teacher' ? 'معلم' : 'إداري'; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if (!empty($row['reason'])): ?>
                                                <span title="<?php echo htmlspecialchars($row['reason']); ?>">
                                                    <?php echo htmlspecialchars(substr($row['reason'], 0, 30)) . (strlen($row['reason']) > 30 ? '...' : ''); ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-center">
                                            <strong class="text-danger"><?php echo number_format($row['deduction_amount'], 2); ?> ج.م</strong>
                                        </td>
                                        <td class="text-center">
                                            <small><?php echo $row['absence_type']; ?></small>
                                        </td>
                                        <td><?php echo htmlspecialchars($row['recorded_by_name'] ?? 'غير محدد'); ?></td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle me-2"></i>
                        لا توجد بيانات للفترة المحددة
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.table-dark th {
    background: linear-gradient(135deg, #495057 0%, #343a40 100%);
}

.badge {
    font-size: 0.8em;
}

@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.9em;
    }
}
</style>

<?php require_once '../includes/footer.php'; ?>
