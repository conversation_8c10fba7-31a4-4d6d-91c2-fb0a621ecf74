<?php
/**
 * إدارة صلاحيات مستخدم محدد
 * Manage Specific User Permissions
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/advanced_permissions.php';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

// التحقق من معرف المستخدم
if (!isset($_GET['user_id']) || !is_numeric($_GET['user_id'])) {
    header('Location: permissions_manager.php');
    exit();
}

$user_id = intval($_GET['user_id']);
$success_message = '';
$error_message = '';

// جلب بيانات المستخدم
$user_stmt = $conn->prepare("SELECT id, full_name, username, email, role, status FROM users WHERE id = ?");
$user_stmt->bind_param("i", $user_id);
$user_stmt->execute();
$user = $user_stmt->get_result()->fetch_assoc();

if (!$user) {
    header('Location: permissions_manager.php');
    exit();
}

// معالجة تحديث الصلاحيات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_permissions'])) {
    $permissions = $_POST['permissions'] ?? [];
    $notes = clean_input($_POST['notes'] ?? '');
    
    $conn->begin_transaction();
    try {
        // حذف الصلاحيات الحالية
        $delete_stmt = $conn->prepare("DELETE FROM user_custom_permissions WHERE user_id = ?");
        $delete_stmt->bind_param("i", $user_id);
        $delete_stmt->execute();
        
        // إضافة الصلاحيات الجديدة
        foreach ($permissions as $permission) {
            $parts = explode(':', $permission);
            if (count($parts) == 2) {
                $type = $parts[0];
                $key = $parts[1];
                grant_custom_permission($user_id, $type, $key, $_SESSION['user_id'], null, $notes);
            }
        }
        
        $conn->commit();
        $success_message = "تم تحديث صلاحيات المستخدم بنجاح";
    } catch (Exception $e) {
        $conn->rollback();
        $error_message = "خطأ في تحديث الصلاحيات: " . $e->getMessage();
    }
}

// جلب الصلاحيات الحالية للمستخدم
$current_permissions = get_user_all_permissions($user_id);

// جلب جميع موارد النظام
$resources_query = "SELECT * FROM system_resources WHERE is_active = 1 ORDER BY resource_type, sort_order, resource_name";
$resources_result = $conn->query($resources_query);
$resources = [];
while ($row = $resources_result->fetch_assoc()) {
    $resources[$row['resource_type']][] = $row;
}

$page_title = 'إدارة صلاحيات: ' . $user['full_name'];
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-user-shield me-2"></i><?php echo $page_title; ?></h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="../admin/">الإدارة</a></li>
                    <li class="breadcrumb-item"><a href="permissions_manager.php">إدارة الصلاحيات</a></li>
                    <li class="breadcrumb-item active"><?php echo htmlspecialchars($user['full_name']); ?></li>
                </ol>
            </nav>
        </div>
        <a href="permissions_manager.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
        </a>
    </div>

    <!-- رسائل التنبيه -->
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- معلومات المستخدم -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-user me-2"></i>معلومات المستخدم</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="avatar-lg bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center">
                            <i class="fas fa-user fa-2x"></i>
                        </div>
                    </div>
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>الاسم:</strong></td>
                            <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                        </tr>
                        <tr>
                            <td><strong>اسم المستخدم:</strong></td>
                            <td><?php echo htmlspecialchars($user['username']); ?></td>
                        </tr>
                        <tr>
                            <td><strong>البريد الإلكتروني:</strong></td>
                            <td><?php echo htmlspecialchars($user['email']); ?></td>
                        </tr>
                        <tr>
                            <td><strong>الدور الأساسي:</strong></td>
                            <td>
                                <span class="badge bg-primary">
                                    <?php 
                                    $role_names = [
                                        'admin' => 'مدير النظام',
                                        'financial_manager' => 'مدير مالي',
                                        'teacher' => 'معلم',
                                        'staff' => 'موظف',
                                        'student' => 'طالب',
                                        'parent' => 'ولي أمر'
                                    ];
                                    echo $role_names[$user['role']] ?? $user['role'];
                                    ?>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>الحالة:</strong></td>
                            <td>
                                <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                    <?php echo $user['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                                </span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- ملخص الصلاحيات -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-chart-pie me-2"></i>ملخص الصلاحيات</h5>
                </div>
                <div class="card-body">
                    <?php
                    $permission_counts = [
                        'page' => count($current_permissions['page'] ?? []),
                        'action' => count($current_permissions['action'] ?? []),
                        'data' => count($current_permissions['data'] ?? []),
                        'report' => count($current_permissions['report'] ?? [])
                    ];
                    $total_permissions = array_sum($permission_counts);
                    ?>
                    <div class="text-center mb-3">
                        <h3 class="text-primary"><?php echo $total_permissions; ?></h3>
                        <p class="text-muted">إجمالي الصلاحيات المخصصة</p>
                    </div>
                    <div class="row text-center">
                        <div class="col-6 mb-2">
                            <div class="bg-light p-2 rounded">
                                <strong><?php echo $permission_counts['page']; ?></strong>
                                <br><small>صفحات</small>
                            </div>
                        </div>
                        <div class="col-6 mb-2">
                            <div class="bg-light p-2 rounded">
                                <strong><?php echo $permission_counts['action']; ?></strong>
                                <br><small>إجراءات</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="bg-light p-2 rounded">
                                <strong><?php echo $permission_counts['data']; ?></strong>
                                <br><small>بيانات</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="bg-light p-2 rounded">
                                <strong><?php echo $permission_counts['report']; ?></strong>
                                <br><small>تقارير</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إدارة الصلاحيات -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-key me-2"></i>إدارة الصلاحيات المخصصة</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>ملاحظة:</strong> الصلاحيات المخصصة تُضاف إلى الصلاحيات الأساسية للدور. 
                            المدير له وصول كامل بغض النظر عن الإعدادات.
                        </div>

                        <!-- تبويبات الصلاحيات -->
                        <ul class="nav nav-tabs" id="permissionTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="pages-tab" data-bs-toggle="tab" data-bs-target="#pages" type="button">
                                    <i class="fas fa-file me-2"></i>الصفحات
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="actions-tab" data-bs-toggle="tab" data-bs-target="#actions" type="button">
                                    <i class="fas fa-cog me-2"></i>الإجراءات
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="data-tab" data-bs-toggle="tab" data-bs-target="#data" type="button">
                                    <i class="fas fa-database me-2"></i>البيانات
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="reports-tab" data-bs-toggle="tab" data-bs-target="#reports" type="button">
                                    <i class="fas fa-chart-bar me-2"></i>التقارير
                                </button>
                            </li>
                        </ul>

                        <div class="tab-content mt-3" id="permissionTabsContent">
                            <?php foreach (['page' => 'الصفحات', 'action' => 'الإجراءات', 'data' => 'البيانات', 'report' => 'التقارير'] as $type => $type_name): ?>
                            <div class="tab-pane fade <?php echo $type === 'page' ? 'show active' : ''; ?>" 
                                 id="<?php echo $type === 'page' ? 'pages' : ($type === 'action' ? 'actions' : ($type === 'data' ? 'data' : 'reports')); ?>">
                                <h6 class="mb-3"><?php echo $type_name; ?></h6>
                                <div class="row">
                                    <?php if (isset($resources[$type])): ?>
                                        <?php foreach ($resources[$type] as $resource): ?>
                                        <div class="col-md-6 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" 
                                                       name="permissions[]" 
                                                       value="<?php echo $type . ':' . $resource['resource_key']; ?>"
                                                       id="perm_<?php echo $resource['id']; ?>"
                                                       <?php echo in_array($resource['resource_key'], $current_permissions[$type] ?? []) ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="perm_<?php echo $resource['id']; ?>">
                                                    <?php if ($resource['icon']): ?>
                                                        <i class="<?php echo htmlspecialchars($resource['icon']); ?> me-2"></i>
                                                    <?php endif; ?>
                                                    <strong><?php echo htmlspecialchars($resource['resource_name']); ?></strong>
                                                    <?php if ($resource['resource_description']): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($resource['resource_description']); ?></small>
                                                    <?php endif; ?>
                                                </label>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <div class="col-12">
                                            <div class="alert alert-info">
                                                <i class="fas fa-info-circle me-2"></i>
                                                لا توجد موارد من نوع "<?php echo $type_name; ?>" في النظام حالياً.
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>

                        <!-- ملاحظات -->
                        <div class="mt-4">
                            <label class="form-label">ملاحظات التغيير</label>
                            <textarea class="form-control" name="notes" rows="3" 
                                      placeholder="اكتب سبب تغيير الصلاحيات (اختياري)"></textarea>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="d-flex justify-content-between mt-4">
                            <div>
                                <button type="button" class="btn btn-outline-success me-2" onclick="selectAll()">
                                    <i class="fas fa-check-double me-2"></i>تحديد الكل
                                </button>
                                <button type="button" class="btn btn-outline-warning" onclick="clearAll()">
                                    <i class="fas fa-times me-2"></i>إلغاء التحديد
                                </button>
                            </div>
                            <button type="submit" name="update_permissions" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ الصلاحيات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function selectAll() {
    const activeTab = document.querySelector('.tab-pane.active');
    const checkboxes = activeTab.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => checkbox.checked = true);
}

function clearAll() {
    const activeTab = document.querySelector('.tab-pane.active');
    const checkboxes = activeTab.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => checkbox.checked = false);
}
</script>

<?php include_once '../includes/footer.php'; ?>
