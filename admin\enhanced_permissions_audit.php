<?php
/**
 * سجل تدقيق الصلاحيات المحسن
 * Enhanced Permissions Audit Log
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/enhanced_permissions.php';

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من الصلاحيات
check_session();
require_permission('settings_permissions', 'read');

// معالجة الفلاتر
$filter_user = intval($_GET['user_id'] ?? 0);
$filter_action = clean_input($_GET['action_type'] ?? '');
$filter_date_from = clean_input($_GET['date_from'] ?? '');
$filter_date_to = clean_input($_GET['date_to'] ?? '');
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 50;
$offset = ($page - 1) * $per_page;

// التحقق من وجود جدول السجل وإنشاؤه إذا لم يكن موجوداً
$table_exists = false;
try {
    $check_table = $conn->query("SHOW TABLES LIKE 'permissions_audit_log'");
    $table_exists = $check_table->num_rows > 0;
    
    if (!$table_exists) {
        // إنشاء الجدول
        $create_table = "
        CREATE TABLE `permissions_audit_log` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(10) UNSIGNED NOT NULL,
            `action_type` enum('role_changed','permission_granted','permission_revoked','login','logout','access_denied') NOT NULL,
            `resource_key` varchar(100) DEFAULT NULL,
            `old_value` varchar(255) DEFAULT NULL,
            `new_value` varchar(255) DEFAULT NULL,
            `changed_by` int(10) UNSIGNED DEFAULT NULL,
            `ip_address` varchar(45) DEFAULT NULL,
            `user_agent` text DEFAULT NULL,
            `notes` text DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `user_id` (`user_id`),
            KEY `action_type` (`action_type`),
            KEY `changed_by` (`changed_by`),
            KEY `created_at` (`created_at`),
            FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
            FOREIGN KEY (`changed_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $conn->query($create_table);
        $table_exists = true;
    }
} catch (Exception $e) {
    $table_exists = false;
}

$total_records = 0;
$total_pages = 0;
$audit_result = null;

if ($table_exists) {
    // بناء استعلام السجل
    $where_conditions = [];
    $params = [];
    $param_types = '';

    if ($filter_user > 0) {
        $where_conditions[] = "pal.user_id = ?";
        $params[] = $filter_user;
        $param_types .= 'i';
    }

    if (!empty($filter_action)) {
        $where_conditions[] = "pal.action_type = ?";
        $params[] = $filter_action;
        $param_types .= 's';
    }

    if (!empty($filter_date_from)) {
        $where_conditions[] = "DATE(pal.created_at) >= ?";
        $params[] = $filter_date_from;
        $param_types .= 's';
    }

    if (!empty($filter_date_to)) {
        $where_conditions[] = "DATE(pal.created_at) <= ?";
        $params[] = $filter_date_to;
        $param_types .= 's';
    }

    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

    // استعلام العد الإجمالي
    $count_query = "
        SELECT COUNT(*) as total 
        FROM permissions_audit_log pal 
        $where_clause
    ";

    if (!empty($params)) {
        $count_stmt = $conn->prepare($count_query);
        $count_stmt->bind_param($param_types, ...$params);
        $count_stmt->execute();
        $total_records = $count_stmt->get_result()->fetch_assoc()['total'];
    } else {
        $count_result = $conn->query($count_query);
        $total_records = $count_result->fetch_assoc()['total'];
    }
    
    $total_pages = ceil($total_records / $per_page);

    // استعلام البيانات
    $audit_query = "
        SELECT pal.*, 
               u.full_name as user_name,
               u.username,
               cb.full_name as changed_by_name
        FROM permissions_audit_log pal
        LEFT JOIN users u ON pal.user_id = u.id
        LEFT JOIN users cb ON pal.changed_by = cb.id
        $where_clause
        ORDER BY pal.created_at DESC
        LIMIT ? OFFSET ?
    ";

    // إضافة معاملات الترقيم
    $final_params = $params;
    $final_params[] = $per_page;
    $final_params[] = $offset;
    $final_param_types = $param_types . 'ii';

    $audit_stmt = $conn->prepare($audit_query);
    $audit_stmt->bind_param($final_param_types, ...$final_params);
    $audit_stmt->execute();
    $audit_result = $audit_stmt->get_result();
}

// الحصول على قائمة المستخدمين للفلتر
$users_query = "SELECT id, full_name, username FROM users WHERE status = 'active' ORDER BY full_name";
$users_result = $conn->query($users_query);

$page_title = 'سجل تدقيق الصلاحيات المحسن';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-history me-2 text-info"></i><?php echo $page_title; ?></h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="../settings/">الإعدادات</a></li>
                    <li class="breadcrumb-item"><a href="enhanced_permissions_manager.php">إدارة الصلاحيات</a></li>
                    <li class="breadcrumb-item active">سجل التدقيق</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="enhanced_permissions_manager.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>العودة للصلاحيات
            </a>
        </div>
    </div>

    <?php if (!$table_exists): ?>
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>تحذير:</strong> جدول سجل التدقيق غير موجود. يرجى تشغيل ملف تحديث قاعدة البيانات أولاً.
        </div>
    <?php else: ?>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo number_format($total_records); ?></h4>
                            <p class="mb-0">إجمالي السجلات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-list fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <?php
                            $today_count = 0;
                            try {
                                $today_result = $conn->query("SELECT COUNT(*) as count FROM permissions_audit_log WHERE DATE(created_at) = CURDATE()");
                                $today_count = $today_result->fetch_assoc()['count'];
                            } catch (Exception $e) {
                                $today_count = 0;
                            }
                            ?>
                            <h4><?php echo $today_count; ?></h4>
                            <p class="mb-0">سجلات اليوم</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-day fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <?php
                            $denied_count = 0;
                            try {
                                $denied_result = $conn->query("SELECT COUNT(*) as count FROM permissions_audit_log WHERE action_type = 'access_denied' AND DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)");
                                $denied_count = $denied_result->fetch_assoc()['count'];
                            } catch (Exception $e) {
                                $denied_count = 0;
                            }
                            ?>
                            <h4><?php echo $denied_count; ?></h4>
                            <p class="mb-0">محاولات مرفوضة (7 أيام)</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-ban fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <?php
                            $active_users = 0;
                            try {
                                $active_result = $conn->query("SELECT COUNT(DISTINCT user_id) as count FROM permissions_audit_log WHERE DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)");
                                $active_users = $active_result->fetch_assoc()['count'];
                            } catch (Exception $e) {
                                $active_users = 0;
                            }
                            ?>
                            <h4><?php echo $active_users; ?></h4>
                            <p class="mb-0">مستخدمين نشطين (30 يوم)</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="card mb-4">
        <div class="card-header">
            <h5><i class="fas fa-filter me-2"></i>فلاتر البحث</h5>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="user_id" class="form-label">المستخدم</label>
                    <select class="form-select" name="user_id" id="user_id">
                        <option value="">-- جميع المستخدمين --</option>
                        <?php 
                        $users_result->data_seek(0); // إعادة تعيين المؤشر
                        while ($user = $users_result->fetch_assoc()): 
                        ?>
                            <option value="<?php echo $user['id']; ?>" 
                                    <?php echo $filter_user == $user['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($user['full_name']); ?> (@<?php echo htmlspecialchars($user['username']); ?>)
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="action_type" class="form-label">نوع العملية</label>
                    <select class="form-select" name="action_type" id="action_type">
                        <option value="">-- جميع العمليات --</option>
                        <option value="role_changed" <?php echo $filter_action === 'role_changed' ? 'selected' : ''; ?>>تغيير الدور</option>
                        <option value="permission_granted" <?php echo $filter_action === 'permission_granted' ? 'selected' : ''; ?>>منح صلاحية</option>
                        <option value="permission_revoked" <?php echo $filter_action === 'permission_revoked' ? 'selected' : ''; ?>>إلغاء صلاحية</option>
                        <option value="login" <?php echo $filter_action === 'login' ? 'selected' : ''; ?>>تسجيل دخول</option>
                        <option value="logout" <?php echo $filter_action === 'logout' ? 'selected' : ''; ?>>تسجيل خروج</option>
                        <option value="access_denied" <?php echo $filter_action === 'access_denied' ? 'selected' : ''; ?>>وصول مرفوض</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" name="date_from" id="date_from" value="<?php echo $filter_date_from; ?>">
                </div>
                <div class="col-md-2">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" name="date_to" id="date_to" value="<?php echo $filter_date_to; ?>">
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>بحث
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول السجل -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5><i class="fas fa-list me-2"></i>سجل العمليات</h5>
            <div>
                <span class="text-muted">عرض <?php echo $offset + 1; ?>-<?php echo min($offset + $per_page, $total_records); ?> من <?php echo $total_records; ?> سجل</span>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>التاريخ والوقت</th>
                            <th>المستخدم</th>
                            <th>نوع العملية</th>
                            <th>المورد</th>
                            <th>التفاصيل</th>
                            <th>من قام بالتغيير</th>
                            <th>عنوان IP</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($audit_result && $audit_result->num_rows > 0): ?>
                            <?php while ($log = $audit_result->fetch_assoc()): ?>
                            <tr>
                                <td>
                                    <small><?php echo date('Y-m-d H:i:s', strtotime($log['created_at'])); ?></small>
                                </td>
                                <td>
                                    <strong><?php echo htmlspecialchars($log['user_name'] ?: 'غير معروف'); ?></strong>
                                    <?php if ($log['username']): ?>
                                        <br><small class="text-muted">@<?php echo htmlspecialchars($log['username']); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-<?php
                                        echo match($log['action_type']) {
                                            'role_changed' => 'warning',
                                            'permission_granted' => 'success',
                                            'permission_revoked' => 'danger',
                                            'login' => 'info',
                                            'logout' => 'secondary',
                                            'access_denied' => 'danger',
                                            default => 'light'
                                        };
                                    ?>">
                                        <?php
                                        echo match($log['action_type']) {
                                            'role_changed' => 'تغيير الدور',
                                            'permission_granted' => 'منح صلاحية',
                                            'permission_revoked' => 'إلغاء صلاحية',
                                            'login' => 'تسجيل دخول',
                                            'logout' => 'تسجيل خروج',
                                            'access_denied' => 'وصول مرفوض',
                                            default => $log['action_type']
                                        };
                                        ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($log['resource_key']): ?>
                                        <code><?php echo htmlspecialchars($log['resource_key']); ?></code>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($log['old_value'] || $log['new_value']): ?>
                                        <?php if ($log['old_value']): ?>
                                            <small class="text-muted">من: <?php echo htmlspecialchars($log['old_value']); ?></small><br>
                                        <?php endif; ?>
                                        <?php if ($log['new_value']): ?>
                                            <small class="text-success">إلى: <?php echo htmlspecialchars($log['new_value']); ?></small>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                    <?php if ($log['notes']): ?>
                                        <br><small class="text-info"><?php echo htmlspecialchars($log['notes']); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($log['changed_by_name']): ?>
                                        <small><?php echo htmlspecialchars($log['changed_by_name']); ?></small>
                                    <?php else: ?>
                                        <span class="text-muted">النظام</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small class="text-muted"><?php echo htmlspecialchars($log['ip_address'] ?: '-'); ?></small>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="7" class="text-center text-muted py-4">
                                    <i class="fas fa-inbox fa-3x mb-3"></i>
                                    <br>لا توجد سجلات تطابق معايير البحث
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- ترقيم الصفحات -->
            <?php if ($total_pages > 1): ?>
                <nav aria-label="ترقيم الصفحات">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">السابق</a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">التالي</a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>

    <?php endif; ?>
</div>

<?php include_once '../includes/footer.php'; ?>
