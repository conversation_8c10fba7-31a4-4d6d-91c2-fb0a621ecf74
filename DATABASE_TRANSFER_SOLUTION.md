# حل مشكلة عدم عرض البيانات عند نقل المشروع
# Database Transfer Solution

**المشكلة:** عدم عرض البيانات المحفوظة (الغياب بالخصم، المراحل، الصفوف، الفصول) عند نقل المشروع من جهاز لآخر.

---

## 🔍 **السبب الجذري للمشكلة**

المشكلة تحدث بسبب أحد الأسباب التالية:

### **1. اسم قاعدة البيانات مختلف:**
- النظام مُعد للعمل مع قاعدة بيانات باسم `school_management`
- عند نقل المشروع، قد يتم إنشاء قاعدة بيانات باسم مختلف
- إعدادات الاتصال في `config/database.php` قد تكون خاطئة

### **2. عد<PERSON> استيراد البيانات بالكامل:**
- تم استيراد هيكل الجداول فقط بدون البيانات
- تم استيراد جزء من البيانات وليس الكل
- حدث خطأ أثناء عملية الاستيراد

### **3. مشاكل في إعدادات الخادم:**
- إعدادات MySQL مختلفة
- صلاحيات المستخدم غير كافية
- إعدادات PHP مختلفة

---

## ✅ **الحل الشامل**

### **الخطوة 1: التحقق من إعدادات قاعدة البيانات**

1. **افتح ملف `config/database.php`**
2. **تأكد من صحة الإعدادات:**

```php
// للاستضافة المحلية (Local Development)
if (!defined('DB_HOST')) define('DB_HOST', 'localhost');
if (!defined('DB_NAME')) define('DB_NAME', 'school_management');  // ⚠️ تأكد من الاسم
if (!defined('DB_USER')) define('DB_USER', 'root');
if (!defined('DB_PASSWORD')) define('DB_PASSWORD', '');
```

3. **إذا كان اسم قاعدة البيانات مختلف، غيّر `DB_NAME` ليطابق الاسم الفعلي**

### **الخطوة 2: التحقق من وجود قاعدة البيانات والبيانات**

1. **افتح phpMyAdmin أو أي أداة إدارة قواعد البيانات**
2. **تحقق من وجود قاعدة البيانات**
3. **تحقق من وجود الجداول التالية:**
   - `educational_stages` (المراحل التعليمية)
   - `grades` (الصفوف الدراسية)
   - `classes` (الفصول)
   - `staff_absences_with_deduction` (الغياب بالخصم)
   - `users` (المستخدمين)

4. **تحقق من وجود البيانات في هذه الجداول:**
```sql
SELECT COUNT(*) FROM educational_stages;
SELECT COUNT(*) FROM grades;
SELECT COUNT(*) FROM classes;
SELECT COUNT(*) FROM staff_absences_with_deduction;
```

### **الخطوة 3: إعادة استيراد قاعدة البيانات بالكامل**

إذا كانت البيانات مفقودة:

1. **احتفظ بنسخة احتياطية من البيانات الحالية (إن وجدت):**
```sql
-- تصدير البيانات الحالية
mysqldump -u root -p school_management > backup_current.sql
```

2. **احذف قاعدة البيانات الحالية وأعد إنشاؤها:**
```sql
DROP DATABASE IF EXISTS school_management;
CREATE DATABASE school_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

3. **استورد ملف قاعدة البيانات الأساسي:**
```bash
mysql -u root -p school_management < database/school_management.sql
```

### **الخطوة 4: التحقق من نجاح العملية**

1. **افتح النظام في المتصفح**
2. **سجل دخول بحساب المدير**
3. **تحقق من الصفحات التالية:**
   - `stages/index.php` - يجب أن تظهر المراحل التعليمية
   - `school_grades/index.php` - يجب أن تظهر الصفوف الدراسية
   - `classes/index.php` - يجب أن تظهر الفصول
   - `attendance/manage_absences.php` - يجب أن تظهر سجلات الغياب

---

## 🛠️ **حلول للمشاكل الشائعة**

### **المشكلة: "Database connection failed"**
**الحل:**
1. تحقق من تشغيل خدمة MySQL
2. تأكد من صحة اسم المستخدم وكلمة المرور
3. تأكد من وجود قاعدة البيانات

### **المشكلة: "Table doesn't exist"**
**الحل:**
1. تأكد من استيراد ملف `school_management.sql` بالكامل
2. تحقق من عدم وجود أخطاء أثناء الاستيراد
3. تأكد من أن اسم قاعدة البيانات صحيح

### **المشكلة: الجداول موجودة لكن فارغة**
**الحل:**
1. تحقق من أن ملف SQL يحتوي على بيانات INSERT
2. تأكد من عدم وجود أخطاء في القيود الخارجية
3. استورد البيانات يدوياً إذا لزم الأمر

### **المشكلة: بعض البيانات تظهر وبعضها لا**
**الحل:**
1. تحقق من فلاتر البحث في الصفحات
2. تأكد من صحة استعلامات SQL
3. تحقق من صلاحيات المستخدم المسجل دخوله

---

## 📋 **قائمة فحص سريعة**

عند نقل المشروع لجهاز جديد، تأكد من:

- [ ] **إعدادات قاعدة البيانات صحيحة في `config/database.php`**
- [ ] **قاعدة البيانات موجودة ويمكن الوصول إليها**
- [ ] **جميع الجداول المطلوبة موجودة**
- [ ] **البيانات موجودة في الجداول**
- [ ] **صلاحيات المجلدات صحيحة (755 للمجلدات، 644 للملفات)**
- [ ] **خدمة MySQL تعمل بشكل صحيح**
- [ ] **إصدار PHP متوافق (7.4 أو أحدث)**

---

## 🔧 **أدوات المساعدة**

### **1. فحص حالة قاعدة البيانات:**
```php
// أضف هذا الكود في أي صفحة للفحص السريع
echo "Database Host: " . DB_HOST . "<br>";
echo "Database Name: " . DB_NAME . "<br>";
echo "Database User: " . DB_USER . "<br>";
echo "Connection Status: " . (check_database_connection() ? "Connected" : "Failed") . "<br>";

// فحص الجداول
$tables = ['educational_stages', 'grades', 'classes', 'staff_absences_with_deduction'];
foreach ($tables as $table) {
    $result = $conn->query("SELECT COUNT(*) as count FROM $table");
    $count = $result ? $result->fetch_assoc()['count'] : 0;
    echo "Table $table: $count records<br>";
}
```

### **2. سكريبت إعادة تعيين قاعدة البيانات:**
```sql
-- احفظ هذا في ملف reset_database.sql
DROP DATABASE IF EXISTS school_management;
CREATE DATABASE school_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE school_management;
SOURCE database/school_management.sql;
```

---

## 🎯 **الخلاصة**

**السبب الأكثر شيوعاً** لعدم عرض البيانات عند نقل المشروع هو:
1. **اسم قاعدة البيانات مختلف** عن المُعرَّف في الإعدادات
2. **عدم استيراد البيانات بالكامل** من الجهاز الأصلي

**الحل الأسرع:**
1. تأكد من أن اسم قاعدة البيانات في `config/database.php` يطابق الاسم الفعلي
2. أعد استيراد ملف `database/school_management.sql` بالكامل
3. تحقق من ظهور البيانات في النظام

**إذا استمرت المشكلة:**
- تحقق من سجلات الأخطاء في مجلد `logs/`
- تأكد من صلاحيات قاعدة البيانات
- جرب إنشاء قاعدة بيانات جديدة بالاسم الصحيح
