-- نظام صلاحيات بسيط ومخصص
-- Simple Custom Permissions System

-- جدول الصلاحيات المخصصة للمستخدمين
CREATE TABLE IF NOT EXISTS `user_page_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL,
  `page_name` varchar(100) NOT NULL COMMENT 'اسم الصفحة أو المسار',
  `can_view` tinyint(1) DEFAULT 0 COMMENT 'صلاحية المشاهدة',
  `can_add` tinyint(1) DEFAULT 0 COMMENT 'صلاحية الإضافة',
  `can_edit` tinyint(1) DEFAULT 0 COMMENT 'صلاحية التعديل',
  `can_delete` tinyint(1) DEFAULT 0 COMMENT 'صلاحية الحذف',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_page_unique` (`user_id`, `page_name`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_page_name` (`page_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الصفحات المتاحة
CREATE TABLE IF NOT EXISTS `available_pages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `page_name` varchar(100) NOT NULL COMMENT 'اسم الصفحة',
  `page_title` varchar(200) NOT NULL COMMENT 'عنوان الصفحة',
  `page_path` varchar(255) NOT NULL COMMENT 'مسار الصفحة',
  `category` varchar(50) DEFAULT NULL COMMENT 'فئة الصفحة',
  `description` text DEFAULT NULL COMMENT 'وصف الصفحة',
  `requires_add` tinyint(1) DEFAULT 0 COMMENT 'تحتاج صلاحية إضافة',
  `requires_edit` tinyint(1) DEFAULT 0 COMMENT 'تحتاج صلاحية تعديل',
  `requires_delete` tinyint(1) DEFAULT 0 COMMENT 'تحتاج صلاحية حذف',
  `is_active` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`id`),
  UNIQUE KEY `page_name_unique` (`page_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج الصفحات الأساسية
INSERT INTO `available_pages` (`page_name`, `page_title`, `page_path`, `category`, `description`, `requires_add`, `requires_edit`, `requires_delete`) VALUES
('students', 'إدارة الطلاب', '/students/', 'الطلاب', 'عرض وإدارة بيانات الطلاب', 1, 1, 1),
('attendance', 'الحضور والغياب', '/attendance/', 'الحضور', 'تسجيل ومتابعة حضور الطلاب', 1, 1, 0),
('grades', 'الدرجات', '/grades/', 'الدرجات', 'إدارة درجات الطلاب', 0, 1, 0),
('finance', 'الشؤون المالية', '/finance/', 'المالية', 'إدارة الرسوم والمدفوعات', 1, 1, 1),
('reports', 'التقارير', '/reports/', 'التقارير', 'عرض وطباعة التقارير', 0, 0, 0),
('users', 'إدارة المستخدمين', '/admin/users/', 'الإدارة', 'إدارة حسابات المستخدمين', 1, 1, 1),
('settings', 'الإعدادات', '/settings/', 'الإدارة', 'إعدادات النظام', 0, 1, 0),
('exams', 'الامتحانات', '/exams/', 'الامتحانات', 'إدارة الامتحانات والنتائج', 1, 1, 1),
('library', 'المكتبة', '/library/', 'المكتبة', 'إدارة الكتب والمراجع', 1, 1, 1),
('communication', 'التواصل', '/communication/', 'التواصل', 'الرسائل والإشعارات', 1, 0, 1);

-- جدول سجل العمليات البسيط
CREATE TABLE IF NOT EXISTS `permissions_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL,
  `action` varchar(50) NOT NULL,
  `page_name` varchar(100) DEFAULT NULL,
  `details` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة بعض الصلاحيات التجريبية (اختياري)
-- يمكن حذف هذا القسم إذا كنت تريد البدء من الصفر

-- مثال: إعطاء مستخدم معين صلاحيات محددة
-- INSERT INTO `user_page_permissions` (`user_id`, `page_name`, `can_view`, `can_add`, `can_edit`, `can_delete`, `notes`) VALUES
-- (2, 'students', 1, 1, 1, 0, 'صلاحيات إدارة الطلاب بدون حذف'),
-- (2, 'attendance', 1, 1, 1, 0, 'صلاحيات الحضور والغياب'),
-- (2, 'grades', 1, 0, 1, 0, 'صلاحية تعديل الدرجات فقط'),
-- (3, 'reports', 1, 0, 0, 0, 'صلاحية عرض التقارير فقط'),
-- (3, 'students', 1, 0, 0, 0, 'صلاحية عرض بيانات الطلاب فقط');

-- فهارس إضافية لتحسين الأداء
CREATE INDEX idx_user_permissions ON user_page_permissions(user_id, page_name);
CREATE INDEX idx_page_permissions ON user_page_permissions(page_name, can_view, can_add, can_edit, can_delete);
