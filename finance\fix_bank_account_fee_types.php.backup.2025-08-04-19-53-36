<?php
/**
 * إصلاح جدول bank_account_fee_types المفقود
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    die('غير مسموح لك بالوصول لهذه الصفحة');
}

echo "<h2>إصلاح جدول bank_account_fee_types</h2>";

try {
    // التحقق من وجود الجدول
    $table_check = $conn->query("SHOW TABLES LIKE 'bank_account_fee_types'");
    
    if ($table_check->num_rows == 0) {
        echo "<p>جدول bank_account_fee_types غير موجود. سيتم إنشاؤه...</p>";
        
        // إنشاء جدول bank_account_fee_types
        $create_table_sql = "
        CREATE TABLE `bank_account_fee_types` (
            `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
            `bank_account_id` int(10) UNSIGNED NOT NULL,
            `fee_type_id` int(10) UNSIGNED NOT NULL,
            `is_active` tinyint(1) NOT NULL DEFAULT 1,
            `created_at` datetime DEFAULT current_timestamp(),
            `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_bank_fee_type` (`bank_account_id`, `fee_type_id`),
            KEY `idx_bank_account_id` (`bank_account_id`),
            KEY `idx_fee_type_id` (`fee_type_id`),
            KEY `idx_is_active` (`is_active`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='ربط الحسابات البنكية بأنواع الرسوم'
        ";
        
        if ($conn->query($create_table_sql)) {
            echo "<div class='alert alert-success'>✅ تم إنشاء جدول bank_account_fee_types بنجاح</div>";
        } else {
            throw new Exception("فشل في إنشاء الجدول: " . $conn->error);
        }
        
        // التحقق من وجود جدول bank_accounts
        $bank_accounts_check = $conn->query("SHOW TABLES LIKE 'bank_accounts'");
        if ($bank_accounts_check->num_rows == 0) {
            echo "<p>جدول bank_accounts غير موجود. سيتم إنشاؤه...</p>";
            
            $create_bank_accounts_sql = "
            CREATE TABLE `bank_accounts` (
                `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
                `account_name` varchar(255) NOT NULL,
                `account_number` varchar(100) NOT NULL,
                `bank_name` varchar(255) NOT NULL,
                `branch_name` varchar(255) DEFAULT NULL,
                `account_type` enum('current','savings','business') NOT NULL DEFAULT 'current',
                `currency` varchar(10) NOT NULL DEFAULT 'EGP',
                `balance` decimal(15,2) NOT NULL DEFAULT 0.00,
                `is_active` tinyint(1) NOT NULL DEFAULT 1,
                `notes` text DEFAULT NULL,
                `created_at` datetime DEFAULT current_timestamp(),
                `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                PRIMARY KEY (`id`),
                UNIQUE KEY `unique_account_number` (`account_number`, `bank_name`),
                KEY `idx_account_name` (`account_name`),
                KEY `idx_bank_name` (`bank_name`),
                KEY `idx_is_active` (`is_active`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='الحسابات البنكية'
            ";
            
            if ($conn->query($create_bank_accounts_sql)) {
                echo "<div class='alert alert-success'>✅ تم إنشاء جدول bank_accounts بنجاح</div>";
                
                // إدراج بيانات تجريبية
                $sample_accounts = [
                    ['الحساب الرئيسي', '*********', 'البنك الأهلي المصري', 'فرع المعادي', 'current', 'EGP', 50000.00],
                    ['حساب الرسوم الدراسية', '*********', 'بنك مصر', 'فرع وسط البلد', 'business', 'EGP', 25000.00],
                    ['حساب الطوارئ', '*********', 'بنك القاهرة', 'فرع النزهة', 'savings', 'EGP', 10000.00]
                ];
                
                $insert_account_sql = "INSERT INTO bank_accounts (account_name, account_number, bank_name, branch_name, account_type, currency, balance) VALUES (?, ?, ?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($insert_account_sql);
                
                foreach ($sample_accounts as $account) {
                    $stmt->bind_param("ssssssd", $account[0], $account[1], $account[2], $account[3], $account[4], $account[5], $account[6]);
                    $stmt->execute();
                }
                
                echo "<div class='alert alert-info'>✅ تم إدراج " . count($sample_accounts) . " حسابات بنكية تجريبية</div>";
            }
        }
        
        // التحقق من وجود جدول fee_types
        $fee_types_check = $conn->query("SHOW TABLES LIKE 'fee_types'");
        if ($fee_types_check->num_rows == 0) {
            echo "<p>جدول fee_types غير موجود. سيتم إنشاؤه...</p>";
            
            $create_fee_types_sql = "
            CREATE TABLE `fee_types` (
                `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
                `name` varchar(255) NOT NULL,
                `description` text DEFAULT NULL,
                `amount` decimal(10,2) NOT NULL DEFAULT 0.00,
                `is_recurring` tinyint(1) NOT NULL DEFAULT 0,
                `recurring_period` enum('monthly','quarterly','semester','yearly') DEFAULT NULL,
                `is_active` tinyint(1) NOT NULL DEFAULT 1,
                `created_at` datetime DEFAULT current_timestamp(),
                `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                PRIMARY KEY (`id`),
                UNIQUE KEY `unique_fee_name` (`name`),
                KEY `idx_is_active` (`is_active`),
                KEY `idx_is_recurring` (`is_recurring`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='أنواع الرسوم'
            ";
            
            if ($conn->query($create_fee_types_sql)) {
                echo "<div class='alert alert-success'>✅ تم إنشاء جدول fee_types بنجاح</div>";
                
                // إدراج أنواع رسوم تجريبية
                $sample_fee_types = [
                    ['الرسوم الدراسية', 'الرسوم الدراسية الأساسية', 5000.00, 1, 'semester'],
                    ['رسوم الكتب', 'رسوم الكتب والمواد التعليمية', 500.00, 1, 'yearly'],
                    ['رسوم النشاطات', 'رسوم الأنشطة الطلابية', 300.00, 1, 'semester'],
                    ['رسوم الامتحانات', 'رسوم الامتحانات النهائية', 200.00, 1, 'semester'],
                    ['رسوم التأخير', 'رسوم التأخير في السداد', 100.00, 0, null]
                ];
                
                $insert_fee_sql = "INSERT INTO fee_types (name, description, amount, is_recurring, recurring_period) VALUES (?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($insert_fee_sql);
                
                foreach ($sample_fee_types as $fee) {
                    $stmt->bind_param("ssdis", $fee[0], $fee[1], $fee[2], $fee[3], $fee[4]);
                    $stmt->execute();
                }
                
                echo "<div class='alert alert-info'>✅ تم إدراج " . count($sample_fee_types) . " أنواع رسوم تجريبية</div>";
            }
        }
        
        // ربط الحسابات البنكية بأنواع الرسوم (بيانات تجريبية)
        $bank_accounts = $conn->query("SELECT id FROM bank_accounts LIMIT 3");
        $fee_types = $conn->query("SELECT id FROM fee_types LIMIT 5");
        
        if ($bank_accounts->num_rows > 0 && $fee_types->num_rows > 0) {
            $bank_ids = [];
            while ($row = $bank_accounts->fetch_assoc()) {
                $bank_ids[] = $row['id'];
            }
            
            $fee_ids = [];
            $fee_types->data_seek(0);
            while ($row = $fee_types->fetch_assoc()) {
                $fee_ids[] = $row['id'];
            }
            
            // ربط كل حساب بنكي ببعض أنواع الرسوم
            $insert_relation_sql = "INSERT IGNORE INTO bank_account_fee_types (bank_account_id, fee_type_id) VALUES (?, ?)";
            $stmt = $conn->prepare($insert_relation_sql);
            
            $relations_count = 0;
            foreach ($bank_ids as $bank_id) {
                foreach ($fee_ids as $fee_id) {
                    // ربط عشوائي (70% احتمال الربط)
                    if (rand(1, 10) <= 7) {
                        $stmt->bind_param("ii", $bank_id, $fee_id);
                        if ($stmt->execute()) {
                            $relations_count++;
                        }
                    }
                }
            }
            
            echo "<div class='alert alert-info'>✅ تم إنشاء $relations_count علاقة ربط بين الحسابات البنكية وأنواع الرسوم</div>";
        }
        
    } else {
        echo "<div class='alert alert-info'>✅ جدول bank_account_fee_types موجود بالفعل</div>";
    }
    
    // عرض إحصائيات
    echo "<hr>";
    echo "<h3>إحصائيات الجداول:</h3>";
    
    $tables_stats = [
        'bank_accounts' => 'الحسابات البنكية',
        'fee_types' => 'أنواع الرسوم',
        'bank_account_fee_types' => 'ربط الحسابات بالرسوم'
    ];
    
    foreach ($tables_stats as $table => $description) {
        $count_result = $conn->query("SELECT COUNT(*) as count FROM $table");
        if ($count_result) {
            $count = $count_result->fetch_assoc()['count'];
            echo "<p><strong>$description ($table):</strong> $count سجل</p>";
        }
    }
    
    echo "<hr>";
    echo "<div class='alert alert-success'>";
    echo "<h4>✅ تم إصلاح جميع المشاكل بنجاح!</h4>";
    echo "<p>يمكنك الآن الوصول لصفحات المالية بدون أخطاء.</p>";
    echo "</div>";
    
    echo "<div class='mt-3'>";
    echo "<a href='installments/bank_accounts.php' class='btn btn-primary me-2'>اختبار الحسابات البنكية</a>";
    echo "<a href='installments/add.php' class='btn btn-success me-2'>اختبار إضافة قسط</a>";
    echo "<a href='../dashboard/' class='btn btn-secondary'>العودة للوحة التحكم</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ حدث خطأ:</h4>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>
