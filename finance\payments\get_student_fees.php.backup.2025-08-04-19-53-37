<?php
/**
 * AJAX handler to fetch student fees and information.
 */

header('Content-Type: application/json');
require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/security.php';

// Basic security: ensure user is logged in
check_session();
if (!check_permission('admin')) {
    echo json_encode(['error' => 'Permission denied']);
    exit();
}

$student_id = intval($_GET['student_id'] ?? 0);

if (empty($student_id)) {
    echo json_encode(['error' => 'Invalid student ID']);
    exit();
}

$response = [
    'fees' => [],
    'student_info' => '',
    'outstanding_fees' => ''
];

// 1. Fetch Student Information
$student_stmt = $conn->prepare("
    SELECT u.full_name, s.national_id, c.class_name 
    FROM students s 
    LEFT JOIN users u ON s.user_id = u.id 
    LEFT JOIN classes c ON s.class_id = c.id 
    WHERE s.id = ?
");
$student_stmt->bind_param('i', $student_id);
$student_stmt->execute();
$student_result = $student_stmt->get_result();
if ($student = $student_result->fetch_assoc()) {
    $response['student_info'] = 
        '<p><strong>' . __('student_name') . ':</strong> ' . htmlspecialchars($student['full_name']) . '</p>' . 
        '<p><strong>' . __('national_id') . ':</strong> ' . htmlspecialchars($student['national_id']) . '</p>' . 
        '<p><strong>' . __('class') . ':</strong> ' . htmlspecialchars($student['class_name']) . '</p>';
}

// 2. Fetch Unpaid Fees
$fees_stmt = $conn->prepare("
    SELECT 
        sf.id, 
        ft.type_name, 
        sf.academic_year, 
        sf.semester,
        sf.final_amount,
        COALESCE(SUM(sp.amount), 0) as paid_amount,
        (sf.final_amount - COALESCE(SUM(sp.amount), 0)) as remaining_amount
    FROM student_fees sf
    JOIN fee_types ft ON sf.fee_type_id = ft.id
    LEFT JOIN student_payments sp ON sf.id = sp.student_fee_id AND sp.status = 'confirmed'
    WHERE sf.student_id = ?
    GROUP BY sf.id
    HAVING remaining_amount > 0
    ORDER BY sf.due_date ASC
");
$fees_stmt->bind_param('i', $student_id);
$fees_stmt->execute();
$fees_result = $fees_stmt->get_result();

$outstanding_html = '<ul class="list-group">';
$total_due = 0;

while ($fee = $fees_result->fetch_assoc()) {
    $response['fees'][] = $fee;
    $total_due += $fee['remaining_amount'];
    $outstanding_html .= '<li class="list-group-item d-flex justify-content-between align-items-center">' . 
        htmlspecialchars($fee['type_name']) . ' (' . $fee['academic_year'] . ')' . 
        '<span class="badge bg-danger rounded-pill">' . number_format($fee['remaining_amount'], 2) . ' ' . get_system_setting('currency_symbol', 'ر.س') . '</span>' .
    '</li>';
}

if ($fees_result->num_rows > 0) {
    $outstanding_html .= '<li class="list-group-item d-flex justify-content-between align-items-center active">' . 
        '<strong>' . __('total_due') . '</strong>' . 
        '<strong>' . number_format($total_due, 2) . ' ' . CURRENCY_SYMBOL . '</strong>' . 
    '</li>';
} else {
    $outstanding_html .= '<li class="list-group-item">' . __('no_outstanding_fees') . '</li>';
}

$outstanding_html .= '</ul>';
$response['outstanding_fees'] = $outstanding_html;

echo json_encode($response);
