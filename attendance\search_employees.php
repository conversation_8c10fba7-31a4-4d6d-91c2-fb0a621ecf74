<?php
/**
 * البحث الذكي للموظفين
 * Smart Employee Search
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بالوصول']);
    exit();
}

// التحقق من وجود مصطلح البحث
if (!isset($_GET['term']) || empty(trim($_GET['term']))) {
    echo json_encode(['success' => false, 'message' => 'مصطلح البحث مطلوب']);
    exit();
}

$search_term = trim($_GET['term']);

try {
    // البحث في الموظفين (المعلمين والإداريين)
    $search_query = "
        SELECT DISTINCT u.id, u.full_name, u.role
        FROM users u
        WHERE u.status = 'active'
        AND u.role IN ('teacher', 'staff')
        AND u.role NOT IN ('admin', 'system_admin')
        AND u.full_name LIKE ?
        ORDER BY u.role, u.full_name ASC
        LIMIT 10
    ";
    
    $stmt = $conn->prepare($search_query);
    $search_param = '%' . $search_term . '%';
    $stmt->bind_param("s", $search_param);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $employees = [];
    while ($row = $result->fetch_assoc()) {
        $employees[] = [
            'id' => $row['id'],
            'full_name' => $row['full_name'],
            'role' => $row['role']
        ];
    }
    
    echo json_encode([
        'success' => true,
        'employees' => $employees,
        'count' => count($employees)
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'خطأ في البحث: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
