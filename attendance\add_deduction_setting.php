<?php
/**
 * صفحة إضافة إعداد خصم جديد
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    redirect_to_login();
}

$user_id = $_SESSION['user_id'];
$error_message = '';
$success_message = '';

// معالجة إضافة الإعداد
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'طلب غير صالح';
    } else {
        try {
            $absence_type = clean_input($_POST['absence_type']);
            $staff_role = clean_input($_POST['staff_role']);
            $deduction_value = floatval($_POST['deduction_value']);
            $deduction_type = clean_input($_POST['deduction_type']);
            $max_allowed = !empty($_POST['max_allowed_per_month']) ? intval($_POST['max_allowed_per_month']) : null;
            $requires_approval = isset($_POST['requires_approval']) ? 1 : 0;
            $description = clean_input($_POST['description']);
            $is_active = isset($_POST['is_active']) ? 1 : 0;

            // التحقق من عدم وجود إعداد مشابه
            $check_stmt = $conn->prepare("SELECT id FROM deduction_settings WHERE absence_type = ? AND staff_role = ?");
            $check_stmt->bind_param("ss", $absence_type, $staff_role);
            $check_stmt->execute();
            
            if ($check_stmt->get_result()->num_rows > 0) {
                $error_message = 'يوجد إعداد مشابه لهذا النوع والفئة بالفعل';
            } else {
                $stmt = $conn->prepare("
                    INSERT INTO deduction_settings 
                    (absence_type, staff_role, deduction_value, deduction_type, max_allowed_per_month, 
                     requires_approval, description, is_active, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
                ");
                $stmt->bind_param("ssdsisii", $absence_type, $staff_role, $deduction_value, $deduction_type, 
                                $max_allowed, $requires_approval, $description, $is_active);
                
                if ($stmt->execute()) {
                    $success_message = 'تم إضافة إعداد الخصم بنجاح';
                    
                    // تسجيل النشاط
                    log_activity($user_id, 'add_deduction_setting', 'deduction_settings', $conn->insert_id);
                    
                    // إعادة توجيه بعد 2 ثانية
                    header("refresh:2;url=deduction_settings.php");
                } else {
                    $error_message = 'حدث خطأ أثناء إضافة الإعداد';
                }
            }
            
        } catch (Exception $e) {
            $error_message = 'حدث خطأ: ' . $e->getMessage();
        }
    }
}

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-plus me-2"></i>
                            إضافة إعداد خصم جديد
                        </h5>
                        <a href="deduction_settings.php" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left me-2"></i>رجوع للإعدادات
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if ($success_message): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                            <br><small>سيتم توجيهك لصفحة الإعدادات خلال ثانيتين...</small>
                        </div>
                    <?php endif; ?>

                    <?php if ($error_message): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST" class="needs-validation" novalidate>
                        <?php echo csrf_token_field(); ?>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">نوع المخالفة <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="absence_type" required 
                                       placeholder="مثال: غياب بدون عذر" value="<?php echo htmlspecialchars($_POST['absence_type'] ?? ''); ?>">
                                <div class="invalid-feedback">
                                    يرجى إدخال نوع المخالفة
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الفئة المستهدفة <span class="text-danger">*</span></label>
                                <select class="form-select" name="staff_role" required>
                                    <option value="">اختر الفئة</option>
                                    <option value="teacher" <?php echo ($_POST['staff_role'] ?? '') === 'teacher' ? 'selected' : ''; ?>>المعلمين فقط</option>
                                    <option value="staff" <?php echo ($_POST['staff_role'] ?? '') === 'staff' ? 'selected' : ''; ?>>الإداريين فقط</option>
                                    <option value="all" <?php echo ($_POST['staff_role'] ?? '') === 'all' ? 'selected' : ''; ?>>جميع الموظفين</option>
                                </select>
                                <div class="invalid-feedback">
                                    يرجى اختيار الفئة المستهدفة
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">نوع الخصم <span class="text-danger">*</span></label>
                                <select class="form-select" name="deduction_type" id="deduction_type" required>
                                    <option value="fixed" <?php echo ($_POST['deduction_type'] ?? 'fixed') === 'fixed' ? 'selected' : ''; ?>>مبلغ ثابت</option>
                                    <option value="percentage" <?php echo ($_POST['deduction_type'] ?? '') === 'percentage' ? 'selected' : ''; ?>>نسبة مئوية</option>
                                    <option value="daily_rate" <?php echo ($_POST['deduction_type'] ?? '') === 'daily_rate' ? 'selected' : ''; ?>>معدل يومي</option>
                                    <option value="hourly_rate" <?php echo ($_POST['deduction_type'] ?? '') === 'hourly_rate' ? 'selected' : ''; ?>>معدل ساعي</option>
                                </select>
                                <div class="invalid-feedback">
                                    يرجى اختيار نوع الخصم
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">قيمة الخصم <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" class="form-control" name="deduction_value" 
                                           step="0.01" min="0" required value="<?php echo htmlspecialchars($_POST['deduction_value'] ?? ''); ?>">
                                    <span class="input-group-text" id="currency-symbol">ج.م</span>
                                </div>
                                <div class="invalid-feedback">
                                    يرجى إدخال قيمة الخصم
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الحد الأقصى شهرياً</label>
                                <input type="number" class="form-control" name="max_allowed_per_month" 
                                       min="1" placeholder="اتركه فارغاً لعدم التحديد" value="<?php echo htmlspecialchars($_POST['max_allowed_per_month'] ?? ''); ?>">
                                <small class="form-text text-muted">عدد المرات المسموح بها شهرياً لتطبيق هذا الخصم</small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="mt-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="requires_approval" id="requires_approval" 
                                               <?php echo isset($_POST['requires_approval']) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="requires_approval">
                                            يتطلب موافقة مسبقة
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="is_active" id="is_active" 
                                               <?php echo !isset($_POST['is_active']) || isset($_POST['is_active']) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="is_active">
                                            نشط
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <label class="form-label">الوصف</label>
                                <textarea class="form-control" name="description" rows="3" 
                                          placeholder="وصف تفصيلي للمخالفة وشروط تطبيق الخصم"><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="deduction_settings.php" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ الإعداد
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تغيير رمز العملة حسب نوع الخصم
    const deductionTypeSelect = document.getElementById('deduction_type');
    const currencySymbol = document.getElementById('currency-symbol');
    
    function updateCurrencySymbol() {
        const value = deductionTypeSelect.value;
        currencySymbol.textContent = value === 'percentage' ? '%' : 'ج.م';
    }
    
    deductionTypeSelect.addEventListener('change', updateCurrencySymbol);
    updateCurrencySymbol(); // تحديث أولي

    // التحقق من صحة النموذج
    const form = document.querySelector('.needs-validation');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
});
</script>

<?php require_once '../includes/footer.php'; ?>
