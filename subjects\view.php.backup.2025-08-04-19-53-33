<?php
/**
 * صفحة عرض تفاصيل المادة الدراسية
 * Subject Details View Page
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

$subject_id = intval($_GET['id'] ?? 0);

if ($subject_id <= 0) {
    $_SESSION['error_message'] = 'معرف المادة غير صحيح';
    header('Location: index.php');
    exit();
}

// جلب بيانات المادة مع المرحلة والصف
$subject_query = "
    SELECT
        s.*,
        es.stage_name,
        g.grade_name
    FROM subjects s
    LEFT JOIN educational_stages es ON s.stage_id = es.id
    LEFT JOIN grades g ON s.grade_id = g.id
    WHERE s.id = ?
";

$stmt = $conn->prepare($subject_query);
if (!$stmt) {
    $_SESSION['error_message'] = 'خطأ في قاعدة البيانات';
    header('Location: index.php');
    exit();
}

$stmt->bind_param('i', $subject_id);
$stmt->execute();
$result = $stmt->get_result();
$subject = $result->fetch_assoc();
$stmt->close();

if (!$subject) {
    $_SESSION['error_message'] = 'المادة غير موجودة';
    header('Location: index.php');
    exit();
}

// جلب الفصول المرتبطة بالمادة - نفس بنية استعلام المعلمين تماماً
$classes_query = "
    SELECT DISTINCT
        c.id,
        c.class_name,
        COALESCE(c.section, 'أ') as section,
        COALESCE(g.grade_name, 'غير محدد') as grade_name,
        COALESCE(es.stage_name, 'غير محدد') as stage_name,
        COUNT(DISTINCT ta.teacher_id) as teachers_count,
        SUM(ta.weekly_hours) as total_hours
    FROM teacher_assignments ta
    JOIN classes c ON ta.class_id = c.id
    LEFT JOIN grades g ON c.grade_id = g.id
    LEFT JOIN educational_stages es ON c.stage_id = es.id
    WHERE ta.subject_id = ? AND ta.status = 'active'
    GROUP BY c.id, c.class_name, c.section, g.grade_name, es.stage_name
    ORDER BY c.class_name
";

$classes_stmt = $conn->prepare($classes_query);
if (!$classes_stmt) {
    error_log("ERROR: Classes query prepare failed: " . $conn->error);
    $related_classes = [];
} else {
    $classes_stmt->bind_param('i', $subject_id);
    $classes_stmt->execute();
    $classes_result = $classes_stmt->get_result();
    $related_classes = [];
    while ($class = $classes_result->fetch_assoc()) {
        $related_classes[] = $class;
    }
    $classes_stmt->close();
}

// جلب المعلمين المرتبطين بالمادة حقيقياً
$teachers_query = "
    SELECT DISTINCT
        t.id,
        u.full_name,
        COALESCE(u.email, 'غير محدد') as email,
        COALESCE(u.phone, 'غير محدد') as phone,
        COUNT(DISTINCT ta.class_id) as classes_taught,
        SUM(ta.weekly_hours) as total_hours
    FROM teacher_assignments ta
    JOIN teachers t ON ta.teacher_id = t.id
    JOIN users u ON t.user_id = u.id
    WHERE ta.subject_id = ? AND ta.status = 'active'
    GROUP BY t.id, u.full_name, u.email, u.phone
    ORDER BY u.full_name
";

$teachers_stmt = $conn->prepare($teachers_query);
$teachers_stmt->bind_param('i', $subject_id);
$teachers_stmt->execute();
$teachers_result = $teachers_stmt->get_result();
$related_teachers = [];
while ($teacher = $teachers_result->fetch_assoc()) {
    $related_teachers[] = $teacher;
}
$teachers_stmt->close();

// تسجيل مقارن للاستعلامين - نفس البنية
error_log("=== SUBJECT VIEW DEBUG ===");
error_log("Subject ID: $subject_id");
error_log("Classes Query Structure: GROUP BY with COUNT and SUM (same as teachers)");
error_log("Teachers Query Structure: GROUP BY with COUNT and SUM");
error_log("Found classes: " . count($related_classes));
error_log("Found teachers: " . count($related_teachers));

if (!empty($related_classes)) {
    error_log("SUCCESS: Classes query working - found " . count($related_classes) . " classes");
    foreach ($related_classes as $i => $class) {
        error_log("Class $i: ID={$class['id']}, Name={$class['class_name']}, Section={$class['section']}");
    }
} else {
    error_log("ISSUE: Classes query returned no results");
    // فحص سريع للتكليفات
    $quick_check = $conn->query("SELECT COUNT(*) as count FROM teacher_assignments WHERE subject_id = $subject_id AND status = 'active'")->fetch_assoc()['count'];
    error_log("Quick check: $quick_check active assignments exist for subject $subject_id");

    // فحص مفصل للاستعلام
    error_log("Testing simplified classes query...");
    $test_query = "SELECT ta.class_id, c.class_name FROM teacher_assignments ta LEFT JOIN classes c ON ta.class_id = c.id WHERE ta.subject_id = $subject_id AND ta.status = 'active'";
    $test_result = $conn->query($test_query);
    if ($test_result && $test_result->num_rows > 0) {
        error_log("Raw data found:");
        while ($row = $test_result->fetch_assoc()) {
            error_log("  class_id={$row['class_id']}, class_name=" . ($row['class_name'] ?? 'NULL'));
        }
    } else {
        error_log("No raw data found at all");
    }
}

if (!empty($related_teachers)) {
    error_log("SUCCESS: Teachers query working - found " . count($related_teachers) . " teachers");
} else {
    error_log("ISSUE: Teachers query returned no results");
}
error_log("=== END SUBJECT VIEW DEBUG ===");

$page_title = 'تفاصيل المادة: ' . $subject['subject_name'];
require_once '../includes/header.php';

// تحديد لون المادة حسب النوع
$subject_colors = [
    'أساسية' => 'primary',
    'اختيارية' => 'success',
    'نشاط' => 'warning',
    'تخصص' => 'info'
];
$color = isset($subject['subject_type']) ? ($subject_colors[$subject['subject_type']] ?? 'secondary') : 'secondary';

// تحديد أيقونة المادة
$subject_icons = [
    'رياضيات' => 'fas fa-calculator',
    'علوم' => 'fas fa-flask',
    'لغة عربية' => 'fas fa-book',
    'لغة إنجليزية' => 'fas fa-language',
    'تاريخ' => 'fas fa-landmark',
    'جغرافيا' => 'fas fa-globe',
    'فيزياء' => 'fas fa-atom',
    'كيمياء' => 'fas fa-vial',
    'أحياء' => 'fas fa-dna',
    'حاسوب' => 'fas fa-laptop',
    'فنون' => 'fas fa-palette',
    'موسيقى' => 'fas fa-music',
    'رياضة' => 'fas fa-running'
];
$icon = $subject_icons[$subject['subject_name']] ?? 'fas fa-book-open';
?>

<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="<?php echo $icon; ?> fa-3x text-<?php echo $color; ?>"></i>
                            </div>
                            <div>
                                <h1 class="h3 mb-1"><?php echo htmlspecialchars($subject['subject_name']); ?></h1>
                                <p class="text-muted mb-0">
                                    <i class="fas fa-tag me-1"></i>
                                    <?php echo htmlspecialchars($subject['subject_code'] ?? 'غير محدد'); ?>
                                </p>
                            </div>
                        </div>
                        <div class="btn-group">
                            <a href="edit.php?id=<?php echo $subject['id']; ?>" class="btn btn-warning">
                                <i class="fas fa-edit me-2"></i>تعديل
                            </a>
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-2"></i>رجوع
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- معلومات المادة الأساسية -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-<?php echo $color; ?> text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>معلومات المادة الأساسية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="40%">اسم المادة:</th>
                                    <td><?php echo htmlspecialchars($subject['subject_name']); ?></td>
                                </tr>
                                <tr>
                                    <th>كود المادة:</th>
                                    <td><?php echo htmlspecialchars($subject['subject_code'] ?? 'غير محدد'); ?></td>
                                </tr>
                                <tr>
                                    <th>نوع المادة:</th>
                                    <td>
                                        <span class="badge bg-<?php echo $color; ?>">
                                            <?php echo htmlspecialchars($subject['subject_type'] ?? 'أساسية'); ?>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>المرحلة التعليمية:</th>
                                    <td><?php echo htmlspecialchars($subject['stage_name'] ?? 'غير محدد'); ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="40%">الصف الدراسي:</th>
                                    <td><?php echo htmlspecialchars($subject['grade_name'] ?? 'غير محدد'); ?></td>
                                </tr>
                                <tr>
                                    <th>عدد الساعات:</th>
                                    <td><?php echo $subject['hours_per_week'] ?? 0; ?> ساعة أسبوعياً</td>
                                </tr>
                                <tr>
                                    <th>الحالة:</th>
                                    <td>
                                        <?php if ($subject['status'] === 'active'): ?>
                                            <span class="badge bg-success">نشط</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">غير نشط</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>تاريخ الإنشاء:</th>
                                    <td><?php echo date('d/m/Y', strtotime($subject['created_at'])); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <?php if (!empty($subject['description'])): ?>
                    <div class="mt-3">
                        <h6>وصف المادة:</h6>
                        <p class="text-muted"><?php echo nl2br(htmlspecialchars($subject['description'])); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>إحصائيات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h3 class="text-primary mb-1"><?php echo count($related_classes); ?></h3>
                                <small class="text-muted">فصل دراسي</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h3 class="text-success mb-1"><?php echo count($related_teachers); ?></h3>
                            <small class="text-muted">معلم</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الفصول المرتبطة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chalkboard-teacher me-2"></i>الفصول المرتبطة بالمادة
                        <span class="badge bg-light text-dark ms-2"><?php echo count($related_classes); ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($related_classes)): ?>
                        <div class="row">
                            <?php foreach ($related_classes as $class): ?>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card border-success">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <i class="fas fa-users me-2"></i>
                                                <?php echo htmlspecialchars($class['class_name']); ?>
                                            </h6>
                                            <p class="card-text small text-muted mb-2">
                                                <strong>المرحلة:</strong> <?php echo htmlspecialchars($class['stage_name'] ?? 'غير محدد'); ?><br>
                                                <strong>الصف:</strong> <?php echo htmlspecialchars($class['grade_name'] ?? 'غير محدد'); ?><br>
                                                <strong>الشعبة:</strong> <?php echo htmlspecialchars($class['section'] ?? 'غير محدد'); ?>
                                            </p>
                                            <div class="row text-center">
                                                <div class="col-6">
                                                    <small class="text-primary">
                                                        <i class="fas fa-chalkboard-teacher me-1"></i>
                                                        <?php echo $class['teachers_count']; ?> معلم
                                                    </small>
                                                </div>
                                                <div class="col-6">
                                                    <small class="text-warning">
                                                        <i class="fas fa-clock me-1"></i>
                                                        <?php echo $class['total_hours']; ?> ساعة
                                                    </small>
                                                </div>
                                            </div>
                                            <div class="text-center mt-2">
                                                <a href="../classes/view.php?id=<?php echo $class['id']; ?>"
                                                   class="btn btn-sm btn-outline-success">
                                                    <i class="fas fa-eye me-1"></i>عرض التفاصيل
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-chalkboard fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد فصول مرتبطة بهذه المادة حالياً</p>
                            <a href="../classes/add.php" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>إضافة فصل جديد
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- المعلمين المرتبطين -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-chalkboard-teacher me-2"></i>المعلمين المختصين بالمادة
                        <span class="badge bg-dark ms-2"><?php echo count($related_teachers); ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($related_teachers)): ?>
                        <div class="row">
                            <?php foreach ($related_teachers as $teacher): ?>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card border-warning">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <i class="fas fa-user-tie me-2"></i>
                                                <?php echo htmlspecialchars($teacher['full_name']); ?>
                                            </h6>
                                            <p class="card-text small text-muted mb-2">
                                                <i class="fas fa-envelope me-1"></i>
                                                <?php echo htmlspecialchars($teacher['email'] ?? 'غير محدد'); ?><br>
                                                <i class="fas fa-phone me-1"></i>
                                                <?php echo htmlspecialchars($teacher['phone'] ?? 'غير محدد'); ?>
                                            </p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <small class="text-warning">
                                                    <i class="fas fa-chalkboard me-1"></i>
                                                    <?php echo $teacher['classes_taught']; ?> فصل
                                                </small>
                                                <a href="../teachers/view.php?id=<?php echo $teacher['id']; ?>"
                                                   class="btn btn-sm btn-outline-warning">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-user-tie fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا يوجد معلمين مختصين بهذه المادة حالياً</p>
                            <a href="../teachers/add.php" class="btn btn-warning">
                                <i class="fas fa-plus me-2"></i>إضافة معلم جديد
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>