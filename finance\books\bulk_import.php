<?php
/**
 * صفحة استيراد الكتب بالجملة
 * Bulk Import Books Page
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    header('Location: ../../dashboard/');
    exit();
}

$errors = [];
$success_message = '';
$imported_count = 0;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'])) {
        die('CSRF token validation failed.');
    }

    if (isset($_FILES['csv_file']) && $_FILES['csv_file']['error'] == UPLOAD_ERR_OK) {
        $file = $_FILES['csv_file']['tmp_name'];
        $file_type = mime_content_type($file);

        if ($file_type == 'text/csv' || $file_type == 'text/plain') {
            $handle = fopen($file, 'r');
            if ($handle !== FALSE) {
                $conn->begin_transaction();
                try {
                    // Skip header row
                    fgetcsv($handle, 1000, ',');

                    $stmt = $conn->prepare("INSERT INTO books (book_title, book_title_en, subject_id, grade_level, isbn, publisher, academic_year, price, stock_quantity, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())");

                    while (($data = fgetcsv($handle, 1000, ',')) !== FALSE) {
                        if (count($data) >= 9) {
                             $stmt->bind_param('ssisssdiss',
                                $data[0], $data[1], $data[2], $data[3], $data[4], 
                                $data[5], $data[6], $data[7], $data[8]
                            );
                            $stmt->execute();
                            $imported_count++;
                        }
                    }
                    $conn->commit();
                    $_SESSION['success_message'] = sprintf(__('%d_books_imported_successfully'), $imported_count);
                    header('Location: index.php');
                    exit();
                } catch (Exception $e) {
                    $conn->rollback();
                    $errors[] = __('error_during_import') . ': ' . $e->getMessage();
                }
                fclose($handle);
            } else {
                $errors[] = __('error_opening_file');
            }
        } else {
            $errors[] = __('invalid_file_type_csv');
        }
    } else {
        $errors[] = __('no_file_uploaded_or_error');
    }
}

include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('bulk_import_books'); ?></h1>
            <p class="text-muted"><?php echo __('upload_csv_to_import_books'); ?></p>
        </div>
        <div>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_books'); ?>
            </a>
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-body">
            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <?php foreach ($errors as $error): ?>
                        <p class="mb-0"><?php echo $error; ?></p>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>

            <div class="alert alert-info">
                <h5><?php echo __('instructions'); ?></h5>
                <p><?php echo __('csv_format_instructions'); ?></p>
                <p><code>book_title, book_title_en, subject_id, grade_level, isbn, publisher, academic_year, price, stock_quantity, status</code></p>
                <p><a href="../../templates/books_template.csv" download class="btn btn-sm btn-outline-primary"><i class="fas fa-download me-2"></i><?php echo __('download_template'); ?></a></p>
            </div>

            <form action="bulk_import.php" method="POST" enctype="multipart/form-data">
                <?php echo generate_csrf_token(); ?>
                <div class="mb-3">
                    <label for="csv_file" class="form-label"><?php echo __('select_csv_file'); ?> <span class="text-danger">*</span></label>
                    <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                </div>
                <button type="submit" class="btn btn-primary"><i class="fas fa-upload me-2"></i><?php echo __('import_books'); ?></button>
            </form>
        </div>
    </div>
</div>

<?php include_once '../../includes/footer.php'; ?>
