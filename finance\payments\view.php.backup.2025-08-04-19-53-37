<?php
require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/security.php';

// التحقق من الجلسة والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

// جلب معرف المدفوعة
$payment_id = intval($_GET['id'] ?? 0);

if ($payment_id <= 0) {
    header('Location: index.php?error=' . urlencode('معرف المدفوعة غير صحيح'));
    exit();
}

// جلب بيانات المدفوعة مع تفاصيل الطالب والرسم
$stmt = $conn->prepare("
    SELECT 
        sp.*,
        u.full_name as student_name,
        s.student_id as student_number,
        s.parent_name,
        s.parent_phone,
        s.address,
        c.class_name,
        c.grade_level,
        sf.final_amount as fee_amount,
        sf.base_amount,
        sf.discount_amount,
        sf.remaining_amount,
        ft.type_name as fee_type_name,
        pu.full_name as processed_by_name
    FROM student_payments sp
    JOIN students s ON sp.student_id = s.id
    JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    LEFT JOIN student_fees sf ON sp.student_fee_id = sf.id
    LEFT JOIN fee_types ft ON sf.fee_type_id = ft.id
    LEFT JOIN users pu ON sp.processed_by = pu.id
    WHERE sp.id = ?
");

if (!$stmt) {
    die("خطأ في تحضير الاستعلام: " . $conn->error);
}

$stmt->bind_param("i", $payment_id);
$stmt->execute();
$payment = $stmt->get_result()->fetch_assoc();

if (!$payment) {
    header('Location: index.php?error=' . urlencode('المدفوعة غير موجودة'));
    exit();
}

$page_title = __('payment_details');
include_once '../../includes/header.php';
?>

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0"><i class="fas fa-eye me-2"></i><?php echo __('payment_details'); ?></h4>
                        <div class="d-flex gap-2">
                            <a href="receipt_improved.php?id=<?php echo $payment['id']; ?>" class="btn btn-light btn-sm">
                                <i class="fas fa-receipt me-1"></i><?php echo __('print_receipt'); ?>
                            </a>
                            <a href="index.php" class="btn btn-light btn-sm">
                                <i class="fas fa-arrow-left me-1"></i><?php echo __('back'); ?>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- معلومات الطالب -->
                    <div class="card border-primary mb-4">
                        <div class="card-header bg-primary text-white">
                            <h6 class="mb-0"><i class="fas fa-user-graduate me-2"></i><?php echo __('student_information'); ?></h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td class="fw-bold"><?php echo __('student_name'); ?>:</td>
                                            <td><?php echo htmlspecialchars($payment['student_name']); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold"><?php echo __('student_number'); ?>:</td>
                                            <td><?php echo htmlspecialchars($payment['student_number']); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold"><?php echo __('class'); ?>:</td>
                                            <td><?php echo htmlspecialchars($payment['class_name'] ?? 'غير محدد'); ?> - <?php echo htmlspecialchars($payment['grade_level'] ?? ''); ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-sm table-borderless">
                                        <?php if (!empty($payment['parent_name'])): ?>
                                        <tr>
                                            <td class="fw-bold"><?php echo __('parent_name'); ?>:</td>
                                            <td><?php echo htmlspecialchars($payment['parent_name']); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                        <?php if (!empty($payment['parent_phone'])): ?>
                                        <tr>
                                            <td class="fw-bold"><?php echo __('parent_phone'); ?>:</td>
                                            <td><?php echo htmlspecialchars($payment['parent_phone']); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                        <?php if (!empty($payment['address'])): ?>
                                        <tr>
                                            <td class="fw-bold"><?php echo __('address'); ?>:</td>
                                            <td><?php echo htmlspecialchars($payment['address']); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تفاصيل المدفوعة -->
                    <div class="card border-success mb-4">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0"><i class="fas fa-money-bill-wave me-2"></i><?php echo __('payment_information'); ?></h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td class="fw-bold"><?php echo __('payment_date'); ?>:</td>
                                            <td><?php echo date('Y-m-d', strtotime($payment['payment_date'])); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold"><?php echo __('payment_method'); ?>:</td>
                                            <td>
                                                <?php
                                                $method_icons = [
                                                    'cash' => 'fas fa-money-bill text-success',
                                                    'bank_transfer' => 'fas fa-university text-primary',
                                                    'check' => 'fas fa-money-check text-info',
                                                    'card' => 'fas fa-credit-card text-warning',
                                                    'online' => 'fas fa-globe text-secondary'
                                                ];
                                                $icon = $method_icons[$payment['payment_method']] ?? 'fas fa-question';
                                                ?>
                                                <i class="<?php echo $icon; ?> me-1"></i>
                                                <?php echo __($payment['payment_method']); ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold"><?php echo __('amount'); ?>:</td>
                                            <td class="text-success fw-bold fs-5">
                                                <?php echo number_format($payment['amount'], 2); ?> <?php echo get_system_setting('currency_symbol', 'ر.س'); ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold"><?php echo __('status'); ?>:</td>
                                            <td>
                                                <span class="badge bg-<?php echo $payment['status'] == 'confirmed' ? 'success' : ($payment['status'] == 'pending' ? 'warning' : 'danger'); ?>">
                                                    <?php echo __($payment['status']); ?>
                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-sm table-borderless">
                                        <?php if (!empty($payment['payment_reference'])): ?>
                                        <tr>
                                            <td class="fw-bold"><?php echo __('payment_reference'); ?>:</td>
                                            <td><?php echo htmlspecialchars($payment['payment_reference']); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                        <?php if (!empty($payment['receipt_number'])): ?>
                                        <tr>
                                            <td class="fw-bold"><?php echo __('receipt_number'); ?>:</td>
                                            <td><?php echo htmlspecialchars($payment['receipt_number']); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                        <?php if (!empty($payment['bank_name'])): ?>
                                        <tr>
                                            <td class="fw-bold"><?php echo __('bank_name'); ?>:</td>
                                            <td><?php echo htmlspecialchars($payment['bank_name']); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                        <tr>
                                            <td class="fw-bold"><?php echo __('processed_by'); ?>:</td>
                                            <td><?php echo htmlspecialchars($payment['processed_by_name'] ?? 'النظام'); ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تفاصيل الرسم المرتبط -->
                    <?php if (!empty($payment['fee_type_name'])): ?>
                    <div class="card border-warning mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0"><i class="fas fa-file-invoice-dollar me-2"></i><?php echo __('related_fee_details'); ?></h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td class="fw-bold"><?php echo __('fee_type'); ?>:</td>
                                            <td><?php echo htmlspecialchars($payment['fee_type_name']); ?></td>
                                        </tr>
                                        <?php if ($payment['base_amount'] > 0): ?>
                                        <tr>
                                            <td class="fw-bold"><?php echo __('base_amount'); ?>:</td>
                                            <td><?php echo number_format($payment['base_amount'], 2); ?> <?php echo get_system_setting('currency_symbol', 'ر.س'); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                        <?php if ($payment['discount_amount'] > 0): ?>
                                        <tr>
                                            <td class="fw-bold text-success"><?php echo __('discount'); ?>:</td>
                                            <td class="text-success">-<?php echo number_format($payment['discount_amount'], 2); ?> <?php echo get_system_setting('currency_symbol', 'ر.س'); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-sm table-borderless">
                                        <?php if ($payment['fee_amount'] > 0): ?>
                                        <tr>
                                            <td class="fw-bold"><?php echo __('total_fee_amount'); ?>:</td>
                                            <td><?php echo number_format($payment['fee_amount'], 2); ?> <?php echo get_system_setting('currency_symbol', 'ر.س'); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                        <?php if ($payment['remaining_amount'] > 0): ?>
                                        <tr>
                                            <td class="fw-bold text-danger"><?php echo __('remaining_amount'); ?>:</td>
                                            <td class="text-danger"><?php echo number_format($payment['remaining_amount'], 2); ?> <?php echo get_system_setting('currency_symbol', 'ر.س'); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- ملاحظات -->
                    <?php if (!empty($payment['notes'])): ?>
                    <div class="card border-info mb-4">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0"><i class="fas fa-sticky-note me-2"></i><?php echo __('notes'); ?></h6>
                        </div>
                        <div class="card-body">
                            <p class="mb-0"><?php echo nl2br(htmlspecialchars($payment['notes'])); ?></p>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- معلومات النظام -->
                    <div class="card border-secondary">
                        <div class="card-header bg-secondary text-white">
                            <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i><?php echo __('system_information'); ?></h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <strong><?php echo __('created_at'); ?>:</strong> <?php echo date('Y-m-d H:i:s', strtotime($payment['created_at'])); ?>
                                    </small>
                                </div>
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <strong><?php echo __('processed_at'); ?>:</strong> <?php echo date('Y-m-d H:i:s', strtotime($payment['processed_at'])); ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="d-flex justify-content-between mt-4">
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
                        </a>
                        <div class="d-flex gap-2">
                            <a href="receipt_improved.php?id=<?php echo $payment['id']; ?>" class="btn btn-success">
                                <i class="fas fa-receipt me-2"></i><?php echo __('print_receipt'); ?>
                            </a>
                            <?php if ($payment['status'] !== 'confirmed'): ?>
                            <a href="edit.php?id=<?php echo $payment['id']; ?>" class="btn btn-warning">
                                <i class="fas fa-edit me-2"></i><?php echo __('edit'); ?>
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once '../../includes/footer.php'; ?>
