<?php
/**
 * صفحة إدارة الطلاب المحسنة مع العمليات الجماعية
 * Enhanced Students Management Page with Bulk Operations
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تعيين اللغة الافتراضية إذا لم تكن محددة
if (!isset($_SESSION['system_language'])) {
    $_SESSION['system_language'] = 'ar';
}

// التحقق من الصلاحيات
check_session();
if (!check_permission('teacher') && !check_permission('staff') && !has_permission('students_view') && !has_permission('student_affairs')) {
    header('Location: ../dashboard/');
    exit();
}

// معالجة الفلاتر المتقدمة
$class_filter = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;
$stage_filter = isset($_GET['stage_id']) ? intval($_GET['stage_id']) : 0;
$grade_filter = isset($_GET['grade_id']) ? intval($_GET['grade_id']) : 0;
$status_filter = isset($_GET['status']) ? trim($_GET['status']) : '';
$gender_filter = isset($_GET['gender']) ? trim($_GET['gender']) : '';
$search_query = isset($_GET['search']) ? trim($_GET['search']) : '';

// جلب قوائم الفلاتر مع معالجة الأخطاء
$classes = [];
$classes_query = "SELECT id, class_name, grade_level FROM classes ORDER BY class_name";
$classes_result = $conn->query($classes_query);
if ($classes_result) {
    $classes = $classes_result->fetch_all(MYSQLI_ASSOC);
}

$stages = [];
$stages_query = "SELECT id, stage_name FROM educational_stages ORDER BY id";
$stages_result = $conn->query($stages_query);
if ($stages_result) {
    $stages = $stages_result->fetch_all(MYSQLI_ASSOC);
}

$grades = [];
$grades_query = "SELECT id, grade_name, 1 as stage_id, 'المرحلة الأساسية' as stage_name FROM grades ORDER BY id";
$grades_result = $conn->query($grades_query);
if ($grades_result) {
    $grades = $grades_result->fetch_all(MYSQLI_ASSOC);
}

// معالجة الحذف
if (isset($_POST['delete_student']) && check_permission('admin')) {
    $student_id = intval($_POST['student_id']);

    global $conn;
    $conn->begin_transaction();

    try {
        // أولاً: الحصول على user_id قبل حذف الطالب
        $stmt = $conn->prepare("SELECT user_id FROM students WHERE id = ?");
        $stmt->bind_param("i", $student_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $student_data = $result->fetch_assoc();
        $user_id = $student_data['user_id'] ?? null;
        $stmt->close();

        // ثانياً: حذف الطالب من جدول students
        $stmt = $conn->prepare("DELETE FROM students WHERE id = ?");
        $stmt->bind_param("i", $student_id);
        $stmt->execute();
        $stmt->close();

        // ثالثاً: حذف المستخدم من جدول users إذا كان موجوداً
        if ($user_id) {
            $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
            $stmt->bind_param("i", $user_id);
            $stmt->execute();
            $stmt->close();
        }

        $conn->commit();

        // تسجيل النشاط
        log_activity($_SESSION['user_id'], 'delete_student', 'students', $student_id, null, [
            'student_id' => $student_id,
            'user_id' => $user_id
        ]);

        $_SESSION['success_message'] = __('deleted_successfully');
    } catch (Exception $e) {
        $conn->rollback();
        $_SESSION['error_message'] = __('error_occurred');
        log_error("Error deleting student: " . $e->getMessage());
    }

    header('Location: index.php');
    exit();
}

// تحميل ملف اللغة قبل استخدام دالة الترجمة
load_language();

$page_title = __('students');
require_once '../includes/header.php';

// بناء شروط البحث الأساسية
$where_conditions = [];
$params = [];
$param_types = '';

// فلتر البحث النصي
if (!empty($search_query)) {
    $where_conditions[] = "(u.full_name LIKE ? OR u.email LIKE ? OR COALESCE(s.national_id, '') LIKE ? OR COALESCE(s.student_id, '') LIKE ?)";
    $search_param = "%{$search_query}%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $param_types .= 'ssss';
}

// فلتر الفصل
if ($class_filter > 0) {
    $where_conditions[] = "s.class_id = ?";
    $params[] = $class_filter;
    $param_types .= 'i';
}

// فلتر الحالة
if (!empty($status_filter)) {
    $where_conditions[] = "u.status = ?";
    $params[] = $status_filter;
    $param_types .= 's';
}

// فلتر الجنس
if (!empty($gender_filter)) {
    $where_conditions[] = "s.gender = ?";
    $params[] = $gender_filter;
    $param_types .= 's';
}

$where_clause = !empty($where_conditions) ? implode(' AND ', $where_conditions) : '1=1';

// الحصول على عدد الصفحات
$count_query = "
    SELECT COUNT(*) as total
    FROM students s
    JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    WHERE $where_clause
";

$count_stmt = $conn->prepare($count_query);
if (!empty($params)) {
    $count_stmt->bind_param($param_types, ...$params);
}
$count_stmt->execute();
$total_records = $count_stmt->get_result()->fetch_assoc()['total'];

// إعدادات الترقيم
$page = intval($_GET['page'] ?? 1);
$records_per_page = ITEMS_PER_PAGE;
$total_pages = ceil($total_records / $records_per_page);
$offset = ($page - 1) * $records_per_page;

// جلب الطلاب مع المعلومات الأساسية
$query = "
    SELECT
        s.id,
        s.student_id,
        s.national_id,
        s.date_of_birth,
        s.gender,
        s.parent_name,
        s.parent_phone,
        s.enrollment_date,
        s.class_id,
        u.id as user_id,
        u.full_name,
        u.email,
        u.phone,
        u.status,
        u.last_login,
        c.class_name,
        c.grade_level
    FROM students s
    JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    WHERE $where_clause
    ORDER BY c.class_name, u.full_name ASC
    LIMIT ? OFFSET ?
";

$stmt = $conn->prepare($query);
if (!$stmt) {
    die("SQL Error: " . $conn->error . "<br>Query: " . htmlspecialchars($query));
}

// إضافة limit/offset للمعاملات
$limit_params = $params;
$limit_params[] = $records_per_page;
$limit_params[] = $offset;
$limit_types = $param_types . "ii";

$stmt->bind_param($limit_types, ...$limit_params);
$stmt->execute();
$students = $stmt->get_result();

// جلب قائمة الفصول للفلترة
$classes = $conn->query("SELECT id, class_name, grade_level FROM classes WHERE status = 'active' ORDER BY grade_level, class_name");

// إحصائيات سريعة
$stats_query = "
    SELECT 
        COUNT(*) as total_students,
        SUM(CASE WHEN u.status = 'active' THEN 1 ELSE 0 END) as active_students,
        SUM(CASE WHEN s.gender = 'male' THEN 1 ELSE 0 END) as male_students,
        SUM(CASE WHEN s.gender = 'female' THEN 1 ELSE 0 END) as female_students
    FROM students s
    JOIN users u ON s.user_id = u.id
";
$stats_result = $conn->query($stats_query);
$stats = $stats_result->fetch_assoc();
?>

<link rel="stylesheet" href="../assets/css/modern-style.css">

<style>
/* تحسينات للعمليات الجماعية */
#bulkOperationsCard {
    border-left: 4px solid #ffc107;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.student-checkbox:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.table tbody tr:has(.student-checkbox:checked) {
    background-color: rgba(13, 110, 253, 0.1);
    border-left: 3px solid #0d6efd;
}

.form-check-input:indeterminate {
    background-color: #6c757d;
    border-color: #6c757d;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
}

/* تحسين مظهر الفلاتر */
.card-header {
    border-bottom: 2px solid rgba(0,0,0,0.1);
}

.form-select:focus, .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* تحسين الجدول */
.table th {
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: rgba(0,0,0,0.05);
}

/* إخفاء عناصر عند الطباعة */
@media print {
    .no-print {
        display: none !important;
    }

    #bulkOperationsCard {
        display: none !important;
    }
}
</style>

<?php
?>

<div class="container-fluid">
    <!-- Top Bar Actions -->
    <div class="d-flex justify-content-between align-items-center mb-4 animate__animated animate__fadeInDown">
        <div>
            <h1 class="h3 mb-0 fw-bold"><?php echo __('students'); ?></h1>
            <p class="text-muted mb-0"><?php echo __('manage_students_info'); ?></p>
        </div>
        <div>
            <a href="add.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i><?php echo __('add_student'); ?>
            </a>
            <a href="import.php" class="btn btn-success">
                <i class="fas fa-upload me-2"></i><?php echo __('import_students'); ?>
            </a>
        </div>
    </div>
    <!-- Statistics Cards -->
    <div class="row mb-4 animate__animated animate__fadeInUp">
        <div class="col-md-3">
            <div class="modern-card glass-card text-center py-3">
                <div class="fs-2 fw-bold text-primary"><?php echo $stats['total_students'] ?? 0; ?></div>
                <div class="text-muted"><?php echo __('total_students'); ?></div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="modern-card glass-card text-center py-3">
                <div class="fs-2 fw-bold text-success"><?php echo $stats['active_students'] ?? 0; ?></div>
                <div class="text-muted"><?php echo __('active_students'); ?></div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="modern-card glass-card text-center py-3">
                <div class="fs-2 fw-bold text-info"><?php echo $stats['male_students'] ?? 0; ?></div>
                <div class="text-muted"><?php echo __('male_students'); ?></div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="modern-card glass-card text-center py-3">
                <div class="fs-2 fw-bold text-pink"><?php echo $stats['female_students'] ?? 0; ?></div>
                <div class="text-muted"><?php echo __('female_students'); ?></div>
            </div>
        </div>
    </div>
    <!-- نظام الفلترة المتقدم -->
    <div class="card mb-4 shadow-sm">
        <div class="card-header bg-light">
            <h6 class="mb-0 fw-bold">
                <i class="fas fa-filter me-2"></i>البحث والفلترة المتقدمة
            </h6>
        </div>
        <div class="card-body">
            <form method="GET" id="advancedFilterForm" class="row g-3">
                <!-- البحث النصي -->
                <div class="col-md-3">
                    <label for="search" class="form-label">البحث</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" id="search" name="search"
                               value="<?php echo htmlspecialchars($search_query); ?>"
                               placeholder="اسم الطالب، الإيميل، الرقم القومي...">
                    </div>
                </div>

                <!-- فلتر الفصل -->
                <div class="col-md-2">
                    <label for="class_id" class="form-label">الفصل</label>
                    <select class="form-select" id="class_id" name="class_id">
                        <option value="">جميع الفصول</option>
                        <?php foreach ($classes as $class): ?>
                            <option value="<?php echo $class['id']; ?>"
                                    <?php echo ($class_filter == $class['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($class['class_name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- مساحة إضافية للتوسع المستقبلي -->
                <div class="col-md-2">
                    <label for="future_filter" class="form-label">فلتر إضافي</label>
                    <select class="form-select" id="future_filter" name="future_filter" disabled>
                        <option value="">قريباً...</option>
                    </select>
                </div>

                <div class="col-md-2">
                    <!-- مساحة فارغة -->
                </div>

                <!-- فلتر الحالة -->
                <div class="col-md-2">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="active" <?php echo ($status_filter == 'active') ? 'selected' : ''; ?>>نشط</option>
                        <option value="inactive" <?php echo ($status_filter == 'inactive') ? 'selected' : ''; ?>>غير نشط</option>
                        <option value="graduated" <?php echo ($status_filter == 'graduated') ? 'selected' : ''; ?>>متخرج</option>
                        <option value="transferred" <?php echo ($status_filter == 'transferred') ? 'selected' : ''; ?>>محول</option>
                    </select>
                </div>

                <!-- فلتر الجنس -->
                <div class="col-md-1">
                    <label for="gender" class="form-label">الجنس</label>
                    <select class="form-select" id="gender" name="gender">
                        <option value="">الكل</option>
                        <option value="male" <?php echo ($gender_filter == 'male') ? 'selected' : ''; ?>>ذكر</option>
                        <option value="female" <?php echo ($gender_filter == 'female') ? 'selected' : ''; ?>>أنثى</option>
                    </select>
                </div>

                <!-- أزرار التحكم -->
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>بحث
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                                <i class="fas fa-times me-2"></i>مسح الفلاتر
                            </button>
                        </div>
                        <div class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            عرض <?php echo $students->num_rows; ?> من أصل <?php echo $total_records; ?> طالب
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- العمليات الجماعية -->
    <div class="card mb-3 shadow-sm" id="bulkOperationsCard" style="display: none;">
        <div class="card-header bg-warning text-dark">
            <h6 class="mb-0 fw-bold">
                <i class="fas fa-tasks me-2"></i>العمليات الجماعية
                <span class="badge bg-dark ms-2" id="selectedCount">0</span>
            </h6>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-3">
                    <select class="form-select" id="bulkOperation">
                        <option value="">اختر العملية</option>
                        <option value="delete">حذف المحدد</option>
                        <option value="transfer">نقل إلى فصل آخر</option>
                        <option value="change_status">تغيير الحالة</option>
                        <option value="promote">ترقية/تصعيد</option>
                    </select>
                </div>

                <!-- خيارات النقل -->
                <div class="col-md-3" id="transferOptions" style="display: none;">
                    <select class="form-select" id="newClassId">
                        <option value="">اختر الفصل الجديد</option>
                        <?php foreach ($classes as $class): ?>
                            <option value="<?php echo $class['id']; ?>">
                                <?php echo htmlspecialchars($class['class_name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- خيارات تغيير الحالة -->
                <div class="col-md-3" id="statusOptions" style="display: none;">
                    <select class="form-select" id="newStatus">
                        <option value="">اختر الحالة الجديدة</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                        <option value="graduated">متخرج</option>
                        <option value="transferred">محول</option>
                    </select>
                </div>

                <!-- خيارات الترقية/التصعيد -->
                <div class="col-md-6" id="promoteOptions" style="display: none;">
                    <div class="row g-2">
                        <div class="col-md-6">
                            <label class="form-label small">المرحلة والصف</label>
                            <select class="form-select" id="targetStageGrade" onchange="updatePromoteClasses()">
                                <option value="">اختر المرحلة والصف</option>

                                <optgroup label="مرحلة الطفولة المبكرة">
                                    <option value="pre_nursery">ما قبل رياض الأطفال</option>
                                    <option value="nursery">رياض الأطفال</option>
                                    <option value="kg1">الروضة الأولى (KG1)</option>
                                    <option value="kg2">الروضة الثانية (KG2)</option>
                                    <option value="pre_primary">التمهيدي</option>
                                </optgroup>

                                <optgroup label="المرحلة الابتدائية">
                                    <option value="primary_1">الصف الأول الابتدائي</option>
                                    <option value="primary_2">الصف الثاني الابتدائي</option>
                                    <option value="primary_3">الصف الثالث الابتدائي</option>
                                    <option value="primary_4">الصف الرابع الابتدائي</option>
                                    <option value="primary_5">الصف الخامس الابتدائي</option>
                                    <option value="primary_6">الصف السادس الابتدائي</option>
                                </optgroup>

                                <optgroup label="المرحلة الإعدادية/المتوسطة">
                                    <option value="middle_1">الصف الأول الإعدادي</option>
                                    <option value="middle_2">الصف الثاني الإعدادي</option>
                                    <option value="middle_3">الصف الثالث الإعدادي</option>
                                </optgroup>

                                <optgroup label="المرحلة الثانوية">
                                    <option value="high_1">الصف الأول الثانوي</option>
                                    <option value="high_2">الصف الثاني الثانوي</option>
                                    <option value="high_3">الصف الثالث الثانوي</option>
                                </optgroup>

                                <optgroup label="التعليم الفني">
                                    <option value="tech_1">الصف الأول الفني</option>
                                    <option value="tech_2">الصف الثاني الفني</option>
                                    <option value="tech_3">الصف الثالث الفني</option>
                                </optgroup>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label small">الفصل المحدد</label>
                            <select class="form-select" id="targetClassId">
                                <option value="">اختر الفصل</option>
                                <?php foreach ($classes as $class): ?>
                                    <option value="<?php echo $class['id']; ?>"
                                            data-level="<?php echo htmlspecialchars($class['grade_level'] ?? ''); ?>">
                                        <?php echo htmlspecialchars($class['class_name']); ?>
                                        <?php if (!empty($class['grade_level'])): ?>
                                            - <?php echo htmlspecialchars($class['grade_level']); ?>
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <button type="button" class="btn btn-warning" onclick="executeBulkOperation()">
                        <i class="fas fa-play me-2"></i>تنفيذ العملية
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="clearSelection()">
                        <i class="fas fa-times me-2"></i>إلغاء التحديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Students Table -->
    <div class="modern-card glass-card animate__animated animate__fadeInUp">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0 fw-bold">
                <?php echo __('students_list'); ?>
                <small class="text-muted ms-2">(<?php echo $total_records; ?> طالب)</small>
            </h5>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="printTable()">
                    <i class="fas fa-print me-1"></i><?php echo __('print'); ?>
                </button>
                <button type="button" class="btn btn-outline-success btn-sm" onclick="exportToExcel()">
                    <i class="fas fa-file-excel me-1"></i><?php echo __('export'); ?>
                </button>
            </div>
        </div>
        <div class="card-body">
            <?php if ($students->num_rows > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover glass-table align-middle" id="studentsTable">
                        <thead class="table-dark">
                            <tr>
                                <th class="no-print" style="width: 40px;">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                        <label class="form-check-label" for="selectAll">
                                            <small>الكل</small>
                                        </label>
                                    </div>
                                </th>
                                <th><?php echo __('photo'); ?></th>
                                <th><?php echo __('student_info'); ?></th>
                                <th><?php echo __('class'); ?></th>
                                <th><?php echo __('contact_info'); ?></th>
                                <th><?php echo __('parent_info'); ?></th>
                                <th><?php echo __('status'); ?></th>
                                <th><?php echo __('last_login'); ?></th>
                                <th class="no-print"><?php echo __('actions'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($student = $students->fetch_assoc()): ?>
                                <tr>
                                    <td class="no-print">
                                        <div class="form-check">
                                            <input class="form-check-input student-checkbox" type="checkbox"
                                                   value="<?php echo $student['id']; ?>"
                                                   data-name="<?php echo htmlspecialchars($student['full_name']); ?>"
                                                   data-class="<?php echo htmlspecialchars($student['class_name'] ?? 'غير محدد'); ?>"
                                                   onchange="updateBulkOperations()">
                                        </div>
                                    </td>
                                    <td>
                                        <?php if (!empty($student['profile_picture'])): ?>
                                            <img src="../uploads/profiles/<?php echo $student['profile_picture']; ?>" alt="<?php echo htmlspecialchars($student['full_name']); ?>" class="rounded-circle border border-2 border-primary shadow-sm" width="40" height="40">
                                        <?php else: ?>
                                            <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                <i class="fas fa-user text-white fs-4"></i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($student['full_name']); ?></strong>
                                            <br>
                                            <?php if (isset($student['student_number'])): ?>
                                            <small class="text-muted">
                                                <?php echo __('student_number'); ?>: <?php echo htmlspecialchars($student['student_number']); ?>
                                            </small>
                                            <?php endif; ?>
                                            <?php if (!empty($student['national_id'])): ?>
                                                <br>
                                                <small class="text-muted">
                                                    <?php echo __('national_id'); ?>: <?php echo htmlspecialchars($student['national_id']); ?>
                                                </small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if (!empty($student['class_name'])): ?>
                                            <span class="badge bg-info">
                                                <?php echo htmlspecialchars($student['class_name']); ?>
                                            </span>
                                            <br>
                                            <small class="text-muted"><?php echo htmlspecialchars($student['grade_level']); ?></small>
                                        <?php else: ?>
                                            <span class="text-muted"><?php echo __('not_assigned'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($student['email'])): ?>
                                            <div><i class="fas fa-envelope me-1"></i><?php echo htmlspecialchars($student['email']); ?></div>
                                        <?php endif; ?>
                                        <?php if (!empty($student['phone'])): ?>
                                            <div><i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($student['phone']); ?></div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($student['parent_name'])): ?>
                                            <div><strong><?php echo htmlspecialchars($student['parent_name']); ?></strong></div>
                                        <?php endif; ?>
                                        <?php if (!empty($student['parent_phone'])): ?>
                                            <div><i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($student['parent_phone']); ?></div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $status_class = '';
                                        $status_text = '';
                                        switch ($student['status']) {
                                            case 'active':
                                                $status_class = 'bg-success';
                                                $status_text = __('active');
                                                break;
                                            case 'inactive':
                                                $status_class = 'bg-warning';
                                                $status_text = __('inactive');
                                                break;
                                            case 'suspended':
                                                $status_class = 'bg-danger';
                                                $status_text = __('suspended');
                                                break;
                                            default:
                                                $status_class = 'bg-secondary';
                                                $status_text = $student['status'];
                                        }
                                        ?>
                                        <span class="badge <?php echo $status_class; ?>">
                                            <?php echo $status_text; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($student['last_login']): ?>
                                            <small class="text-muted">
                                                <?php echo format_datetime($student['last_login'], 'Y/m/d H:i'); ?>
                                            </small>
                                        <?php else: ?>
                                            <small class="text-muted"><?php echo __('never_logged_in'); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td class="no-print">
                                        <div class="btn-group btn-group-sm">
                                            <a href="view.php?id=<?php echo $student['id']; ?>"
                                               class="btn btn-outline-info"
                                               title="<?php echo __('view'); ?>">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if (check_permission('admin')): ?>
                                            <a href="edit.php?id=<?php echo $student['id']; ?>"
                                               class="btn btn-outline-primary"
                                               title="<?php echo __('edit'); ?>">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button"
                                                    class="btn btn-outline-danger"
                                                    onclick="confirmDelete(<?php echo $student['id']; ?>, '<?php echo htmlspecialchars($student['full_name'], ENT_QUOTES); ?>')"
                                                    title="<?php echo __('delete'); ?>">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="Students pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo ($page - 1); ?>&search=<?php echo urlencode($search); ?>&class_id=<?php echo urlencode($class_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                                        <?php echo __('previous'); ?>
                                    </a>
                                </li>
                            <?php endif; ?>
                            <?php $start = max(1, $page - 2); $end = min($total_pages, $page + 2); for ($i = $start; $i <= $end; $i++): ?>
                                <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&class_id=<?php echo urlencode($class_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            <?php if ($page < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo ($page + 1); ?>&search=<?php echo urlencode($search); ?>&class_id=<?php echo urlencode($class_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                                        <?php echo __('next'); ?>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted"><?php echo __('no_students_found'); ?></h5>
                    <p class="text-muted"><?php echo __('try_different_search'); ?></p>
                    <a href="add.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i><?php echo __('add_first_student'); ?>
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Hidden form for deletion -->
<form id="deleteForm" method="POST" style="display: none;">
    <input type="hidden" name="student_id" id="deleteStudentId">
    <input type="hidden" name="delete_student" value="1">
</form>

<script>
function confirmDelete(studentId, studentName) {
    // استخدام SweetAlert للتأكيد
    Swal.fire({
        title: '<?php echo __('confirm_delete'); ?>',
        html: `
            <div class="text-center mb-3">
                <i class="fas fa-user-times fa-3x text-danger"></i>
            </div>
            <p><?php echo __('are_you_sure_delete_student'); ?></p>
            <div class="alert alert-warning">
                <strong><?php echo __('student_name'); ?>:</strong> ${studentName}
            </div>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong><?php echo __('warning'); ?>:</strong> <?php echo __('delete_student_warning'); ?>
            </div>
            <p class="text-muted small"><?php echo __('this_action_cannot_be_undone'); ?></p>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '<i class="fas fa-trash me-2"></i><?php echo __('delete'); ?>',
        cancelButtonText: '<i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>',
        reverseButtons: true,
        customClass: {
            confirmButton: 'btn btn-danger',
            cancelButton: 'btn btn-secondary'
        },
        buttonsStyling: false
    }).then((result) => {
        if (result.isConfirmed) {
            // إظهار رسالة تحميل
            Swal.fire({
                title: '<?php echo __('deleting'); ?>...',
                text: '<?php echo __('please_wait'); ?>',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // إرسال النموذج
            document.getElementById('deleteStudentId').value = studentId;
            document.getElementById('deleteForm').submit();
        }
    });
}

    // Print table
    function printTable() {
        window.print();
    }

    // Export to Excel
    function exportToExcel() {
        const table = document.getElementById('studentsTable');
        const wb = XLSX.utils.table_to_book(table);
        XLSX.writeFile(wb, 'students_<?php echo date('Y-m-d'); ?>.xlsx');
    }

    // Auto-submit search form on input
    document.getElementById('search').addEventListener('input', function() {
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.form.submit();
        }, 500);
    });

    // العمليات الجماعية
    function toggleSelectAll() {
        const selectAll = document.getElementById('selectAll');
        const checkboxes = document.querySelectorAll('.student-checkbox');

        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll.checked;
        });

        updateBulkOperations();
    }

    function updateBulkOperations() {
        const checkboxes = document.querySelectorAll('.student-checkbox:checked');
        const bulkCard = document.getElementById('bulkOperationsCard');
        const selectedCount = document.getElementById('selectedCount');

        selectedCount.textContent = checkboxes.length;

        if (checkboxes.length > 0) {
            bulkCard.style.display = 'block';
        } else {
            bulkCard.style.display = 'none';
        }

        // تحديث حالة "تحديد الكل"
        const selectAll = document.getElementById('selectAll');
        const allCheckboxes = document.querySelectorAll('.student-checkbox');
        selectAll.checked = checkboxes.length === allCheckboxes.length;
        selectAll.indeterminate = checkboxes.length > 0 && checkboxes.length < allCheckboxes.length;
    }

    function clearSelection() {
        document.querySelectorAll('.student-checkbox').forEach(checkbox => {
            checkbox.checked = false;
        });
        document.getElementById('selectAll').checked = false;
        updateBulkOperations();
    }

    // إظهار/إخفاء خيارات العمليات
    document.getElementById('bulkOperation').addEventListener('change', function() {
        const operation = this.value;

        // إخفاء جميع الخيارات
        document.getElementById('transferOptions').style.display = 'none';
        document.getElementById('statusOptions').style.display = 'none';
        document.getElementById('promoteOptions').style.display = 'none';

        // إظهار الخيارات المناسبة
        if (operation === 'transfer') {
            document.getElementById('transferOptions').style.display = 'block';
        } else if (operation === 'change_status') {
            document.getElementById('statusOptions').style.display = 'block';
        } else if (operation === 'promote') {
            document.getElementById('promoteOptions').style.display = 'block';
        }
    });

    // تحديث قائمة الفصول عند تغيير المرحلة والصف في الترقية
    function updatePromoteClasses() {
        const stageGrade = document.getElementById('targetStageGrade').value;
        const classSelect = document.getElementById('targetClassId');
        const classOptions = classSelect.querySelectorAll('option');

        // إعداد مطابقة المراحل والصفوف مع مستويات الفصول
        const levelMapping = {
            // مرحلة الطفولة المبكرة
            'pre_nursery': ['ما قبل رياض الأطفال', 'ما قبل الروضة', 'حضانة', 'Pre-Nursery'],
            'nursery': ['رياض الأطفال', 'الروضة', 'Nursery'],
            'kg1': ['الروضة الأولى', 'KG1', 'روضة أولى', 'Kindergarten 1'],
            'kg2': ['الروضة الثانية', 'KG2', 'روضة ثانية', 'Kindergarten 2'],
            'pre_primary': ['التمهيدي', 'تمهيدي', 'Pre-Primary'],

            // المرحلة الابتدائية
            'primary_1': ['الصف الأول الابتدائي', 'أول ابتدائي', '1 ابتدائي', 'Grade 1'],
            'primary_2': ['الصف الثاني الابتدائي', 'ثاني ابتدائي', '2 ابتدائي', 'Grade 2'],
            'primary_3': ['الصف الثالث الابتدائي', 'ثالث ابتدائي', '3 ابتدائي', 'Grade 3'],
            'primary_4': ['الصف الرابع الابتدائي', 'رابع ابتدائي', '4 ابتدائي', 'Grade 4'],
            'primary_5': ['الصف الخامس الابتدائي', 'خامس ابتدائي', '5 ابتدائي', 'Grade 5'],
            'primary_6': ['الصف السادس الابتدائي', 'سادس ابتدائي', '6 ابتدائي', 'Grade 6'],

            // المرحلة الإعدادية/المتوسطة
            'middle_1': ['الصف الأول الإعدادي', 'أول إعدادي', '1 إعدادي', 'أول متوسط', 'Grade 7'],
            'middle_2': ['الصف الثاني الإعدادي', 'ثاني إعدادي', '2 إعدادي', 'ثاني متوسط', 'Grade 8'],
            'middle_3': ['الصف الثالث الإعدادي', 'ثالث إعدادي', '3 إعدادي', 'ثالث متوسط', 'Grade 9'],

            // المرحلة الثانوية
            'high_1': ['الصف الأول الثانوي', 'أول ثانوي', '1 ثانوي', 'Grade 10'],
            'high_2': ['الصف الثاني الثانوي', 'ثاني ثانوي', '2 ثانوي', 'Grade 11'],
            'high_3': ['الصف الثالث الثانوي', 'ثالث ثانوي', '3 ثانوي', 'Grade 12'],

            // التعليم الفني
            'tech_1': ['الصف الأول الفني', 'أول فني', 'فني أول'],
            'tech_2': ['الصف الثاني الفني', 'ثاني فني', 'فني ثاني'],
            'tech_3': ['الصف الثالث الفني', 'ثالث فني', 'فني ثالث']
        };

        classOptions.forEach(option => {
            if (option.value === '') {
                option.style.display = 'block';
                return;
            }

            if (!stageGrade) {
                option.style.display = 'block';
                return;
            }

            const classLevel = option.dataset.level || '';
            const targetLevels = levelMapping[stageGrade] || [];

            // البحث عن تطابق في مستوى الفصل
            const isMatch = targetLevels.some(level =>
                classLevel.includes(level) ||
                option.textContent.includes(level)
            );

            option.style.display = isMatch ? 'block' : 'none';
        });

        classSelect.value = '';
    }

    function executeBulkOperation() {
        const operation = document.getElementById('bulkOperation').value;
        const checkboxes = document.querySelectorAll('.student-checkbox:checked');

        if (!operation) {
            alert('يرجى اختيار العملية المطلوبة');
            return;
        }

        if (checkboxes.length === 0) {
            alert('يرجى تحديد طالب واحد على الأقل');
            return;
        }

        const studentIds = Array.from(checkboxes).map(cb => cb.value);
        const studentNames = Array.from(checkboxes).map(cb => cb.dataset.name);

        let confirmMessage = '';
        let formData = new FormData();

        formData.append('operation', operation);
        formData.append('csrf_token', '<?php echo generate_csrf_token(); ?>');
        studentIds.forEach(id => formData.append('student_ids[]', id));

        switch (operation) {
            case 'delete':
                confirmMessage = `هل أنت متأكد من حذف ${checkboxes.length} طالب؟\n\nسيتم حذف جميع البيانات المرتبطة بهم (الدرجات، الحضور، إلخ).\n\nهذه العملية لا يمكن التراجع عنها!`;
                break;

            case 'transfer':
                const newClassId = document.getElementById('newClassId').value;
                if (!newClassId) {
                    alert('يرجى اختيار الفصل الجديد');
                    return;
                }
                const className = document.getElementById('newClassId').selectedOptions[0].text;
                confirmMessage = `هل تريد نقل ${checkboxes.length} طالب إلى فصل: ${className}؟`;
                formData.append('new_class_id', newClassId);
                break;

            case 'change_status':
                const newStatus = document.getElementById('newStatus').value;
                if (!newStatus) {
                    alert('يرجى اختيار الحالة الجديدة');
                    return;
                }
                const statusText = document.getElementById('newStatus').selectedOptions[0].text;
                confirmMessage = `هل تريد تغيير حالة ${checkboxes.length} طالب إلى: ${statusText}؟`;
                formData.append('new_status', newStatus);
                break;

            case 'promote':
                const targetClassId = document.getElementById('targetClassId').value;
                if (!targetClassId) {
                    alert('يرجى اختيار الفصل المحدد للترقية');
                    return;
                }
                const targetClassName = document.getElementById('targetClassId').selectedOptions[0].text;
                const targetStageGrade = document.getElementById('targetStageGrade').selectedOptions[0].text;

                confirmMessage = `هل تريد ترقية ${checkboxes.length} طالب إلى:\n${targetStageGrade}\nفصل: ${targetClassName}؟`;
                formData.append('target_class_id', targetClassId);
                break;
        }

        if (confirm(confirmMessage)) {
            // إظهار مؤشر التحميل
            const executeBtn = event.target;
            const originalText = executeBtn.innerHTML;
            executeBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التنفيذ...';
            executeBtn.disabled = true;

            fetch('bulk_operations.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`تمت العملية بنجاح!\n\nنجح: ${data.success_count}\nفشل: ${data.error_count}`);
                    location.reload();
                } else {
                    alert('حدث خطأ: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال');
            })
            .finally(() => {
                executeBtn.innerHTML = originalText;
                executeBtn.disabled = false;
            });
        }
    }

    // تحديث الصفوف في الفلترة
    function updateGrades() {
        const stageId = document.getElementById('stage_id').value;
        const gradeSelect = document.getElementById('grade_id');
        const gradeOptions = gradeSelect.querySelectorAll('option');

        gradeOptions.forEach(option => {
            if (option.value === '' || option.dataset.stage === stageId) {
                option.style.display = 'block';
            } else {
                option.style.display = 'none';
            }
        });

        gradeSelect.value = '';
    }

    function clearFilters() {
        document.getElementById('advancedFilterForm').reset();
        window.location.href = 'index.php';
    }
</script>

<!-- Include XLSX library for Excel export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

<?php require_once '../includes/footer.php'; ?>
