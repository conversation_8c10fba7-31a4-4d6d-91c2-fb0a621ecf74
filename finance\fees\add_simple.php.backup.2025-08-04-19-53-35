<?php
require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/security.php';

// التحقق من الجلسة والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

$error_message = '';
$success_message = '';

// معالجة إضافة الرسم
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من رمز CSRF
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = __('invalid_request');
    } else {
        // جمع البيانات وتنظيفها
        $student_id = intval($_POST['student_id'] ?? 0);
        $fee_type_id = intval($_POST['fee_type_id'] ?? 0);
        $semester = clean_input($_POST['semester'] ?? '');
        $amount = floatval($_POST['amount'] ?? 0);
        $discount = floatval($_POST['discount'] ?? 0);
        $due_date = clean_input($_POST['due_date'] ?? '');
        $notes = clean_input($_POST['notes'] ?? '');
        
        // التحقق من صحة البيانات
        if ($student_id <= 0) {
            $error_message = __('invalid_student');
        } elseif ($fee_type_id <= 0) {
            $error_message = __('invalid_fee_type');
        } elseif ($amount <= 0) {
            $error_message = __('invalid_amount');
        } elseif (empty($due_date)) {
            $error_message = __('invalid_due_date');
        } else {
            try {
                // بدء المعاملة
                $conn->begin_transaction();
                
                // حساب المبلغ النهائي
                $final_amount = $amount - $discount;
                
                // جلب اسم نوع الرسم
                $fee_type_stmt = $conn->prepare("SELECT type_name FROM fee_types WHERE id = ?");
                $fee_type_stmt->bind_param("i", $fee_type_id);
                $fee_type_stmt->execute();
                $fee_type_result = $fee_type_stmt->get_result();
                $fee_type_name = 'رسم عام';
                if ($fee_type_result->num_rows > 0) {
                    $fee_type_name = $fee_type_result->fetch_assoc()['type_name'];
                }
                
                // إضافة الرسم - استعلام مبسط
                $fee_stmt = $conn->prepare("
                    INSERT INTO student_fees (
                        student_id, fee_type_id, semester, base_amount, discount_amount, 
                        final_amount, remaining_amount, due_date, status, notes, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, 0, ?, 'paid', ?, NOW())
                ");
                
                if (!$fee_stmt) {
                    throw new Exception("خطأ في تحضير استعلام الرسم: " . $conn->error);
                }
                
                // ربط المعاملات: i i s d d d s s (8 معاملات)
                $fee_stmt->bind_param("iisdddsss",
                    $student_id,    // i
                    $fee_type_id,   // i
                    $semester,      // s
                    $amount,        // d
                    $discount,      // d
                    $final_amount,  // d
                    $due_date,      // s
                    $notes          // s
                );
                
                if (!$fee_stmt->execute()) {
                    throw new Exception("خطأ في إضافة الرسم: " . $conn->error);
                }
                
                $fee_id = $conn->insert_id;
                
                // إنشاء دفعة تلقائية
                $payment_reference = 'FEE-' . date('YmdHis') . '-' . str_pad($fee_id, 6, '0', STR_PAD_LEFT);
                $receipt_number = 'REC-' . date('YmdHis') . '-' . str_pad($fee_id, 6, '0', STR_PAD_LEFT);
                
                $payment_stmt = $conn->prepare("
                    INSERT INTO student_payments (
                        student_id, student_fee_id, payment_reference, amount, payment_method,
                        payment_date, receipt_number, notes, status, processed_by, processed_at, created_at
                    ) VALUES (?, ?, ?, ?, 'cash', ?, ?, ?, 'confirmed', ?, NOW(), NOW())
                ");
                
                if (!$payment_stmt) {
                    throw new Exception("خطأ في تحضير استعلام الدفعة: " . $conn->error);
                }
                
                $payment_notes = 'دفع رسم تلقائي - ' . $fee_type_name;
                $current_user_id = $_SESSION['user_id'] ?? 1;
                
                // ربط المعاملات: i i s d s s s i (8 معاملات)
                $payment_stmt->bind_param("iisdsssi", 
                    $student_id,        // i
                    $fee_id,            // i
                    $payment_reference, // s
                    $final_amount,      // d
                    $due_date,          // s
                    $receipt_number,    // s
                    $payment_notes,     // s
                    $current_user_id    // i
                );
                
                if (!$payment_stmt->execute()) {
                    throw new Exception("خطأ في إضافة الدفعة: " . $conn->error);
                }
                
                // تأكيد المعاملة
                $conn->commit();
                
                // إعادة توجيه مع رسالة نجاح
                header("Location: index.php?success=1&fee_type=" . urlencode($fee_type_name) . "&amount=" . $final_amount);
                exit();
                
            } catch (Exception $e) {
                // إلغاء المعاملة
                $conn->rollback();
                $error_message = $e->getMessage();
                error_log("Add fee error: " . $e->getMessage());
            }
        }
    }
}

// جلب قوائم البيانات
$students = $conn->query("
    SELECT s.id, u.full_name, s.student_id as student_number, c.class_name
    FROM students s
    JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    WHERE s.status = 'active'
    ORDER BY u.full_name
");

$fee_types = $conn->query("
    SELECT id, type_name, description 
    FROM fee_types 
    WHERE status = 'active' 
    ORDER BY type_name
");

$page_title = __('add_fee');
include_once '../../includes/header.php';
?>

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0"><i class="fas fa-plus me-2"></i><?php echo __('add_fee'); ?></h4>
                        <a href="index.php" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-left me-1"></i><?php echo __('back'); ?>
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                    </div>
                    <?php endif; ?>
                    
                    <!-- تنبيه مهم -->
                    <div class="alert alert-info">
                        <h6 class="alert-heading"><i class="fas fa-info-circle me-2"></i><?php echo __('important_note'); ?></h6>
                        <p class="mb-0"><?php echo __('fee_auto_payment_note'); ?></p>
                    </div>
                    
                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        
                        <!-- اختيار الطالب -->
                        <div class="mb-3">
                            <label class="form-label"><?php echo __('student'); ?> <span class="text-danger">*</span></label>
                            <select class="form-select" name="student_id" required>
                                <option value=""><?php echo __('select_student'); ?></option>
                                <?php while ($student = $students->fetch_assoc()): ?>
                                    <option value="<?php echo $student['id']; ?>">
                                        <?php echo htmlspecialchars($student['full_name']); ?> 
                                        (ID: <?php echo htmlspecialchars($student['student_number']); ?>)
                                        <?php if (!empty($student['class_name'])): ?>
                                            - <?php echo htmlspecialchars($student['class_name']); ?>
                                        <?php endif; ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                        
                        <!-- نوع الرسم -->
                        <div class="mb-3">
                            <label class="form-label"><?php echo __('fee_type'); ?> <span class="text-danger">*</span></label>
                            <select class="form-select" name="fee_type_id" required>
                                <option value=""><?php echo __('select_fee_type'); ?></option>
                                <?php while ($fee_type = $fee_types->fetch_assoc()): ?>
                                    <option value="<?php echo $fee_type['id']; ?>">
                                        <?php echo htmlspecialchars($fee_type['type_name']); ?>
                                        <?php if (!empty($fee_type['description'])): ?>
                                            - <?php echo htmlspecialchars($fee_type['description']); ?>
                                        <?php endif; ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                        
                        <div class="row">
                            <!-- الفصل الدراسي -->
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label"><?php echo __('semester'); ?> <span class="text-danger">*</span></label>
                                    <select class="form-select" name="semester" required>
                                        <option value="first"><?php echo __('first_semester'); ?></option>
                                        <option value="second"><?php echo __('second_semester'); ?></option>
                                        <option value="annual"><?php echo __('annual'); ?></option>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- المبلغ -->
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label"><?php echo __('amount'); ?> <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" name="amount" min="0" step="0.01" required>
                                        <span class="input-group-text"><?php echo get_system_setting('currency_symbol', 'ر.س'); ?></span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- الخصم -->
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label"><?php echo __('discount'); ?></label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" name="discount" min="0" step="0.01" value="0">
                                        <span class="input-group-text"><?php echo get_system_setting('currency_symbol', 'ر.س'); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- تاريخ الاستحقاق -->
                        <div class="mb-3">
                            <label class="form-label"><?php echo __('due_date'); ?> <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" name="due_date" value="<?php echo date('Y-m-d'); ?>" required>
                        </div>
                        
                        <!-- ملاحظات -->
                        <div class="mb-4">
                            <label class="form-label"><?php echo __('notes'); ?></label>
                            <textarea class="form-control" name="notes" rows="3" placeholder="<?php echo __('notes_placeholder'); ?>"></textarea>
                        </div>
                        
                        <!-- أزرار التحكم -->
                        <div class="d-flex justify-content-between">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i><?php echo __('cancel'); ?>
                            </a>
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-save me-2"></i><?php echo __('add_fee'); ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once '../../includes/footer.php'; ?>
