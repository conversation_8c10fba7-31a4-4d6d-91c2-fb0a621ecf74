<?php
/**
 * صفحة تعديل حساب بنكي
 * Edit Bank Account Page
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    header('Location: ../../dashboard/');
    exit();
}

$error_message = '';
$success_message = '';

// التحقق من وجود معرف الحساب
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: bank_accounts.php?error=' . urlencode('معرف الحساب غير صالح'));
    exit();
}

$account_id = intval($_GET['id']);

// جلب بيانات الحساب
$stmt = $conn->prepare("SELECT * FROM bank_accounts WHERE id = ?");
$stmt->bind_param("i", $account_id);
$stmt->execute();
$account = $stmt->get_result()->fetch_assoc();

if (!$account) {
    header('Location: bank_accounts.php?error=' . urlencode('الحساب غير موجود'));
    exit();
}

// جلب جميع أنواع الرسوم
$fee_types_query = "SELECT id, type_name, description FROM fee_types ORDER BY type_name";
$fee_types_result = $conn->query($fee_types_query);

// جلب أنواع الرسوم المرتبطة بهذا الحساب حالياً
$linked_fee_types_query = "SELECT fee_type_id FROM bank_account_fee_types WHERE bank_account_id = ?";
$linked_stmt = $conn->prepare($linked_fee_types_query);
$linked_stmt->bind_param("i", $account_id);
$linked_stmt->execute();
$linked_result = $linked_stmt->get_result();

$linked_fee_types = [];
while ($row = $linked_result->fetch_assoc()) {
    $linked_fee_types[] = $row['fee_type_id'];
}

// معالجة تحديث الحساب
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من رمز CSRF
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'طلب غير صالح';
    } else {
        $bank_name = clean_input($_POST['bank_name'] ?? '');
        $account_number = clean_input($_POST['account_number'] ?? '');
        $account_name = clean_input($_POST['account_name'] ?? '');
        $iban = clean_input($_POST['iban'] ?? '');
        $swift_code = clean_input($_POST['swift_code'] ?? '');
        $branch_name = clean_input($_POST['branch_name'] ?? '');
        $notes = clean_input($_POST['notes'] ?? '');
        $is_active = isset($_POST['is_active']) ? 1 : 0;
        $fee_types = $_POST['fee_types'] ?? [];

        if (empty($bank_name) || empty($account_number)) {
            $error_message = 'اسم البنك ورقم الحساب مطلوبان';
        } else {
            // التحقق من عدم تكرار الحساب (باستثناء الحساب الحالي)
            $check_stmt = $conn->prepare("SELECT id FROM bank_accounts WHERE bank_name = ? AND account_number = ? AND id != ?");
            $check_stmt->bind_param("ssi", $bank_name, $account_number, $account_id);
            $check_stmt->execute();
            $existing = $check_stmt->get_result()->fetch_assoc();

            if ($existing) {
                $error_message = 'هذا الحساب موجود مسبقاً';
            } else {
                $conn->begin_transaction();

                try {
                    // تحديث بيانات الحساب البنكي
                    $update_stmt = $conn->prepare("UPDATE bank_accounts SET bank_name = ?, account_number = ?, account_name = ?, iban = ?, swift_code = ?, branch_name = ?, notes = ?, is_active = ? WHERE id = ?");
                    $update_stmt->bind_param("sssssssii", $bank_name, $account_number, $account_name, $iban, $swift_code, $branch_name, $notes, $is_active, $account_id);
                    $update_stmt->execute();

                    // حذف الربط القديم مع أنواع الرسوم
                    $delete_relations_stmt = $conn->prepare("DELETE FROM bank_account_fee_types WHERE bank_account_id = ?");
                    $delete_relations_stmt->bind_param("i", $account_id);
                    $delete_relations_stmt->execute();

                    // إضافة الربط الجديد مع أنواع الرسوم
                    if (!empty($fee_types)) {
                        $insert_relation_stmt = $conn->prepare("INSERT INTO bank_account_fee_types (bank_account_id, fee_type_id, is_default, created_by) VALUES (?, ?, 0, ?)");

                        foreach ($fee_types as $fee_type_id) {
                            $fee_type_id = intval($fee_type_id);
                            if ($fee_type_id > 0) {
                                $insert_relation_stmt->bind_param("iii", $account_id, $fee_type_id, $_SESSION['user_id']);
                                $insert_relation_stmt->execute();
                            }
                        }
                    }

                    $conn->commit();

                    // إعادة توجيه مع رسالة نجاح
                    header('Location: bank_accounts.php?success=' . urlencode('تم تحديث الحساب البنكي وربطه بأنواع الرسوم بنجاح'));
                    exit();

                } catch (Exception $e) {
                    $conn->rollback();
                    $error_message = 'خطأ في تحديث الحساب البنكي: ' . $e->getMessage();
                }
            }
        }
    }
}

$page_title = 'تعديل الحساب البنكي';
include_once '../../includes/header.php';
?>

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0"><i class="fas fa-edit me-2"></i>تعديل الحساب البنكي</h4>
                </div>
                <div class="card-body">
                    <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                    </div>
                    <?php endif; ?>

                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم البنك <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" name="bank_name" value="<?php echo htmlspecialchars($_POST['bank_name'] ?? $account['bank_name'] ?? ''); ?>" required>
                                    <small class="text-muted">مثال: البنك الأهلي السعودي</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">رقم الحساب <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" name="account_number" value="<?php echo htmlspecialchars($_POST['account_number'] ?? $account['account_number'] ?? ''); ?>" required>
                                    <small class="text-muted">رقم الحساب البنكي</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم صاحب الحساب</label>
                                    <input type="text" class="form-control" name="account_name" value="<?php echo htmlspecialchars($_POST['account_name'] ?? $account['account_name'] ?? ''); ?>">
                                    <small class="text-muted">اسم المؤسسة أو الشخص</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">رقم الآيبان (IBAN)</label>
                                    <input type="text" class="form-control" name="iban" value="<?php echo htmlspecialchars($_POST['iban'] ?? $account['iban'] ?? ''); ?>" placeholder="SA...">
                                    <small class="text-muted">رقم الحساب المصرفي الدولي</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">رمز السويفت (SWIFT)</label>
                                    <input type="text" class="form-control" name="swift_code" value="<?php echo htmlspecialchars($_POST['swift_code'] ?? $account['swift_code'] ?? ''); ?>">
                                    <small class="text-muted">رمز التحويل الدولي</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم الفرع</label>
                                    <input type="text" class="form-control" name="branch_name" value="<?php echo htmlspecialchars($_POST['branch_name'] ?? $account['branch_name'] ?? ''); ?>">
                                    <small class="text-muted">فرع البنك</small>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" name="notes" rows="3" placeholder="أي ملاحظات إضافية..."><?php echo htmlspecialchars($_POST['notes'] ?? $account['notes'] ?? ''); ?></textarea>
                        </div>

                        <!-- ربط أنواع الرسوم -->
                        <div class="card border-info mb-4">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0"><i class="fas fa-link me-2"></i>ربط أنواع الرسوم</h6>
                            </div>
                            <div class="card-body">
                                <p class="text-muted mb-3">اختر أنواع الرسوم التي سيتم ربطها بهذا الحساب البنكي:</p>

                                <?php if ($fee_types_result && $fee_types_result->num_rows > 0): ?>
                                    <div class="mb-3">
                                        <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="selectAllFeeTypes()">
                                            <i class="fas fa-check-square me-1"></i>تحديد الكل
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="deselectAllFeeTypes()">
                                            <i class="fas fa-square me-1"></i>إلغاء تحديد الكل
                                        </button>
                                    </div>

                                    <div class="row">
                                        <?php while ($fee_type = $fee_types_result->fetch_assoc()): ?>
                                            <div class="col-md-6 mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox"
                                                           name="fee_types[]"
                                                           value="<?php echo $fee_type['id']; ?>"
                                                           id="fee_type_<?php echo $fee_type['id']; ?>"
                                                           <?php echo in_array($fee_type['id'], $linked_fee_types) ? 'checked' : ''; ?>>
                                                    <label class="form-check-label" for="fee_type_<?php echo $fee_type['id']; ?>">
                                                        <strong><?php echo htmlspecialchars($fee_type['type_name']); ?></strong>
                                                        <?php if (!empty($fee_type['description'])): ?>
                                                            <br><small class="text-muted"><?php echo htmlspecialchars($fee_type['description']); ?></small>
                                                        <?php endif; ?>
                                                    </label>
                                                </div>
                                            </div>
                                        <?php endwhile; ?>
                                    </div>

                                    <div class="alert alert-info mt-3">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>ملاحظة:</strong> عند إضافة أقساط جديدة، سيظهر هذا الحساب كخيار متاح فقط لأنواع الرسوم المحددة أعلاه.
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        لا توجد أنواع رسوم متاحة للربط. يرجى إضافة أنواع الرسوم أولاً.
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_active" id="is_active"
                                       <?php echo (isset($_POST['is_active']) ? 'checked' : ($account['is_active'] ? 'checked' : '')); ?>>
                                <label class="form-check-label" for="is_active">
                                    <strong>الحساب نشط</strong>
                                </label>
                                <small class="form-text text-muted d-block">إلغاء التفعيل سيمنع استخدام هذا الحساب في الأقساط الجديدة</small>
                            </div>
                        </div>

                        <!-- أزرار التحكم -->
                        <div class="d-flex justify-content-between">
                            <a href="bank_accounts.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>العودة
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-2"></i>حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function selectAllFeeTypes() {
    const checkboxes = document.querySelectorAll('input[name="fee_types[]"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
}

function deselectAllFeeTypes() {
    const checkboxes = document.querySelectorAll('input[name="fee_types[]"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
}

// تأكيد الحفظ عند عدم وجود تحديد لأنواع الرسوم
function confirmSave() {
    const checkedCount = document.querySelectorAll('input[name="fee_types[]"]:checked').length;
    if (checkedCount === 0) {
        return confirm('لم تقم بتحديد أي نوع من أنواع الرسوم. هل تريد المتابعة؟');
    }
    return true;
}

// ربط دالة التأكيد بالنموذج
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            if (!confirmSave()) {
                e.preventDefault();
            }
        });
    }
});
</script>

<?php include_once '../../includes/footer.php'; ?>


