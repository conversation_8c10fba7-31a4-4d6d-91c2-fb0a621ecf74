<?php
/**
 * معاينة المصروف اليومي
 * View Daily Expense
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    header('Location: ../../dashboard/');
    exit();
}

// التحقق من وجود معرف المصروف
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error_message'] = 'معرف المصروف غير صحيح';
    header('Location: index.php');
    exit();
}

$expense_id = intval($_GET['id']);

// جلب بيانات المصروف
$expense_query = "
    SELECT 
        de.*,
        ec.category_name,
        ec.icon,
        ec.color,
        u.full_name as created_by_name,
        au.full_name as approved_by_name
    FROM daily_expenses de
    JOIN expense_categories ec ON de.category_id = ec.id
    LEFT JOIN users u ON de.created_by = u.id
    LEFT JOIN users au ON de.approved_by = au.id
    WHERE de.id = ?
";

$expense_stmt = $conn->prepare($expense_query);
$expense_stmt->bind_param("i", $expense_id);
$expense_stmt->execute();
$expense_result = $expense_stmt->get_result();

if ($expense_result->num_rows === 0) {
    $_SESSION['error_message'] = 'المصروف غير موجود';
    header('Location: index.php');
    exit();
}

$expense = $expense_result->fetch_assoc();

$page_title = 'معاينة المصروف - ' . $expense['description'];
include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-eye me-2"></i>معاينة المصروف
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="../index.php">المالية</a></li>
                    <li class="breadcrumb-item"><a href="index.php">المصروفات</a></li>
                    <li class="breadcrumb-item active">معاينة المصروف</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
            </a>
            <a href="edit.php?id=<?php echo $expense['id']; ?>" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>تعديل
            </a>
        </div>
    </div>

    <div class="row">
        <!-- معلومات المصروف الأساسية -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>معلومات المصروف
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">التاريخ:</label>
                            <p class="mb-0"><?php echo date('d/m/Y', strtotime($expense['expense_date'])); ?></p>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">الفئة:</label>
                            <p class="mb-0">
                                <span class="badge" style="background-color: <?php echo $expense['color']; ?>">
                                    <i class="<?php echo $expense['icon']; ?> me-1"></i>
                                    <?php echo htmlspecialchars($expense['category_name']); ?>
                                </span>
                            </p>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">المبلغ:</label>
                            <p class="mb-0 fs-5 fw-bold text-success"><?php echo format_currency($expense['amount']); ?></p>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">طريقة الدفع:</label>
                            <p class="mb-0">
                                <?php
                                $payment_methods = [
                                    'cash' => ['نقدي', 'success'],
                                    'bank_transfer' => ['تحويل بنكي', 'info'],
                                    'credit_card' => ['بطاقة ائتمان', 'warning'],
                                    'check' => ['شيك', 'secondary']
                                ];
                                $method = $payment_methods[$expense['payment_method']] ?? ['غير محدد', 'light'];
                                ?>
                                <span class="badge bg-<?php echo $method[1]; ?>"><?php echo $method[0]; ?></span>
                            </p>
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label class="form-label fw-bold">الوصف:</label>
                            <p class="mb-0"><?php echo htmlspecialchars($expense['description']); ?></p>
                        </div>
                        
                        <?php if ($expense['receipt_number']): ?>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">رقم الإيصال:</label>
                            <p class="mb-0"><?php echo htmlspecialchars($expense['receipt_number']); ?></p>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($expense['vendor_name']): ?>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">اسم المورد:</label>
                            <p class="mb-0"><?php echo htmlspecialchars($expense['vendor_name']); ?></p>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($expense['vendor_phone']): ?>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">هاتف المورد:</label>
                            <p class="mb-0"><?php echo htmlspecialchars($expense['vendor_phone']); ?></p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- معلومات الحالة والموافقة -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-check-circle me-2"></i>حالة المصروف
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label fw-bold">الحالة الحالية:</label>
                        <p class="mb-0">
                            <?php
                            $status_badges = [
                                'pending' => ['في انتظار الموافقة', 'warning'],
                                'approved' => ['معتمد', 'success'],
                                'rejected' => ['مرفوض', 'danger'],
                                'paid' => ['مدفوع', 'info']
                            ];
                            $status = $status_badges[$expense['status']] ?? ['غير محدد', 'light'];
                            ?>
                            <span class="badge bg-<?php echo $status[1]; ?> fs-6"><?php echo $status[0]; ?></span>
                        </p>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label fw-bold">مسجل بواسطة:</label>
                        <p class="mb-0"><?php echo htmlspecialchars($expense['created_by_name'] ?? 'غير محدد'); ?></p>
                        <small class="text-muted">
                            <?php echo date('d/m/Y H:i', strtotime($expense['created_at'])); ?>
                        </small>
                    </div>
                    
                    <?php if ($expense['approved_by_name']): ?>
                    <div class="mb-3">
                        <label class="form-label fw-bold">معتمد بواسطة:</label>
                        <p class="mb-0"><?php echo htmlspecialchars($expense['approved_by_name']); ?></p>
                        <?php if ($expense['approved_at']): ?>
                        <small class="text-muted">
                            <?php echo date('d/m/Y H:i', strtotime($expense['approved_at'])); ?>
                        </small>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($expense['updated_at'] != $expense['created_at']): ?>
                    <div class="mb-3">
                        <label class="form-label fw-bold">آخر تحديث:</label>
                        <small class="text-muted d-block">
                            <?php echo date('d/m/Y H:i', strtotime($expense['updated_at'])); ?>
                        </small>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- إجراءات سريعة -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tools me-2"></i>إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="edit.php?id=<?php echo $expense['id']; ?>" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>تعديل المصروف
                        </a>
                        
                        <?php if ($expense['status'] == 'pending'): ?>
                        <button class="btn btn-success" onclick="approveExpense(<?php echo $expense['id']; ?>)">
                            <i class="fas fa-check me-2"></i>اعتماد المصروف
                        </button>
                        <button class="btn btn-danger" onclick="rejectExpense(<?php echo $expense['id']; ?>)">
                            <i class="fas fa-times me-2"></i>رفض المصروف
                        </button>
                        <?php endif; ?>
                        
                        <button class="btn btn-outline-danger" onclick="deleteExpense(<?php echo $expense['id']; ?>)">
                            <i class="fas fa-trash me-2"></i>حذف المصروف
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once '../../includes/footer.php'; ?>

<script>
function approveExpense(expenseId) {
    if (confirm('هل أنت متأكد من اعتماد هذا المصروف؟')) {
        fetch('approve_expense.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                expense_id: expenseId,
                action: 'approve'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
        });
    }
}

function rejectExpense(expenseId) {
    if (confirm('هل أنت متأكد من رفض هذا المصروف؟')) {
        fetch('approve_expense.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                expense_id: expenseId,
                action: 'reject'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
        });
    }
}

function deleteExpense(expenseId) {
    if (confirm('هل أنت متأكد من حذف هذا المصروف؟ لا يمكن التراجع عن هذا الإجراء.')) {
        window.location.href = 'delete.php?id=' + expenseId;
    }
}
</script>
