<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';
require_once 'WhatsAppService.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('teacher')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'غير مصرح']);
    exit();
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'طريقة طلب غير مدعومة']);
    exit();
}

// التحقق من نوع المحتوى
$content_type = $_SERVER['CONTENT_TYPE'] ?? '';
if (strpos($content_type, 'application/json') !== false) {
    $input = json_decode(file_get_contents('php://input'), true);
} else {
    $input = $_POST;
}

global $conn;

try {
    // استخراج البيانات
    $student_id = intval($input['student_id'] ?? 0);
    $subject = clean_input($input['subject'] ?? '');
    $message = clean_input($input['message'] ?? '');
    $message_type = clean_input($input['message_type'] ?? 'general');
    $priority = clean_input($input['priority'] ?? 'medium');
    $send_via = clean_input($input['send_via'] ?? 'whatsapp');
    
    // التحقق من البيانات المطلوبة
    $errors = [];
    
    if ($student_id <= 0) {
        $errors[] = 'معرف الطالب مطلوب';
    }
    if (empty($subject)) {
        $errors[] = 'موضوع الرسالة مطلوب';
    }
    if (empty($message)) {
        $errors[] = 'محتوى الرسالة مطلوب';
    }
    
    if (!empty($errors)) {
        echo json_encode(['success' => false, 'errors' => $errors]);
        exit();
    }
    
    // جلب بيانات الطالب
    $student_query = "
        SELECT 
            s.id,
            s.student_id as student_number,
            u.full_name as student_name,
            s.parent_phone,
            c.class_name
        FROM students s
        JOIN users u ON s.user_id = u.id
        LEFT JOIN classes c ON s.class_id = c.id
        WHERE s.id = ? AND s.status = 'active'
    ";
    
    $stmt = $conn->prepare($student_query);
    $stmt->bind_param('i', $student_id);
    $stmt->execute();
    $student = $stmt->get_result()->fetch_assoc();
    $stmt->close();
    
    if (!$student) {
        echo json_encode(['success' => false, 'error' => 'الطالب غير موجود أو غير نشط']);
        exit();
    }
    
    if (empty($student['parent_phone'])) {
        echo json_encode(['success' => false, 'error' => 'لا يوجد رقم هاتف لولي الأمر']);
        exit();
    }
    
    // إعداد الرسالة الكاملة
    $full_message = "السلام عليكم ولي أمر الطالب/ة " . $student['student_name'] . "\n\n" . $message . "\n\nمع تحيات إدارة المدرسة";
    
    // حفظ الرسالة في قاعدة البيانات
    $insert_query = "
        INSERT INTO parent_communications 
        (student_id, parent_phone, message_type, subject, message, priority, sent_via, status, sent_by, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', ?, NOW())
    ";
    
    $stmt = $conn->prepare($insert_query);
    $stmt->bind_param('issssssi', 
        $student_id, 
        $student['parent_phone'], 
        $message_type, 
        $subject, 
        $full_message, 
        $priority, 
        $send_via, 
        $_SESSION['user_id']
    );
    
    if (!$stmt->execute()) {
        throw new Exception('فشل في حفظ الرسالة: ' . $stmt->error);
    }
    
    $message_id = $conn->insert_id;
    $stmt->close();
    
    // إرسال الرسالة
    $send_result = ['success' => true, 'message' => 'تم حفظ الرسالة'];
    
    if ($send_via === 'whatsapp') {
        $whatsapp = new WhatsAppService($conn);
        $send_result = $whatsapp->sendMessage($student['parent_phone'], $full_message, $message_id);
    } elseif ($send_via === 'sms') {
        // يمكن إضافة خدمة SMS هنا
        $send_result = ['success' => true, 'message' => 'تم إرسال الرسالة النصية'];
    } elseif ($send_via === 'email') {
        // يمكن إضافة خدمة البريد الإلكتروني هنا
        $send_result = ['success' => true, 'message' => 'تم إرسال البريد الإلكتروني'];
    }
    
    // تحديث حالة الرسالة
    if ($send_result['success']) {
        $update_query = "UPDATE parent_communications SET status = 'sent', sent_at = NOW() WHERE id = ?";
        $stmt = $conn->prepare($update_query);
        $stmt->bind_param('i', $message_id);
        $stmt->execute();
        $stmt->close();
    } else {
        $update_query = "UPDATE parent_communications SET status = 'failed' WHERE id = ?";
        $stmt = $conn->prepare($update_query);
        $stmt->bind_param('i', $message_id);
        $stmt->execute();
        $stmt->close();
    }
    
    // إرجاع النتيجة
    echo json_encode([
        'success' => $send_result['success'],
        'message' => $send_result['message'] ?? ($send_result['success'] ? 'تم إرسال الرسالة بنجاح' : 'فشل في إرسال الرسالة'),
        'message_id' => $message_id,
        'student' => $student,
        'sent_via' => $send_via,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    error_log("Error in send_message_ajax.php: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'error' => 'حدث خطأ في إرسال الرسالة: ' . $e->getMessage()
    ]);
}
?>
