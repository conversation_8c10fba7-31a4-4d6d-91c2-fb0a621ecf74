<?php
require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/security.php';

check_session();
if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    header('Location: ../../dashboard/');
    exit();
}

$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = __('invalid_request');
    } else {
        $student_id = intval($_POST['student_id'] ?? 0);
        $fee_type_id = intval($_POST['fee_type_id'] ?? 0);
        $semester = clean_input($_POST['semester'] ?? '');
        $amount = floatval($_POST['amount'] ?? 0);
        $discount = floatval($_POST['discount'] ?? 0);
        $due_date = clean_input($_POST['due_date'] ?? '');
        $notes = clean_input($_POST['notes'] ?? '');
        
        if ($student_id <= 0 || $fee_type_id <= 0 || $amount <= 0 || empty($due_date)) {
            $error_message = 'يرجى ملء جميع الحقول المطلوبة';
        } else {
            try {
                $conn->begin_transaction();
                
                $final_amount = $amount - $discount;
                
                // جلب اسم نوع الرسم
                $fee_type_stmt = $conn->prepare("SELECT type_name FROM fee_types WHERE id = ?");
                $fee_type_stmt->bind_param("i", $fee_type_id);
                $fee_type_stmt->execute();
                $fee_type_result = $fee_type_stmt->get_result();
                $fee_type_name = 'رسم عام';
                if ($fee_type_result->num_rows > 0) {
                    $fee_type_name = $fee_type_result->fetch_assoc()['type_name'];
                }
                
                // إضافة الرسم مع القيم الافتراضية
                $fee_stmt = $conn->prepare("
                    INSERT INTO student_fees (
                        student_id, fee_type_id, fee_structure_id, academic_year_id, semester, 
                        base_amount, discount_amount, final_amount, remaining_amount, due_date, status, notes, created_at
                    ) VALUES (?, ?, 1, 1, ?, ?, ?, ?, 0, ?, 'paid', ?, NOW())
                ");
                
                if (!$fee_stmt) {
                    throw new Exception("خطأ في تحضير الاستعلام: " . $conn->error);
                }
                
                $fee_stmt->bind_param("iisdddss",
                    $student_id, $fee_type_id, $semester, $amount, $discount, $final_amount, $due_date, $notes
                );
                
                if (!$fee_stmt->execute()) {
                    throw new Exception("خطأ في إضافة الرسم: " . $conn->error);
                }
                
                $fee_id = $conn->insert_id;
                
                // إنشاء دفعة تلقائية
                $payment_reference = 'FEE-' . date('YmdHis') . '-' . str_pad($fee_id, 6, '0', STR_PAD_LEFT);
                $receipt_number = 'REC-' . date('YmdHis') . '-' . str_pad($fee_id, 6, '0', STR_PAD_LEFT);
                
                $payment_stmt = $conn->prepare("
                    INSERT INTO student_payments (
                        student_id, student_fee_id, payment_reference, amount, payment_method,
                        payment_date, receipt_number, notes, status, processed_by, processed_at, created_at
                    ) VALUES (?, ?, ?, ?, 'cash', ?, ?, ?, 'confirmed', ?, NOW(), NOW())
                ");
                
                if (!$payment_stmt) {
                    throw new Exception("خطأ في تحضير استعلام الدفعة: " . $conn->error);
                }
                
                $payment_notes = 'دفع رسم تلقائي - ' . $fee_type_name;
                $current_user_id = $_SESSION['user_id'] ?? 1;
                
                $payment_stmt->bind_param("iisdsssi", 
                    $student_id, $fee_id, $payment_reference, $final_amount, 
                    $due_date, $receipt_number, $payment_notes, $current_user_id
                );
                
                if (!$payment_stmt->execute()) {
                    throw new Exception("خطأ في إضافة الدفعة: " . $conn->error);
                }
                
                $conn->commit();
                header("Location: index.php?success=1&fee_type=" . urlencode($fee_type_name) . "&amount=" . $final_amount);
                exit();
                
            } catch (Exception $e) {
                $conn->rollback();
                $error_message = $e->getMessage();
            }
        }
    }
}

$students = $conn->query("
    SELECT s.id, u.full_name, s.student_id as student_number, c.class_name
    FROM students s
    JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    WHERE s.status = 'active'
    ORDER BY u.full_name
");

$fee_types = $conn->query("
    SELECT id, type_name, description 
    FROM fee_types 
    WHERE status = 'active' 
    ORDER BY type_name
");

$page_title = __('add_fee');
include_once '../../includes/header.php';
?>

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0"><i class="fas fa-plus me-2"></i><?php echo __('add_fee'); ?></h4>
                </div>
                <div class="card-body">
                    <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                    </div>
                    <?php endif; ?>
                    
                    <div class="alert alert-info">
                        <p class="mb-0">عند إضافة رسم جديد، سيتم اعتباره مدفوعاً تلقائياً وسيظهر في سجل المدفوعات.</p>
                    </div>
                    
                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        
                        <div class="mb-3">
                            <label class="form-label">الطالب <span class="text-danger">*</span></label>
                            <select class="form-select" name="student_id" required>
                                <option value="">اختر الطالب</option>
                                <?php while ($student = $students->fetch_assoc()): ?>
                                    <option value="<?php echo $student['id']; ?>">
                                        <?php echo htmlspecialchars($student['full_name']); ?> 
                                        (ID: <?php echo htmlspecialchars($student['student_number']); ?>)
                                        <?php if (!empty($student['class_name'])): ?>
                                            - <?php echo htmlspecialchars($student['class_name']); ?>
                                        <?php endif; ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">نوع الرسم <span class="text-danger">*</span></label>
                            <select class="form-select" name="fee_type_id" required>
                                <option value="">اختر نوع الرسم</option>
                                <?php while ($fee_type = $fee_types->fetch_assoc()): ?>
                                    <option value="<?php echo $fee_type['id']; ?>">
                                        <?php echo htmlspecialchars($fee_type['type_name']); ?>
                                        <?php if (!empty($fee_type['description'])): ?>
                                            - <?php echo htmlspecialchars($fee_type['description']); ?>
                                        <?php endif; ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">الفصل الدراسي <span class="text-danger">*</span></label>
                                    <select class="form-select" name="semester" required>
                                        <option value="first">الفصل الأول</option>
                                        <option value="second">الفصل الثاني</option>
                                        <option value="annual">سنوي</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">المبلغ <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" name="amount" min="0" step="0.01" required>
                                        <span class="input-group-text"><?php echo get_system_setting('currency_symbol', 'ر.س'); ?></span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">الخصم</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" name="discount" min="0" step="0.01" value="0">
                                        <span class="input-group-text"><?php echo get_system_setting('currency_symbol', 'ر.س'); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">تاريخ الاستحقاق <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" name="due_date" value="<?php echo date('Y-m-d'); ?>" required>
                        </div>
                        
                        <div class="mb-4">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-control" name="notes" rows="3"></textarea>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-save me-2"></i>إضافة الرسم
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once '../../includes/footer.php'; ?>
