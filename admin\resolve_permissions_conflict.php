<?php
/**
 * حل تضارب ملفات الصلاحيات نهائياً
 * Resolve Permissions Files Conflict Permanently
 */

require_once '../includes/config.php';
require_once '../includes/database.php';

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من الصلاحيات (مدير فقط)
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/?error=access_denied');
    exit();
}

$success_message = '';
$error_message = '';
$conflicts_found = [];
$fixes_applied = [];

// فحص جميع ملفات الصلاحيات
function scan_permissions_files() {
    global $conflicts_found;
    
    $files_to_check = [
        '../includes/functions.php',
        '../includes/permissions.php',
        '../includes/enhanced_permissions.php',
        '../includes/advanced_permissions.php'
    ];
    
    $function_definitions = [];
    
    foreach ($files_to_check as $file) {
        if (file_exists($file)) {
            $content = file_get_contents($file);
            
            // البحث عن تعريفات الدوال
            if (preg_match('/function\s+has_permission\s*\(/', $content)) {
                $function_definitions['has_permission'][] = $file;
            }
            
            if (preg_match('/function\s+require_permission\s*\(/', $content)) {
                $function_definitions['require_permission'][] = $file;
            }
            
            if (preg_match('/function\s+check_permission\s*\(/', $content)) {
                $function_definitions['check_permission'][] = $file;
            }
        }
    }
    
    // تحديد التضارب
    foreach ($function_definitions as $func_name => $files) {
        if (count($files) > 1) {
            $conflicts_found[$func_name] = $files;
        }
    }
    
    return count($conflicts_found) > 0;
}

// تطبيق الإصلاحات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['resolve_conflicts'])) {
    try {
        // الحل 1: إنشاء ملف صلاحيات موحد
        $unified_permissions = '<?php
/**
 * نظام الصلاحيات الموحد
 * Unified Permissions System
 */

// منع الوصول المباشر
if (!defined(\'SYSTEM_INIT\') && !defined(\'FUNCTIONS_LOADED\')) {
    die(\'Direct access not allowed\');
}

// تحميل النظام المحسن إذا كان متوفراً
if (file_exists(__DIR__ . \'/enhanced_permissions.php\') && !defined(\'ENHANCED_PERMISSIONS_LOADED\')) {
    require_once __DIR__ . \'/enhanced_permissions.php\';
}

/**
 * دالة التحقق من الصلاحيات الموحدة
 */
if (!function_exists(\'unified_has_permission\')) {
    function unified_has_permission($permission_key, $required_level = \'read\', $user_id = null) {
        // التحقق من تسجيل الدخول
        if (!isset($_SESSION[\'user_id\'])) {
            return false;
        }
        
        // المدير العام له جميع الصلاحيات
        if (isset($_SESSION[\'role\']) && $_SESSION[\'role\'] === \'admin\') {
            return true;
        }
        
        // استخدام النظام المحسن إذا كان متوفراً
        if (defined(\'ENHANCED_PERMISSIONS_LOADED\') && function_exists(\'enhanced_has_permission\')) {
            return enhanced_has_permission($permission_key, $required_level, $user_id);
        }
        
        // العودة للنظام الأساسي
        return basic_permission_check($permission_key, $user_id);
    }
}

/**
 * دالة التحقق الأساسية
 */
if (!function_exists(\'basic_permission_check\')) {
    function basic_permission_check($permission_key, $user_id = null) {
        global $conn;
        
        if ($user_id === null) {
            $user_id = $_SESSION[\'user_id\'] ?? 0;
        }
        
        try {
            $stmt = $conn->prepare("
                SELECT is_granted 
                FROM user_custom_permissions 
                WHERE user_id = ? AND (resource_key = ? OR permission_key = ?) AND is_granted = 1
                AND (expires_at IS NULL OR expires_at > NOW())
                LIMIT 1
            ");
            $stmt->bind_param("iss", $user_id, $permission_key, $permission_key);
            $stmt->execute();
            $result = $stmt->get_result();
            
            return $result->num_rows > 0;
        } catch (Exception $e) {
            error_log("Error in basic_permission_check: " . $e->getMessage());
            return false;
        }
    }
}

/**
 * دالة التحقق مع إعادة التوجيه الموحدة
 */
if (!function_exists(\'unified_require_permission\')) {
    function unified_require_permission($permission_key, $required_level = \'read\', $redirect_url = \'../dashboard/\') {
        if (!unified_has_permission($permission_key, $required_level)) {
            header(\'Location: \' . $redirect_url . \'?error=access_denied\');
            exit();
        }
    }
}

/**
 * دوال التوافق
 */
if (!function_exists(\'has_permission\')) {
    function has_permission($permission_key, $required_level = \'read\') {
        return unified_has_permission($permission_key, $required_level);
    }
}

if (!function_exists(\'require_permission\')) {
    function require_permission($permission_key, $required_level = \'read\', $redirect_url = \'../dashboard/\') {
        unified_require_permission($permission_key, $required_level, $redirect_url);
    }
}

/**
 * دالة فحص الصلاحيات للتوافق مع النظام القديم
 */
if (!function_exists(\'check_permission\')) {
    function check_permission($role) {
        if (!isset($_SESSION[\'role\'])) {
            return false;
        }
        
        if ($role === \'admin\') {
            return $_SESSION[\'role\'] === \'admin\';
        }
        
        return $_SESSION[\'role\'] === $role || $_SESSION[\'role\'] === \'admin\';
    }
}
?>';
        
        file_put_contents('../includes/unified_permissions.php', $unified_permissions);
        $fixes_applied[] = 'تم إنشاء ملف الصلاحيات الموحد';
        
        // الحل 2: تحديث ملف functions.php
        $functions_content = file_get_contents('../includes/functions.php');
        
        // إزالة تعريفات الدوال المتضاربة
        $functions_content = preg_replace('/\/\*\*[\s\S]*?\*\/\s*if\s*\(\s*!function_exists\s*\(\s*[\'"]require_permission[\'"]\s*\)\s*\)\s*\{[\s\S]*?\}\s*\}/', '', $functions_content);
        
        // إضافة تحميل النظام الموحد
        $new_include = "\n// تحميل نظام الصلاحيات الموحد\nif (file_exists(__DIR__ . '/unified_permissions.php')) {\n    require_once __DIR__ . '/unified_permissions.php';\n}\n";
        
        if (strpos($functions_content, 'unified_permissions.php') === false) {
            $functions_content = str_replace('<?php', '<?php' . $new_include, $functions_content);
        }
        
        file_put_contents('../includes/functions.php', $functions_content);
        $fixes_applied[] = 'تم تحديث ملف functions.php';
        
        // الحل 3: تعطيل الدوال المتضاربة في ملف permissions.php
        if (file_exists('../includes/permissions.php')) {
            $permissions_content = file_get_contents('../includes/permissions.php');
            
            // تعليق تعريف دالة require_permission
            $permissions_content = preg_replace(
                '/(function\s+require_permission\s*\([^}]+\})/s',
                '// تم تعطيل هذه الدالة لتجنب التضارب - استخدم unified_require_permission بدلاً منها
// $1',
                $permissions_content
            );
            
            file_put_contents('../includes/permissions.php', $permissions_content);
            $fixes_applied[] = 'تم تعطيل الدوال المتضاربة في permissions.php';
        }
        
        // الحل 4: إنشاء ملف تحميل آمن
        $safe_loader = '<?php
/**
 * محمل الصلاحيات الآمن
 * Safe Permissions Loader
 */

// منع التحميل المتكرر
if (defined(\'PERMISSIONS_SYSTEM_LOADED\')) {
    return;
}

define(\'PERMISSIONS_SYSTEM_LOADED\', true);

// تحميل النظام الموحد
if (file_exists(__DIR__ . \'/unified_permissions.php\')) {
    require_once __DIR__ . \'/unified_permissions.php\';
} else {
    // العودة للنظام الأساسي
    if (file_exists(__DIR__ . \'/functions.php\')) {
        require_once __DIR__ . \'/functions.php\';
    }
}
?>';
        
        file_put_contents('../includes/safe_permissions_loader.php', $safe_loader);
        $fixes_applied[] = 'تم إنشاء محمل الصلاحيات الآمن';
        
        $success_message = 'تم حل جميع تضارب الصلاحيات بنجاح!';
        
    } catch (Exception $e) {
        $error_message = 'خطأ في حل التضارب: ' . $e->getMessage();
    }
}

// فحص التضارب
$has_conflicts = scan_permissions_files();

$page_title = 'حل تضارب ملفات الصلاحيات';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <!-- رأس الصفحة -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="fas fa-tools me-2 text-danger"></i><?php echo $page_title; ?></h2>
                <p class="text-muted">حل نهائي لجميع مشاكل تضارب دوال الصلاحيات</p>
            </div>
        </div>

        <!-- رسائل التنبيه -->
        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (!empty($fixes_applied)): ?>
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle me-2"></i>الإصلاحات المطبقة:</h6>
                <ul class="mb-0">
                    <?php foreach ($fixes_applied as $fix): ?>
                        <li><?php echo $fix; ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- حالة التضارب -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-search me-2"></i>فحص التضارب في ملفات الصلاحيات</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($has_conflicts): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>تم العثور على تضارب في الدوال!</strong>
                            </div>
                            
                            <h6>التضارب الموجود:</h6>
                            <?php foreach ($conflicts_found as $func_name => $files): ?>
                                <div class="mb-3">
                                    <strong class="text-danger">دالة <?php echo $func_name; ?>():</strong>
                                    <ul class="mt-2">
                                        <?php foreach ($files as $file): ?>
                                            <li><code><?php echo $file; ?></code></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endforeach; ?>
                            
                            <form method="POST" class="mt-4">
                                <input type="hidden" name="resolve_conflicts" value="1">
                                <button type="submit" class="btn btn-danger btn-lg">
                                    <i class="fas fa-tools me-2"></i>حل التضارب نهائياً
                                </button>
                            </form>
                            
                        <?php else: ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>ممتاز!</strong> لا يوجد تضارب في ملفات الصلاحيات.
                            </div>
                            
                            <div class="text-center">
                                <a href="test_enhanced_permissions.php" class="btn btn-primary">
                                    <i class="fas fa-vial me-2"></i>اختبار النظام
                                </a>
                                <a href="enhanced_permissions_manager.php" class="btn btn-success">
                                    <i class="fas fa-shield-alt me-2"></i>إدارة الصلاحيات
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات الحل -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle me-2"></i>ما يتم في الحل النهائي</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>الإصلاحات المطبقة:</h6>
                                <ul>
                                    <li>إنشاء نظام صلاحيات موحد</li>
                                    <li>حل جميع تضارب الدوال</li>
                                    <li>إنشاء دوال توافق</li>
                                    <li>محمل آمن للصلاحيات</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>المزايا:</h6>
                                <ul>
                                    <li>عدم كسر الكود الموجود</li>
                                    <li>دعم النظام القديم والجديد</li>
                                    <li>أداء محسن</li>
                                    <li>سهولة الصيانة</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-lightbulb me-2"></i>
                            <strong>نصيحة:</strong> بعد تطبيق الحل، استخدم صفحة الاختبار للتأكد من عمل النظام بشكل صحيح.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
