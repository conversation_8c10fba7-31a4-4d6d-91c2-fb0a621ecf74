<?php
/**
 * إدارة الصلاحيات والأدوار المتقدم
 * Advanced Permissions and Roles Management
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

$success_message = '';
$error_message = '';

// معالجة تحديث الدور الأساسي
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_role'])) {
    $user_id = intval($_POST['user_id']);
    $new_role = clean_input($_POST['role']);

    $valid_roles = ['admin', 'teacher', 'student', 'staff', 'parent', 'financial_manager', 'librarian', 'nurse', 'security', 'maintenance'];

    if ($user_id > 0 && in_array($new_role, $valid_roles)) {
        // الحصول على الدور القديم
        $old_role_stmt = $conn->prepare("SELECT role, full_name FROM users WHERE id = ?");
        $old_role_stmt->bind_param("i", $user_id);
        $old_role_stmt->execute();
        $old_data = $old_role_stmt->get_result()->fetch_assoc();

        // تحديث الدور
        $stmt = $conn->prepare("UPDATE users SET role = ? WHERE id = ?");
        $stmt->bind_param("si", $new_role, $user_id);

        if ($stmt->execute()) {
            // تسجيل التغيير في سجل المراجعة
            $audit_stmt = $conn->prepare("
                INSERT INTO permissions_audit_log
                (user_id, action_type, old_value, new_value, changed_by, ip_address, notes)
                VALUES (?, 'role_changed', ?, ?, ?, ?, ?)
            ");
            $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            $notes = "تم تغيير دور المستخدم " . $old_data['full_name'];
            $audit_stmt->bind_param("isssss", $user_id, $old_data['role'], $new_role, $_SESSION['user_id'], $ip, $notes);
            $audit_stmt->execute();

            $success_message = "تم تحديث دور المستخدم بنجاح";
        } else {
            $error_message = "خطأ في تحديث الدور";
        }
    }
}

// معالجة تحديث الصلاحيات المخصصة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_custom_permissions'])) {
    $user_id = intval($_POST['user_id']);
    $permissions = $_POST['permissions'] ?? [];

    if ($user_id > 0) {
        $conn->begin_transaction();
        try {
            // حذف الصلاحيات الحالية
            $delete_stmt = $conn->prepare("DELETE FROM user_custom_permissions WHERE user_id = ?");
            $delete_stmt->bind_param("i", $user_id);
            $delete_stmt->execute();

            // إضافة الصلاحيات الجديدة
            $insert_stmt = $conn->prepare("
                INSERT INTO user_custom_permissions
                (user_id, permission_type, permission_key, is_granted, granted_by)
                VALUES (?, ?, ?, 1, ?)
            ");

            foreach ($permissions as $permission) {
                $parts = explode(':', $permission);
                if (count($parts) == 2) {
                    $type = $parts[0];
                    $key = $parts[1];
                    $insert_stmt->bind_param("issi", $user_id, $type, $key, $_SESSION['user_id']);
                    $insert_stmt->execute();
                }
            }

            $conn->commit();
            $success_message = "تم تحديث الصلاحيات المخصصة بنجاح";
        } catch (Exception $e) {
            $conn->rollback();
            $error_message = "خطأ في تحديث الصلاحيات: " . $e->getMessage();
        }
    }
}

// جلب المستخدمين
$users_query = "
    SELECT id, full_name, username, email, role, status 
    FROM users 
    WHERE status != 'deleted' 
    ORDER BY full_name
";
$users_result = $conn->query($users_query);

$page_title = 'إدارة الصلاحيات والأدوار';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-user-shield me-2"></i><?php echo $page_title; ?></h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="../admin/">الإدارة</a></li>
                    <li class="breadcrumb-item active">إدارة الصلاحيات</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- رسائل التنبيه -->
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- قائمة الأدوار والصلاحيات -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-list me-2"></i>الأدوار والصلاحيات</h5>
                </div>
                <div class="card-body">
                    <?php foreach (ROLE_PERMISSIONS as $role => $permissions): ?>
                        <div class="mb-3">
                            <h6 class="text-primary">
                                <i class="fas fa-user-tag me-2"></i>
                                <?php 
                                $role_names = [
                                    'admin' => 'مدير النظام',
                                    'financial_manager' => 'مدير مالي',
                                    'teacher' => 'معلم',
                                    'staff' => 'موظف',
                                    'student' => 'طالب',
                                    'parent' => 'ولي أمر'
                                ];
                                echo $role_names[$role] ?? $role;
                                ?>
                            </h6>
                            <div class="small text-muted">
                                <?php 
                                $permission_count = count($permissions);
                                echo "عدد الصلاحيات: $permission_count";
                                ?>
                            </div>
                            <div class="mt-2">
                                <?php foreach (array_slice($permissions, 0, 3) as $permission): ?>
                                    <span class="badge bg-secondary me-1"><?php echo $permission; ?></span>
                                <?php endforeach; ?>
                                <?php if (count($permissions) > 3): ?>
                                    <span class="badge bg-light text-dark">+<?php echo count($permissions) - 3; ?> أخرى</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <hr>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- قائمة المستخدمين -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-users me-2"></i>إدارة صلاحيات المستخدمين</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>المستخدم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الدور الحالي</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($user = $users_result->fetch_assoc()): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                <?php echo strtoupper(substr($user['full_name'], 0, 1)); ?>
                                            </div>
                                            <div>
                                                <strong><?php echo htmlspecialchars($user['full_name']); ?></strong>
                                                <br><small class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo match($user['role']) {
                                                'admin' => 'danger',
                                                'financial_manager' => 'success',
                                                'teacher' => 'primary',
                                                'staff' => 'info',
                                                'student' => 'secondary',
                                                'parent' => 'warning',
                                                default => 'light'
                                            };
                                        ?>">
                                            <?php 
                                            $role_names = [
                                                'admin' => 'مدير النظام',
                                                'financial_manager' => 'مدير مالي',
                                                'teacher' => 'معلم',
                                                'staff' => 'موظف',
                                                'student' => 'طالب',
                                                'parent' => 'ولي أمر'
                                            ];
                                            echo $role_names[$user['role']] ?? $user['role'];
                                            ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                            <?php echo $user['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="edit_user_role.php?user_id=<?php echo $user['id']; ?>"
                                               class="btn btn-outline-primary">
                                                <i class="fas fa-edit"></i> تعديل الدور
                                            </a>
                                            <a href="user_permissions_detail.php?user_id=<?php echo $user['id']; ?>"
                                               class="btn btn-outline-success">
                                                <i class="fas fa-key"></i> الصلاحيات المخصصة
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<?php include_once '../includes/footer.php'; ?>
