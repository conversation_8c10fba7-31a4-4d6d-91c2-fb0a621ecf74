<?php
/**
 * صفحة حذف المادة الدراسية - مبسطة
 * Delete Subject Page - Simplified
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    $_SESSION['error_message'] = 'ليس لديك صلاحية للوصول';
    header('Location: ../dashboard/');
    exit();
}

// الحصول على معرف المادة
$subject_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// تسجيل مفصل
error_log("=== DELETE REQUEST START ===");
error_log("Subject ID: $subject_id");
error_log("Method: " . $_SERVER['REQUEST_METHOD']);
error_log("GET confirm: " . (isset($_GET['confirm']) ? $_GET['confirm'] : 'not set'));
error_log("POST data: " . print_r($_POST, true));

if ($subject_id <= 0) {
    error_log("ERROR: Invalid subject ID");
    $_SESSION['error_message'] = 'معرف المادة غير صحيح';
    header('Location: index.php');
    exit();
}

// جلب بيانات المادة
$subject_query = "SELECT id, subject_name FROM subjects WHERE id = ?";
$stmt = $conn->prepare($subject_query);
if (!$stmt) {
    error_log("ERROR: Database prepare failed: " . $conn->error);
    $_SESSION['error_message'] = 'خطأ في قاعدة البيانات';
    header('Location: index.php');
    exit();
}

$stmt->bind_param('i', $subject_id);
$stmt->execute();
$result = $stmt->get_result();
$subject = $result->fetch_assoc();
$stmt->close();

if (!$subject) {
    error_log("ERROR: Subject not found with ID: $subject_id");
    $_SESSION['error_message'] = 'المادة غير موجودة';
    header('Location: index.php');
    exit();
}

error_log("Subject found: " . $subject['subject_name']);

// معالجة طلب الحذف - مبسط
if (isset($_GET['confirm']) && $_GET['confirm'] == '1') {
    error_log("Confirm parameter found, proceeding with deletion");

    try {
        // حذف المادة مباشرة
        $delete_query = "DELETE FROM subjects WHERE id = ?";
        $stmt = $conn->prepare($delete_query);

        if (!$stmt) {
            error_log("ERROR: Delete prepare failed: " . $conn->error);
            throw new Exception('خطأ في إعداد الاستعلام: ' . $conn->error);
        }

        $stmt->bind_param('i', $subject_id);
        $result = $stmt->execute();

        if (!$result) {
            error_log("ERROR: Delete execute failed: " . $stmt->error);
            throw new Exception('خطأ في تنفيذ الحذف: ' . $stmt->error);
        }

        $affected_rows = $stmt->affected_rows;
        $stmt->close();

        error_log("Delete executed successfully. Affected rows: $affected_rows");

        if ($affected_rows > 0) {
            error_log("Subject deleted successfully: " . $subject['subject_name']);
            $_SESSION['success_message'] = 'تم حذف المادة "' . $subject['subject_name'] . '" بنجاح';
        } else {
            error_log("WARNING: No rows affected during deletion");
            $_SESSION['warning_message'] = 'لم يتم العثور على المادة للحذف';
        }

        error_log("Redirecting to index.php");
        header('Location: index.php');
        exit();

    } catch (Exception $e) {
        error_log("EXCEPTION during deletion: " . $e->getMessage());
        $_SESSION['error_message'] = 'خطأ في حذف المادة: ' . $e->getMessage();
        header('Location: index.php');
        exit();
    }
} else {
    // لم يتم تأكيد الحذف
    error_log("ERROR: No confirmation parameter found");
    $_SESSION['error_message'] = 'لم يتم تأكيد الحذف';
    header('Location: index.php');
    exit();
}

error_log("=== DELETE REQUEST END ===");
?>
}

$page_title = 'حذف المادة';

// إذا لم يكن طلب POST، عرض صفحة التأكيد
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-trash text-danger me-2"></i>
                حذف المادة
            </h2>
            <p class="text-muted mb-0">تأكيد حذف المادة الدراسية</p>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة للمواد
            </a>
        </div>
    </div>

    <!-- Confirmation Card -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تأكيد الحذف
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            تحذير
                        </h6>
                        <p class="mb-0">هل أنت متأكد من حذف هذه المادة؟</p>
                    </div>

                    <!-- Subject Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">المادة المراد حذفها</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <td class="fw-bold text-muted">اسم المادة:</td>
                                            <td><?php echo htmlspecialchars($subject['subject_name']); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">رمز المادة:</td>
                                            <td><code><?php echo htmlspecialchars($subject['subject_code']); ?></code></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <td class="fw-bold text-muted">الساعات المعتمدة:</td>
                                            <td><?php echo $subject['credit_hours']; ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">الحالة:</td>
                                            <td>
                                                <?php if ($subject['status'] === 'active'): ?>
                                                    <span class="badge bg-success">نشط</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">غير نشط</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Consequences Information -->
                    <div class="alert alert-warning">
                        <h6 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>
                            عواقب الحذف
                        </h6>
                        <ul class="mb-0">
                            <li>سيتم حذف المادة نهائياً من النظام</li>
                            <li>لا يمكن التراجع عن هذا الإجراء</li>
                            <li>ستتم إزالة جميع المراجع لهذه المادة</li>
                        </ul>
                    </div>

                    <!-- Confirmation Form -->
                    <form method="POST" class="mt-4">
                        <input type="hidden" name="confirm_delete" value="1">
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                            <label class="form-check-label" for="confirmDelete">
                                أفهم عواقب حذف هذه المادة وأريد المتابعة
                            </label>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-danger" id="deleteBtn" disabled>
                                <i class="fas fa-trash me-2"></i>حذف المادة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تفعيل زر الحذف عند تأكيد الفهم
document.getElementById('confirmDelete').addEventListener('change', function() {
    document.getElementById('deleteBtn').disabled = !this.checked;
});

// تأكيد إضافي عند الإرسال
document.querySelector('form').addEventListener('submit', function(e) {
    if (!confirm('هل أنت متأكد تماماً من حذف هذه المادة؟')) {
        e.preventDefault();
    }
});
</script>

<?php require_once '../includes/footer.php'; ?>
