<?php
/**
 * إضافة بيانات تجريبية لإعدادات الخصم
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    die('غير مسموح لك بالوصول لهذه الصفحة');
}

echo "<h2>إضافة بيانات تجريبية لإعدادات الخصم</h2>";

try {
    // التحقق من وجود العمود staff_role
    $check_column = $conn->query("SHOW COLUMNS FROM deduction_settings LIKE 'staff_role'");
    if ($check_column->num_rows == 0) {
        echo "<p>إضافة العمود staff_role...</p>";
        $conn->query("ALTER TABLE deduction_settings ADD COLUMN staff_role ENUM('teacher','staff','all') NOT NULL DEFAULT 'all' AFTER absence_type");
        echo "<p>✅ تم إضافة العمود staff_role</p>";
    }
    
    // حذف البيانات القديمة إذا وجدت
    $conn->query("DELETE FROM deduction_settings");
    echo "<p>تم حذف البيانات القديمة</p>";
    
    // إضافة بيانات تجريبية شاملة
    $sample_data = [
        // إعدادات المعلمين
        [
            'absence_type' => 'غياب بدون عذر',
            'staff_role' => 'teacher',
            'deduction_value' => 150.00,
            'deduction_type' => 'daily_rate',
            'max_allowed_per_month' => 3,
            'requires_approval' => 1,
            'description' => 'خصم من الراتب اليومي للمعلم في حالة الغياب بدون عذر مقبول',
            'is_active' => 1
        ],
        [
            'absence_type' => 'تأخير عن العمل',
            'staff_role' => 'teacher',
            'deduction_value' => 25.00,
            'deduction_type' => 'fixed',
            'max_allowed_per_month' => 5,
            'requires_approval' => 0,
            'description' => 'خصم ثابت للمعلم عند التأخير عن موعد بداية العمل',
            'is_active' => 1
        ],
        [
            'absence_type' => 'غياب شخصي',
            'staff_role' => 'teacher',
            'deduction_value' => 75.00,
            'deduction_type' => 'daily_rate',
            'max_allowed_per_month' => 2,
            'requires_approval' => 1,
            'description' => 'خصم للمعلم في حالة الغياب لأسباب شخصية مع إشعار مسبق',
            'is_active' => 1
        ],
        [
            'absence_type' => 'مخالفة اللوائح',
            'staff_role' => 'teacher',
            'deduction_value' => 5.00,
            'deduction_type' => 'percentage',
            'max_allowed_per_month' => 2,
            'requires_approval' => 1,
            'description' => 'خصم نسبة من راتب المعلم عند مخالفة اللوائح الداخلية',
            'is_active' => 1
        ],
        
        // إعدادات الإداريين
        [
            'absence_type' => 'غياب بدون عذر',
            'staff_role' => 'staff',
            'deduction_value' => 120.00,
            'deduction_type' => 'daily_rate',
            'max_allowed_per_month' => 3,
            'requires_approval' => 1,
            'description' => 'خصم من الراتب اليومي للإداري في حالة الغياب بدون عذر مقبول',
            'is_active' => 1
        ],
        [
            'absence_type' => 'تأخير عن العمل',
            'staff_role' => 'staff',
            'deduction_value' => 20.00,
            'deduction_type' => 'fixed',
            'max_allowed_per_month' => 5,
            'requires_approval' => 0,
            'description' => 'خصم ثابت للإداري عند التأخير عن موعد بداية العمل',
            'is_active' => 1
        ],
        [
            'absence_type' => 'غياب شخصي',
            'staff_role' => 'staff',
            'deduction_value' => 60.00,
            'deduction_type' => 'daily_rate',
            'max_allowed_per_month' => 2,
            'requires_approval' => 1,
            'description' => 'خصم للإداري في حالة الغياب لأسباب شخصية مع إشعار مسبق',
            'is_active' => 1
        ],
        [
            'absence_type' => 'مخالفة اللوائح',
            'staff_role' => 'staff',
            'deduction_value' => 4.00,
            'deduction_type' => 'percentage',
            'max_allowed_per_month' => 2,
            'requires_approval' => 1,
            'description' => 'خصم نسبة من راتب الإداري عند مخالفة اللوائح الداخلية',
            'is_active' => 1
        ],
        
        // إعدادات عامة (تطبق على الجميع)
        [
            'absence_type' => 'مخالفة السلوك المهني',
            'staff_role' => 'all',
            'deduction_value' => 10.00,
            'deduction_type' => 'percentage',
            'max_allowed_per_month' => 1,
            'requires_approval' => 1,
            'description' => 'خصم نسبة من الراتب عند مخالفة قواعد السلوك المهني',
            'is_active' => 1
        ],
        [
            'absence_type' => 'عدم الالتزام بالزي الرسمي',
            'staff_role' => 'all',
            'deduction_value' => 15.00,
            'deduction_type' => 'fixed',
            'max_allowed_per_month' => 10,
            'requires_approval' => 0,
            'description' => 'خصم ثابت عند عدم ارتداء الزي الرسمي المحدد',
            'is_active' => 1
        ],
        [
            'absence_type' => 'استخدام الهاتف أثناء العمل',
            'staff_role' => 'all',
            'deduction_value' => 10.00,
            'deduction_type' => 'fixed',
            'max_allowed_per_month' => 8,
            'requires_approval' => 0,
            'description' => 'خصم ثابت عند استخدام الهاتف الشخصي بشكل مفرط أثناء العمل',
            'is_active' => 1
        ],
        [
            'absence_type' => 'التأخير في تسليم التقارير',
            'staff_role' => 'all',
            'deduction_value' => 50.00,
            'deduction_type' => 'daily_rate',
            'max_allowed_per_month' => 3,
            'requires_approval' => 1,
            'description' => 'خصم عند عدم تسليم التقارير المطلوبة في المواعيد المحددة',
            'is_active' => 1
        ]
    ];
    
    $insert_sql = "
        INSERT INTO deduction_settings 
        (absence_type, staff_role, deduction_value, deduction_type, max_allowed_per_month, 
         requires_approval, description, is_active, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    ";
    
    $stmt = $conn->prepare($insert_sql);
    $inserted_count = 0;
    
    foreach ($sample_data as $data) {
        $stmt->bind_param("ssdsisii", 
            $data['absence_type'], 
            $data['staff_role'], 
            $data['deduction_value'], 
            $data['deduction_type'], 
            $data['max_allowed_per_month'], 
            $data['requires_approval'], 
            $data['description'], 
            $data['is_active']
        );
        
        if ($stmt->execute()) {
            $inserted_count++;
        }
    }
    
    echo "<div class='alert alert-success'>";
    echo "<h4>✅ تم إضافة البيانات التجريبية بنجاح!</h4>";
    echo "<p>تم إدراج <strong>$inserted_count</strong> إعداد خصم</p>";
    echo "<ul>";
    echo "<li><strong>للمعلمين:</strong> 4 إعدادات</li>";
    echo "<li><strong>للإداريين:</strong> 4 إعدادات</li>";
    echo "<li><strong>عامة (للجميع):</strong> 4 إعدادات</li>";
    echo "</ul>";
    echo "</div>";
    
    // عرض الإعدادات المضافة
    echo "<h3>الإعدادات المضافة:</h3>";
    $result = $conn->query("SELECT * FROM deduction_settings ORDER BY staff_role, absence_type");
    
    echo "<table class='table table-striped'>";
    echo "<tr><th>نوع المخالفة</th><th>الفئة</th><th>نوع الخصم</th><th>القيمة</th><th>الحد الأقصى</th><th>يتطلب موافقة</th><th>نشط</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        $role_text = '';
        switch($row['staff_role']) {
            case 'teacher': $role_text = 'المعلمين'; break;
            case 'staff': $role_text = 'الإداريين'; break;
            default: $role_text = 'الجميع';
        }
        
        $type_text = '';
        switch($row['deduction_type']) {
            case 'fixed': $type_text = 'مبلغ ثابت'; break;
            case 'percentage': $type_text = 'نسبة مئوية'; break;
            case 'daily_rate': $type_text = 'معدل يومي'; break;
            case 'hourly_rate': $type_text = 'معدل ساعي'; break;
        }
        
        echo "<tr>";
        echo "<td><strong>{$row['absence_type']}</strong></td>";
        echo "<td>{$role_text}</td>";
        echo "<td>{$type_text}</td>";
        echo "<td>{$row['deduction_value']} " . ($row['deduction_type'] === 'percentage' ? '%' : 'ج.م') . "</td>";
        echo "<td>" . ($row['max_allowed_per_month'] ?: 'غير محدد') . "</td>";
        echo "<td>" . ($row['requires_approval'] ? 'نعم' : 'لا') . "</td>";
        echo "<td>" . ($row['is_active'] ? '✅' : '❌') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<hr>";
    echo "<div class='mt-3'>";
    echo "<a href='deduction_settings.php' class='btn btn-primary me-2'>عرض إعدادات الخصم</a>";
    echo "<a href='add_deduction_setting.php' class='btn btn-success me-2'>إضافة إعداد جديد</a>";
    echo "<a href='../dashboard/' class='btn btn-secondary'>العودة للوحة التحكم</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ حدث خطأ:</h4>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>
