<?php
/**
 * إضافة مصروف يومي جديد
 * Add New Daily Expense
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

$success_message = '';
$error_message = '';

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $expense_date = clean_input($_POST['expense_date'] ?? '');
    $category_id = intval($_POST['category_id'] ?? 0);
    $amount = floatval($_POST['amount'] ?? 0);
    $description = clean_input($_POST['description'] ?? '');
    $receipt_number = clean_input($_POST['receipt_number'] ?? '');
    $payment_method = clean_input($_POST['payment_method'] ?? 'cash');
    $vendor_name = clean_input($_POST['vendor_name'] ?? '');
    $vendor_phone = clean_input($_POST['vendor_phone'] ?? '');
    
    // التحقق من صحة البيانات
    if (empty($expense_date) || $category_id <= 0 || $amount <= 0 || empty($description)) {
        $error_message = 'يرجى ملء جميع الحقول المطلوبة';
    } else {
        try {
            // التحقق من حدود الإنفاق
            $category_check = $conn->prepare("
                SELECT daily_limit, monthly_limit
                FROM expense_categories
                WHERE id = ? AND is_active = 1
            ");
            $category_check->bind_param("i", $category_id);
            $category_check->execute();
            $category_info = $category_check->get_result()->fetch_assoc();
            
            if (!$category_info) {
                throw new Exception('الفئة المحددة غير صحيحة');
            }
            
            // فحص الحد اليومي
            if ($category_info['daily_limit'] && $amount > $category_info['daily_limit']) {
                $error_message = 'المبلغ يتجاوز الحد اليومي المسموح (' . format_currency($category_info['daily_limit']) . ')';
            } else {
                // فحص الحد الشهري
                $month_start = date('Y-m-01', strtotime($expense_date));
                $month_end = date('Y-m-t', strtotime($expense_date));
                
                $monthly_spent_query = $conn->prepare("
                    SELECT COALESCE(SUM(amount), 0) as monthly_total 
                    FROM daily_expenses 
                    WHERE category_id = ? AND expense_date BETWEEN ? AND ? AND status != 'rejected'
                ");
                $monthly_spent_query->bind_param("iss", $category_id, $month_start, $month_end);
                $monthly_spent_query->execute();
                $monthly_spent = $monthly_spent_query->get_result()->fetch_assoc()['monthly_total'];
                
                if ($category_info['monthly_limit'] && ($monthly_spent + $amount) > $category_info['monthly_limit']) {
                    $error_message = 'المبلغ يتجاوز الحد الشهري المسموح. المتبقي: ' . 
                                   format_currency($category_info['monthly_limit'] - $monthly_spent);
                } else {
                    // تحديد الحالة الأولية (جميع المصروفات معتمدة تلقائياً)
                    $initial_status = 'approved';

                    // إدراج المصروف
                    $insert_stmt = $conn->prepare("
                        INSERT INTO daily_expenses 
                        (expense_date, category_id, amount, description, receipt_number, 
                         payment_method, vendor_name, vendor_phone, status, created_by) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    
                    $user_id = $_SESSION['user_id'];
                    $insert_stmt->bind_param("sidssssssi",
                        $expense_date, $category_id, $amount, $description, $receipt_number,
                        $payment_method, $vendor_name, $vendor_phone, $initial_status, $user_id
                    );
                    
                    if ($insert_stmt->execute()) {
                        $expense_id = $conn->insert_id;

                        $success_message = 'تم إضافة المصروف بنجاح';
                        
                        // إعادة تعيين النموذج
                        $_POST = [];
                    } else {
                        throw new Exception('فشل في إضافة المصروف');
                    }
                }
            }
        } catch (Exception $e) {
            $error_message = $e->getMessage();
        }
    }
}

// جلب الفئات النشطة
$categories_query = "SELECT * FROM expense_categories WHERE is_active = 1 ORDER BY category_name";
$categories_result = $conn->query($categories_query);

$page_title = 'إضافة مصروف جديد';
include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-plus me-2"></i>إضافة مصروف جديد
            </h1>
            <p class="text-muted mb-0">تسجيل مصروف يومي جديد</p>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
            </a>
        </div>
    </div>

    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-8">
            <!-- نموذج إضافة المصروف -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-edit me-2"></i>بيانات المصروف
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" id="expenseForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">تاريخ المصروف <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" name="expense_date" 
                                       value="<?php echo $_POST['expense_date'] ?? date('Y-m-d'); ?>" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">فئة المصروف <span class="text-danger">*</span></label>
                                <select class="form-select" name="category_id" required id="categorySelect">
                                    <option value="">اختر الفئة</option>
                                    <?php while ($category = $categories_result->fetch_assoc()): ?>
                                        <option value="<?php echo $category['id']; ?>"
                                                data-daily-limit="<?php echo $category['daily_limit']; ?>"
                                                data-monthly-limit="<?php echo $category['monthly_limit']; ?>"
                                                <?php echo (($_POST['category_id'] ?? '') == $category['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($category['category_name']); ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                                <div id="categoryLimits" class="form-text"></div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">المبلغ <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" class="form-control" name="amount" step="0.01" min="0.01"
                                           value="<?php echo $_POST['amount'] ?? ''; ?>" required id="amountInput">
                                    <span class="input-group-text"><?php echo get_currency_symbol(); ?></span>
                                </div>
                                <div id="amountWarning" class="form-text text-warning"></div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">طريقة الدفع</label>
                                <select class="form-select" name="payment_method">
                                    <option value="cash" <?php echo (($_POST['payment_method'] ?? 'cash') == 'cash') ? 'selected' : ''; ?>>نقدي</option>
                                    <option value="bank_transfer" <?php echo (($_POST['payment_method'] ?? '') == 'bank_transfer') ? 'selected' : ''; ?>>تحويل بنكي</option>
                                    <option value="credit_card" <?php echo (($_POST['payment_method'] ?? '') == 'credit_card') ? 'selected' : ''; ?>>بطاقة ائتمان</option>
                                    <option value="check" <?php echo (($_POST['payment_method'] ?? '') == 'check') ? 'selected' : ''; ?>>شيك</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">وصف المصروف <span class="text-danger">*</span></label>
                            <textarea class="form-control" name="description" rows="3" required 
                                      placeholder="اكتب وصفاً مفصلاً للمصروف..."><?php echo $_POST['description'] ?? ''; ?></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم الإيصال</label>
                                <input type="text" class="form-control" name="receipt_number" 
                                       value="<?php echo $_POST['receipt_number'] ?? ''; ?>"
                                       placeholder="رقم الإيصال أو الفاتورة">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم المورد</label>
                                <input type="text" class="form-control" name="vendor_name" 
                                       value="<?php echo $_POST['vendor_name'] ?? ''; ?>"
                                       placeholder="اسم المحل أو المورد">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">رقم هاتف المورد</label>
                            <input type="tel" class="form-control" name="vendor_phone" 
                                   value="<?php echo $_POST['vendor_phone'] ?? ''; ?>"
                                   placeholder="رقم هاتف المورد">
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="index.php" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ المصروف
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- معلومات مساعدة -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>معلومات مساعدة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6>نصائح مهمة:</h6>
                        <ul class="mb-0 small">
                            <li>تأكد من صحة التاريخ والمبلغ</li>
                            <li>اكتب وصفاً واضحاً للمصروف</li>
                            <li>احتفظ بالإيصالات الأصلية</li>
                            <li>بعض الفئات تحتاج موافقة إدارية</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6>حدود الإنفاق:</h6>
                        <p class="mb-0 small">
                            كل فئة لها حدود يومية وشهرية. سيتم تنبيهك عند تجاوز هذه الحدود.
                        </p>
                    </div>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>إحصائيات اليوم
                    </h6>
                </div>
                <div class="card-body">
                    <?php
                    $today = date('Y-m-d');
                    $today_stats = $conn->query("
                        SELECT 
                            COUNT(*) as count,
                            COALESCE(SUM(amount), 0) as total
                        FROM daily_expenses 
                        WHERE expense_date = '$today'
                    ")->fetch_assoc();
                    ?>
                    <div class="text-center">
                        <h4 class="text-primary"><?php echo $today_stats['count']; ?></h4>
                        <p class="mb-1">مصروف اليوم</p>
                        <h5 class="text-success"><?php echo format_currency($today_stats['total']); ?></h5>
                        <p class="mb-0 small text-muted">إجمالي المبلغ</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once '../../includes/footer.php'; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const categorySelect = document.getElementById('categorySelect');
    const amountInput = document.getElementById('amountInput');
    const categoryLimits = document.getElementById('categoryLimits');
    const amountWarning = document.getElementById('amountWarning');

    function updateCategoryInfo() {
        const selectedOption = categorySelect.options[categorySelect.selectedIndex];
        if (selectedOption.value) {
            const dailyLimit = selectedOption.dataset.dailyLimit;
            const monthlyLimit = selectedOption.dataset.monthlyLimit;

            let limitsText = '';
            if (dailyLimit && dailyLimit > 0) {
                limitsText += `الحد اليومي: ${parseFloat(dailyLimit).toLocaleString()} ج.م`;
            }
            if (monthlyLimit && monthlyLimit > 0) {
                if (limitsText) limitsText += ' | ';
                limitsText += `الحد الشهري: ${parseFloat(monthlyLimit).toLocaleString()} ج.م`;
            }

            categoryLimits.textContent = limitsText;
            checkAmountLimits();
        } else {
            categoryLimits.textContent = '';
            amountWarning.textContent = '';
        }
    }

    function checkAmountLimits() {
        const selectedOption = categorySelect.options[categorySelect.selectedIndex];
        const amount = parseFloat(amountInput.value) || 0;
        
        if (selectedOption.value && amount > 0) {
            const dailyLimit = parseFloat(selectedOption.dataset.dailyLimit) || 0;
            
            if (dailyLimit > 0 && amount > dailyLimit) {
                amountWarning.textContent = `تحذير: المبلغ يتجاوز الحد اليومي (${dailyLimit.toLocaleString()} ج.م)`;
                amountWarning.className = 'form-text text-danger';
            } else if (dailyLimit > 0 && amount > (dailyLimit * 0.8)) {
                amountWarning.textContent = `تنبيه: المبلغ قريب من الحد اليومي (${dailyLimit.toLocaleString()} ج.م)`;
                amountWarning.className = 'form-text text-warning';
            } else {
                amountWarning.textContent = '';
            }
        } else {
            amountWarning.textContent = '';
        }
    }

    categorySelect.addEventListener('change', updateCategoryInfo);
    amountInput.addEventListener('input', checkAmountLimits);

    // تحديث المعلومات عند تحميل الصفحة
    updateCategoryInfo();
});
</script>
