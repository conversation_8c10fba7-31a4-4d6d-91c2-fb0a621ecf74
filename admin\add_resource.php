<?php
/**
 * إضافة مورد نظام جديد - صفحة مستقلة
 * Add New System Resource - Standalone Page
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

$success_message = '';
$error_message = '';

// معالجة إضافة مورد جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_resource'])) {
    $resource_type = clean_input($_POST['resource_type']);
    $resource_key = clean_input($_POST['resource_key']);
    $resource_name = clean_input($_POST['resource_name']);
    $resource_description = clean_input($_POST['resource_description']);
    $resource_path = clean_input($_POST['resource_path']);
    $parent_resource = clean_input($_POST['parent_resource']);
    $icon = clean_input($_POST['icon']);
    $sort_order = intval($_POST['sort_order']);
    
    if (!empty($resource_type) && !empty($resource_key) && !empty($resource_name)) {
        // التحقق من عدم تكرار المفتاح
        $check_stmt = $conn->prepare("SELECT id FROM system_resources WHERE resource_type = ? AND resource_key = ?");
        $check_stmt->bind_param("ss", $resource_type, $resource_key);
        $check_stmt->execute();
        
        if ($check_stmt->get_result()->num_rows > 0) {
            $error_message = "يوجد مورد بنفس النوع والمفتاح مسبقاً";
        } else {
            $stmt = $conn->prepare("
                INSERT INTO system_resources 
                (resource_type, resource_key, resource_name, resource_description, resource_path, parent_resource, icon, sort_order) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->bind_param("sssssssi", $resource_type, $resource_key, $resource_name, $resource_description, $resource_path, $parent_resource, $icon, $sort_order);
            
            if ($stmt->execute()) {
                $success_message = "تم إضافة المورد بنجاح";
                // إعادة تعيين النموذج
                $_POST = [];
            } else {
                $error_message = "خطأ في إضافة المورد: " . $conn->error;
            }
        }
    } else {
        $error_message = "يرجى ملء جميع الحقول المطلوبة";
    }
}

// جلب الموارد الموجودة للمرجع
$existing_resources = [];
$existing_query = "SELECT resource_type, resource_key, resource_name FROM system_resources WHERE is_active = 1 ORDER BY resource_type, resource_name";
$existing_result = $conn->query($existing_query);
while ($row = $existing_result->fetch_assoc()) {
    $existing_resources[$row['resource_type']][] = $row;
}

$page_title = 'إضافة مورد نظام جديد';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-plus-circle me-2"></i><?php echo $page_title; ?></h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="../admin/">الإدارة</a></li>
                    <li class="breadcrumb-item"><a href="system_resources.php">موارد النظام</a></li>
                    <li class="breadcrumb-item active">إضافة مورد جديد</li>
                </ol>
            </nav>
        </div>
        <a href="system_resources.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
        </a>
    </div>

    <!-- رسائل التنبيه -->
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- نموذج الإضافة -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-plus me-2"></i>بيانات المورد الجديد</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>ملاحظة:</strong> موارد النظام تُستخدم لتحديد الصفحات والإجراءات والبيانات والتقارير 
                            التي يمكن منح المستخدمين صلاحيات للوصول إليها.
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">نوع المورد *</label>
                                <select class="form-select" name="resource_type" required>
                                    <option value="">اختر النوع</option>
                                    <option value="page" <?php echo ($_POST['resource_type'] ?? '') === 'page' ? 'selected' : ''; ?>>
                                        صفحة - Page
                                    </option>
                                    <option value="action" <?php echo ($_POST['resource_type'] ?? '') === 'action' ? 'selected' : ''; ?>>
                                        إجراء - Action
                                    </option>
                                    <option value="data" <?php echo ($_POST['resource_type'] ?? '') === 'data' ? 'selected' : ''; ?>>
                                        بيانات - Data
                                    </option>
                                    <option value="report" <?php echo ($_POST['resource_type'] ?? '') === 'report' ? 'selected' : ''; ?>>
                                        تقرير - Report
                                    </option>
                                </select>
                                <div class="form-text">
                                    اختر نوع المورد المناسب
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">مفتاح المورد *</label>
                                <input type="text" class="form-control" name="resource_key" 
                                       value="<?php echo htmlspecialchars($_POST['resource_key'] ?? ''); ?>" 
                                       required placeholder="مثال: users_management">
                                <div class="form-text">
                                    مفتاح فريد للمورد (بالإنجليزية، بدون مسافات)
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">اسم المورد *</label>
                            <input type="text" class="form-control" name="resource_name" 
                                   value="<?php echo htmlspecialchars($_POST['resource_name'] ?? ''); ?>" 
                                   required placeholder="مثال: إدارة المستخدمين">
                            <div class="form-text">
                                الاسم الذي سيظهر في واجهة إدارة الصلاحيات
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">وصف المورد</label>
                            <textarea class="form-control" name="resource_description" rows="3" 
                                      placeholder="وصف مختصر للمورد وما يمكن فعله به"><?php echo htmlspecialchars($_POST['resource_description'] ?? ''); ?></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">مسار المورد</label>
                                <input type="text" class="form-control" name="resource_path" 
                                       value="<?php echo htmlspecialchars($_POST['resource_path'] ?? ''); ?>" 
                                       placeholder="/admin/users/">
                                <div class="form-text">
                                    المسار النسبي للصفحة (للصفحات فقط)
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الأيقونة</label>
                                <input type="text" class="form-control" name="icon" 
                                       value="<?php echo htmlspecialchars($_POST['icon'] ?? ''); ?>" 
                                       placeholder="fas fa-users">
                                <div class="form-text">
                                    فئة CSS للأيقونة (Font Awesome)
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">المورد الأب</label>
                                <input type="text" class="form-control" name="parent_resource" 
                                       value="<?php echo htmlspecialchars($_POST['parent_resource'] ?? ''); ?>" 
                                       placeholder="admin">
                                <div class="form-text">
                                    مفتاح المورد الأب (للتجميع الهرمي)
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">ترتيب العرض</label>
                                <input type="number" class="form-control" name="sort_order" 
                                       value="<?php echo htmlspecialchars($_POST['sort_order'] ?? '0'); ?>">
                                <div class="form-text">
                                    رقم الترتيب (الأصغر يظهر أولاً)
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="system_resources.php" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" name="add_resource" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>إضافة المورد
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- الموارد الموجودة للمرجع -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-list me-2"></i>الموارد الموجودة</h6>
                </div>
                <div class="card-body">
                    <?php foreach (['page' => 'الصفحات', 'action' => 'الإجراءات', 'data' => 'البيانات', 'report' => 'التقارير'] as $type => $type_name): ?>
                        <?php if (isset($existing_resources[$type])): ?>
                            <div class="mb-3">
                                <h6 class="text-primary"><?php echo $type_name; ?></h6>
                                <div class="small">
                                    <?php foreach (array_slice($existing_resources[$type], 0, 5) as $resource): ?>
                                        <div class="d-flex justify-content-between align-items-center mb-1">
                                            <span><?php echo htmlspecialchars($resource['resource_name']); ?></span>
                                            <code class="small"><?php echo htmlspecialchars($resource['resource_key']); ?></code>
                                        </div>
                                    <?php endforeach; ?>
                                    <?php if (count($existing_resources[$type]) > 5): ?>
                                        <small class="text-muted">و <?php echo count($existing_resources[$type]) - 5; ?> أخرى...</small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- أمثلة -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6><i class="fas fa-lightbulb me-2"></i>أمثلة</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>صفحة:</strong>
                        <br><small>النوع: page</small>
                        <br><small>المفتاح: users_management</small>
                        <br><small>الاسم: إدارة المستخدمين</small>
                        <br><small>المسار: /admin/users/</small>
                    </div>
                    <div class="mb-3">
                        <strong>إجراء:</strong>
                        <br><small>النوع: action</small>
                        <br><small>المفتاح: delete</small>
                        <br><small>الاسم: حذف</small>
                    </div>
                    <div class="mb-3">
                        <strong>بيانات:</strong>
                        <br><small>النوع: data</small>
                        <br><small>المفتاح: all_students</small>
                        <br><small>الاسم: جميع بيانات الطلاب</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
