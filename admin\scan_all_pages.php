<?php
/**
 * فحص جميع صفحات النظام لإصلاح الصلاحيات
 * Scan All System Pages for Permission Fixes
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

// دالة للبحث في الملفات
function scanDirectory($dir, $pattern = '*.php') {
    $files = [];
    $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($dir));
    
    foreach ($iterator as $file) {
        if ($file->isFile() && $file->getExtension() === 'php') {
            $relativePath = str_replace(dirname(__DIR__) . DIRECTORY_SEPARATOR, '', $file->getPathname());
            $relativePath = str_replace('\\', '/', $relativePath);
            $files[] = $relativePath;
        }
    }
    
    return $files;
}

// دالة لفحص محتوى الملف
function analyzeFile($filePath) {
    $fullPath = dirname(__DIR__) . '/' . $filePath;
    
    if (!file_exists($fullPath)) {
        return null;
    }
    
    $content = file_get_contents($fullPath);
    if ($content === false) {
        return null;
    }
    
    $analysis = [
        'path' => $filePath,
        'has_permission_check' => false,
        'permission_patterns' => [],
        'needs_fix' => false,
        'suggested_fix' => '',
        'line_numbers' => []
    ];
    
    // البحث عن أنماط فحص الصلاحيات
    $patterns = [
        '/if\s*\(\s*!check_permission\s*\(\s*[\'"]admin[\'"]\s*\)\s*\)\s*\{/' => 'admin_only',
        '/if\s*\(\s*!check_permission\s*\(\s*[\'"]teacher[\'"]\s*\)\s*\)\s*\{/' => 'teacher_only',
        '/if\s*\(\s*!check_permission\s*\(\s*[\'"]staff[\'"]\s*\)\s*\)\s*\{/' => 'staff_only',
        '/if\s*\(\s*!check_permission\s*\(\s*[\'"]student[\'"]\s*\)\s*\)\s*\{/' => 'student_only'
    ];
    
    $lines = explode("\n", $content);
    
    foreach ($patterns as $pattern => $type) {
        if (preg_match($pattern, $content, $matches, PREG_OFFSET_CAPTURE)) {
            $analysis['has_permission_check'] = true;
            $analysis['permission_patterns'][] = $type;
            
            // العثور على رقم السطر
            $offset = $matches[0][1];
            $lineNumber = substr_count(substr($content, 0, $offset), "\n") + 1;
            $analysis['line_numbers'][] = $lineNumber;
            
            // تحديد ما إذا كان يحتاج إصلاح
            if ($type === 'admin_only' || $type === 'teacher_only') {
                $analysis['needs_fix'] = true;
                
                if ($type === 'admin_only') {
                    $analysis['suggested_fix'] = "if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {";
                } elseif ($type === 'teacher_only') {
                    $analysis['suggested_fix'] = "if (!check_permission('teacher') && !check_permission('staff') && !has_permission('teacher_access')) {";
                }
            }
        }
    }
    
    return $analysis;
}

// فحص جميع المجلدات الرئيسية
$directories = [
    'students',
    'teachers', 
    'classes',
    'subjects',
    'exams',
    'reports',
    'finance',
    'settings',
    'dashboard',
    'admin',
    'parent_communication'
];

$scan_results = [];
$total_files = 0;
$files_need_fix = 0;

foreach ($directories as $dir) {
    $dirPath = dirname(__DIR__) . '/' . $dir;
    
    if (!is_dir($dirPath)) {
        continue;
    }
    
    $files = scanDirectory($dirPath);
    
    foreach ($files as $file) {
        $analysis = analyzeFile($file);
        
        if ($analysis !== null) {
            $total_files++;
            
            if ($analysis['needs_fix']) {
                $files_need_fix++;
            }
            
            if ($analysis['has_permission_check']) {
                $scan_results[] = $analysis;
            }
        }
    }
}

$page_title = 'فحص جميع صفحات النظام';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-search me-2"></i><?php echo $page_title; ?></h2>
            <p class="text-muted">فحص شامل لجميع صفحات النظام لتحديد الصفحات التي تحتاج إصلاح صلاحيات</p>
        </div>
        <div>
            <a href="fix_all_pages.php" class="btn btn-danger me-2">
                <i class="fas fa-tools me-2"></i>إصلاح جميع الصفحات
            </a>
            <a href="../settings/permissions.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة
            </a>
        </div>
    </div>

    <!-- ملخص النتائج -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-primary"><?php echo $total_files; ?></h3>
                    <p class="mb-0">إجمالي الملفات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-info"><?php echo count($scan_results); ?></h3>
                    <p class="mb-0">ملفات بها فحص صلاحيات</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-warning"><?php echo $files_need_fix; ?></h3>
                    <p class="mb-0">ملفات تحتاج إصلاح</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-success"><?php echo count($scan_results) - $files_need_fix; ?></h3>
                    <p class="mb-0">ملفات سليمة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- نتائج الفحص -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5><i class="fas fa-list me-2"></i>نتائج الفحص التفصيلية</h5>
        </div>
        <div class="card-body">
            <?php if (empty($scan_results)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    لم يتم العثور على ملفات تحتوي على فحص صلاحيات.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>الملف</th>
                                <th>نوع الفحص</th>
                                <th>رقم السطر</th>
                                <th>الحالة</th>
                                <th>الإصلاح المقترح</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($scan_results as $result): ?>
                                <tr>
                                    <td>
                                        <code><?php echo htmlspecialchars($result['path']); ?></code>
                                    </td>
                                    <td>
                                        <?php foreach ($result['permission_patterns'] as $pattern): ?>
                                            <span class="badge bg-<?php 
                                                echo match($pattern) {
                                                    'admin_only' => 'danger',
                                                    'teacher_only' => 'warning', 
                                                    'staff_only' => 'success',
                                                    'student_only' => 'info',
                                                    default => 'secondary'
                                                };
                                            ?> me-1">
                                                <?php echo str_replace('_only', '', $pattern); ?>
                                            </span>
                                        <?php endforeach; ?>
                                    </td>
                                    <td>
                                        <?php foreach ($result['line_numbers'] as $line): ?>
                                            <span class="badge bg-secondary me-1"><?php echo $line; ?></span>
                                        <?php endforeach; ?>
                                    </td>
                                    <td>
                                        <?php if ($result['needs_fix']): ?>
                                            <span class="badge bg-warning">يحتاج إصلاح</span>
                                        <?php else: ?>
                                            <span class="badge bg-success">سليم</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($result['suggested_fix']): ?>
                                            <small class="text-muted">
                                                <?php echo htmlspecialchars(substr($result['suggested_fix'], 0, 50)); ?>...
                                            </small>
                                        <?php else: ?>
                                            <span class="text-muted">لا يحتاج</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- إحصائيات حسب النوع -->
    <div class="card mt-4">
        <div class="card-header bg-secondary text-white">
            <h6><i class="fas fa-chart-pie me-2"></i>إحصائيات حسب نوع الفحص</h6>
        </div>
        <div class="card-body">
            <?php
            $pattern_counts = [];
            foreach ($scan_results as $result) {
                foreach ($result['permission_patterns'] as $pattern) {
                    $pattern_counts[$pattern] = ($pattern_counts[$pattern] ?? 0) + 1;
                }
            }
            ?>
            
            <div class="row">
                <?php foreach ($pattern_counts as $pattern => $count): ?>
                    <div class="col-md-3 mb-3">
                        <div class="text-center">
                            <h4 class="text-<?php 
                                echo match($pattern) {
                                    'admin_only' => 'danger',
                                    'teacher_only' => 'warning',
                                    'staff_only' => 'success', 
                                    'student_only' => 'info',
                                    default => 'secondary'
                                };
                            ?>"><?php echo $count; ?></h4>
                            <p class="mb-0"><?php echo str_replace('_only', ' فقط', $pattern); ?></p>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- توصيات -->
    <div class="card mt-4">
        <div class="card-header bg-info text-white">
            <h6><i class="fas fa-lightbulb me-2"></i>التوصيات</h6>
        </div>
        <div class="card-body">
            <?php if ($files_need_fix > 0): ?>
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>يوجد <?php echo $files_need_fix; ?> ملف يحتاج إصلاح:</h6>
                    <ul class="mb-0">
                        <li>الملفات التي تتحقق من صلاحية "admin" فقط يجب أن تدعم "staff" أيضاً</li>
                        <li>الملفات التي تتحقق من صلاحية "teacher" فقط يجب أن تدعم "staff" أيضاً</li>
                        <li>يُنصح بإضافة صلاحيات مخصصة للمرونة</li>
                    </ul>
                </div>
                
                <div class="text-center">
                    <a href="fix_all_pages.php" class="btn btn-danger">
                        <i class="fas fa-tools me-2"></i>إصلاح جميع الصفحات تلقائياً
                    </a>
                </div>
            <?php else: ?>
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle me-2"></i>ممتاز!</h6>
                    <p class="mb-0">جميع الصفحات تدعم الصلاحيات المطلوبة أو لا تحتاج إصلاح.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
