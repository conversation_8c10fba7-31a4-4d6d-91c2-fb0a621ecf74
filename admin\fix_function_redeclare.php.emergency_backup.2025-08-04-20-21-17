<?php
/**
 * إصلاح خطأ إعادة تعريف الدوال
 * Fix Function Redeclaration Error
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

$results = [];
$success = true;

// معالجة الإصلاح
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fix_redeclare'])) {
    try {
        $results[] = "🚀 بدء إصلاح خطأ إعادة تعريف الدوال";
        
        // قائمة الملفات التي قد تحتوي على تعريفات مكررة
        $files_to_check = [
            'admin/quick_fix_permissions.php',
            'admin/debug_permissions_table.php',
            'admin/fix_permissions_compatibility.php',
            'admin/test_staff_access.php',
            'admin/system_permissions_summary.php',
            'admin/staff_permissions_manager.php',
            'admin/fix_htmlspecialchars_errors.php'
        ];
        
        $fixed_files = 0;
        $skipped_files = 0;
        
        foreach ($files_to_check as $file_path) {
            $full_path = "../$file_path";
            
            if (!file_exists($full_path)) {
                $results[] = "⚠️ الملف غير موجود: $file_path";
                $skipped_files++;
                continue;
            }
            
            $content = file_get_contents($full_path);
            if ($content === false) {
                $results[] = "❌ فشل في قراءة الملف: $file_path";
                $skipped_files++;
                continue;
            }
            
            $original_content = $content;
            
            // البحث عن تعريفات الدوال المكررة وحذفها
            $patterns_to_remove = [
                '/\/\/\s*دالة مساعدة.*?\n.*?function safe_html.*?\{.*?\}\s*\n/s',
                '/function safe_html\s*\([^}]+\}\s*\n/s',
                '/\/\*\*[^*]*\*\/\s*\nfunction safe_html\s*\([^}]+\}\s*\n/s'
            ];
            
            $changes = 0;
            foreach ($patterns_to_remove as $pattern) {
                $new_content = preg_replace($pattern, '', $content);
                if ($new_content !== $content) {
                    $content = $new_content;
                    $changes++;
                }
            }
            
            // حذف التعليقات المتعلقة بالدالة
            $content = preg_replace('/\/\/\s*دالة مساعدة لتجنب خطأ htmlspecialchars مع null\s*\n/', '', $content);
            
            // حفظ الملف إذا تم تغييره
            if ($content !== $original_content) {
                // إنشاء نسخة احتياطية
                $backup_path = $full_path . '.backup.' . date('Y-m-d-H-i-s');
                copy($full_path, $backup_path);
                
                // كتابة المحتوى الجديد
                if (file_put_contents($full_path, $content)) {
                    $results[] = "✅ تم إصلاح: $file_path";
                    $fixed_files++;
                } else {
                    $results[] = "❌ فشل في كتابة الملف: $file_path";
                    $skipped_files++;
                }
            } else {
                $results[] = "ℹ️ لا يحتاج إصلاح: $file_path";
                $skipped_files++;
            }
        }
        
        // التحقق من وجود الدالة في functions.php
        $functions_file = '../includes/functions.php';
        $functions_content = file_get_contents($functions_file);
        
        if (strpos($functions_content, 'function safe_html') !== false) {
            $results[] = "✅ دالة safe_html موجودة في functions.php";
        } else {
            // إضافة الدالة إلى functions.php
            $safe_html_function = "\n/**\n * دالة آمنة لـ htmlspecialchars تتعامل مع القيم null\n * Safe htmlspecialchars function that handles null values\n */\nfunction safe_html(\$value, \$default = '') {\n    return htmlspecialchars(\$value ?? \$default, ENT_QUOTES, 'UTF-8');\n}\n";
            
            $functions_content .= $safe_html_function;
            
            // إنشاء نسخة احتياطية
            $backup_file = $functions_file . '.backup.' . date('Y-m-d-H-i-s');
            copy($functions_file, $backup_file);
            
            if (file_put_contents($functions_file, $functions_content)) {
                $results[] = "✅ تم إضافة دالة safe_html إلى functions.php";
            } else {
                throw new Exception("فشل في كتابة ملف functions.php");
            }
        }
        
        $results[] = "\n📊 ملخص العملية:";
        $results[] = "✅ تم إصلاح $fixed_files ملف";
        $results[] = "⚠️ تم تخطي $skipped_files ملف";
        
        $results[] = "\n🎉 تم إصلاح خطأ إعادة تعريف الدوال بنجاح!";
        $results[] = "📋 الآن يمكن استخدام جميع الصفحات بدون أخطاء";
        
    } catch (Exception $e) {
        $success = false;
        $results[] = "❌ خطأ: " . $e->getMessage();
    }
}

$page_title = 'إصلاح خطأ إعادة تعريف الدوال';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-exclamation-triangle me-2"></i><?php echo $page_title; ?></h2>
            <p class="text-muted">إصلاح خطأ "Cannot redeclare safe_html()" في النظام</p>
        </div>
        <a href="../settings/permissions.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة
        </a>
    </div>

    <!-- شرح المشكلة -->
    <div class="alert alert-danger">
        <h6><i class="fas fa-exclamation-triangle me-2"></i>المشكلة:</h6>
        <p class="mb-2"><strong>خطأ:</strong> <code>Fatal error: Cannot redeclare safe_html()</code></p>
        <p class="mb-0"><strong>السبب:</strong> الدالة <code>safe_html()</code> تم تعريفها في أكثر من ملف، مما يسبب تضارب.</p>
    </div>

    <!-- نتائج الإصلاح -->
    <?php if (!empty($results)): ?>
        <div class="card mb-4">
            <div class="card-header <?php echo $success ? 'bg-success' : 'bg-danger'; ?> text-white">
                <h5>
                    <i class="fas fa-<?php echo $success ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                    <?php echo $success ? 'تم الإصلاح بنجاح!' : 'فشل في الإصلاح'; ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="bg-dark text-light p-3 rounded" style="font-family: monospace; white-space: pre-line; max-height: 400px; overflow-y: auto;">
<?php echo implode("\n", $results); ?>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- نموذج الإصلاح -->
    <?php if (empty($results) || !$success): ?>
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5><i class="fas fa-tools me-2"></i>إصلاح خطأ إعادة تعريف الدوال</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>ما سيتم عمله:</h6>
                    <ol>
                        <li><strong>فحص الملفات:</strong> البحث عن تعريفات مكررة للدالة safe_html</li>
                        <li><strong>حذف التعريفات المكررة:</strong> إزالة الدوال من الملفات الفردية</li>
                        <li><strong>الاحتفاظ بتعريف واحد:</strong> في ملف functions.php فقط</li>
                        <li><strong>إنشاء نسخ احتياطية:</strong> من جميع الملفات المعدلة</li>
                        <li><strong>اختبار النتيجة:</strong> التأكد من عدم وجود أخطاء</li>
                    </ol>
                </div>

                <div class="card mb-3">
                    <div class="card-header">
                        <h6><i class="fas fa-list me-2"></i>الملفات التي سيتم فحصها:</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="small">
                                    <li>admin/quick_fix_permissions.php</li>
                                    <li>admin/debug_permissions_table.php</li>
                                    <li>admin/fix_permissions_compatibility.php</li>
                                    <li>admin/test_staff_access.php</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="small">
                                    <li>admin/system_permissions_summary.php</li>
                                    <li>admin/staff_permissions_manager.php</li>
                                    <li>admin/fix_htmlspecialchars_errors.php</li>
                                    <li>includes/functions.php (الوحيد الذي سيحتفظ بالدالة)</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <form method="POST">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="confirmFix" required>
                        <label class="form-check-label" for="confirmFix">
                            <strong>أؤكد أنني أريد إصلاح خطأ إعادة تعريف الدوال</strong>
                        </label>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="../settings/permissions.php" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" name="fix_redeclare" class="btn btn-primary" id="fixButton" disabled>
                            <i class="fas fa-tools me-2"></i>إصلاح الخطأ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    <?php endif; ?>

    <!-- تعليمات الاختبار -->
    <?php if ($success && !empty($results)): ?>
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5><i class="fas fa-clipboard-check me-2"></i>اختبار النتيجة</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <h6><i class="fas fa-thumbs-up me-2"></i>تم الإصلاح!</h6>
                    <p class="mb-0">الآن يجب أن تعمل جميع الصفحات بدون خطأ إعادة تعريف الدوال.</p>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">اختبر الصفحات:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <a href="debug_permissions_table.php" target="_blank">
                                    <i class="fas fa-bug me-2"></i>تشخيص الصلاحيات
                                </a>
                            </li>
                            <li class="list-group-item">
                                <a href="quick_fix_permissions.php" target="_blank">
                                    <i class="fas fa-bolt me-2"></i>إصلاح سريع للصلاحيات
                                </a>
                            </li>
                            <li class="list-group-item">
                                <a href="test_staff_access.php" target="_blank">
                                    <i class="fas fa-vial me-2"></i>اختبار وصول الإداري
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-success">يجب أن تعمل الآن:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">✅ جميع الصفحات بدون أخطاء</li>
                            <li class="list-group-item">✅ دالة safe_html متاحة في كل مكان</li>
                            <li class="list-group-item">✅ لا توجد تعريفات مكررة</li>
                            <li class="list-group-item">✅ النظام يعمل بسلاسة</li>
                        </ul>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <a href="debug_permissions_table.php" class="btn btn-info me-2">
                        <i class="fas fa-bug me-2"></i>اختبار التشخيص
                    </a>
                    <a href="quick_fix_permissions.php" class="btn btn-success me-2">
                        <i class="fas fa-bolt me-2"></i>متابعة إصلاح الصلاحيات
                    </a>
                    <button class="btn btn-secondary" onclick="window.location.reload()">
                        <i class="fas fa-redo me-2"></i>إعادة الإصلاح
                    </button>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
// تفعيل زر الإصلاح عند تأكيد الاختيار
document.getElementById('confirmFix')?.addEventListener('change', function() {
    document.getElementById('fixButton').disabled = !this.checked;
});
</script>

<?php include_once '../includes/footer.php'; ?>
