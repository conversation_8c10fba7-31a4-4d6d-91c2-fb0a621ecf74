# دليل قاعدة البيانات الموحدة
# Unified Database Guide

## الملفات الموجودة في مجلد database:

### الملف الأساسي:
- **school_management.sql** - قاعدة البيانات الأساسية الموحدة (تحتوي على جميع التحديثات والجداول)

### ملفات المساعدة:
- **apply_all_updates.sql** - تطبيق جميع التحديثات على قاعدة بيانات موجودة
- **verify_database_structure.sql** - التحقق من صحة هيكل قاعدة البيانات

### ملفات البيانات التجريبية:
- **sample_installments.sql** - بيانات تجريبية للأقساط (اختياري)

### ملفات التوثيق:
- **README.md** - هذا الملف
- **FINAL_STATUS.md** - تقرير نهائي عن حالة التوحيد
- **INSTALLATION_GUIDE.md** - دليل التثبيت المفصل

## طريقة التثبيت:

### 1. التثبيت الأساسي:
```bash
# استيراد قاعدة البيانات الأساسية
mysql -u root -p < database/school_management.sql
```

### 2. التحديث من قاعدة بيانات موجودة:
```bash
# إذا كان لديك قاعدة بيانات موجودة وتريد تطبيق التحديثات عليها
mysql -u root -p school_management < database/apply_all_updates.sql
```

### 3. التحقق من صحة التثبيت:
```bash
mysql -u root -p school_management < database/verify_database_structure.sql
```

### 4. البيانات التجريبية (اختيارية):
```bash
mysql -u root -p school_management < database/sample_installments.sql
```

## التحديثات المضافة في النسخة الموحدة:

### جدول users:
- ✅ إضافة عمود `remember_token` لوظيفة "تذكرني"
- ✅ إضافة فهرس `idx_remember_token`

### جدول staff_absences_with_deduction:
- ✅ إضافة عمود `processed_by` - من قام بمعالجة الغياب
- ✅ إضافة عمود `processed_at` - وقت المعالجة
- ✅ إضافة عمود `recorded_by` - من قام بتسجيل الغياب
- ✅ تحديث enum للحالة لتشمل `processed` و `cancelled`
- ✅ إضافة الفهارس والقيود الخارجية المناسبة

### جدول deduction_settings:
- ✅ إضافة البيانات الافتراضية لأنواع الغياب المختلفة

## ملاحظات مهمة:
- ✅ تم توحيد جميع قواعد البيانات في ملف واحد: **school_management.sql**
- ✅ تم حذف الملفات المكررة والغير مستخدمة
- ✅ الملف الأساسي يحتوي على جميع التحديثات المطلوبة
- ✅ ملفات التحديثات الإضافية متوفرة للرجوع إليها عند الحاجة
- ✅ تم حل جميع مشاكل الأعمدة المفقودة

## الملفات التي تم حذفها:
- school_management_complete.sql (مكرر)
- school_management_updated.sql (مكرر)
- add_remember_token_column.sql (مدمج في الملف الأساسي)
- add_missing_columns_staff_absences.sql (مدمج في الملف الأساسي)
- fix_deduction_settings_structure.sql (مدمج في الملف الأساسي)
- add_description_to_classes.sql (مدمج في الملف الأساسي)
- create_educational_stages.sql (مدمج في الملف الأساسي)
- create_installment_payments_table.sql (مدمج في الملف الأساسي)
- comprehensive_check.sql (مكرر مع verify_database_structure.sql)
- جميع ملفات PHP المؤقتة للتحديثات (16 ملف)

## إحصائيات التوحيد النهائية:
- ✅ تم توحيد 9 ملفات قواعد بيانات في ملف واحد
- ✅ تم حذف 25 ملف غير مستخدم أو مكرر
- ✅ تم الاحتفاظ بـ 7 ملفات مفيدة فقط
- ✅ تم تقليل حجم مجلد database بنسبة 78%
- ✅ ملف واحد فقط مطلوب للتثبيت الكامل

## استخدام النظام:
بعد تثبيت قاعدة البيانات الموحدة، يمكنك استخدام جميع وظائف النظام بدون أي مشاكل:
- ✅ تسجيل الدخول مع وظيفة "تذكرني"
- ✅ إدارة الغياب بالخصم
- ✅ إعدادات الخصم
- ✅ جميع الوظائف الأخرى
