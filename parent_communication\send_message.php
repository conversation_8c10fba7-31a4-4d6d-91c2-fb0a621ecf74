<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';
require_once 'WhatsAppService.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('teacher')) {
    header('Location: ../dashboard/');
    exit();
}

global $conn;

// معالجة إرسال الرسالة
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $student_id = intval($_POST['student_id'] ?? 0);
    $message_type = clean_input($_POST['message_type'] ?? 'general');
    $subject = clean_input($_POST['subject'] ?? '');
    $message = clean_input($_POST['message'] ?? '');
    $priority = clean_input($_POST['priority'] ?? 'medium');
    $send_via = clean_input($_POST['send_via'] ?? 'whatsapp');
    
    $errors = [];
    
    // التحقق من البيانات
    if ($student_id <= 0) {
        $errors[] = __('select_student');
    }
    if (empty($subject)) {
        $errors[] = __('message_subject') . ' ' . __('required_field');
    }
    if (empty($message)) {
        $errors[] = __('message_content') . ' ' . __('required_field');
    }
    
    if (empty($errors)) {
        // جلب بيانات الطالب وولي الأمر
        $student_query = "
            SELECT 
                s.id,
                s.student_id as student_number,
                u.full_name as student_name,
                s.parent_phone,
                c.class_name
            FROM students s
            JOIN users u ON s.user_id = u.id
            LEFT JOIN classes c ON s.class_id = c.id
            WHERE s.id = ?
        ";
        
        $stmt = $conn->prepare($student_query);
        $stmt->bind_param('i', $student_id);
        $stmt->execute();
        $student = $stmt->get_result()->fetch_assoc();
        $stmt->close();
        
        if ($student && !empty($student['parent_phone'])) {
            // إضافة اسم الطالب للرسالة
            $full_message = "السلام عليكم ولي أمر الطالب/ة " . $student['student_name'] . "\n\n" . $message . "\n\nمع تحيات إدارة المدرسة";
            
            // حفظ الرسالة في قاعدة البيانات
            $insert_query = "
                INSERT INTO parent_communications 
                (student_id, parent_phone, message_type, subject, message, priority, sent_via, status, sent_by, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', ?, NOW())
            ";
            
            $stmt = $conn->prepare($insert_query);
            $stmt->bind_param('issssssi', 
                $student_id, 
                $student['parent_phone'], 
                $message_type, 
                $subject, 
                $full_message, 
                $priority, 
                $send_via, 
                $_SESSION['user_id']
            );
            
            if ($stmt->execute()) {
                $message_id = $conn->insert_id;
                $stmt->close();
                
                // إرسال الرسالة عبر واتساب
                if ($send_via === 'whatsapp') {
                    $whatsapp = new WhatsAppService($conn);
                    $result = $whatsapp->sendMessage($student['parent_phone'], $full_message, $message_id);
                    
                    if ($result['success']) {
                        if ($result['method'] === 'whatsapp_web') {
                            $_SESSION['success_message'] = __('message_sent_successfully') . '<br><a href="' . $result['whatsapp_url'] . '" target="_blank" class="btn btn-success btn-sm mt-2"><i class="fab fa-whatsapp me-1"></i>فتح واتساب لإرسال الرسالة</a>';
                        } else {
                            $_SESSION['success_message'] = __('message_sent_successfully');
                        }
                    } else {
                        $_SESSION['error_message'] = 'فشل في إرسال الرسالة: ' . $result['error'];
                    }
                } else {
                    $_SESSION['success_message'] = 'تم حفظ الرسالة بنجاح. سيتم إرسالها عبر ' . __($send_via);
                }
                
                header('Location: send_message.php');
                exit();
            } else {
                $errors[] = 'فشل في حفظ الرسالة: ' . $stmt->error;
                $stmt->close();
            }
        } else {
            $errors[] = 'لم يتم العثور على الطالب أو رقم هاتف ولي الأمر غير متوفر';
        }
    }
    
    if (!empty($errors)) {
        $_SESSION['error_message'] = implode('<br>', $errors);
    }
}

// جلب قائمة الطلاب
$students_query = "
    SELECT
        s.id,
        s.student_id as student_number,
        u.full_name as student_name,
        c.class_name,
        s.parent_phone,
        CASE WHEN s.parent_phone IS NOT NULL AND s.parent_phone != '' THEN 1 ELSE 0 END as has_phone
    FROM students s
    JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    WHERE s.status = 'active'
    ORDER BY u.full_name
";

$students_result = $conn->query($students_query);
$students = [];
if ($students_result) {
    while ($row = $students_result->fetch_assoc()) {
        $students[] = $row;
    }
}

// جلب قوالب الرسائل النشطة
$templates_query = "
    SELECT
        id,
        template_name,
        category,
        subject,
        message_body,
        variables
    FROM message_templates
    WHERE is_active = 1
    ORDER BY template_name
";

$templates_result = $conn->query($templates_query);
$templates = [];
if ($templates_result) {
    while ($row = $templates_result->fetch_assoc()) {
        $templates[] = $row;
    }
}

// التحقق من وجود قالب محدد
$selected_template = null;
$template_id = isset($_GET['template_id']) ? intval($_GET['template_id']) : 0;
if ($template_id > 0) {
    foreach ($templates as $template) {
        if ($template['id'] == $template_id) {
            $selected_template = $template;
            break;
        }
    }
}



$page_title = __('send_message');
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-paper-plane me-2"></i>
            <?php echo __('send_message'); ?>
        </h2>
        <div class="btn-group">
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back'); ?>
            </a>
            <a href="bulk_message.php" class="btn btn-info">
                <i class="fas fa-broadcast-tower me-2"></i><?php echo __('bulk_message'); ?>
            </a>
        </div>
    </div>

    <!-- رسائل النجاح والخطأ -->
    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php elseif (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- نموذج إرسال الرسالة -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-envelope me-2"></i>إرسال رسالة جديدة
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <!-- اختيار الطالب -->
                        <div class="mb-3">
                            <label for="student_id" class="form-label">
                                <i class="fas fa-user-graduate me-2"></i><?php echo __('select_student'); ?>
                                <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="student_id" name="student_id" required>
                                <option value="">اختر الطالب...</option>
                                <?php foreach ($students as $student): ?>
                                    <option value="<?php echo $student['id']; ?>" 
                                            data-phone="<?php echo $student['parent_phone']; ?>"
                                            data-class="<?php echo $student['class_name']; ?>"
                                            <?php echo !$student['has_phone'] ? 'disabled' : ''; ?>>
                                        <?php echo safe_html($student['student_name']); ?> 
                                        (<?php echo safe_html($student['student_number']); ?>)
                                        <?php if ($student['class_name']): ?>
                                            - <?php echo safe_html($student['class_name']); ?>
                                        <?php endif; ?>
                                        <?php if (!$student['has_phone']): ?>
                                            - <span class="text-danger">لا يوجد رقم هاتف</span>
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text" id="student-info"></div>
                            <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                        </div>

                        <div class="row">
                            <!-- نوع الرسالة -->
                            <div class="col-md-6 mb-3">
                                <label for="message_type" class="form-label">
                                    <i class="fas fa-tag me-2"></i><?php echo __('message_type'); ?>
                                </label>
                                <select class="form-select" id="message_type" name="message_type">
                                    <option value="general"><?php echo __('general'); ?></option>
                                    <option value="behavior"><?php echo __('student_behavior'); ?></option>
                                    <option value="academic"><?php echo __('academic'); ?></option>
                                    <option value="attendance"><?php echo __('attendance'); ?></option>
                                    <option value="emergency"><?php echo __('emergency'); ?></option>
                                </select>
                            </div>

                            <!-- الأولوية -->
                            <div class="col-md-6 mb-3">
                                <label for="priority" class="form-label">
                                    <i class="fas fa-exclamation-circle me-2"></i><?php echo __('message_priority'); ?>
                                </label>
                                <select class="form-select" id="priority" name="priority">
                                    <option value="low"><?php echo __('low'); ?></option>
                                    <option value="medium" selected><?php echo __('medium'); ?></option>
                                    <option value="high"><?php echo __('high'); ?></option>
                                    <option value="urgent"><?php echo __('urgent'); ?></option>
                                </select>
                            </div>
                        </div>

                        <!-- اختيار قالب -->
                        <?php if (!empty($templates)): ?>
                        <div class="mb-3">
                            <label for="template_select" class="form-label">
                                <i class="fas fa-file-alt me-2"></i><?php echo __('use_template'); ?> (اختياري)
                            </label>
                            <select class="form-select" id="template_select" onchange="loadTemplate()">
                                <option value="">اختر قالب محفوظ...</option>
                                <?php foreach ($templates as $template): ?>
                                    <option value="<?php echo $template['id']; ?>"
                                            data-subject="<?php echo safe_html($template['subject']); ?>"
                                            data-content="<?php echo safe_html($template['message_body']); ?>"
                                            data-type="<?php echo $template['category']; ?>"
                                            <?php echo $selected_template && $selected_template['id'] == $template['id'] ? 'selected' : ''; ?>>
                                        <?php echo safe_html($template['template_name']); ?>
                                        (<?php echo __($template['category']); ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                اختر قالب محفوظ لملء الحقول تلقائياً
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- موضوع الرسالة -->
                        <div class="mb-3">
                            <label for="subject" class="form-label">
                                <i class="fas fa-heading me-2"></i><?php echo __('message_subject'); ?>
                                <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="subject" name="subject"
                                   value="<?php echo $selected_template ? safe_html($selected_template['subject']) : ''; ?>"
                                   placeholder="أدخل موضوع الرسالة..." required>
                            <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                        </div>

                        <!-- محتوى الرسالة -->
                        <div class="mb-3">
                            <label for="message" class="form-label">
                                <i class="fas fa-comment me-2"></i><?php echo __('message_content'); ?>
                                <span class="text-danger">*</span>
                            </label>
                            <textarea class="form-control" id="message" name="message" rows="6"
                                      placeholder="اكتب محتوى الرسالة هنا..." required><?php echo $selected_template ? safe_html($selected_template['message_body']) : ''; ?></textarea>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                سيتم إضافة اسم الطالب وتحية المدرسة تلقائياً. يمكنك استخدام المتغيرات مثل {student_name}, {class_name}, {date}
                            </div>
                            <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                        </div>

                        <!-- طريقة الإرسال -->
                        <div class="mb-3">
                            <label for="send_via" class="form-label">
                                <i class="fas fa-share me-2"></i><?php echo __('send_via'); ?>
                            </label>
                            <select class="form-select" id="send_via" name="send_via">
                                <option value="whatsapp">
                                    <i class="fab fa-whatsapp"></i> <?php echo __('whatsapp'); ?>
                                </option>
                                <option value="sms"><?php echo __('sms'); ?></option>
                                <option value="email"><?php echo __('email'); ?></option>
                            </select>
                        </div>

                        <!-- أزرار الإجراء -->
                        <div class="d-flex justify-content-end gap-2">
                            <button type="button" class="btn btn-secondary" onclick="window.history.back()">
                                <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i><?php echo __('send_now'); ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- قوالب الرسائل -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file-alt me-2"></i><?php echo __('message_templates'); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($templates)): ?>
                        <div class="accordion" id="templatesAccordion">
                            <?php 
                            $categories = [];
                            foreach ($templates as $template) {
                                $categories[$template['category']][] = $template;
                            }
                            
                            $index = 0;
                            foreach ($categories as $category => $categoryTemplates): 
                            ?>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="heading<?php echo $index; ?>">
                                        <button class="accordion-button <?php echo $index > 0 ? 'collapsed' : ''; ?>" 
                                                type="button" data-bs-toggle="collapse" 
                                                data-bs-target="#collapse<?php echo $index; ?>">
                                            <?php echo __($category); ?>
                                        </button>
                                    </h2>
                                    <div id="collapse<?php echo $index; ?>" 
                                         class="accordion-collapse collapse <?php echo $index === 0 ? 'show' : ''; ?>" 
                                         data-bs-parent="#templatesAccordion">
                                        <div class="accordion-body">
                                            <?php foreach ($categoryTemplates as $template): ?>
                                                <div class="template-item mb-2 p-2 border rounded">
                                                    <h6 class="mb-1"><?php echo safe_html($template['template_name']); ?></h6>
                                                    <p class="mb-1 small text-muted"><?php echo safe_html($template['subject']); ?></p>
                                                    <button type="button" class="btn btn-sm btn-outline-primary use-template"
                                                            data-subject="<?php echo htmlspecialchars($template['subject']); ?>"
                                                            data-message="<?php echo htmlspecialchars($template['message_body']); ?>">
                                                        <i class="fas fa-copy me-1"></i><?php echo __('use_template'); ?>
                                                    </button>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php 
                            $index++;
                            endforeach; 
                            ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-3">
                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد قوالب متاحة</p>
                            <a href="message_templates.php" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus me-1"></i>إضافة قالب
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحديث معلومات الطالب عند الاختيار
document.getElementById('student_id').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    const studentInfo = document.getElementById('student-info');
    
    if (selectedOption.value) {
        const phone = selectedOption.dataset.phone;
        const className = selectedOption.dataset.class;
        
        let info = '';
        if (className) {
            info += '<i class="fas fa-school me-1"></i>الفصل: ' + className + ' ';
        }
        if (phone) {
            info += '<i class="fas fa-phone me-1"></i>هاتف ولي الأمر: ' + phone;
        } else {
            info += '<i class="fas fa-exclamation-triangle me-1 text-danger"></i>لا يوجد رقم هاتف لولي الأمر';
        }
        
        studentInfo.innerHTML = info;
    } else {
        studentInfo.innerHTML = '';
    }
});

// استخدام القوالب
document.querySelectorAll('.use-template').forEach(button => {
    button.addEventListener('click', function() {
        const subject = this.dataset.subject;
        const message = this.dataset.message;
        
        document.getElementById('subject').value = subject;
        document.getElementById('message').value = message;
        
        // إظهار رسالة تأكيد
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
        toast.style.zIndex = '9999';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check me-2"></i>تم استخدام القالب
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        
        document.body.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // إزالة التوست بعد إخفاؤه
        toast.addEventListener('hidden.bs.toast', () => {
            document.body.removeChild(toast);
        });
    });
});

// تحميل قالب
function loadTemplate() {
    const select = document.getElementById('template_select');
    if (!select) return;

    const selectedOption = select.options[select.selectedIndex];

    if (selectedOption.value) {
        // ملء الحقول من البيانات المحفوظة في الخيار
        document.getElementById('subject').value = selectedOption.dataset.subject || '';
        document.getElementById('message').value = selectedOption.dataset.content || '';
        document.getElementById('message_type').value = selectedOption.dataset.type || 'general';

        // إظهار رسالة تأكيد
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
        toast.style.zIndex = '9999';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check me-2"></i>تم تحميل القالب بنجاح
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;

        document.body.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        // إزالة التوست بعد إخفاؤه
        toast.addEventListener('hidden.bs.toast', () => {
            document.body.removeChild(toast);
        });
    } else {
        // مسح الحقول
        document.getElementById('subject').value = '';
        document.getElementById('message').value = '';
        document.getElementById('message_type').value = 'general';
    }
}

// تحميل القالب المحدد عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    const templateSelect = document.getElementById('template_select');
    if (templateSelect && templateSelect.value) {
        loadTemplate();
    }
});

// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        Array.prototype.forEach.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?php require_once '../includes/footer.php'; ?>
