<?php
if (session_status() === PHP_SESSION_NONE) { session_start(); }
require_once '../includes/config.php';
require_once '../includes/database.php';
/**
 * صفحة إضافة طالب جديد
 * Add New Student Page
 */

require_once '../includes/functions.php';
require_once '../includes/security.php';

$error_message = '';
$success_message = '';

// معالجة إضافة الطالب
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من رمز CSRF
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = __('invalid_request');
        unset($_SESSION['success_message']); // امسح رسالة النجاح عند أي خطأ أمني
    } else {
        // جمع البيانات وتنظيفها
        $full_name = clean_input($_POST['full_name'] ?? '');
        $username = clean_input($_POST['username'] ?? '');
        $email = clean_input($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        $phone = clean_input($_POST['phone'] ?? '');
        $student_id = clean_input($_POST['student_id'] ?? '');
        $national_id = clean_input($_POST['national_id'] ?? '');
        $date_of_birth = clean_input($_POST['date_of_birth'] ?? '');
        $gender = clean_input($_POST['gender'] ?? '');
        $address = clean_input($_POST['address'] ?? '');
        $class_id = intval($_POST['class_id'] ?? 0);
        $parent_name = clean_input($_POST['parent_name'] ?? '');
        $parent_phone = clean_input($_POST['parent_phone'] ?? '');
        $parent_email = clean_input($_POST['parent_email'] ?? '');
        $parent_national_id = clean_input($_POST['parent_national_id'] ?? '');
        $emergency_contact_name = clean_input($_POST['emergency_contact_name'] ?? '');
        $emergency_contact_phone = clean_input($_POST['emergency_contact_phone'] ?? '');
        $medical_conditions = clean_input($_POST['medical_conditions'] ?? '');
        $enrollment_date = clean_input($_POST['enrollment_date'] ?? date('Y-m-d'));
        $status = clean_input($_POST['status'] ?? 'active');

        // التحقق من صحة البيانات
        $errors = [];

        if (empty($full_name)) {
            $errors[] = __('full_name') . ' ' . __('required_field');
        }

        if (empty($username)) {
            $errors[] = __('username') . ' ' . __('required_field');
        } elseif (get_user_by_username($username)) {
            $errors[] = __('username_exists');
        }

        if (empty($email)) {
            $errors[] = __('email') . ' ' . __('required_field');
        } elseif (!validate_email($email)) {
            $errors[] = __('invalid_email');
        } elseif (get_user_by_email($email)) {
            $errors[] = __('email_exists');
        }

        if (empty($password)) {
            $errors[] = __('password') . ' ' . __('required_field');
        } elseif (!validate_password($password)) {
            $errors[] = __('password_too_short');
        } elseif ($password !== $confirm_password) {
            $errors[] = __('passwords_not_match');
        }

        if (empty($student_id)) {
            $errors[] = __('student_id') . ' ' . __('required_field');
        } else {
            $stmt = $conn->prepare("SELECT id FROM students WHERE student_id = ?");
            $stmt->bind_param("s", $student_id);
            $stmt->execute();
            if ($stmt->get_result()->num_rows > 0) {
                $errors[] = __('student_id_exists');
            }
        }

        if (!empty($national_id) && !validate_national_id($national_id)) {
            $errors['national_id'] = __('invalid_national_id'); // الرسالة ستظهر فقط إذا كان الرقم أقل من 5 أو أكثر من 20 رقم
        }

        if (!empty($phone) && !validate_phone($phone)) {
            $errors['phone'] = __('invalid_phone');
        }

        if (!empty($parent_phone) && !validate_phone($parent_phone)) {
            $errors['parent_phone'] = __('invalid_parent_phone');
        }

        if (!empty($parent_email) && !validate_email($parent_email)) {
            $errors[] = __('invalid_parent_email');
        }

        if (empty($gender) || !in_array($gender, ['male', 'female'])) {
            $errors[] = __('gender') . ' ' . __('required_field');
        }

        if (empty($errors)) {
            global $conn;
            $conn->begin_transaction();

            try {
                // إنشاء المستخدم
                $hashed_password = hash_password($password);
                $user_stmt = $conn->prepare("
                    INSERT INTO users (username, email, password, full_name, phone, role, status, created_at) 
                    VALUES (?, ?, ?, ?, ?, 'student', 'active', NOW())
                ");
                if (!$user_stmt) throw new Exception($conn->error);
                $user_stmt->bind_param("sssss", $username, $email, $hashed_password, $full_name, $phone);
                if (!$user_stmt->execute()) throw new Exception($user_stmt->error);
                $user_id = $conn->insert_id;

                // إنشاء سجل الطالب
                $student_stmt = $conn->prepare("
                    INSERT INTO students (
                        user_id, student_id, national_id, date_of_birth, gender, 
                        address, parent_name, parent_phone, parent_email, parent_national_id,
                        emergency_contact_name, emergency_contact_phone, medical_conditions, class_id, enrollment_date, status, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
                ");
                if (!$student_stmt) throw new Exception($conn->error);
                $student_stmt->bind_param(
                    "issssssssssssiss",
                    $user_id, $student_id, $national_id, $date_of_birth, $gender,
                    $address, $parent_name, $parent_phone, $parent_email, $parent_national_id,
                    $emergency_contact_name, $emergency_contact_phone, $medical_conditions, $class_id, $enrollment_date, $status
                );
                if (!$student_stmt->execute()) throw new Exception($student_stmt->error);
                $student_row_id = $conn->insert_id;

                // معالجة رفع الصورة الشخصية
                if (isset($_FILES['profile_picture']) && $_FILES['profile_picture']['error'] === UPLOAD_ERR_OK) {
                    $upload_result = upload_file($_FILES['profile_picture'], 'profiles', ALLOWED_IMAGE_TYPES);
                    if ($upload_result['success']) {
                        $update_stmt = $conn->prepare("UPDATE users SET profile_picture = ? WHERE id = ?");
                        $update_stmt->bind_param("si", $upload_result['filename'], $user_id);
                        $update_stmt->execute();
                    }
                }

                $conn->commit();

                // تسجيل النشاط
                log_activity($_SESSION['user_id'], 'add_student', 'students', $student_row_id, null, [
                    'student_name' => $full_name,
                    'student_number' => $student_id
                ]);

                // إرسال إشعار
                add_notification($user_id, __('welcome_to_system'), __('your_account_created_successfully'), 'success');

                $_SESSION['success_message'] = __('student_added_successfully');
                header('Location: index.php');
                exit();

            } catch (Exception $e) {
                $conn->rollback();
                log_error("Error adding student: " . $e->getMessage());
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    $error_message = __('error_occurred') . '<br>' . htmlspecialchars($e->getMessage());
                } else {
                    $error_message = __('error_occurred');
                }
            }
        } else {
            $error_message = implode('<br>', $errors);
        }
    }
}

$page_title = __('add_student');
require_once '../includes/header.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

// جلب قائمة الفصول مرتبة حسب المراحل والصفوف
$classes = $conn->query("
    SELECT
        c.id,
        c.class_name,
        c.grade_level,
        c.section,
        es.stage_name,
        es.sort_order as stage_sort_order,
        g.grade_name,
        g.sort_order as grade_sort_order
    FROM classes c
    LEFT JOIN educational_stages es ON c.stage_id = es.id
    LEFT JOIN grades g ON c.grade_id = g.id
    WHERE c.status = 'active'
    ORDER BY es.sort_order ASC, g.sort_order ASC, c.section ASC, c.class_name ASC
");

// إنشاء رقم طالب تلقائي
$current_year = date('Y');
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM students WHERE YEAR(enrollment_date) = ?");
$stmt->bind_param("i", $current_year);
$stmt->execute();
$student_count = $stmt->get_result()->fetch_assoc()['count'];
$suggested_student_number = $current_year . str_pad($student_count + 1, 4, '0', STR_PAD_LEFT);
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('add_student'); ?></h1>
            <p class="text-muted"><?php echo __('add_new_student_info'); ?></p>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
            </a>
        </div>
    </div>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php elseif (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $_SESSION['success_message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['success_message']); ?>
    <?php endif; ?>

    <!-- Add Student Form -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-user-plus me-2"></i><?php echo __('student_information'); ?>
            </h5>
        </div>
        <div class="card-body">
            <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">

                <div class="row">
                    <!-- المعلومات الشخصية -->
                    <div class="col-lg-6">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-user me-2"></i><?php echo __('personal_information'); ?>
                        </h6>

                        <div class="mb-3">
                            <label for="full_name" class="form-label"><?php echo __('full_name'); ?> <span class="text-danger">*</span></label>
                            <input type="text" 
                                   class="form-control" 
                                   id="full_name" 
                                   name="full_name" 
                                   value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>"
                                   required>
                            <div class="invalid-feedback">
                                <?php echo __('required_field'); ?>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="username" class="form-label"><?php echo __('username'); ?> <span class="text-danger">*</span></label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="username" 
                                           name="username" 
                                           value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                                           required>
                                    <div class="invalid-feedback">
                                        <?php echo __('required_field'); ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="student_id" class="form-label"><?php echo __('student_id'); ?> <span class="text-danger">*</span></label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="student_id" 
                                           name="student_id" 
                                           value="<?php echo htmlspecialchars($_POST['student_id'] ?? $suggested_student_number); ?>"
                                           required>
                                    <div class="invalid-feedback">
                                        <?php echo __('required_field'); ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label"><?php echo __('email'); ?> <span class="text-danger">*</span></label>
                            <input type="email" 
                                   class="form-control" 
                                   id="email" 
                                   name="email" 
                                   value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                                   required>
                            <div class="invalid-feedback">
                                <?php echo __('required_field'); ?>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label"><?php echo __('password'); ?> <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="password" 
                                               class="form-control" 
                                               id="password" 
                                               name="password" 
                                               required>
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback">
                                        <?php echo __('required_field'); ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label"><?php echo __('confirm_password'); ?> <span class="text-danger">*</span></label>
                                    <input type="password" 
                                           class="form-control" 
                                           id="confirm_password" 
                                           name="confirm_password" 
                                           required>
                                    <div class="invalid-feedback">
                                        <?php echo __('required_field'); ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="national_id" class="form-label"><?php echo __('national_id'); ?></label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="national_id" 
                                           name="national_id" 
                                           value="<?php echo htmlspecialchars($_POST['national_id'] ?? ''); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label"><?php echo __('phone'); ?></label>
                                    <input type="tel" 
                                           class="form-control" 
                                           id="phone" 
                                           name="phone" 
                                           value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="date_of_birth" class="form-label"><?php echo __('date_of_birth'); ?></label>
                                    <input type="date" 
                                           class="form-control" 
                                           id="date_of_birth" 
                                           name="date_of_birth" 
                                           value="<?php echo htmlspecialchars($_POST['date_of_birth'] ?? ''); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="gender" class="form-label"><?php echo __('gender'); ?> <span class="text-danger">*</span></label>
                                    <select class="form-select" id="gender" name="gender" required>
                                        <option value=""><?php echo __('choose_gender'); ?></option>
                                        <option value="male" <?php echo (($_POST['gender'] ?? '') === 'male') ? 'selected' : ''; ?>>
                                            <?php echo __('male'); ?>
                                        </option>
                                        <option value="female" <?php echo (($_POST['gender'] ?? '') === 'female') ? 'selected' : ''; ?>>
                                            <?php echo __('female'); ?>
                                        </option>
                                    </select>
                                    <div class="invalid-feedback">
                                        <?php echo __('required_field'); ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label"><?php echo __('address'); ?></label>
                            <textarea class="form-control" 
                                      id="address" 
                                      name="address" 
                                      rows="3"><?php echo htmlspecialchars($_POST['address'] ?? ''); ?></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="profile_picture" class="form-label"><?php echo __('profile_picture'); ?></label>
                            <input type="file" 
                                   class="form-control" 
                                   id="profile_picture" 
                                   name="profile_picture" 
                                   accept="image/*"
                                   onchange="previewFile(this, 'imagePreview')">
                            <div id="imagePreview" class="mt-2"></div>
                        </div>
                    </div>

                    <!-- المعلومات الأكاديمية -->
                    <div class="col-lg-6">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-graduation-cap me-2"></i><?php echo __('academic_information'); ?>
                        </h6>

                        <div class="mb-3">
                            <label for="class_id" class="form-label"><?php echo __('class'); ?></label>
                            <select class="form-select" id="class_id" name="class_id">
                                <option value=""><?php echo __('choose_class'); ?></option>
                                <?php
                                $current_stage = '';
                                while ($class = $classes->fetch_assoc()):
                                    // إضافة عنوان المرحلة إذا تغيرت
                                    if ($current_stage != $class['stage_name'] && !empty($class['stage_name'])) {
                                        if ($current_stage != '') echo '</optgroup>';
                                        echo '<optgroup label="' . htmlspecialchars($class['stage_name']) . '">';
                                        $current_stage = $class['stage_name'];
                                    }

                                    // تحديد النص المعروض للفصل
                                    $display_text = '';

                                    // استخدام grade_name إذا كان متوفراً، وإلا استخدم grade_level
                                    if (!empty($class['grade_name'])) {
                                        $display_text = $class['grade_name'];
                                    } elseif (!empty($class['grade_level'])) {
                                        $display_text = $class['grade_level'];
                                    } else {
                                        $display_text = $class['class_name'];
                                    }

                                    // إضافة الشعبة إذا كانت متوفرة
                                    if (!empty($class['section'])) {
                                        $display_text .= ' - ' . $class['section'];
                                    }

                                    // إذا لم تكن هناك شعبة، أضف اسم الفصل إذا كان مختلفاً
                                    if (empty($class['section']) && !empty($class['class_name']) && $class['class_name'] != $display_text) {
                                        $display_text .= ' (' . $class['class_name'] . ')';
                                    }
                                ?>
                                    <option value="<?php echo $class['id']; ?>"
                                            <?php echo (($_POST['class_id'] ?? '') == $class['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($display_text); ?>
                                    </option>
                                <?php endwhile; ?>
                                <?php if ($current_stage != '') echo '</optgroup>'; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="enrollment_date" class="form-label"><?php echo __('enrollment_date'); ?></label>
                            <input type="date" 
                                   class="form-control" 
                                   id="enrollment_date" 
                                   name="enrollment_date" 
                                   value="<?php echo htmlspecialchars($_POST['enrollment_date'] ?? date('Y-m-d')); ?>">
                        </div>

                        <h6 class="text-primary mb-3 mt-4">
                            <i class="fas fa-users me-2"></i><?php echo __('parent_information'); ?>
                        </h6>

                        <div class="mb-3">
                            <label for="parent_name" class="form-label"><?php echo __('parent_name'); ?></label>
                            <input type="text" 
                                   class="form-control" 
                                   id="parent_name" 
                                   name="parent_name" 
                                   value="<?php echo htmlspecialchars($_POST['parent_name'] ?? ''); ?>">
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="parent_phone" class="form-label"><?php echo __('parent_phone'); ?></label>
                                    <input type="tel" 
                                           class="form-control" 
                                           id="parent_phone" 
                                           name="parent_phone" 
                                           value="<?php echo htmlspecialchars($_POST['parent_phone'] ?? ''); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="parent_email" class="form-label"><?php echo __('parent_email'); ?></label>
                                    <input type="email" 
                                           class="form-control" 
                                           id="parent_email" 
                                           name="parent_email" 
                                           value="<?php echo htmlspecialchars($_POST['parent_email'] ?? ''); ?>">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="parent_national_id" class="form-label"><?php echo __('parent_national_id'); ?></label>
                            <input type="text" 
                                   class="form-control" 
                                   id="parent_national_id" 
                                   name="parent_national_id" 
                                   value="<?php echo htmlspecialchars($_POST['parent_national_id'] ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="emergency_contact_name" class="form-label"><?php echo __('emergency_contact_name'); ?></label>
                            <input type="text" 
                                   class="form-control" 
                                   id="emergency_contact_name" 
                                   name="emergency_contact_name" 
                                   value="<?php echo htmlspecialchars($_POST['emergency_contact_name'] ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="emergency_contact_phone" class="form-label"><?php echo __('emergency_contact_phone'); ?></label>
                            <input type="tel" 
                                   class="form-control" 
                                   id="emergency_contact_phone" 
                                   name="emergency_contact_phone" 
                                   value="<?php echo htmlspecialchars($_POST['emergency_contact_phone'] ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="medical_conditions" class="form-label"><?php echo __('medical_conditions'); ?></label>
                            <textarea class="form-control" 
                                      id="medical_conditions" 
                                      name="medical_conditions" 
                                      rows="4"
                                      placeholder="<?php echo __('any_medical_conditions'); ?>"><?php echo htmlspecialchars($_POST['medical_conditions'] ?? ''); ?></textarea>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i><?php echo __('add_student'); ?>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Toggle password visibility
    function togglePassword(fieldId) {
        const field = document.getElementById(fieldId);
        const button = field.nextElementSibling;
        const icon = button.querySelector('i');
        
        if (field.type === 'password') {
            field.type = 'text';
            icon.className = 'fas fa-eye-slash';
        } else {
            field.type = 'password';
            icon.className = 'fas fa-eye';
        }
    }

    // Password confirmation validation
    document.getElementById('confirm_password').addEventListener('input', function() {
        const password = document.getElementById('password').value;
        const confirmPassword = this.value;
        
        if (password !== confirmPassword) {
            this.setCustomValidity('<?php echo __('passwords_not_match'); ?>');
        } else {
            this.setCustomValidity('');
        }
    });

    // Generate username from full name
    document.getElementById('full_name').addEventListener('input', function() {
        const fullName = this.value;
        const username = fullName.toLowerCase()
            .replace(/\s+/g, '')
            .replace(/[^a-z0-9]/g, '')
            .substring(0, 20);
        
        if (username && !document.getElementById('username').value) {
            document.getElementById('username').value = username;
        }
    });

    // Auto-generate student number
    document.getElementById('student_id').addEventListener('blur', function() {
        if (!this.value) {
            this.value = '<?php echo $suggested_student_number; ?>';
        }
    });
</script>

<?php require_once '../includes/footer.php'; ?>
