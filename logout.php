<?php
/**
 * صفحة تسجيل الخروج
 * Logout Page
 */

define('SYSTEM_INIT', true);
require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/functions.php';
require_once 'includes/security.php';

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// تسجيل النشاط قبل تسجيل الخروج
if (is_logged_in()) {
    log_activity($_SESSION['user_id'], 'logout');
}

// حذف رمز التذكر إذا كان موجوداً
if (isset($_COOKIE['remember_token']) && is_logged_in()) {
    global $conn;
    $stmt = $conn->prepare("UPDATE users SET remember_token = NULL WHERE id = ?");
    $stmt->bind_param("i", $_SESSION['user_id']);
    $stmt->execute();
    
    // حذف الكوكي
    setcookie('remember_token', '', time() - 3600, '/');
}

// تدمير الجلسة
session_destroy();

// إعادة التوجيه إلى صفحة تسجيل الدخول
header('Location: login.php?logged_out=1');
exit();
?>
