<?php
/**
 * صفحة تعديل إعداد الخصم
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    redirect_to_login();
}

$user_id = $_SESSION['user_id'];
$error_message = '';
$success_message = '';

// التحقق من وجود معرف الإعداد
$setting_id = intval($_GET['id'] ?? 0);
if (!$setting_id) {
    header('Location: deduction_settings.php');
    exit;
}

// جلب بيانات الإعداد
$stmt = $conn->prepare("SELECT * FROM deduction_settings WHERE id = ?");
$stmt->bind_param("i", $setting_id);
$stmt->execute();
$setting = $stmt->get_result()->fetch_assoc();

if (!$setting) {
    header('Location: deduction_settings.php');
    exit;
}

// معالجة تحديث الإعداد
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'طلب غير صالح';
    } else {
        try {
            $deduction_value = floatval($_POST['deduction_value']);
            $deduction_type = clean_input($_POST['deduction_type']);
            $max_allowed = !empty($_POST['max_allowed_per_month']) ? intval($_POST['max_allowed_per_month']) : null;
            $requires_approval = isset($_POST['requires_approval']) ? 1 : 0;
            $description = clean_input($_POST['description']);
            $is_active = isset($_POST['is_active']) ? 1 : 0;

            $stmt = $conn->prepare("
                UPDATE deduction_settings
                SET deduction_value = ?, deduction_type = ?, max_allowed_per_month = ?,
                    requires_approval = ?, description = ?, is_active = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->bind_param("dsisiii", $deduction_value, $deduction_type, $max_allowed, 
                            $requires_approval, $description, $is_active, $setting_id);
            
            if ($stmt->execute()) {
                $success_message = 'تم تحديث إعداد الخصم بنجاح';
                
                // تسجيل النشاط
                log_activity($user_id, 'update_deduction_setting', 'deduction_settings', $setting_id);
                
                // تحديث البيانات المعروضة
                $stmt = $conn->prepare("SELECT * FROM deduction_settings WHERE id = ?");
                $stmt->bind_param("i", $setting_id);
                $stmt->execute();
                $setting = $stmt->get_result()->fetch_assoc();
                
                // إعادة توجيه بعد 2 ثانية
                header("refresh:2;url=deduction_settings.php");
            } else {
                $error_message = 'حدث خطأ أثناء تحديث الإعداد';
            }
            
        } catch (Exception $e) {
            $error_message = 'حدث خطأ: ' . $e->getMessage();
        }
    }
}

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-edit me-2"></i>
                            تعديل إعداد الخصم
                        </h5>
                        <a href="deduction_settings.php" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left me-2"></i>رجوع للإعدادات
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if ($success_message): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                            <br><small>سيتم توجيهك لصفحة الإعدادات خلال ثانيتين...</small>
                        </div>
                    <?php endif; ?>

                    <?php if ($error_message): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                        </div>
                    <?php endif; ?>

                    <!-- معلومات الإعداد الحالي -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>معلومات الإعداد:</h6>
                        <p class="mb-1"><strong>نوع المخالفة:</strong> <?php echo htmlspecialchars($setting['absence_type']); ?></p>
                        <p class="mb-0"><strong>الفئة المستهدفة:</strong> 
                            <?php
                            switch($setting['staff_role']) {
                                case 'teacher': echo 'المعلمين'; break;
                                case 'staff': echo 'الإداريين'; break;
                                default: echo 'جميع الموظفين';
                            }
                            ?>
                        </p>
                    </div>

                    <form method="POST" class="needs-validation" novalidate>
                        <?php echo csrf_token_field(); ?>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">نوع الخصم <span class="text-danger">*</span></label>
                                <select class="form-select" name="deduction_type" id="deduction_type" required>
                                    <option value="fixed" <?php echo $setting['deduction_type'] === 'fixed' ? 'selected' : ''; ?>>مبلغ ثابت</option>
                                    <option value="percentage" <?php echo $setting['deduction_type'] === 'percentage' ? 'selected' : ''; ?>>نسبة مئوية</option>
                                    <option value="daily_rate" <?php echo $setting['deduction_type'] === 'daily_rate' ? 'selected' : ''; ?>>معدل يومي</option>
                                    <option value="hourly_rate" <?php echo $setting['deduction_type'] === 'hourly_rate' ? 'selected' : ''; ?>>معدل ساعي</option>
                                </select>
                                <div class="invalid-feedback">
                                    يرجى اختيار نوع الخصم
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">قيمة الخصم <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" class="form-control" name="deduction_value" 
                                           step="0.01" min="0" required value="<?php echo $setting['deduction_value']; ?>">
                                    <span class="input-group-text" id="currency-symbol">
                                        <?php echo $setting['deduction_type'] === 'percentage' ? '%' : 'ج.م'; ?>
                                    </span>
                                </div>
                                <div class="invalid-feedback">
                                    يرجى إدخال قيمة الخصم
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الحد الأقصى شهرياً</label>
                                <input type="number" class="form-control" name="max_allowed_per_month" 
                                       min="1" placeholder="اتركه فارغاً لعدم التحديد" 
                                       value="<?php echo $setting['max_allowed_per_month']; ?>">
                                <small class="form-text text-muted">عدد المرات المسموح بها شهرياً لتطبيق هذا الخصم</small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="mt-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="requires_approval" id="requires_approval" 
                                               <?php echo $setting['requires_approval'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="requires_approval">
                                            يتطلب موافقة مسبقة
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="is_active" id="is_active" 
                                               <?php echo $setting['is_active'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="is_active">
                                            نشط
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <label class="form-label">الوصف</label>
                                <textarea class="form-control" name="description" rows="3" 
                                          placeholder="وصف تفصيلي للمخالفة وشروط تطبيق الخصم"><?php echo htmlspecialchars($setting['description']); ?></textarea>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="deduction_settings.php" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ التعديلات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تغيير رمز العملة حسب نوع الخصم
    const deductionTypeSelect = document.getElementById('deduction_type');
    const currencySymbol = document.getElementById('currency-symbol');
    
    function updateCurrencySymbol() {
        const value = deductionTypeSelect.value;
        currencySymbol.textContent = value === 'percentage' ? '%' : 'ج.م';
    }
    
    deductionTypeSelect.addEventListener('change', updateCurrencySymbol);

    // التحقق من صحة النموذج
    const form = document.querySelector('.needs-validation');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
});
</script>

<?php require_once '../includes/footer.php'; ?>
