<?php
/**
 * صفحة تعديل المعلم
 * Edit Teacher Page
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    header('Location: ../dashboard/');
    exit();
}

$teacher_id = intval($_GET['id'] ?? 0);
$page_title = __('edit_teacher');
$error_message = '';
$success_message = '';

if (empty($teacher_id)) {
    $_SESSION['error_message'] = __('invalid_teacher_id');
    header('Location: index.php');
    exit();
}

// جلب بيانات المعلم
global $conn;
$teacher_stmt = $conn->prepare("
    SELECT
        t.*,
        u.username,
        u.email,
        u.status as user_status,
        COALESCE(u.full_name, u.username) as full_name
    FROM teachers t
    JOIN users u ON t.user_id = u.id
    WHERE t.id = ?
");
$teacher_stmt->bind_param("i", $teacher_id);
$teacher_stmt->execute();
$teacher = $teacher_stmt->get_result()->fetch_assoc();

if (!$teacher) {
    $_SESSION['error_message'] = __('teacher_not_found');
    header('Location: index.php');
    exit();
}

// معالجة تحديث البيانات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = __('invalid_request');
    } else {
        // جمع البيانات
        $email = clean_input($_POST['email'] ?? '');
        $phone = clean_input($_POST['phone'] ?? '');
        $employee_id = clean_input($_POST['employee_id'] ?? '');
        $hire_date = clean_input($_POST['hire_date'] ?? '');
        $department = clean_input($_POST['department'] ?? '');
        $specialization = clean_input($_POST['specialization'] ?? '');
        $salary = floatval($_POST['salary'] ?? 0);
        $notes = clean_input($_POST['notes'] ?? '');
        $status = clean_input($_POST['status'] ?? 'active');

        // التحقق من صحة البيانات
        $errors = [];

        // تم إزالة التحقق من full_name لأنه غير موجود في قاعدة البيانات

        if (empty($email)) {
            $errors[] = __('email') . ' ' . __('required_field');
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = __('invalid_email_format');
        }

        if (empty($employee_id)) {
            $errors[] = __('employee_id') . ' ' . __('required_field');
        }

        if (!empty($date_of_birth) && !validate_date($date_of_birth)) {
            $errors[] = __('invalid_date_of_birth');
        }

        if (!empty($hire_date) && !validate_date($hire_date)) {
            $errors[] = __('invalid_hire_date');
        }

        if (!empty($phone) && !validate_phone($phone)) {
            $errors['phone'] = __('invalid_phone');
        }

        // التحقق من عدم تكرار البريد الإلكتروني
        if (!empty($email)) {
            $email_check = $conn->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
            $email_check->bind_param("si", $email, $teacher['user_id']);
            $email_check->execute();
            if ($email_check->get_result()->num_rows > 0) {
                $errors[] = __('email_already_exists');
            }
        }

        // التحقق من عدم تكرار رقم الموظف
        if (!empty($employee_id)) {
            $employee_check = $conn->prepare("SELECT id FROM teachers WHERE employee_id = ? AND id != ?");
            $employee_check->bind_param("si", $employee_id, $teacher_id);
            $employee_check->execute();
            if ($employee_check->get_result()->num_rows > 0) {
                $errors[] = __('employee_id_already_exists');
            }
        }

        if (empty($errors)) {
            $conn->begin_transaction();

            try {
                // تحديث بيانات المستخدم
                $update_user_stmt = $conn->prepare("
                    UPDATE users SET
                        email = ?, status = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $update_user_stmt->bind_param("ssi",
                    $email, $status, $teacher['user_id']
                );
                $update_user_stmt->execute();

                // تحديث بيانات المعلم
                $update_teacher_stmt = $conn->prepare("
                    UPDATE teachers SET
                        employee_id = ?, phone = ?, hire_date = ?, department = ?,
                        specialization = ?, salary = ?, notes = ?, status = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $update_teacher_stmt->bind_param("sssssdsi",
                    $employee_id, $phone, $hire_date, $department,
                    $specialization, $salary, $notes, $status, $teacher_id
                );
                $update_teacher_stmt->execute();

                $conn->commit();

                // تسجيل النشاط
                log_activity($_SESSION['user_id'], 'edit_teacher', 'teachers', $teacher_id, 
                    ['old_name' => $teacher['full_name']], 
                    ['new_name' => $full_name, 'department' => $department]
                );

                // إرسال إشعار للمعلم
                add_notification($teacher['user_id'], __('profile_updated'), 
                    __('your_profile_has_been_updated'), 'info', 'teachers/view.php?id=' . $teacher_id);

                $_SESSION['success_message'] = __('teacher_updated_successfully');
                header('Location: view.php?id=' . $teacher_id);
                exit();

            } catch (Exception $e) {
                $conn->rollback();
                log_error("Error updating teacher: " . $e->getMessage());
                $error_message = __('error_occurred');
            }
        } else {
            $error_message = implode('<br>', $errors);
        }
    }
}

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('edit_teacher'); ?></h1>
            <p class="text-muted"><?php echo __('update_teacher_information'); ?></p>
        </div>
        <div>
            <a href="view.php?id=<?php echo $teacher_id; ?>" class="btn btn-outline-info">
                <i class="fas fa-eye me-2"></i><?php echo __('view_profile'); ?>
            </a>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
            </a>
        </div>
    </div>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Edit Form -->
    <form method="POST" class="needs-validation" novalidate>
        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">

        <div class="row">
            <!-- Personal Information -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-user me-2"></i><?php echo __('personal_information'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- تم إزالة حقل full_name لأنه غير موجود في قاعدة البيانات -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="employee_id" class="form-label"><?php echo __('employee_id'); ?> <span class="text-danger">*</span></label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="employee_id" 
                                           name="employee_id" 
                                           value="<?php echo htmlspecialchars($teacher['employee_id']); ?>"
                                           required>
                                    <div class="invalid-feedback">
                                        <?php echo __('required_field'); ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">
                                        <i class="fas fa-envelope me-1"></i><?php echo __('email'); ?> <span class="text-danger">*</span>
                                    </label>
                                    <input type="email" 
                                           class="form-control" 
                                           id="email" 
                                           name="email" 
                                           value="<?php echo htmlspecialchars($teacher['email']); ?>"
                                           required>
                                    <div class="invalid-feedback">
                                        <?php echo __('required_field'); ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">
                                        <i class="fas fa-phone me-1"></i><?php echo __('phone'); ?>
                                    </label>
                                    <input type="tel"
                                           class="form-control"
                                           id="phone"
                                           name="phone"
                                           value="<?php echo htmlspecialchars($teacher['phone'] ?? ''); ?>"
                                           pattern="[0-9+\-\s\(\)]+"
                                           placeholder="<?php echo __('phone_placeholder'); ?>"
                                           title="<?php echo __('phone_format_help'); ?>">
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        <?php echo __('phone_format_example'); ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تم إزالة حقول date_of_birth وgender وnational_id لأنها غير موجودة في قاعدة البيانات -->

                        <!-- تم إزالة حقل address لأنه غير موجود في قاعدة البيانات -->
                    </div>
                </div>

                <!-- Professional Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-briefcase me-2"></i><?php echo __('professional_information'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="hire_date" class="form-label"><?php echo __('hire_date'); ?></label>
                                    <input type="date" 
                                           class="form-control" 
                                           id="hire_date" 
                                           name="hire_date" 
                                           value="<?php echo htmlspecialchars($teacher['hire_date']); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="department" class="form-label"><?php echo __('department'); ?></label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="department" 
                                           name="department" 
                                           value="<?php echo htmlspecialchars($teacher['department']); ?>">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="qualification" class="form-label"><?php echo __('qualification'); ?></label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="qualification" 
                                           name="qualification" 
                                           value="<?php echo htmlspecialchars($teacher['qualification']); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="specialization" class="form-label"><?php echo __('specialization'); ?></label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="specialization" 
                                           name="specialization" 
                                           value="<?php echo htmlspecialchars($teacher['specialization']); ?>">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="experience_years" class="form-label"><?php echo __('experience_years'); ?></label>
                                    <input type="number" 
                                           class="form-control" 
                                           id="experience_years" 
                                           name="experience_years" 
                                           value="<?php echo intval($teacher['experience_years']); ?>"
                                           min="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="salary" class="form-label"><?php echo __('salary'); ?></label>
                                    <div class="input-group">
                                        <input type="number" 
                                               class="form-control" 
                                               id="salary" 
                                               name="salary" 
                                               value="<?php echo floatval($teacher['salary']); ?>"
                                               step="0.01"
                                               min="0">
                                        <span class="input-group-text"><?php echo get_system_setting('currency_symbol', 'ر.س'); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label"><?php echo __('status'); ?></label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="active" <?php echo ($teacher['user_status'] === 'active') ? 'selected' : ''; ?>>
                                            <?php echo __('active'); ?>
                                        </option>
                                        <option value="inactive" <?php echo ($teacher['user_status'] === 'inactive') ? 'selected' : ''; ?>>
                                            <?php echo __('inactive'); ?>
                                        </option>
                                        <option value="suspended" <?php echo ($teacher['user_status'] === 'suspended') ? 'selected' : ''; ?>>
                                            <?php echo __('suspended'); ?>
                                        </option>
                                        <option value="retired" <?php echo ($teacher['user_status'] === 'retired') ? 'selected' : ''; ?>>
                                            <?php echo __('retired'); ?>
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="col-lg-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i><?php echo __('additional_information'); ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="notes" class="form-label">
                                <i class="fas fa-sticky-note me-1"></i><?php echo __('notes'); ?>
                            </label>
                            <textarea class="form-control" 
                                      id="notes" 
                                      name="notes" 
                                      rows="4"
                                      placeholder="<?php echo __('notes_placeholder'); ?>"><?php echo htmlspecialchars($teacher['notes'] ?? ''); ?></textarea>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                <?php echo __('notes_help_text'); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-end gap-2">
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i><?php echo __('update_teacher'); ?>
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
    // Form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if (form.checkValidity() === false) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        }, false);
    })();

    // تحسين حقل الهاتف
    document.addEventListener('DOMContentLoaded', function() {
        const phoneInput = document.getElementById('phone');

        if (phoneInput) {
            // تنسيق الهاتف أثناء الكتابة
            phoneInput.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, ''); // إزالة كل شيء عدا الأرقام

                // تنسيق الرقم السعودي
                if (value.startsWith('966')) {
                    value = '+966 ' + value.substring(3);
                } else if (value.startsWith('05')) {
                    value = value.replace(/(\d{3})(\d{3})(\d{4})/, '$1 $2 $3');
                } else if (value.length >= 10) {
                    value = value.replace(/(\d{3})(\d{3})(\d{4})/, '$1 $2 $3');
                }

                e.target.value = value;
            });

            // التحقق من صحة الهاتف
            phoneInput.addEventListener('blur', function(e) {
                const value = e.target.value.replace(/\D/g, '');
                const isValid = /^(05|9665)\d{8}$/.test(value) || /^966\d{9}$/.test(value);

                if (value && !isValid) {
                    e.target.classList.add('is-invalid');
                    e.target.classList.remove('is-valid');
                } else if (value) {
                    e.target.classList.add('is-valid');
                    e.target.classList.remove('is-invalid');
                } else {
                    e.target.classList.remove('is-invalid', 'is-valid');
                }
            });
        }
    });
</script>

<?php require_once '../includes/footer.php'; ?>
