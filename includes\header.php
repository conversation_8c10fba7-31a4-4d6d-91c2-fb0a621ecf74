<?php
/**
 * ملف الهيدر الأساسي
 * Main Header File
 */

// بدء output buffering لتجنب مشاكل headers
if (!ob_get_level()) {
    ob_start();
}

// منع الوصول المباشر
if (!defined('SYSTEM_INIT')) {
    define('SYSTEM_INIT', true);
}

// تحميل الثوابت أولاً
require_once __DIR__ . '/../config/constants.php';

// تحميل الملفات الأساسية
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/functions.php';

// تطبيق إعدادات النظام بعد تحميل قاعدة البيانات
if (function_exists('apply_system_settings')) {
    apply_system_settings();
}

// التحقق من الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// الحصول على معلومات المستخدم الحالي
$current_user = null;
if (is_logged_in()) {
    $current_user = get_user_by_id($_SESSION['user_id']);
}

// الحصول على اللغة الحالية
$current_language = get_current_language();
// $_SESSION['system_language'] = $current_language; // تم التعليق حتى لا يعيد تعيين اللغة

// تحميل ملف الترجمة
load_language();

// الحصول على المظهر الحالي
$current_theme = $_SESSION['user_theme'] ?? get_system_setting('theme', 'light');

// الحصول على عنوان الصفحة
$page_title = $page_title ?? __('dashboard');
$system_name = get_system_setting('school_name', __('system_name'));

// الحصول على الإشعارات غير المقروءة
$unread_notifications = 0;
if (is_logged_in()) {
    $unread_notifications = count_unread_notifications($_SESSION['user_id']);
}
?>
<!DOCTYPE html>
<html lang="<?php echo $current_language; ?>" dir="<?php echo $current_language === 'ar' ? 'rtl' : 'ltr'; ?>" data-theme="<?php echo $current_theme; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<?php echo $system_name; ?> - نظام إدارة مدارس متكامل">
    <meta name="keywords" content="مدرسة, إدارة, طلاب, معلمين, امتحانات, درجات">
    <meta name="author" content="School Management System">
    
    <title><?php echo $page_title . ' - ' . $system_name; ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo SYSTEM_URL; ?>/assets/images/favicon.ico">
    
    <!-- CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="<?php echo SYSTEM_URL; ?>/assets/css/style.css" rel="stylesheet">
    <?php if ($current_language === 'ar'): ?>
    <link href="<?php echo SYSTEM_URL; ?>/assets/css/rtl.css" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Theme CSS -->
    <link href="<?php echo SYSTEM_URL; ?>/assets/css/themes/<?php echo $current_theme; ?>.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --border-radius: 10px;
            --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }
        
        [data-theme="dark"] {
            --primary-color: #4c63d2;
            --secondary-color: #6c5ce7;
            --light-color: #2c3e50;
            --dark-color: #ecf0f1;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            transition: var(--transition);
        }
        
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: var(--box-shadow);
        }
        
        [data-theme="dark"] .navbar {
            background: rgba(44, 62, 80, 0.95) !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            transition: var(--transition);
        }
        
        [data-theme="dark"] .sidebar {
            background: rgba(44, 62, 80, 0.95);
        }
        
        .main-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            min-height: calc(100vh - 100px);
            transition: var(--transition);
        }
        
        [data-theme="dark"] .main-content {
            background: rgba(44, 62, 80, 0.95);
            color: var(--dark-color);
        }
        
        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            transition: var(--transition);
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }
        
        .btn {
            border-radius: var(--border-radius);
            transition: var(--transition);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: var(--danger-color);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .language-switcher, .theme-switcher {
            cursor: pointer;
            padding: 8px 12px;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }
        
        .language-switcher:hover, .theme-switcher:hover {
            background: rgba(0, 0, 0, 0.1);
        }
        
        .loading-spinner {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 9999;
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .sidebar-nav .nav-link {
            color: var(--dark-color);
            padding: 12px 20px;
            margin: 5px 0;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }
        
        .sidebar-nav .nav-link:hover,
        .sidebar-nav .nav-link.active {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            transform: translateX(5px);
        }
        
        [data-theme="dark"] .sidebar-nav .nav-link {
            color: var(--dark-color);
        }
        
        .stats-card {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: var(--border-radius);
            padding: 20px;
            margin: 10px 0;
            transition: var(--transition);
        }
        
        .stats-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }
        
        .table {
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
        }
        
        .table th {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
        }
        
        .form-control, .form-select {
            border-radius: var(--border-radius);
            border: 2px solid #e9ecef;
            transition: var(--transition);
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .modal-content {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .modal-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }
        
        .alert {
            border-radius: var(--border-radius);
            border: none;
        }
        
        .progress {
            border-radius: var(--border-radius);
            height: 10px;
        }
        
        .progress-bar {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 0;
                left: -250px;
                width: 250px;
                height: 100vh;
                z-index: 1000;
                transition: left 0.3s ease;
            }
            
            .sidebar.show {
                left: 0;
            }
            
            .main-content {
                margin-left: 0;
            }
        }
        
        /* Print styles */
        @media print {
            .navbar, .sidebar, .no-print {
                display: none !important;
            }
            
            .main-content {
                margin: 0 !important;
                box-shadow: none !important;
                background: white !important;
            }
        }
        /* إصلاح z-index للمودال وطبقة الخلفية */
        .modal-backdrop {
            z-index: 2050 !important;
        }
        .modal {
            z-index: 2100 !important;
        }
    </style>
</head>
<body>
    <!-- Loading Spinner -->
    <div class="loading-spinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden"><?php echo __('loading'); ?></span>
        </div>
    </div>

    <?php if (is_logged_in()): ?>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container-fluid">
            <!-- Mobile Menu Toggle -->
            <button class="btn btn-outline-primary d-lg-none me-2" type="button" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
            
            <!-- Brand -->
            <a class="navbar-brand fw-bold" href="<?php echo SYSTEM_URL; ?>/dashboard/">
                <i class="fas fa-graduation-cap text-primary me-2"></i>
                <?php echo $system_name; ?>
            </a>
            
            <!-- Right Side Items -->
            <div class="d-flex align-items-center">
                <!-- Language Switcher -->
                <div class="dropdown me-3">
                    <button class="language-switcher border-0 bg-transparent" data-bs-toggle="dropdown">
                        <i class="fas fa-globe me-1"></i>
                        <?php echo $current_language === 'ar' ? 'العربية' : 'English'; ?>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="changeLanguage('ar')">العربية</a></li>
                        <li><a class="dropdown-item" href="#" onclick="changeLanguage('en')">English</a></li>
                    </ul>
                </div>
                
                <!-- Theme Switcher -->
                <button class="theme-switcher border-0 bg-transparent me-3" onclick="toggleTheme()">
                    <i class="fas fa-moon" id="themeIcon"></i>
                </button>
                
                <!-- Notifications -->
                <div class="dropdown me-3">
                    <button class="btn btn-outline-primary position-relative" data-bs-toggle="dropdown">
                        <i class="fas fa-bell"></i>
                        <?php if ($unread_notifications > 0): ?>
                        <span class="notification-badge"><?php echo $unread_notifications; ?></span>
                        <?php endif; ?>
                    </button>
                    <div class="dropdown-menu dropdown-menu-end" style="width: 300px;">
                        <h6 class="dropdown-header"><?php echo __('notifications'); ?></h6>
                        <div id="notificationsList">
                            <!-- Notifications will be loaded here -->
                        </div>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item text-center" href="<?php echo SYSTEM_URL; ?>/notifications/">
                            <?php echo __('view_all_notifications'); ?>
                        </a>
                    </div>
                </div>
                
                <!-- User Menu -->
                <div class="dropdown">
                    <button class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>
                        <?php echo $current_user && isset($current_user['full_name']) ? htmlspecialchars($current_user['full_name']) : __('user'); ?>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/profile/">
                            <i class="fas fa-user me-2"></i><?php echo __('profile'); ?>
                        </a></li>
                        <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/settings/">
                            <i class="fas fa-cog me-2"></i><?php echo __('settings'); ?>
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="<?php echo SYSTEM_URL; ?>/logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i><?php echo __('logout'); ?>
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="container-fluid" style="margin-top: 80px;">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-2 p-0">
                <div class="sidebar p-3" id="sidebar">
                    <nav class="sidebar-nav">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'index.php' && strpos($_SERVER['REQUEST_URI'], '/dashboard/') !== false ? 'active' : ''; ?>" 
                                   href="<?php echo SYSTEM_URL; ?>/dashboard/">
                                    <i class="fas fa-tachometer-alt me-2"></i><?php echo __('dashboard'); ?>
                                </a>
                            </li>
                            
                            <?php if (check_permission('admin') || has_permission('staff_access') || has_permission('students_view') || has_permission('student_affairs')): ?>
                            <li class="nav-item">
                                <a class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/students/') !== false ? 'active' : ''; ?>"
                                   href="<?php echo SYSTEM_URL; ?>/students/">
                                    <i class="fas fa-user-graduate me-2"></i><?php echo has_permission('student_affairs') ? 'شئون الطلاب' : __('students'); ?>
                                </a>
                            </li>
                            <?php endif; ?>
                            
                            <?php if (check_permission('admin') || has_permission('staff_access') || has_permission('teachers_view') || has_permission('staff_affairs')): ?>
                            <li class="nav-item">
                                <a class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/teachers/') !== false ? 'active' : ''; ?>"
                                   href="<?php echo SYSTEM_URL; ?>/teachers/">
                                    <i class="fas fa-chalkboard-teacher me-2"></i><?php echo has_permission('staff_affairs') ? 'شئون العاملين' : __('teachers'); ?>
                                </a>
                            </li>
                            <?php endif; ?>

                            <?php if (check_permission('admin')): ?>
                            <li class="nav-item">
                                <a class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/administrators/') !== false ? 'active' : ''; ?>"
                                   href="<?php echo SYSTEM_URL; ?>/administrators/">
                                    <i class="fas fa-user-tie me-2"></i><?php echo __('administrators'); ?>
                                </a>
                            </li>
                            <?php endif; ?>
                            
                            <?php if (check_permission('admin')): ?>
                            <!-- 1. المراحل الدراسية -->
                            <li class="nav-item">
                                <a class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/stages/') !== false ? 'active' : ''; ?>"
                                   href="<?php echo SYSTEM_URL; ?>/stages/">
                                    <i class="fas fa-layer-group me-2"></i><?php echo __('educational_stages'); ?>
                                </a>
                            </li>
                            <!-- 2. الصفوف الدراسية -->
                            <li class="nav-item">
                                <a class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/school_grades/') !== false ? 'active' : ''; ?>"
                                   href="<?php echo SYSTEM_URL; ?>/school_grades/">
                                    <i class="fas fa-graduation-cap me-2"></i><?php echo __('school_grades'); ?>
                                </a>
                            </li>
                            <?php endif; ?>

                            <!-- 3. الفصول -->
                            <?php if (check_permission('admin') || check_permission('teacher') || check_permission('staff') || has_permission('classes_view')): ?>
                            <li class="nav-item">
                                <a class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/classes/') !== false ? 'active' : ''; ?>"
                                   href="<?php echo SYSTEM_URL; ?>/classes/">
                                    <i class="fas fa-school me-2"></i><?php echo __('classes'); ?>
                                </a>
                            </li>
                            <?php endif; ?>

                            <!-- 4. المواد -->
                            <?php if (check_permission('admin') || check_permission('teacher') || check_permission('staff') || has_permission('subjects_view')): ?>
                            <li class="nav-item">
                                <a class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/subjects/') !== false ? 'active' : ''; ?>"
                                   href="<?php echo SYSTEM_URL; ?>/subjects/">
                                    <i class="fas fa-book me-2"></i><?php echo __('subjects'); ?>
                                </a>
                            </li>
                            <?php endif; ?>

                            <!-- 5. الامتحانات -->
                            <?php if (check_permission('admin') || check_permission('teacher') || has_permission('exams_view')): ?>
                            <li class="nav-item">
                                <a class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/exams/') !== false ? 'active' : ''; ?>"
                                   href="<?php echo SYSTEM_URL; ?>/exams/">
                                    <i class="fas fa-file-alt me-2"></i><?php echo __('exams'); ?>
                                </a>
                            </li>
                            <?php endif; ?>

                            <!-- قسم المالية -->
                            <?php if (check_permission('admin')): ?>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle <?php echo strpos($_SERVER['REQUEST_URI'], '/finance/') !== false ? 'active' : ''; ?>"
                                   href="#" id="financeDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-money-bill-wave me-2"></i><?php echo __('finance'); ?>
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="financeDropdown">
                                    <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/finance/">
                                        <i class="fas fa-chart-pie me-2"></i><?php echo __('finance_overview'); ?>
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/finance/fee_types/">
                                        <i class="fas fa-tags me-2 text-primary"></i>إدارة أنواع الرسوم
                                    </a></li>
                                    <li class="dropdown-submenu">
                                        <a class="dropdown-item dropdown-toggle" href="#">
                                            <i class="fas fa-money-bill-wave me-2 text-warning"></i>المصروفات اليومية
                                            <span class="badge bg-warning ms-2">جديد</span>
                                        </a>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/finance/expenses/">
                                                <i class="fas fa-list me-2"></i>قائمة المصروفات
                                            </a></li>
                                            <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/finance/expenses/add.php">
                                                <i class="fas fa-plus me-2"></i>إضافة مصروف جديد
                                            </a></li>
                                            <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/finance/expenses/categories.php">
                                                <i class="fas fa-tags me-2"></i>إدارة الفئات
                                            </a></li>
                                            <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/finance/expenses/daily_summary.php">
                                                <i class="fas fa-calendar-day me-2"></i>الملخص اليومي
                                            </a></li>
                                        </ul>
                                    </li>
                                    <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/finance/payments/detailed_list.php">
                                        <i class="fas fa-list-alt me-2 text-info"></i>قائمة المدفوعات
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/finance/installments/">
                                        <i class="fas fa-calendar-alt me-2"></i><?php echo __('installments'); ?>
                                    </a></li>
                                    <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/finance/books/">
                                        <i class="fas fa-book me-2"></i><?php echo __('books_management'); ?>
                                    </a></li>
                                </ul>
                            </li>
                            <?php endif; ?>


                            <?php if (check_permission('admin') || check_permission('teacher')): ?>
                            <li class="nav-item">
                                <a class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/attendance/') !== false ? 'active' : ''; ?>"
                                   href="<?php echo SYSTEM_URL; ?>/attendance/">
                                    <i class="fas fa-calendar-check me-2"></i><?php echo __('attendance'); ?>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/leaves/') !== false ? 'active' : ''; ?>"
                                   href="<?php echo SYSTEM_URL; ?>/leaves/">
                                    <i class="fas fa-calendar-alt me-2"></i><?php echo __('staff_leaves'); ?>
                                </a>
                            </li>
                            <?php endif; ?>
                            
                            <!-- قسم التقارير -->
                            <?php if (check_permission('admin') || check_permission('teacher') || has_permission('reports_view') || has_permission('student_reports') || has_permission('attendance_reports') || has_permission('exam_reports')): ?>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle <?php echo strpos($_SERVER['REQUEST_URI'], '/reports/') !== false ? 'active' : ''; ?>"
                                   href="#" id="reportsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-chart-bar me-2"></i><?php echo __('reports'); ?>
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="reportsDropdown">
                                    <?php if (check_permission('admin') || has_permission('reports_view')): ?>
                                    <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/reports/">
                                        <i class="fas fa-chart-line me-2"></i><?php echo __('general_reports'); ?>
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <?php endif; ?>

                                    <?php if (check_permission('admin') || check_permission('teacher') || check_permission('staff') || has_permission('student_reports')): ?>
                                    <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/reports/students.php">
                                        <i class="fas fa-user-graduate me-2"></i><?php echo __('student_reports'); ?>
                                    </a></li>
                                    <?php endif; ?>

                                    <?php if (check_permission('admin') || check_permission('teacher') || check_permission('staff') || has_permission('attendance_reports')): ?>
                                    <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/reports/attendance.php">
                                        <i class="fas fa-calendar-check me-2"></i><?php echo __('attendance_reports'); ?>
                                    </a></li>
                                    <?php endif; ?>

                                    <?php if (check_permission('staff') || has_permission('staff_reports')): ?>
                                    <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/reports/staff.php">
                                        <i class="fas fa-users me-2"></i>تقارير العاملين
                                    </a></li>
                                    <?php endif; ?>

                                    <?php if (check_permission('admin') || check_permission('teacher') || has_permission('exam_reports')): ?>
                                    <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/reports/exams.php">
                                        <i class="fas fa-file-alt me-2"></i><?php echo __('exam_reports'); ?>
                                    </a></li>
                                    <?php endif; ?>

                                    <?php if (check_permission('admin') || has_permission('financial_reports')): ?>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/reports/financial.php">
                                        <i class="fas fa-money-bill-wave me-2"></i><?php echo __('financial_reports'); ?>
                                    </a></li>
                                    <?php endif; ?>
                                </ul>
                            </li>
                            <?php endif; ?>

                            <!-- قسم إدارة التواصل -->
                            <?php if (check_permission('admin') || check_permission('teacher')): ?>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle <?php echo strpos($_SERVER['REQUEST_URI'], '/parent_communication/') !== false ? 'active' : ''; ?>"
                                   href="#" id="communicationDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fab fa-whatsapp me-2"></i><?php echo __('parent_communication'); ?>
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="communicationDropdown">
                                    <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/parent_communication/">
                                        <i class="fas fa-tachometer-alt me-2"></i><?php echo __('communication_dashboard'); ?>
                                    </a></li>
                                    <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/parent_communication/whatsapp_interface.php">
                                        <i class="fab fa-whatsapp me-2"></i><?php echo __('whatsapp_interface'); ?>
                                    </a></li>
                                    <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/parent_communication/student_reports.php">
                                        <i class="fas fa-clipboard-list me-2"></i><?php echo __('behavior_reports'); ?>
                                    </a></li>
                                    <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/parent_communication/bulk_message.php">
                                        <i class="fas fa-broadcast-tower me-2"></i><?php echo __('bulk_message'); ?>
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/parent_communication/send_message.php">
                                        <i class="fas fa-paper-plane me-2"></i><?php echo __('send_message'); ?>
                                    </a></li>
                                    <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/parent_communication/bulk_message.php">
                                        <i class="fas fa-broadcast-tower me-2"></i><?php echo __('bulk_message'); ?>
                                    </a></li>
                                    <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/parent_communication/student_reports.php">
                                        <i class="fas fa-clipboard-list me-2"></i><?php echo __('behavior_reports'); ?>
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/parent_communication/message_templates.php">
                                        <i class="fas fa-file-alt me-2"></i><?php echo __('message_templates'); ?>
                                    </a></li>
                                    <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/parent_communication/communication_reports.php">
                                        <i class="fas fa-chart-line me-2"></i><?php echo __('communication_reports'); ?>
                                    </a></li>
                                </ul>
                            </li>
                            <?php endif; ?>

                            <?php if (check_permission('admin')): ?>
                            <li class="nav-item">
                                <a class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/users/') !== false ? 'active' : ''; ?>"
                                   href="<?php echo SYSTEM_URL; ?>/users/">
                                    <i class="fas fa-users me-2"></i><?php echo __('users'); ?>
                                </a>
                            </li>
                            
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle <?php echo strpos($_SERVER['REQUEST_URI'], '/admin/permissions') !== false || strpos($_SERVER['REQUEST_URI'], '/admin/enhanced_permissions') !== false || strpos($_SERVER['REQUEST_URI'], '/admin/roles') !== false ? 'active' : ''; ?>"
                                   href="#" id="permissionsDropdown" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-shield-alt me-2"></i>إدارة الصلاحيات
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/admin/enhanced_permissions_manager.php">
                                        <i class="fas fa-shield-alt me-2"></i>النظام المحسن
                                    </a></li>
                                    <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/admin/roles_manager.php">
                                        <i class="fas fa-user-tag me-2"></i>إدارة الأدوار
                                    </a></li>
                                    <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/admin/enhanced_permissions_audit.php">
                                        <i class="fas fa-history me-2"></i>سجل التدقيق
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/admin/apply_enhanced_permissions.php">
                                        <i class="fas fa-rocket me-2"></i>تطبيق النظام المحسن
                                    </a></li>
                                    <li><a class="dropdown-item" href="<?php echo SYSTEM_URL; ?>/admin/permissions_manager.php">
                                        <i class="fas fa-cog me-2"></i>النظام القديم
                                    </a></li>
                                </ul>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/settings/') !== false ? 'active' : ''; ?>"
                                   href="<?php echo SYSTEM_URL; ?>/settings/">
                                    <i class="fas fa-cog me-2"></i><?php echo __('settings'); ?>
                                </a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-lg-10">
                <div class="main-content p-4 fade-in">
    <?php endif; ?>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <script>
        // Global JavaScript functions
        
        // Theme switcher
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            document.documentElement.setAttribute('data-theme', newTheme);
            document.getElementById('themeIcon').className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            
            // Save theme preference
            fetch('<?php echo SYSTEM_URL; ?>/api/save_preference.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    key: 'theme',
                    value: newTheme
                })
            });
        }
        
        // Language switcher
        function changeLanguage(lang) {
            // تحديث الجلسة مباشرة عبر AJAX
            fetch('<?php echo SYSTEM_URL; ?>/api/set_language.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    language: lang
                })
            }).then(() => {
                location.reload();
            });
        }
        
        // Sidebar toggle for mobile
        document.getElementById('sidebarToggle')?.addEventListener('click', function() {
            document.getElementById('sidebar').classList.toggle('show');
        });
        
        // Initialize theme icon
        document.addEventListener('DOMContentLoaded', function() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            document.getElementById('themeIcon').className = currentTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            
            // Load notifications
            loadNotifications();
        });
        
        // Load notifications
        function loadNotifications() {
            fetch('<?php echo SYSTEM_URL; ?>/api/get_notifications.php')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('notificationsList');
                    if (data.length === 0) {
                        container.innerHTML = '<div class="dropdown-item text-muted"><?php echo __('no_notifications'); ?></div>';
                    } else {
                        container.innerHTML = data.map(notification => `
                            <a class="dropdown-item ${notification.is_read ? '' : 'fw-bold'}" href="#" onclick="markAsRead(${notification.id})">
                                <div class="d-flex justify-content-between">
                                    <span>${notification.title}</span>
                                    <small class="text-muted">${notification.created_at}</small>
                                </div>
                                <small class="text-muted">${notification.message}</small>
                            </a>
                        `).join('');
                    }
                });
        }
        
        // Mark notification as read
        function markAsRead(notificationId) {
            fetch('<?php echo SYSTEM_URL; ?>/api/mark_notification_read.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    notification_id: notificationId
                })
            }).then(() => {
                loadNotifications();
            });
        }
        
        // Show loading spinner
        function showLoading() {
            document.querySelector('.loading-spinner').style.display = 'block';
        }
        
        // Hide loading spinner
        function hideLoading() {
            document.querySelector('.loading-spinner').style.display = 'none';
        }
        
        // Success message
        function showSuccess(message) {
            Swal.fire({
                icon: 'success',
                title: '<?php echo __('success'); ?>',
                text: message,
                timer: 3000,
                showConfirmButton: false
            });
        }
        
        // Error message
        function showError(message) {
            Swal.fire({
                icon: 'error',
                title: '<?php echo __('error'); ?>',
                text: message
            });
        }
        
        // Confirmation dialog
        function confirmAction(message, callback) {
            Swal.fire({
                title: '<?php echo __('are_you_sure'); ?>',
                text: message,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: '<?php echo __('confirm'); ?>',
                cancelButtonText: '<?php echo __('cancel'); ?>'
            }).then((result) => {
                if (result.isConfirmed) {
                    callback();
                }
            });
        }

        // تفعيل القوائم الفرعية
        document.addEventListener('DOMContentLoaded', function() {
            // للشاشات الكبيرة - عرض عند hover
            const dropdownSubmenus = document.querySelectorAll('.dropdown-submenu');

            dropdownSubmenus.forEach(function(submenu) {
                const dropdownToggle = submenu.querySelector('.dropdown-toggle');
                const dropdownMenu = submenu.querySelector('.dropdown-menu');

                // للشاشات الصغيرة - عرض عند النقر
                if (window.innerWidth <= 767) {
                    dropdownToggle.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        // إخفاء القوائم الأخرى
                        dropdownSubmenus.forEach(function(otherSubmenu) {
                            if (otherSubmenu !== submenu) {
                                otherSubmenu.querySelector('.dropdown-menu').style.display = 'none';
                            }
                        });

                        // تبديل عرض القائمة الحالية
                        if (dropdownMenu.style.display === 'block') {
                            dropdownMenu.style.display = 'none';
                        } else {
                            dropdownMenu.style.display = 'block';
                        }
                    });
                }
            });
        });
    </script>

    <!-- CSS للقوائم الفرعية -->
    <style>
    .dropdown-submenu {
        position: relative;
    }

    .dropdown-submenu .dropdown-menu {
        top: 0;
        left: 100%;
        margin-top: -1px;
        border-radius: 0 6px 6px 6px;
    }

    .dropdown-submenu:hover .dropdown-menu {
        display: block;
    }

    .dropdown-submenu .dropdown-toggle::after {
        transform: rotate(-90deg);
        position: absolute;
        right: 6px;
        top: 50%;
        margin-top: -3px;
    }

    /* تحسين للشاشات الصغيرة */
    @media (max-width: 767px) {
        .dropdown-submenu .dropdown-menu {
            position: static;
            float: none;
            width: auto;
            margin-top: 0;
            background-color: rgba(0,0,0,.125);
            border: 0;
            border-radius: 0;
            box-shadow: none;
        }

        .dropdown-submenu .dropdown-menu .dropdown-item {
            padding-left: 2rem;
        }
    }
    </style>

<?php
// إنهاء output buffering وإرسال المحتوى
if (ob_get_level()) {
    ob_end_flush();
}
?>
