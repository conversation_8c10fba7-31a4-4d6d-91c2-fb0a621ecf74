<?php
/**
 * تصدير التقارير المالية
 * Export Financial Reports
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();

if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    header('Location: ../dashboard/');
    exit();
}

// فلاتر التقرير
$filter_year = clean_input($_GET['year'] ?? date('Y'));
$filter_month = clean_input($_GET['month'] ?? '');
$filter_type = clean_input($_GET['type'] ?? 'overview');
$export_format = clean_input($_GET['export'] ?? 'excel');

// إعداد headers للتحميل
if ($export_format === 'excel') {
    header('Content-Type: application/vnd.ms-excel; charset=utf-8');
    header('Content-Disposition: attachment; filename="financial_report_' . $filter_year . ($filter_month ? '_' . $filter_month : '') . '_' . date('Y-m-d') . '.xls"');
    header('Pragma: no-cache');
    header('Expires: 0');
}

// إحصائيات الرسوم
$fees_query = "
    SELECT 
        COUNT(*) as total_fees,
        SUM(final_amount) as total_amount,
        SUM(CASE WHEN status = 'paid' THEN final_amount ELSE 0 END) as paid_amount,
        SUM(CASE WHEN status = 'pending' THEN final_amount ELSE 0 END) as pending_amount,
        SUM(CASE WHEN status = 'overdue' THEN final_amount ELSE 0 END) as overdue_amount
    FROM student_fees 
    WHERE YEAR(created_at) = ?
    " . (!empty($filter_month) ? " AND MONTH(created_at) = ?" : "");

$fees_stmt = $conn->prepare($fees_query);
if (!empty($filter_month)) {
    $fees_stmt->bind_param("ii", $filter_year, $filter_month);
} else {
    $fees_stmt->bind_param("i", $filter_year);
}
$fees_stmt->execute();
$fees_stats = $fees_stmt->get_result()->fetch_assoc();

// إحصائيات المدفوعات
$payments_query = "
    SELECT 
        COUNT(*) as total_payments,
        SUM(amount) as total_amount,
        COUNT(CASE WHEN payment_method = 'cash' THEN 1 END) as cash_payments,
        SUM(CASE WHEN payment_method = 'cash' THEN amount ELSE 0 END) as cash_amount,
        COUNT(CASE WHEN payment_method = 'bank_transfer' THEN 1 END) as bank_payments,
        SUM(CASE WHEN payment_method = 'bank_transfer' THEN amount ELSE 0 END) as bank_amount
    FROM student_payments 
    WHERE status = 'confirmed'
    AND YEAR(payment_date) = ?
    " . (!empty($filter_month) ? " AND MONTH(payment_date) = ?" : "");

$payments_stmt = $conn->prepare($payments_query);
if (!empty($filter_month)) {
    $payments_stmt->bind_param("ii", $filter_year, $filter_month);
} else {
    $payments_stmt->bind_param("i", $filter_year);
}
$payments_stmt->execute();
$payments_stats = $payments_stmt->get_result()->fetch_assoc();

// التأكد من أن القيم ليست null
$fees_stats = [
    'total_fees' => $fees_stats['total_fees'] ?? 0,
    'total_amount' => $fees_stats['total_amount'] ?? 0,
    'paid_amount' => $fees_stats['paid_amount'] ?? 0,
    'pending_amount' => $fees_stats['pending_amount'] ?? 0,
    'overdue_amount' => $fees_stats['overdue_amount'] ?? 0
];

$payments_stats = [
    'total_payments' => $payments_stats['total_payments'] ?? 0,
    'total_amount' => $payments_stats['total_amount'] ?? 0,
    'cash_payments' => $payments_stats['cash_payments'] ?? 0,
    'cash_amount' => $payments_stats['cash_amount'] ?? 0,
    'bank_payments' => $payments_stats['bank_payments'] ?? 0,
    'bank_amount' => $payments_stats['bank_amount'] ?? 0
];

// حساب معدل التحصيل
$collection_rate = $fees_stats['total_amount'] > 0 ? 
    ($fees_stats['paid_amount'] / $fees_stats['total_amount']) * 100 : 0;

// إنشاء ملف Excel
echo "\xEF\xBB\xBF"; // UTF-8 BOM
?>

<table border="1" style="border-collapse: collapse; width: 100%; font-family: Arial, sans-serif;">
    <tr style="background-color: #f8f9fa; font-weight: bold;">
        <td colspan="4" style="text-align: center; padding: 10px; font-size: 16px;">
            التقرير المالي - <?php echo $filter_year; ?>
            <?php if (!empty($filter_month)): ?>
                - الشهر <?php echo $filter_month; ?>
            <?php endif; ?>
        </td>
    </tr>
    <tr style="background-color: #e9ecef;">
        <td colspan="4" style="text-align: center; padding: 5px;">
            تاريخ التصدير: <?php echo date('Y-m-d H:i:s'); ?>
        </td>
    </tr>
    
    <!-- إحصائيات الرسوم -->
    <tr style="background-color: #007bff; color: white; font-weight: bold;">
        <td colspan="4" style="text-align: center; padding: 8px;">إحصائيات الرسوم</td>
    </tr>
    <tr>
        <td style="padding: 5px; font-weight: bold;">إجمالي الرسوم</td>
        <td style="padding: 5px;"><?php echo number_format($fees_stats['total_fees']); ?></td>
        <td style="padding: 5px; font-weight: bold;">إجمالي المبلغ</td>
        <td style="padding: 5px;"><?php echo number_format($fees_stats['total_amount'], 2); ?> ر.س</td>
    </tr>
    <tr>
        <td style="padding: 5px; font-weight: bold;">المبلغ المدفوع</td>
        <td style="padding: 5px; color: green;"><?php echo number_format($fees_stats['paid_amount'], 2); ?> ر.س</td>
        <td style="padding: 5px; font-weight: bold;">المبلغ المعلق</td>
        <td style="padding: 5px; color: orange;"><?php echo number_format($fees_stats['pending_amount'], 2); ?> ر.س</td>
    </tr>
    <tr>
        <td style="padding: 5px; font-weight: bold;">المبلغ المتأخر</td>
        <td style="padding: 5px; color: red;"><?php echo number_format($fees_stats['overdue_amount'], 2); ?> ر.س</td>
        <td style="padding: 5px; font-weight: bold;">معدل التحصيل</td>
        <td style="padding: 5px; color: blue;"><?php echo number_format($collection_rate, 1); ?>%</td>
    </tr>
    
    <!-- إحصائيات المدفوعات -->
    <tr style="background-color: #28a745; color: white; font-weight: bold;">
        <td colspan="4" style="text-align: center; padding: 8px;">إحصائيات المدفوعات</td>
    </tr>
    <tr>
        <td style="padding: 5px; font-weight: bold;">إجمالي المدفوعات</td>
        <td style="padding: 5px;"><?php echo number_format($payments_stats['total_payments']); ?></td>
        <td style="padding: 5px; font-weight: bold;">إجمالي المبلغ</td>
        <td style="padding: 5px;"><?php echo number_format($payments_stats['total_amount'], 2); ?> ر.س</td>
    </tr>
    <tr>
        <td style="padding: 5px; font-weight: bold;">المدفوعات النقدية</td>
        <td style="padding: 5px;"><?php echo number_format($payments_stats['cash_payments']); ?></td>
        <td style="padding: 5px; font-weight: bold;">مبلغ النقدي</td>
        <td style="padding: 5px;"><?php echo number_format($payments_stats['cash_amount'], 2); ?> ر.س</td>
    </tr>
    <tr>
        <td style="padding: 5px; font-weight: bold;">التحويلات البنكية</td>
        <td style="padding: 5px;"><?php echo number_format($payments_stats['bank_payments']); ?></td>
        <td style="padding: 5px; font-weight: bold;">مبلغ التحويلات</td>
        <td style="padding: 5px;"><?php echo number_format($payments_stats['bank_amount'], 2); ?> ر.س</td>
    </tr>
</table>

<?php if ($filter_type === 'detailed'): ?>
<br><br>
<table border="1" style="border-collapse: collapse; width: 100%; font-family: Arial, sans-serif;">
    <tr style="background-color: #6c757d; color: white; font-weight: bold;">
        <td colspan="8" style="text-align: center; padding: 8px;">تفاصيل الرسوم</td>
    </tr>
    <tr style="background-color: #f8f9fa; font-weight: bold;">
        <td style="padding: 5px;">الطالب</td>
        <td style="padding: 5px;">نوع الرسم</td>
        <td style="padding: 5px;">المبلغ الأساسي</td>
        <td style="padding: 5px;">الخصم</td>
        <td style="padding: 5px;">المبلغ النهائي</td>
        <td style="padding: 5px;">الحالة</td>
        <td style="padding: 5px;">تاريخ الاستحقاق</td>
        <td style="padding: 5px;">تاريخ الإنشاء</td>
    </tr>
    
    <?php
    // جلب تفاصيل الرسوم
    $detailed_query = "
        SELECT 
            sf.*,
            u.full_name as student_name,
            ft.type_name as fee_type_name
        FROM student_fees sf
        JOIN students s ON sf.student_id = s.id
        JOIN users u ON s.user_id = u.id
        LEFT JOIN fee_types ft ON sf.fee_type_id = ft.id
        WHERE YEAR(sf.created_at) = ?
        " . (!empty($filter_month) ? " AND MONTH(sf.created_at) = ?" : "") . "
        ORDER BY sf.created_at DESC
        LIMIT 1000
    ";
    
    $detailed_stmt = $conn->prepare($detailed_query);
    if (!empty($filter_month)) {
        $detailed_stmt->bind_param("ii", $filter_year, $filter_month);
    } else {
        $detailed_stmt->bind_param("i", $filter_year);
    }
    $detailed_stmt->execute();
    $detailed_result = $detailed_stmt->get_result();
    
    while ($fee = $detailed_result->fetch_assoc()):
    ?>
    <tr>
        <td style="padding: 3px;"><?php echo htmlspecialchars($fee['student_name']); ?></td>
        <td style="padding: 3px;"><?php echo htmlspecialchars($fee['fee_type_name'] ?? 'رسم عام'); ?></td>
        <td style="padding: 3px;"><?php echo number_format($fee['base_amount'], 2); ?></td>
        <td style="padding: 3px;"><?php echo number_format($fee['discount_amount'], 2); ?></td>
        <td style="padding: 3px;"><?php echo number_format($fee['final_amount'], 2); ?></td>
        <td style="padding: 3px;"><?php echo $fee['status']; ?></td>
        <td style="padding: 3px;"><?php echo $fee['due_date']; ?></td>
        <td style="padding: 3px;"><?php echo date('Y-m-d', strtotime($fee['created_at'])); ?></td>
    </tr>
    <?php endwhile; ?>
</table>
<?php endif; ?>
