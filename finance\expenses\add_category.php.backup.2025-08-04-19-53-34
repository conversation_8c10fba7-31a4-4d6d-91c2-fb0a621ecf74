<?php
/**
 * إضافة فئة مصروفات جديدة
 * Add New Expense Category
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

$success_message = '';
$error_message = '';

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $category_name = clean_input($_POST['category_name'] ?? '');
    $description = clean_input($_POST['description'] ?? '');
    $icon = clean_input($_POST['icon'] ?? 'fas fa-money-bill');
    $color = clean_input($_POST['color'] ?? '#007bff');
    $daily_limit = floatval($_POST['daily_limit'] ?? 0);
    $monthly_limit = floatval($_POST['monthly_limit'] ?? 0);
    
    if (empty($category_name)) {
        $error_message = 'يرجى إدخال اسم الفئة';
    } else {
        $insert_stmt = $conn->prepare("
            INSERT INTO expense_categories 
            (category_name, description, icon, color, daily_limit, monthly_limit, created_by) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        $user_id = $_SESSION['user_id'];
        $insert_stmt->bind_param("ssssddi", 
            $category_name, $description, $icon, $color, 
            $daily_limit, $monthly_limit, $user_id
        );
        
        if ($insert_stmt->execute()) {
            $success_message = 'تم إضافة الفئة بنجاح';
            // إعادة تعيين النموذج
            $_POST = [];
        } else {
            $error_message = 'فشل في إضافة الفئة';
        }
    }
}

$page_title = 'إضافة فئة جديدة';
include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-plus me-2"></i>إضافة فئة مصروفات جديدة
            </h1>
            <p class="text-muted mb-0">إنشاء فئة جديدة لتصنيف المصروفات</p>
        </div>
        <div>
            <a href="categories.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة للفئات
            </a>
        </div>
    </div>

    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-8">
            <!-- نموذج إضافة الفئة -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-edit me-2"></i>بيانات الفئة الجديدة
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">اسم الفئة <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="category_name" 
                                   value="<?php echo htmlspecialchars($_POST['category_name'] ?? ''); ?>" 
                                   required placeholder="مثال: مصروفات إدارية">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">الوصف</label>
                            <textarea class="form-control" name="description" rows="3" 
                                      placeholder="وصف مختصر للفئة..."><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الأيقونة</label>
                                <select class="form-select" name="icon">
                                    <option value="fas fa-money-bill" <?php echo (($_POST['icon'] ?? 'fas fa-money-bill') == 'fas fa-money-bill') ? 'selected' : ''; ?>>💵 نقود</option>
                                    <option value="fas fa-tools" <?php echo (($_POST['icon'] ?? '') == 'fas fa-tools') ? 'selected' : ''; ?>>🔧 أدوات</option>
                                    <option value="fas fa-lightbulb" <?php echo (($_POST['icon'] ?? '') == 'fas fa-lightbulb') ? 'selected' : ''; ?>>💡 كهرباء</option>
                                    <option value="fas fa-broom" <?php echo (($_POST['icon'] ?? '') == 'fas fa-broom') ? 'selected' : ''; ?>>🧹 نظافة</option>
                                    <option value="fas fa-graduation-cap" <?php echo (($_POST['icon'] ?? '') == 'fas fa-graduation-cap') ? 'selected' : ''; ?>>🎓 تعليمية</option>
                                    <option value="fas fa-clipboard-list" <?php echo (($_POST['icon'] ?? '') == 'fas fa-clipboard-list') ? 'selected' : ''; ?>>📋 إدارية</option>
                                    <option value="fas fa-bus" <?php echo (($_POST['icon'] ?? '') == 'fas fa-bus') ? 'selected' : ''; ?>>🚌 نقل</option>
                                    <option value="fas fa-coffee" <?php echo (($_POST['icon'] ?? '') == 'fas fa-coffee') ? 'selected' : ''; ?>>☕ ضيافة</option>
                                    <option value="fas fa-heartbeat" <?php echo (($_POST['icon'] ?? '') == 'fas fa-heartbeat') ? 'selected' : ''; ?>>❤️ طبية</option>
                                    <option value="fas fa-shield-alt" <?php echo (($_POST['icon'] ?? '') == 'fas fa-shield-alt') ? 'selected' : ''; ?>>🛡️ أمن</option>
                                    <option value="fas fa-wrench" <?php echo (($_POST['icon'] ?? '') == 'fas fa-wrench') ? 'selected' : ''; ?>>🔧 صيانة</option>
                                    <option value="fas fa-car" <?php echo (($_POST['icon'] ?? '') == 'fas fa-car') ? 'selected' : ''; ?>>🚗 مواصلات</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اللون</label>
                                <input type="color" class="form-control form-control-color" name="color" 
                                       value="<?php echo $_POST['color'] ?? '#007bff'; ?>">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الحد اليومي (<?php echo get_currency_symbol(); ?>)</label>
                                <input type="number" class="form-control" name="daily_limit" step="0.01" min="0"
                                       value="<?php echo $_POST['daily_limit'] ?? ''; ?>" 
                                       placeholder="0.00">
                                <div class="form-text">اتركه فارغاً إذا لم تريد حد أقصى</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الحد الشهري (<?php echo get_currency_symbol(); ?>)</label>
                                <input type="number" class="form-control" name="monthly_limit" step="0.01" min="0"
                                       value="<?php echo $_POST['monthly_limit'] ?? ''; ?>" 
                                       placeholder="0.00">
                                <div class="form-text">اتركه فارغاً إذا لم تريد حد أقصى</div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="categories.php" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ الفئة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- معلومات مساعدة -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>معلومات مساعدة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6>نصائح مهمة:</h6>
                        <ul class="mb-0 small">
                            <li>اختر اسماً واضحاً ومميزاً للفئة</li>
                            <li>الوصف يساعد في فهم نوع المصروفات</li>
                            <li>اختر أيقونة ولون مناسبين للتمييز</li>
                            <li>الحدود اليومية والشهرية اختيارية</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6>أمثلة على الفئات:</h6>
                        <ul class="mb-0 small">
                            <li><strong>مصروفات إدارية:</strong> قرطاسية، أوراق</li>
                            <li><strong>مصروفات تشغيلية:</strong> كهرباء، مياه</li>
                            <li><strong>مصروفات صيانة:</strong> إصلاحات، قطع غيار</li>
                            <li><strong>مصروفات نظافة:</strong> مواد تنظيف</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- معاينة الفئة -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-eye me-2"></i>معاينة الفئة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="card border" id="preview-card">
                        <div class="card-header d-flex align-items-center" id="preview-header" style="background-color: #007bff; color: white;">
                            <i class="fas fa-money-bill me-2" id="preview-icon"></i>
                            <h6 class="mb-0" id="preview-name">اسم الفئة</h6>
                        </div>
                        <div class="card-body">
                            <p class="card-text text-muted small" id="preview-description">وصف الفئة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once '../../includes/footer.php'; ?>

<script>
// معاينة مباشرة للفئة
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.querySelector('input[name="category_name"]');
    const descriptionInput = document.querySelector('textarea[name="description"]');
    const iconSelect = document.querySelector('select[name="icon"]');
    const colorInput = document.querySelector('input[name="color"]');
    
    const previewName = document.getElementById('preview-name');
    const previewDescription = document.getElementById('preview-description');
    const previewIcon = document.getElementById('preview-icon');
    const previewHeader = document.getElementById('preview-header');
    
    function updatePreview() {
        previewName.textContent = nameInput.value || 'اسم الفئة';
        previewDescription.textContent = descriptionInput.value || 'وصف الفئة';
        previewIcon.className = iconSelect.value + ' me-2';
        previewHeader.style.backgroundColor = colorInput.value;
    }
    
    nameInput.addEventListener('input', updatePreview);
    descriptionInput.addEventListener('input', updatePreview);
    iconSelect.addEventListener('change', updatePreview);
    colorInput.addEventListener('change', updatePreview);
    
    // تحديث أولي
    updatePreview();
});
</script>

<style>
.card {
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

#preview-card {
    transition: all 0.3s ease;
}

.alert {
    border: none;
    border-radius: 10px;
}
</style>
