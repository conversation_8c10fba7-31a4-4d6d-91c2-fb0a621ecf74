<?php
session_start();
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
if (!is_logged_in()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مسموح بالوصول']);
    exit;
}

// التحقق من وجود معرف الطالب
if (!isset($_GET['student_id']) || empty($_GET['student_id'])) {
    echo json_encode(['success' => false, 'message' => 'معرف الطالب مطلوب']);
    exit;
}

$student_id = intval($_GET['student_id']);

try {
    // جلب معلومات الطالب والصف الدراسي المرتبط به
    $student_stmt = $conn->prepare("
        SELECT 
            s.id as student_id,
            s.student_id as student_number,
            u.full_name as student_name,
            c.id as class_id,
            c.class_name,
            c.grade_id,
            g.id as grade_id,
            g.grade_name,
            g.grade_code,
            es.stage_name
        FROM students s
        JOIN users u ON s.user_id = u.id
        LEFT JOIN classes c ON s.class_id = c.id
        LEFT JOIN grades g ON c.grade_id = g.id
        LEFT JOIN educational_stages es ON g.stage_id = es.id
        WHERE s.id = ? AND s.status = 'active'
    ");
    
    $student_stmt->bind_param("i", $student_id);
    $student_stmt->execute();
    $student_result = $student_stmt->get_result();
    
    if ($student_result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'الطالب غير موجود أو غير نشط']);
        exit;
    }
    
    $student = $student_result->fetch_assoc();
    
    // التحقق من وجود صف دراسي للطالب
    if (empty($student['grade_id'])) {
        echo json_encode([
            'success' => false, 
            'message' => 'الطالب غير مسجل في صف دراسي محدد',
            'student_info' => [
                'name' => $student['student_name'],
                'number' => $student['student_number'],
                'class_name' => $student['class_name'] ?? 'غير محدد'
            ]
        ]);
        exit;
    }
    
    // جلب الرسوم المرتبطة بالصف الدراسي للطالب
    $fees_stmt = $conn->prepare("
        SELECT 
            ftg.fee_type_id,
            ftg.amount,
            ft.type_name,
            ft.description,
            ft.default_amount,
            ftg.is_active
        FROM fee_type_grades ftg
        JOIN fee_types ft ON ftg.fee_type_id = ft.id
        WHERE ftg.grade_id = ? 
        AND ftg.is_active = 1 
        AND ft.status = 'active'
        ORDER BY ft.type_name
    ");
    
    $fees_stmt->bind_param("i", $student['grade_id']);
    $fees_stmt->execute();
    $fees_result = $fees_stmt->get_result();
    
    $fees = [];
    while ($fee = $fees_result->fetch_assoc()) {
        $fees[] = [
            'fee_type_id' => $fee['fee_type_id'],
            'type_name' => $fee['type_name'],
            'description' => $fee['description'],
            'amount' => $fee['amount'],
            'default_amount' => $fee['default_amount']
        ];
    }
    
    // إرجاع النتيجة
    echo json_encode([
        'success' => true,
        'student_info' => [
            'id' => $student['student_id'],
            'name' => $student['student_name'],
            'number' => $student['student_number'],
            'class_name' => $student['class_name'],
            'grade_name' => $student['grade_name'],
            'grade_code' => $student['grade_code'],
            'stage_name' => $student['stage_name']
        ],
        'fees' => $fees,
        'fees_count' => count($fees)
    ]);
    
} catch (Exception $e) {
    error_log("Error in get_student_grade_fees.php: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'حدث خطأ في جلب بيانات الرسوم: ' . $e->getMessage()
    ]);
}
?>
