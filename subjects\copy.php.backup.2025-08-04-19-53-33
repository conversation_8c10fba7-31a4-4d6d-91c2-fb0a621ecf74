<?php
/**
 * نسخ مادة دراسية
 * Copy Subject
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

// التحقق من وجود معرف المادة
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error_message'] = 'معرف المادة غير صحيح';
    header('Location: index.php');
    exit();
}

$subject_id = intval($_GET['id']);

// جلب بيانات المادة الأصلية
$subject_query = "SELECT * FROM subjects WHERE id = ?";
$subject_stmt = $conn->prepare($subject_query);
$subject_stmt->bind_param("i", $subject_id);
$subject_stmt->execute();
$subject_result = $subject_stmt->get_result();

if ($subject_result->num_rows === 0) {
    $_SESSION['error_message'] = 'المادة غير موجودة';
    header('Location: index.php');
    exit();
}

$original_subject = $subject_result->fetch_assoc();

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'])) {
        $error_message = 'رمز الأمان غير صحيح';
    } else {
        $subject_name = clean_input($_POST['subject_name']);
        $subject_code = clean_input($_POST['subject_code']);
        $department = clean_input($_POST['department']);
        $description = clean_input($_POST['description']);
        $credit_hours = intval($_POST['credit_hours']);
        $status = clean_input($_POST['status']);
        
        $errors = [];
        
        // التحقق من البيانات
        if (empty($subject_name)) {
            $errors[] = 'اسم المادة مطلوب';
        }
        
        if (empty($subject_code)) {
            $errors[] = 'كود المادة مطلوب';
        } else {
            // التحقق من عدم تكرار الكود
            $check_query = "SELECT id FROM subjects WHERE subject_code = ? AND id != ?";
            $check_stmt = $conn->prepare($check_query);
            $check_stmt->bind_param("si", $subject_code, $subject_id);
            $check_stmt->execute();
            if ($check_stmt->get_result()->num_rows > 0) {
                $errors[] = 'كود المادة موجود مسبقاً';
            }
        }
        
        if ($credit_hours < 1) {
            $errors[] = 'عدد الساعات المعتمدة يجب أن يكون أكبر من صفر';
        }
        
        if (!in_array($status, ['active', 'inactive'])) {
            $errors[] = 'حالة المادة غير صحيحة';
        }
        
        if (empty($errors)) {
            try {
                $conn->begin_transaction();
                
                // إدراج المادة الجديدة
                $insert_query = "
                    INSERT INTO subjects (
                        subject_name, subject_code, department, description, 
                        credit_hours, stage_id, grade_id, status, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
                ";
                
                $insert_stmt = $conn->prepare($insert_query);
                $insert_stmt->bind_param(
                    "ssssiiis",
                    $subject_name, $subject_code, $department, $description,
                    $credit_hours, $original_subject['stage_id'], 
                    $original_subject['grade_id'], $status
                );
                
                if ($insert_stmt->execute()) {
                    $new_subject_id = $conn->insert_id;
                    $conn->commit();
                    
                    // تسجيل النشاط
                    log_activity($_SESSION['user_id'], 'copy_subject', 'subjects', $new_subject_id, [
                        'original_id' => $subject_id,
                        'subject_name' => $subject_name,
                        'subject_code' => $subject_code
                    ]);
                    
                    $_SESSION['success_message'] = 'تم نسخ المادة بنجاح';
                    header('Location: view.php?id=' . $new_subject_id);
                    exit();
                } else {
                    throw new Exception('فشل في نسخ المادة');
                }
                
            } catch (Exception $e) {
                $conn->rollback();
                $error_message = 'حدث خطأ: ' . $e->getMessage();
                log_error("Error copying subject: " . $e->getMessage());
            }
        } else {
            $error_message = implode('<br>', $errors);
        }
    }
} else {
    // تعيين القيم الافتراضية من المادة الأصلية
    $subject_name = $original_subject['subject_name'] . ' (نسخة)';
    $subject_code = $original_subject['subject_code'] . '_COPY';
    $department = $original_subject['department'];
    $description = $original_subject['description'];
    $credit_hours = $original_subject['credit_hours'];
    $status = $original_subject['status'];
}

$page_title = 'نسخ المادة - ' . $original_subject['subject_name'];
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-copy me-2"></i>نسخ المادة
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="index.php">المواد الدراسية</a></li>
                    <li class="breadcrumb-item active">نسخ المادة</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
            </a>
        </div>
    </div>

    <!-- عرض الرسائل -->
    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- المادة الأصلية -->
        <div class="col-lg-6 mb-4">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-book me-2"></i>المادة الأصلية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label fw-bold">اسم المادة:</label>
                        <p class="mb-0"><?php echo htmlspecialchars($original_subject['subject_name']); ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">كود المادة:</label>
                        <p class="mb-0"><?php echo htmlspecialchars($original_subject['subject_code']); ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">القسم:</label>
                        <p class="mb-0"><?php echo htmlspecialchars($original_subject['department'] ?: 'غير محدد'); ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">الساعات المعتمدة:</label>
                        <p class="mb-0"><?php echo $original_subject['credit_hours']; ?> ساعة</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-bold">الحالة:</label>
                        <p class="mb-0">
                            <?php if ($original_subject['status'] == 'active'): ?>
                                <span class="badge bg-success">نشط</span>
                            <?php else: ?>
                                <span class="badge bg-secondary">غير نشط</span>
                            <?php endif; ?>
                        </p>
                    </div>
                    <?php if (!empty($original_subject['description'])): ?>
                        <div class="mb-3">
                            <label class="form-label fw-bold">الوصف:</label>
                            <p class="mb-0"><?php echo htmlspecialchars($original_subject['description']); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- نموذج النسخ -->
        <div class="col-lg-6">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-plus me-2"></i>المادة الجديدة
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <?php echo csrf_token_field(); ?>
                        
                        <div class="mb-3">
                            <label for="subject_name" class="form-label">اسم المادة <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="subject_name" name="subject_name" 
                                   value="<?php echo htmlspecialchars($subject_name); ?>" required>
                            <div class="invalid-feedback">يرجى إدخال اسم المادة</div>
                        </div>

                        <div class="mb-3">
                            <label for="subject_code" class="form-label">كود المادة <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="subject_code" name="subject_code" 
                                   value="<?php echo htmlspecialchars($subject_code); ?>" required>
                            <div class="invalid-feedback">يرجى إدخال كود المادة</div>
                        </div>

                        <div class="mb-3">
                            <label for="department" class="form-label">القسم</label>
                            <input type="text" class="form-control" id="department" name="department" 
                                   value="<?php echo htmlspecialchars($department); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="credit_hours" class="form-label">الساعات المعتمدة</label>
                            <input type="number" class="form-control" id="credit_hours" name="credit_hours" 
                                   value="<?php echo $credit_hours; ?>" min="1" max="10">
                        </div>

                        <div class="mb-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="active" <?php echo ($status == 'active') ? 'selected' : ''; ?>>نشط</option>
                                <option value="inactive" <?php echo ($status == 'inactive') ? 'selected' : ''; ?>>غير نشط</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">الوصف</label>
                            <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($description); ?></textarea>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-copy me-2"></i>نسخ المادة
                            </button>
                            <a href="index.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?php include_once '../includes/footer.php'; ?>
