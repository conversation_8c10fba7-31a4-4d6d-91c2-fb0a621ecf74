<?php
/**
 * إعداد تلقائي للنظام البسيط للصلاحيات
 * Auto Setup Simple Permissions System
 */

require_once '../includes/config.php';
require_once '../includes/database.php';

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من الصلاحيات (مدير فقط)
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/?error=access_denied');
    exit();
}

$setup_results = [];
$errors = [];

// تشغيل الإعداد التلقائي
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['auto_setup'])) {
    try {
        // الخطوة 1: إنشاء الجداول
        $tables_sql = [
            'user_page_permissions' => "CREATE TABLE IF NOT EXISTS `user_page_permissions` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(10) UNSIGNED NOT NULL,
                `page_name` varchar(100) NOT NULL,
                `can_view` tinyint(1) DEFAULT 0,
                `can_add` tinyint(1) DEFAULT 0,
                `can_edit` tinyint(1) DEFAULT 0,
                `can_delete` tinyint(1) DEFAULT 0,
                `notes` text DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `user_page_unique` (`user_id`, `page_name`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            'available_pages' => "CREATE TABLE IF NOT EXISTS `available_pages` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `page_name` varchar(100) NOT NULL,
                `page_title` varchar(200) NOT NULL,
                `page_path` varchar(255) NOT NULL,
                `category` varchar(50) DEFAULT NULL,
                `description` text DEFAULT NULL,
                `requires_add` tinyint(1) DEFAULT 0,
                `requires_edit` tinyint(1) DEFAULT 0,
                `requires_delete` tinyint(1) DEFAULT 0,
                `is_active` tinyint(1) DEFAULT 1,
                PRIMARY KEY (`id`),
                UNIQUE KEY `page_name_unique` (`page_name`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
            
            'permissions_log' => "CREATE TABLE IF NOT EXISTS `permissions_log` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(10) UNSIGNED NOT NULL,
                `action` varchar(50) NOT NULL,
                `page_name` varchar(100) DEFAULT NULL,
                `details` text DEFAULT NULL,
                `ip_address` varchar(45) DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
        ];
        
        foreach ($tables_sql as $table_name => $sql) {
            if ($conn->query($sql)) {
                $setup_results[] = "✅ تم إنشاء جدول: $table_name";
            } else {
                $errors[] = "❌ خطأ في إنشاء جدول $table_name: " . $conn->error;
            }
        }
        
        // الخطوة 2: إدراج البيانات الأساسية
        $pages_count = $conn->query("SELECT COUNT(*) as count FROM available_pages")->fetch_assoc()['count'];
        
        if ($pages_count == 0) {
            $basic_pages = [
                ['students', 'إدارة الطلاب', '/students/', 'الطلاب', 'عرض وإدارة بيانات الطلاب', 1, 1, 1],
                ['attendance', 'الحضور والغياب', '/attendance/', 'الحضور', 'تسجيل ومتابعة حضور الطلاب', 1, 1, 0],
                ['grades', 'الدرجات', '/grades/', 'الدرجات', 'إدارة درجات الطلاب', 0, 1, 0],
                ['finance', 'الشؤون المالية', '/finance/', 'المالية', 'إدارة الرسوم والمدفوعات', 1, 1, 1],
                ['reports', 'التقارير', '/reports/', 'التقارير', 'عرض وطباعة التقارير', 0, 0, 0],
                ['users', 'إدارة المستخدمين', '/admin/users/', 'الإدارة', 'إدارة حسابات المستخدمين', 1, 1, 1],
                ['settings', 'الإعدادات', '/settings/', 'الإدارة', 'إعدادات النظام', 0, 1, 0],
                ['exams', 'الامتحانات', '/exams/', 'الامتحانات', 'إدارة الامتحانات والنتائج', 1, 1, 1],
                ['library', 'المكتبة', '/library/', 'المكتبة', 'إدارة الكتب والمراجع', 1, 1, 1],
                ['communication', 'التواصل', '/communication/', 'التواصل', 'الرسائل والإشعارات', 1, 0, 1]
            ];
            
            $stmt = $conn->prepare("INSERT INTO available_pages (page_name, page_title, page_path, category, description, requires_add, requires_edit, requires_delete) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
            
            $inserted_count = 0;
            foreach ($basic_pages as $page) {
                $stmt->bind_param("sssssiiii", ...$page);
                if ($stmt->execute()) {
                    $inserted_count++;
                }
            }
            
            $setup_results[] = "✅ تم إدراج $inserted_count صفحة أساسية";
        } else {
            $setup_results[] = "ℹ️ البيانات الأساسية موجودة مسبقاً ($pages_count صفحة)";
        }
        
        // الخطوة 3: إنشاء الفهارس
        $indexes = [
            "CREATE INDEX IF NOT EXISTS idx_user_permissions ON user_page_permissions(user_id, page_name)",
            "CREATE INDEX IF NOT EXISTS idx_page_permissions ON user_page_permissions(page_name, can_view)",
            "CREATE INDEX IF NOT EXISTS idx_log_user ON permissions_log(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_log_action ON permissions_log(action)",
            "CREATE INDEX IF NOT EXISTS idx_log_date ON permissions_log(created_at)"
        ];
        
        foreach ($indexes as $index_sql) {
            $conn->query($index_sql);
        }
        $setup_results[] = "✅ تم إنشاء الفهارس المطلوبة";
        
        // الخطوة 4: إزالة أي قيود على المدير
        $admin_restrictions = $conn->query("
            DELETE upp FROM user_page_permissions upp
            JOIN users u ON upp.user_id = u.id 
            WHERE u.role = 'admin'
        ");
        
        if ($admin_restrictions) {
            $removed_count = $conn->affected_rows;
            if ($removed_count > 0) {
                $setup_results[] = "✅ تم إزالة $removed_count قيد من حسابات المدير";
            } else {
                $setup_results[] = "ℹ️ لا توجد قيود على المدير";
            }
        }
        
        // الخطوة 5: تسجيل الإعداد
        $conn->query("INSERT INTO permissions_log (user_id, action, details, ip_address) VALUES ({$_SESSION['user_id']}, 'system_setup', 'تم إعداد النظام البسيط للصلاحيات', '{$_SERVER['REMOTE_ADDR']}')");
        
        $setup_results[] = "🎉 تم إكمال الإعداد التلقائي بنجاح!";
        
    } catch (Exception $e) {
        $errors[] = "❌ خطأ في الإعداد التلقائي: " . $e->getMessage();
    }
}

// فحص حالة النظام
$system_status = [
    'tables_exist' => true,
    'data_exists' => false,
    'functions_loaded' => false
];

// فحص الجداول
$required_tables = ['user_page_permissions', 'available_pages', 'permissions_log'];
foreach ($required_tables as $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows == 0) {
        $system_status['tables_exist'] = false;
        break;
    }
}

// فحص البيانات
if ($system_status['tables_exist']) {
    $pages_count = $conn->query("SELECT COUNT(*) as count FROM available_pages")->fetch_assoc()['count'];
    $system_status['data_exists'] = $pages_count > 0;
}

// فحص الدوال
$system_status['functions_loaded'] = file_exists('../includes/simple_permissions.php') && 
                                   file_exists('../includes/page_protection.php') && 
                                   file_exists('../includes/admin_protection.php');

$page_title = 'الإعداد التلقائي للنظام البسيط';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <!-- رأس الصفحة -->
        <div class="text-center mb-5">
            <h1><i class="fas fa-magic me-3 text-primary"></i><?php echo $page_title; ?></h1>
            <p class="lead text-muted">إعداد شامل وتلقائي للنظام البسيط للصلاحيات</p>
        </div>

        <!-- نتائج الإعداد -->
        <?php if (!empty($setup_results)): ?>
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle me-2"></i>نتائج الإعداد:</h5>
                <ul class="mb-0">
                    <?php foreach ($setup_results as $result): ?>
                        <li><?php echo $result; ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- أخطاء الإعداد -->
        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-circle me-2"></i>أخطاء الإعداد:</h5>
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo $error; ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- حالة النظام -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-database fa-3x mb-3 text-<?php echo $system_status['tables_exist'] ? 'success' : 'danger'; ?>"></i>
                        <h5>الجداول</h5>
                        <span class="badge bg-<?php echo $system_status['tables_exist'] ? 'success' : 'danger'; ?>">
                            <?php echo $system_status['tables_exist'] ? 'موجودة' : 'غير موجودة'; ?>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-list fa-3x mb-3 text-<?php echo $system_status['data_exists'] ? 'success' : 'warning'; ?>"></i>
                        <h5>البيانات الأساسية</h5>
                        <span class="badge bg-<?php echo $system_status['data_exists'] ? 'success' : 'warning'; ?>">
                            <?php echo $system_status['data_exists'] ? 'موجودة' : 'غير موجودة'; ?>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-code fa-3x mb-3 text-<?php echo $system_status['functions_loaded'] ? 'success' : 'danger'; ?>"></i>
                        <h5>ملفات النظام</h5>
                        <span class="badge bg-<?php echo $system_status['functions_loaded'] ? 'success' : 'danger'; ?>">
                            <?php echo $system_status['functions_loaded'] ? 'موجودة' : 'غير موجودة'; ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- إجراءات الإعداد -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cogs me-2"></i>إجراءات الإعداد</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($system_status['tables_exist'] && $system_status['data_exists'] && $system_status['functions_loaded']): ?>
                            <div class="alert alert-success text-center">
                                <i class="fas fa-check-circle fa-3x mb-3"></i>
                                <h4>النظام جاهز للاستخدام!</h4>
                                <p>تم إعداد جميع مكونات النظام البسيط للصلاحيات بنجاح</p>
                                
                                <div class="d-flex justify-content-center gap-3 mt-4">
                                    <a href="simple_permissions_manager.php" class="btn btn-success btn-lg">
                                        <i class="fas fa-shield-alt me-2"></i>إدارة الصلاحيات
                                    </a>
                                    <a href="test_simple_permissions.php" class="btn btn-primary btn-lg">
                                        <i class="fas fa-vial me-2"></i>اختبار النظام
                                    </a>
                                    <a href="../examples/protected_students_page.php" class="btn btn-info btn-lg">
                                        <i class="fas fa-eye me-2"></i>مثال تطبيقي
                                    </a>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-warning text-center">
                                <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                                <h4>النظام يحتاج إعداد</h4>
                                <p>اضغط الزر أدناه لتشغيل الإعداد التلقائي</p>
                                
                                <form method="POST" class="mt-4">
                                    <input type="hidden" name="auto_setup" value="1">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-magic me-2"></i>تشغيل الإعداد التلقائي
                                    </button>
                                </form>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات الإعداد -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle me-2"></i>ما يتم في الإعداد التلقائي</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>إنشاء الجداول:</h6>
                                <ul>
                                    <li><code>user_page_permissions</code> - صلاحيات المستخدمين</li>
                                    <li><code>available_pages</code> - الصفحات المتاحة</li>
                                    <li><code>permissions_log</code> - سجل العمليات</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>إعداد البيانات:</h6>
                                <ul>
                                    <li>إدراج 10 صفحات أساسية</li>
                                    <li>إنشاء الفهارس المطلوبة</li>
                                    <li>إزالة قيود المدير</li>
                                    <li>تسجيل عملية الإعداد</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-3">
                            <i class="fas fa-shield-alt me-2"></i>
                            <strong>ضمان:</strong> المدير محمي بالكامل ولن يتأثر بأي قيود أو تغييرات
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
