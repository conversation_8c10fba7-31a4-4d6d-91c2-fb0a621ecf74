<?php
/**
 * صفحة إدارة الأقساط
 * Installments Management Page
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// معالجة تغيير اللغة من URL
if (isset($_GET['lang']) && in_array($_GET['lang'], ['ar', 'en'])) {
    $_SESSION['system_language'] = $_GET['lang'];
}

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

// تحديث حالات الأقساط تلقائياً (مرة واحدة كل ساعة)
$last_update_key = 'last_installment_status_update';
$last_update = $_SESSION[$last_update_key] ?? 0;
$current_time = time();

// إذا مر أكثر من ساعة على آخر تحديث، قم بالتحديث
if (($current_time - $last_update) > 3600) { // 3600 ثانية = ساعة واحدة
    try {
        // تحديث الأقساط المتأخرة
        $conn->query("
            UPDATE student_installments
            SET status = 'overdue', updated_at = NOW()
            WHERE due_date < CURDATE()
            AND status = 'pending'
            AND paid_amount < total_amount
        ");

        // تحديث الأقساط المدفوعة
        $conn->query("
            UPDATE student_installments
            SET status = 'paid', updated_at = NOW()
            WHERE paid_amount >= total_amount
            AND total_amount > 0
            AND status != 'paid'
        ");

        // تحديث الأقساط الجزئية
        $conn->query("
            UPDATE student_installments
            SET status = 'partial', updated_at = NOW()
            WHERE paid_amount > 0
            AND paid_amount < total_amount
            AND status NOT IN ('paid', 'partial')
        ");

        // حفظ وقت آخر تحديث
        $_SESSION[$last_update_key] = $current_time;

    } catch (Exception $e) {
        // تسجيل الخطأ دون إيقاف التطبيق
        error_log("Auto status update error: " . $e->getMessage());
    }
}

// معالجة تحديث حالة القسط
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_installment'])) {
    $installment_id = intval($_POST['installment_id']);
    $new_status = clean_input($_POST['status']);
    $paid_amount = floatval($_POST['paid_amount'] ?? 0);
    $payment_date = clean_input($_POST['payment_date'] ?? date('Y-m-d'));

    global $conn;
    $conn->begin_transaction();

    try {
        // تحديث القسط
        $update_stmt = $conn->prepare("
            UPDATE student_installments
            SET status = ?, paid_amount = ?, paid_date = ?, updated_at = NOW()
            WHERE id = ?
        ");
        $update_stmt->bind_param("sdsi", $new_status, $paid_amount, $payment_date, $installment_id);
        $update_stmt->execute();

        // إذا تم دفع القسط، إنشاء سجل دفعة
        if ($new_status === 'paid' && $paid_amount > 0) {
            // جلب بيانات القسط
            $installment_stmt = $conn->prepare("
                SELECT si.*, sf.student_id, sf.id as fee_id
                FROM student_installments si
                JOIN student_fees sf ON si.student_fee_id = sf.id
                WHERE si.id = ?
            ");
            $installment_stmt->bind_param("i", $installment_id);
            $installment_stmt->execute();
            $installment_data = $installment_stmt->get_result()->fetch_assoc();

            if ($installment_data) {
                // إنشاء رقم مرجعي للدفعة
                $payment_reference = 'INST-' . date('Ymd') . '-' . str_pad($installment_id, 6, '0', STR_PAD_LEFT);

                // إدراج سجل الدفعة
                $payment_stmt = $conn->prepare("
                    INSERT INTO student_payments (
                        student_id, student_fee_id, payment_reference, amount, payment_method,
                        payment_date, notes, status, processed_by, processed_at, created_at
                    ) VALUES (?, ?, ?, ?, 'installment', ?, ?, 'confirmed', ?, NOW(), NOW())
                ");
                $notes = __('installment_payment') . ' #' . $installment_data['installment_number'];
                $payment_stmt->bind_param("iisdsssi",
                    $installment_data['student_id'], $installment_data['fee_id'],
                    $payment_reference, $paid_amount, $payment_date, $notes, $_SESSION['user_id']
                );
                $payment_stmt->execute();

                // إرسال إشعار للطالب
                add_notification($installment_data['student_id'], __('installment_paid'),
                    __('installment_payment_received') . ': ' . number_format($paid_amount, 2),
                    'success', 'finance/installments/');
            }
        }

        $conn->commit();

        // تسجيل النشاط
        log_activity($_SESSION['user_id'], 'update_installment', 'student_installments', $installment_id, null, [
            'status' => $new_status,
            'paid_amount' => $paid_amount
        ]);

        $_SESSION['success_message'] = __('installment_updated_successfully');

    } catch (Exception $e) {
        $conn->rollback();
        log_error("Error updating installment: " . $e->getMessage());
        $_SESSION['error_message'] = __('error_occurred');
    }

    header('Location: index.php');
    exit();
}

// معالجة البحث والفلترة
$search = clean_input($_GET['search'] ?? '');
$class_filter = clean_input($_GET['class_id'] ?? '');
$status_filter = clean_input($_GET['status'] ?? '');
$plan_filter = clean_input($_GET['plan_id'] ?? '');
$date_from = clean_input($_GET['date_from'] ?? '');
$date_to = clean_input($_GET['date_to'] ?? '');
$overdue_filter = isset($_GET['overdue_only']);

// بناء استعلام البحث
$where_conditions = ["1=1"];
$params = [];
$types = "";

if (!empty($search)) {
    $where_conditions[] = "(u.full_name LIKE ? OR si.receipt_number LIKE ? OR s.student_id LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param]);
    $types .= "sss";
}

if (!empty($class_filter)) {
    $where_conditions[] = "s.class_id = ?";
    $params[] = $class_filter;
    $types .= "i";
}

if (!empty($status_filter)) {
    $where_conditions[] = "si.status = ?";
    $params[] = $status_filter;
    $types .= "s";
}

if (!empty($plan_filter)) {
    $where_conditions[] = "si.installment_plan_id = ?";
    $params[] = $plan_filter;
    $types .= "i";
}

if ($overdue_filter) {
    $where_conditions[] = "si.due_date < CURDATE() AND si.status = 'pending'";
}

if (!empty($date_from)) {
    $where_conditions[] = "si.due_date >= ?";
    $params[] = $date_from;
    $types .= "s";
}

if (!empty($date_to)) {
    $where_conditions[] = "si.due_date <= ?";
    $params[] = $date_to;
    $types .= "s";
}

$where_clause = implode(" AND ", $where_conditions);

// Base query parts
$base_query_from = "
    FROM `student_installments` `si`
    JOIN `students` `s` ON `si`.`student_id` = `s`.`id`
    JOIN `users` `u` ON `s`.`user_id` = `u`.`id`
    LEFT JOIN `classes` `c` ON `s`.`class_id` = `c`.`id`
    LEFT JOIN `installment_plans` `ip` ON `si`.`installment_plan_id` = `ip`.`id`
    LEFT JOIN `student_fees` `sf` ON `si`.`student_fee_id` = `sf`.`id`
    LEFT JOIN `fee_types` `ft` ON COALESCE(si.fee_type_id, sf.fee_type_id) = `ft`.`id`
";

// Count total records with filters
$count_query = "SELECT COUNT(*) as total " . $base_query_from . " WHERE " . $where_clause;
$count_stmt = $conn->prepare($count_query);
if ($count_stmt === false) {
    die('Database error: Failed to prepare count query.<br>' . htmlspecialchars($count_query));
}
if (!empty($params)) {
    $count_stmt->bind_param($types, ...$params);
}
$count_stmt->execute();
$total_records = $count_stmt->get_result()->fetch_assoc()['total'];
$count_stmt->close();

// Pagination settings
$page = intval($_GET['page'] ?? 1);
$records_per_page = ITEMS_PER_PAGE;
$total_pages = ceil($total_records / $records_per_page);
$offset = ($page - 1) * $records_per_page;

// Fetch records for the current page
$main_query = "
    SELECT
        si.*,
        u.full_name as student_name,
        s.id as student_id,
        c.class_name,
        ip.plan_name,
        ft.type_name as fee_type_name,
        CASE WHEN si.due_date < CURDATE() AND si.status = 'pending' THEN 1 ELSE 0 END as is_overdue,
        DATEDIFF(CURDATE(), si.due_date) as days_overdue
    " . $base_query_from . "
    WHERE " . $where_clause . "
    ORDER BY si.due_date ASC, si.created_at DESC
    LIMIT ? OFFSET ?
";

$stmt = $conn->prepare($main_query);
if ($stmt === false) {
    die('Database error: Failed to prepare main query.<br>' . htmlspecialchars($main_query));
}

// Bind parameters for the main query
$main_params = $params;
$main_params[] = $records_per_page;
$main_params[] = $offset;
$main_types = $types . 'ii';

if (!empty($params)) {
    $stmt->bind_param($main_types, ...$main_params);
} else {
    $stmt->bind_param('ii', $records_per_page, $offset);
}

$stmt->execute();
$installments = $stmt->get_result();

// جلب قوائم الفلترة
$classes = $conn->query("SELECT id, class_name, grade_level FROM classes WHERE status = 'active' ORDER BY grade_level, class_name");

$installment_plans = $conn->query("SELECT id, plan_name FROM installment_plans WHERE status = 'active' ORDER BY plan_name");

// إحصائيات سريعة مع الحالات الصحيحة
$stats_query = "
    SELECT
        COUNT(*) as total_installments,
        SUM(si.total_amount) as total_amount,
        SUM(si.paid_amount) as total_paid,
        COUNT(CASE
            WHEN si.paid_amount >= si.total_amount AND si.total_amount > 0 THEN 1
            END) as paid_count,
        COUNT(CASE
            WHEN si.paid_amount > 0 AND si.paid_amount < si.total_amount THEN 1
            END) as partial_count,
        COUNT(CASE
            WHEN si.paid_amount < si.total_amount AND si.due_date >= CURDATE() THEN 1
            END) as pending_count,
        COUNT(CASE
            WHEN si.paid_amount < si.total_amount AND si.due_date < CURDATE() THEN 1
            END) as overdue_count
    " . $base_query_from . "
    WHERE " . $where_clause;


$stats_stmt = $conn->prepare($stats_query);
if ($stats_stmt === false) {
    die('Database error: Failed to prepare stats query.<br>' . htmlspecialchars($conn->error));
}

if (!empty($params)) {
    $stats_stmt->bind_param($types, ...$params);
}

$stats_stmt->execute();
$stats = $stats_stmt->get_result()->fetch_assoc();

// التأكد من أن القيم ليست null
$stats = [
    'total_installments' => $stats['total_installments'] ?? 0,
    'paid_count' => $stats['paid_count'] ?? 0,
    'partial_count' => $stats['partial_count'] ?? 0,
    'pending_count' => $stats['pending_count'] ?? 0,
    'overdue_count' => $stats['overdue_count'] ?? 0,
    'total_amount' => $stats['total_amount'] ?? 0,
    'total_paid' => $stats['total_paid'] ?? 0
];

$collection_rate = $stats['total_amount'] > 0 ?
    ($stats['total_paid'] / $stats['total_amount']) * 100 : 0;

include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('installments_management'); ?></h1>
            <p class="text-muted"><?php echo __('manage_student_installments'); ?></p>
        </div>
        <div>
            <a href="../fees/" class="btn btn-outline-info">
                <i class="fas fa-money-bill me-2"></i><?php echo __('fees_management'); ?>
            </a>
            <a href="../payments/" class="btn btn-outline-success">
                <i class="fas fa-credit-card me-2"></i><?php echo __('payments'); ?>
            </a>
            <a href="bank_accounts.php" class="btn btn-outline-warning ms-2">
                <i class="fas fa-university me-2"></i>أرقام الحساب البنكي
            </a>
            <a href="update_installment_status.php" class="btn btn-info ms-2"
               onclick="return confirm('هل تريد تحديث حالات جميع الأقساط تلقائياً؟\n\nسيتم:\n- تحويل الأقساط المتأخرة إلى حالة متأخر\n- تحويل الأقساط المدفوعة إلى حالة مدفوع\n- تحديث الأقساط الجزئية')"
               title="تحديث حالات الأقساط تلقائياً">
                <i class="fas fa-sync-alt me-2"></i>تحديث الحالات
            </a>
            <a href="add.php" class="btn btn-primary ms-2">
                <i class="fas fa-plus me-2"></i><?php echo __('add_installment'); ?>
            </a>
        </div>
    </div>

    <!-- رسائل النجاح والخطأ -->
    <?php if (isset($_GET['success']) && $_GET['success'] == 1): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i><?php echo __('installment_added_successfully'); ?>
        <?php if (isset($_GET['receipt'])): ?>
            - رقم الإيصال: <strong><?php echo htmlspecialchars($_GET['receipt']); ?></strong>
        <?php endif; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if (isset($_GET['payment_success']) && $_GET['payment_success'] == 1): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-money-bill-wave me-2"></i><?php echo __('payment_processed_successfully'); ?>
        <?php if (isset($_GET['amount'])): ?>
            - <?php echo __('amount'); ?>: <?php echo number_format($_GET['amount'], 2); ?> <?php echo get_system_setting('currency_symbol', 'ر.س'); ?>
        <?php endif; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if (isset($_GET['payment_error'])): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($_GET['payment_error']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if (isset($_GET['delete_success']) && $_GET['delete_success'] == 1): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-trash me-2"></i><?php echo __('installment_deleted_successfully'); ?>
        <?php if (isset($_GET['student_name']) && isset($_GET['installment_number'])): ?>
            - <?php echo __('student'); ?>: <?php echo htmlspecialchars($_GET['student_name']); ?>
            - <?php echo __('installment_number'); ?>: #<?php echo htmlspecialchars($_GET['installment_number']); ?>
        <?php endif; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-gradient p-3 rounded-3">
                                <i class="fas fa-list text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($stats['total_installments']); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('total_installments'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-gradient p-3 rounded-3">
                                <i class="fas fa-check-circle text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($stats['paid_count']); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('paid_installments'); ?></p>
                            <small class="text-success">
                                <?php echo number_format($collection_rate, 1); ?>% <?php echo __('collection_rate'); ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-gradient p-3 rounded-3">
                                <i class="fas fa-clock text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($stats['pending_count']); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('pending_installments'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-gradient p-3 rounded-3">
                                <i class="fas fa-adjust text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($stats['partial_count']); ?></h3>
                            <p class="text-muted mb-0">أقساط جزئية</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-danger bg-gradient p-3 rounded-3">
                                <i class="fas fa-exclamation-triangle text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="mb-0"><?php echo number_format($stats['overdue_count']); ?></h3>
                            <p class="text-muted mb-0"><?php echo __('overdue_installments'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- فلتر البحث الذكي -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-search me-2"></i>فلتر البحث الذكي
                <button class="btn btn-sm btn-outline-light float-end" type="button" data-bs-toggle="collapse" data-bs-target="#searchFilters" aria-expanded="false">
                    <i class="fas fa-filter me-1"></i>إظهار/إخفاء الفلاتر
                </button>
            </h5>
        </div>
        <div class="collapse show" id="searchFilters">
            <div class="card-body">
                <form method="GET" action="" id="searchForm">
                    <div class="row g-3">
                        <!-- البحث بالاسم -->
                        <div class="col-md-4">
                            <label class="form-label">
                                <i class="fas fa-user me-1"></i>البحث بالاسم
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" name="search"
                                       value="<?php echo htmlspecialchars($search); ?>"
                                       placeholder="اسم الطالب، رقم الإيصال، أو رقم الطالب..."
                                       autocomplete="off">
                            </div>
                            <small class="text-muted">البحث في أسماء الطلاب، أرقام الإيصالات، وأرقام الطلاب</small>
                        </div>

                        <!-- فلتر التاريخ من -->
                        <div class="col-md-4">
                            <label class="form-label">
                                <i class="fas fa-calendar-alt me-1"></i>من تاريخ
                            </label>
                            <input type="date" class="form-control" name="date_from"
                                   value="<?php echo htmlspecialchars($date_from); ?>">
                            <small class="text-muted">تاريخ الاستحقاق من</small>
                        </div>

                        <!-- فلتر التاريخ إلى -->
                        <div class="col-md-4">
                            <label class="form-label">
                                <i class="fas fa-calendar-alt me-1"></i>إلى تاريخ
                            </label>
                            <input type="date" class="form-control" name="date_to"
                                   value="<?php echo htmlspecialchars($date_to); ?>">
                            <small class="text-muted">تاريخ الاستحقاق إلى</small>
                        </div>

                        <!-- فلتر الفصل -->
                        <div class="col-md-3">
                            <label class="form-label">
                                <i class="fas fa-school me-1"></i>الفصل الدراسي
                            </label>
                            <select class="form-select" name="class_id">
                                <option value="">جميع الفصول</option>
                                <?php if ($classes && $classes->num_rows > 0): ?>
                                    <?php while ($class = $classes->fetch_assoc()): ?>
                                        <option value="<?php echo $class['id']; ?>"
                                                <?php echo ($class_filter == $class['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($class['class_name']); ?>
                                            <?php if (!empty($class['grade_level'])): ?>
                                                - <?php echo htmlspecialchars($class['grade_level']); ?>
                                            <?php endif; ?>
                                        </option>
                                    <?php endwhile; ?>
                                <?php endif; ?>
                            </select>
                        </div>

                        <!-- فلتر الحالة -->
                        <div class="col-md-3">
                            <label class="form-label">
                                <i class="fas fa-flag me-1"></i>حالة القسط
                            </label>
                            <select class="form-select" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="pending" <?php echo ($status_filter == 'pending') ? 'selected' : ''; ?>>
                                    معلق
                                </option>
                                <option value="paid" <?php echo ($status_filter == 'paid') ? 'selected' : ''; ?>>
                                    مدفوع
                                </option>
                                <option value="partial" <?php echo ($status_filter == 'partial') ? 'selected' : ''; ?>>
                                    مدفوع جزئياً
                                </option>
                                <option value="overdue" <?php echo ($status_filter == 'overdue') ? 'selected' : ''; ?>>
                                    متأخر
                                </option>
                            </select>
                        </div>

                        <!-- فلتر المتأخرات -->
                        <div class="col-md-3">
                            <label class="form-label">
                                <i class="fas fa-exclamation-triangle me-1"></i>المتأخرات فقط
                            </label>
                            <div class="form-check form-switch mt-2">
                                <input class="form-check-input" type="checkbox" name="overdue_only"
                                       id="overdue_only" <?php echo $overdue_filter ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="overdue_only">
                                    إظهار المتأخرات فقط
                                </label>
                            </div>
                        </div>

                        <!-- أزرار التحكم -->
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>بحث
                                </button>
                                <a href="?" class="btn btn-outline-secondary">
                                    <i class="fas fa-undo me-1"></i>إعادة تعيين
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- فلاتر سريعة -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="d-flex flex-wrap gap-2 mb-3">
                                <span class="text-muted me-2">فلاتر سريعة:</span>
                                <a href="?status=pending" class="btn btn-sm btn-outline-warning">
                                    <i class="fas fa-clock me-1"></i>المعلقة
                                </a>
                                <a href="?status=paid" class="btn btn-sm btn-outline-success">
                                    <i class="fas fa-check me-1"></i>المدفوعة
                                </a>
                                <a href="?overdue_only=1" class="btn btn-sm btn-outline-danger">
                                    <i class="fas fa-exclamation-triangle me-1"></i>المتأخرة
                                </a>
                                <a href="?date_from=<?php echo date('Y-m-01'); ?>&date_to=<?php echo date('Y-m-t'); ?>" class="btn btn-sm btn-outline-info">
                                    <i class="fas fa-calendar me-1"></i>هذا الشهر
                                </a>
                                <a href="?date_from=<?php echo date('Y-01-01'); ?>&date_to=<?php echo date('Y-12-31'); ?>" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-calendar-alt me-1"></i>هذا العام
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات النتائج -->
                    <?php if (!empty($search) || !empty($class_filter) || !empty($status_filter) || !empty($date_from) || !empty($date_to) || $overdue_filter): ?>
                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>نتائج البحث:</strong> تم العثور على <?php echo number_format($total_records); ?> قسط

                                <?php if (!empty($search)): ?>
                                    <span class="badge bg-primary ms-2">البحث: <?php echo htmlspecialchars($search); ?></span>
                                <?php endif; ?>

                                <?php if (!empty($date_from) || !empty($date_to)): ?>
                                    <span class="badge bg-success ms-2">
                                        التاريخ:
                                        <?php if (!empty($date_from)): ?>من <?php echo $date_from; ?><?php endif; ?>
                                        <?php if (!empty($date_to)): ?>إلى <?php echo $date_to; ?><?php endif; ?>
                                    </span>
                                <?php endif; ?>

                                <?php if (!empty($status_filter)): ?>
                                    <span class="badge bg-warning ms-2">الحالة: <?php echo $status_filter; ?></span>
                                <?php endif; ?>

                                <?php if (!empty($class_filter)): ?>
                                    <span class="badge bg-secondary ms-2">الفصل محدد</span>
                                <?php endif; ?>

                                <?php if ($overdue_filter): ?>
                                    <span class="badge bg-danger ms-2">المتأخرات فقط</span>
                                <?php endif; ?>

                                <a href="?" class="btn btn-sm btn-outline-secondary ms-2">
                                    <i class="fas fa-times me-1"></i>مسح الفلاتر
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </form>
            </div>
        </div>
    </div>

    <!-- جدول الأقساط -->
    <div class="card mb-4">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-list me-2"></i><?php echo __('installments_list'); ?></h5>
            <div class="text-muted">
                <small>إجمالي النتائج: <?php echo number_format($total_records); ?> قسط</small>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="table-light">
                        <tr>
                            <th><?php echo __('student'); ?></th>
                            <th>رقم الإيصال</th>
                            <th>نوع الرسوم</th>
                            <th>رقم القسط</th>
                            <th>المبلغ (<?php echo get_currency_symbol(); ?>)</th>
                            <th><?php echo __('due_date'); ?></th>
                            <th><?php echo __('status'); ?></th>
                            <th><?php echo __('actions'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while ($row = $installments->fetch_assoc()): ?>
                            <?php
                                // تحديد الحالة الفعلية بناءً على البيانات
                                $actual_status = $row['status'];

                                // إذا كان المبلغ المدفوع يساوي أو يزيد عن المبلغ الإجمالي
                                if ($row['paid_amount'] >= $row['total_amount'] && $row['total_amount'] > 0) {
                                    $actual_status = 'paid';
                                }
                                // إذا كان هناك مبلغ مدفوع جزئي
                                elseif ($row['paid_amount'] > 0 && $row['paid_amount'] < $row['total_amount']) {
                                    $actual_status = 'partial';
                                }
                                // إذا كان تاريخ الاستحقاق مضى ولم يتم الدفع
                                elseif ($row['due_date'] < date('Y-m-d') && $row['paid_amount'] < $row['total_amount']) {
                                    $actual_status = 'overdue';
                                }
                                // إذا لم يتم الدفع وتاريخ الاستحقاق لم يحن بعد
                                elseif ($row['paid_amount'] < $row['total_amount']) {
                                    $actual_status = 'pending';
                                }

                                $status_badge = '';
                                switch ($actual_status) {
                                    case 'paid':
                                        $status_badge = '<span class="badge bg-success"><i class="fas fa-check me-1"></i>مدفوع</span>';
                                        break;
                                    case 'partial':
                                        $status_badge = '<span class="badge bg-info"><i class="fas fa-adjust me-1"></i>جزئي</span>';
                                        break;
                                    case 'pending':
                                        $status_badge = '<span class="badge bg-warning text-dark"><i class="fas fa-clock me-1"></i>معلق</span>';
                                        break;
                                    case 'overdue':
                                        $status_badge = '<span class="badge bg-danger"><i class="fas fa-exclamation-triangle me-1"></i>متأخر</span>';
                                        break;
                                    default:
                                        $status_badge = '<span class="badge bg-secondary">' . htmlspecialchars($actual_status) . '</span>';
                                }

                                // إضافة معلومات إضافية
                                if ($actual_status == 'overdue') {
                                    $days_overdue = (strtotime(date('Y-m-d')) - strtotime($row['due_date'])) / (60 * 60 * 24);
                                    $status_badge .= '<br><small class="text-muted">متأخر ' . floor($days_overdue) . ' يوم</small>';
                                }

                                if ($actual_status == 'partial') {
                                    $remaining = $row['total_amount'] - $row['paid_amount'];
                                    $status_badge .= '<br><small class="text-muted">متبقي: ' . number_format($remaining, 2) . '</small>';
                                }
                            ?>
                            <tr>
                                <td class="col-student"><?php echo htmlspecialchars($row['student_name']); ?></td>
                                <td class="col-receipt">
                                    <code><?php echo htmlspecialchars($row['receipt_number'] ?? 'غير محدد'); ?></code>
                                </td>
                                <td class="col-fee-type">
                                    <span class="badge bg-info text-dark"><?php echo htmlspecialchars($row['fee_type_name'] ?? 'غير محدد'); ?></span>
                                </td>
                                <td class="col-installment">
                                    <?php
                                    $installment_text = '';
                                    switch ($row['installment_number']) {
                                        case 0:
                                            $installment_text = 'إجمالي';
                                            break;
                                        case 1:
                                            $installment_text = 'القسط الأول';
                                            break;
                                        case 2:
                                            $installment_text = 'القسط الثاني';
                                            break;
                                        case 3:
                                            $installment_text = 'القسط الثالث';
                                            break;
                                        default:
                                            $installment_text = 'القسط ' . $row['installment_number'];
                                    }
                                    echo htmlspecialchars($installment_text);
                                    ?>
                                </td>
                                <td class="col-total">
                                    <div>
                                        <strong><?php echo format_currency($row['total_amount']); ?></strong>
                                        <?php if ($row['paid_amount'] > 0): ?>
                                            <br>
                                            <small class="text-success">
                                                مدفوع: <?php echo format_currency($row['paid_amount']); ?>
                                            </small>
                                            <?php if ($row['paid_amount'] < $row['total_amount']): ?>
                                                <br>
                                                <small class="text-danger">
                                                    متبقي: <?php echo format_currency($row['total_amount'] - $row['paid_amount']); ?>
                                                </small>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                    $due_date = $row['due_date'];
                                    $is_overdue = ($due_date < date('Y-m-d')) && ($row['paid_amount'] < $row['total_amount']);
                                    $is_today = ($due_date == date('Y-m-d'));
                                    $is_soon = ($due_date > date('Y-m-d')) && ($due_date <= date('Y-m-d', strtotime('+7 days')));
                                    ?>

                                    <div class="<?php echo $is_overdue ? 'text-danger' : ($is_today ? 'text-warning' : ($is_soon ? 'text-info' : '')); ?>">
                                        <?php echo htmlspecialchars($due_date); ?>

                                        <?php if ($is_overdue): ?>
                                            <br><small><i class="fas fa-exclamation-triangle me-1"></i>متأخر</small>
                                        <?php elseif ($is_today): ?>
                                            <br><small><i class="fas fa-clock me-1"></i>اليوم</small>
                                        <?php elseif ($is_soon): ?>
                                            <br><small><i class="fas fa-calendar-alt me-1"></i>قريباً</small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td><?php echo $status_badge; ?></td>
                                <td>
                                    <?php if ($row['status'] !== 'paid'): ?>
                                        <a href="quick_pay_form.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-success" title="<?php echo __('quick_payment'); ?>">
                                            <i class="fas fa-money-bill-wave"></i> <?php echo __('pay'); ?>
                                        </a>
                                    <?php endif; ?>
                                    <a href="receipt.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-info" title="<?php echo __('view_receipt'); ?>">
                                        <i class="fas fa-receipt"></i> <?php echo __('view'); ?>
                                    </a>
                                    <a href="edit.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-warning" title="تعديل القسط">
                                        <i class="fas fa-edit"></i> تعديل
                                    </a>
                                    <a href="delete.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('<?php echo __('confirm_delete_installment'); ?>');" title="<?php echo __('delete_installment'); ?>">
                                        <i class="fas fa-trash"></i> <?php echo __('delete'); ?>
                                    </a>
                                </td>
                            </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>





<script>
// تحسينات JavaScript للفلتر الذكي
document.addEventListener('DOMContentLoaded', function() {
    // البحث التلقائي أثناء الكتابة
    const searchInput = document.querySelector('input[name="search"]');
    const searchForm = document.getElementById('searchForm');
    let searchTimeout;

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                if (this.value.length >= 2 || this.value.length === 0) {
                    searchForm.submit();
                }
            }, 500); // انتظار 500ms بعد توقف الكتابة
        });
    }

    // تحديث تلقائي عند تغيير الفلاتر
    const filterSelects = document.querySelectorAll('select[name="class_id"], select[name="status"]');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            searchForm.submit();
        });
    });

    // تحديث تلقائي عند تغيير التواريخ
    const dateInputs = document.querySelectorAll('input[name="date_from"], input[name="date_to"]');
    dateInputs.forEach(input => {
        input.addEventListener('change', function() {
            searchForm.submit();
        });
    });

    // تحديث تلقائي عند تغيير checkbox المتأخرات
    const overdueCheckbox = document.getElementById('overdue_only');
    if (overdueCheckbox) {
        overdueCheckbox.addEventListener('change', function() {
            searchForm.submit();
        });
    }

    // تحسين عرض النتائج
    const totalRecords = <?php echo $total_records; ?>;
    if (totalRecords === 0) {
        const tableBody = document.querySelector('tbody');
        if (tableBody && tableBody.children.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-5">
                        <div class="text-muted">
                            <i class="fas fa-search fa-3x mb-3"></i>
                            <h5>لا توجد نتائج</h5>
                            <p>لم يتم العثور على أقساط تطابق معايير البحث</p>
                            <a href="?" class="btn btn-primary">
                                <i class="fas fa-undo me-2"></i>إعادة تعيين الفلاتر
                            </a>
                        </div>
                    </td>
                </tr>
            `;
        }
    }

    // إضافة تأثيرات بصرية للبحث النشط
    const activeFilters = document.querySelectorAll('.badge');
    if (activeFilters.length > 0) {
        const searchCard = document.querySelector('#searchFilters').closest('.card');
        searchCard.classList.add('border-primary');
        searchCard.style.boxShadow = '0 0 0 0.2rem rgba(0,123,255,.25)';
    }

    // تحسين UX للتواريخ
    const dateFromInput = document.querySelector('input[name="date_from"]');
    const dateToInput = document.querySelector('input[name="date_to"]');

    if (dateFromInput && dateToInput) {
        dateFromInput.addEventListener('change', function() {
            if (this.value && !dateToInput.value) {
                // إذا تم اختيار تاريخ البداية فقط، اجعل تاريخ النهاية بعد شهر
                const fromDate = new Date(this.value);
                const toDate = new Date(fromDate);
                toDate.setMonth(toDate.getMonth() + 1);
                dateToInput.value = toDate.toISOString().split('T')[0];
            }
        });

        dateToInput.addEventListener('change', function() {
            if (this.value && dateFromInput.value) {
                const fromDate = new Date(dateFromInput.value);
                const toDate = new Date(this.value);

                if (toDate < fromDate) {
                    alert('تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
                    this.value = '';
                }
            }
        });
    }

    // إضافة shortcuts للوحة المفاتيح
    document.addEventListener('keydown', function(e) {
        // Ctrl + F للتركيز على البحث
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            searchInput.focus();
        }

        // Escape لمسح البحث
        if (e.key === 'Escape' && document.activeElement === searchInput) {
            searchInput.value = '';
            searchForm.submit();
        }
    });

    // إضافة loading state للبحث
    searchForm.addEventListener('submit', function() {
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;

        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري البحث...';
        submitBtn.disabled = true;

        // إعادة تفعيل الزر بعد 3 ثوان (في حالة عدم تحميل الصفحة)
        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 3000);
    });
});

// دالة لحفظ حالة الفلاتر في localStorage
function saveFilterState() {
    const filters = {
        search: document.querySelector('input[name="search"]').value,
        class_id: document.querySelector('select[name="class_id"]').value,
        status: document.querySelector('select[name="status"]').value,
        date_from: document.querySelector('input[name="date_from"]').value,
        date_to: document.querySelector('input[name="date_to"]').value,
        overdue_only: document.querySelector('input[name="overdue_only"]').checked
    };

    localStorage.setItem('installments_filters', JSON.stringify(filters));
}

// دالة لاستعادة حالة الفلاتر من localStorage
function restoreFilterState() {
    const savedFilters = localStorage.getItem('installments_filters');
    if (savedFilters) {
        const filters = JSON.parse(savedFilters);

        // استعادة القيم فقط إذا لم تكن هناك قيم في URL
        const urlParams = new URLSearchParams(window.location.search);
        if (!urlParams.has('search') && !urlParams.has('class_id') && !urlParams.has('status')) {
            document.querySelector('input[name="search"]').value = filters.search || '';
            document.querySelector('select[name="class_id"]').value = filters.class_id || '';
            document.querySelector('select[name="status"]').value = filters.status || '';
            document.querySelector('input[name="date_from"]').value = filters.date_from || '';
            document.querySelector('input[name="date_to"]').value = filters.date_to || '';
            document.querySelector('input[name="overdue_only"]').checked = filters.overdue_only || false;
        }
    }
}
</script>

<style>
/* تحسينات CSS للفلتر الذكي */
#searchFilters .form-label {
    font-weight: 600;
    color: #495057;
}

#searchFilters .input-group-text {
    background-color: #f8f9fa;
    border-color: #ced4da;
}

#searchFilters .form-control:focus,
#searchFilters .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.badge {
    font-size: 0.75em;
    padding: 0.35em 0.65em;
}

/* تحسين شكل الجدول */
.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* تحسين شكل الأزرار */
.btn-group .btn {
    margin: 0 1px;
}

/* تحسين responsive */
@media (max-width: 768px) {
    #searchFilters .col-md-4,
    #searchFilters .col-md-3 {
        margin-bottom: 1rem;
    }

    .float-end {
        float: none !important;
        margin-top: 0.5rem;
    }
}

/* تحسين شكل النتائج الفارغة */
.table tbody tr td .text-muted {
    color: #6c757d !important;
}

.table tbody tr td .text-muted i {
    opacity: 0.5;
}

/* تحسين شكل الـ loading state */
.btn:disabled {
    opacity: 0.7;
}

/* تحسين شكل الفلاتر النشطة */
.card.border-primary {
    transition: all 0.3s ease;
}

/* تحسين شكل الـ collapse */
.collapse {
    transition: height 0.35s ease;
}

.collapsing {
    transition: height 0.35s ease;
}
</style>

<?php include_once '../../includes/footer.php'; ?>