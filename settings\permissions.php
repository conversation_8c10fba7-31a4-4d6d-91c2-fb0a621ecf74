<?php
/**
 * إدارة الصلاحيات والأدوار - صفحة الإعدادات
 * Permissions and Roles Management - Settings Page
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

// جلب إحصائيات الصلاحيات
$permissions_stats = [];
try {
    $stats_query = "
        SELECT 
            (SELECT COUNT(*) FROM users WHERE status = 'active') as total_users,
            (SELECT COUNT(*) FROM user_custom_permissions WHERE is_granted = 1) as custom_permissions,
            (SELECT COUNT(*) FROM system_resources WHERE is_active = 1) as active_resources,
            (SELECT COUNT(*) FROM permissions_audit_log WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)) as recent_changes
    ";
    $stats_result = $conn->query($stats_query);
    if ($stats_result) {
        $permissions_stats = $stats_result->fetch_assoc();
    }
} catch (Exception $e) {
    // في حالة عدم وجود الجداول بعد
    $permissions_stats = [
        'total_users' => 0,
        'custom_permissions' => 0,
        'active_resources' => 0,
        'recent_changes' => 0
    ];
}

// جلب توزيع الأدوار
$roles_distribution = [];
try {
    $roles_query = "
        SELECT role, COUNT(*) as count 
        FROM users 
        WHERE status = 'active' 
        GROUP BY role 
        ORDER BY count DESC
    ";
    $roles_result = $conn->query($roles_query);
    if ($roles_result) {
        while ($row = $roles_result->fetch_assoc()) {
            $roles_distribution[] = $row;
        }
    }
} catch (Exception $e) {
    $roles_distribution = [];
}

$page_title = 'إدارة الصلاحيات والأدوار';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-shield-alt me-2"></i><?php echo $page_title; ?></h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="index.php">الإعدادات</a></li>
                    <li class="breadcrumb-item active">إدارة الصلاحيات</li>
                </ol>
            </nav>
        </div>
        <a href="index.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة للإعدادات
        </a>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $permissions_stats['total_users']; ?></h4>
                            <p class="mb-0">إجمالي المستخدمين</p>
                        </div>
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $permissions_stats['custom_permissions']; ?></h4>
                            <p class="mb-0">الصلاحيات المخصصة</p>
                        </div>
                        <i class="fas fa-key fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $permissions_stats['active_resources']; ?></h4>
                            <p class="mb-0">موارد النظام</p>
                        </div>
                        <i class="fas fa-sitemap fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $permissions_stats['recent_changes']; ?></h4>
                            <p class="mb-0">تغييرات آخر 30 يوم</p>
                        </div>
                        <i class="fas fa-history fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- الإدارة الرئيسية -->
        <div class="col-lg-8">
            <div class="row">
                <!-- إدارة المستخدمين والأدوار -->
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-users me-2"></i>إدارة المستخدمين والأدوار
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">تحكم في أدوار المستخدمين وصلاحياتهم المخصصة</p>
                            
                            <div class="d-grid gap-2">
                                <a href="../admin/permissions_manager.php" class="btn btn-outline-primary">
                                    <i class="fas fa-user-shield me-2"></i>إدارة صلاحيات المستخدمين
                                </a>
                                <a href="../admin/edit_user_role.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-user-edit me-2"></i>تعديل أدوار المستخدمين
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إدارة موارد النظام -->
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-success text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-sitemap me-2"></i>إدارة موارد النظام
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">إدارة الصفحات والإجراءات والبيانات والتقارير</p>
                            
                            <div class="d-grid gap-2">
                                <a href="../admin/system_resources.php" class="btn btn-outline-success">
                                    <i class="fas fa-list me-2"></i>عرض موارد النظام
                                </a>
                                <a href="../admin/add_resource.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-plus me-2"></i>إضافة مورد جديد
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إعدادات الصلاحيات -->
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-info text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-cog me-2"></i>إعدادات الصلاحيات
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">تخصيص إعدادات نظام الصلاحيات والأمان</p>
                            
                            <div class="d-grid gap-2">
                                <a href="../admin/test_clean_input_fix.php" class="btn btn-primary">
                                    <i class="fas fa-shield-alt me-2"></i>اختبار إصلاح الدوال
                                </a>
                                <a href="../admin/check_actual_user_permissions.php" class="btn btn-success">
                                    <i class="fas fa-user-check me-2"></i>فحص الصلاحيات الفعلية
                                </a>
                                <a href="../admin/custom_permissions_manager.php" class="btn btn-info">
                                    <i class="fas fa-users-cog me-2"></i>إدارة الصلاحيات المخصصة
                                </a>
                                <a href="../admin/remove_all_user_permissions.php" class="btn btn-warning">
                                    <i class="fas fa-trash me-2"></i>حذف جميع صلاحيات المستخدم
                                </a>
                                <a href="../admin/fix_missing_functions.php" class="btn btn-outline-danger">
                                    <i class="fas fa-tools me-2"></i>إصلاح الدوال المفقودة
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- سجل التغييرات -->
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-warning text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-history me-2"></i>سجل التغييرات والمراجعة
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">مراجعة جميع تغييرات الصلاحيات والأدوار</p>
                            
                            <div class="d-grid gap-2">
                                <a href="../admin/permissions_audit.php" class="btn btn-outline-warning">
                                    <i class="fas fa-search me-2"></i>عرض سجل التغييرات
                                </a>
                                <button class="btn btn-outline-secondary" onclick="exportAuditLog()">
                                    <i class="fas fa-download me-2"></i>تصدير السجل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- العمود الجانبي -->
        <div class="col-lg-4">
            <!-- توزيع الأدوار -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>توزيع الأدوار
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($roles_distribution)): ?>
                        <?php 
                        $role_names = [
                            'admin' => 'مدير النظام',
                            'financial_manager' => 'مدير مالي',
                            'teacher' => 'معلم',
                            'staff' => 'موظف',
                            'student' => 'طالب',
                            'parent' => 'ولي أمر'
                        ];
                        $colors = ['primary', 'success', 'info', 'warning', 'secondary', 'dark'];
                        ?>
                        <?php foreach ($roles_distribution as $index => $role): ?>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="badge bg-<?php echo $colors[$index % count($colors)]; ?>">
                                    <?php echo $role_names[$role['role']] ?? $role['role']; ?>
                                </span>
                                <strong><?php echo $role['count']; ?></strong>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p class="text-muted">لا توجد بيانات متاحة</p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- إرشادات سريعة -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-lightbulb me-2"></i>إرشادات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info alert-sm">
                        <h6><i class="fas fa-info-circle me-2"></i>كيفية إدارة الصلاحيات:</h6>
                        <ol class="small mb-0">
                            <li>حدد المستخدم المراد تعديل صلاحياته</li>
                            <li>اختر الدور الأساسي المناسب</li>
                            <li>أضف صلاحيات مخصصة حسب الحاجة</li>
                            <li>راجع التغييرات في سجل المراجعة</li>
                        </ol>
                    </div>
                    
                    <div class="alert alert-warning alert-sm">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيهات مهمة:</h6>
                        <ul class="small mb-0">
                            <li>المدير له وصول كامل دائماً</li>
                            <li>تغيير الأدوار يؤثر فوراً</li>
                            <li>جميع التغييرات مسجلة</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- روابط مفيدة -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-link me-2"></i>روابط مفيدة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="../users/" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-users me-2"></i>إدارة المستخدمين
                        </a>
                        <a href="../dashboard/" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                        </a>
                        <a href="index.php" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-cog me-2"></i>إعدادات النظام
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportAuditLog() {
    // يمكن إضافة منطق تصدير سجل المراجعة هنا
    alert('سيتم تطبيق ميزة التصدير قريباً');
}
</script>

<?php include_once '../includes/footer.php'; ?>
