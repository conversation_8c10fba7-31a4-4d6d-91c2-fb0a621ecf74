<?php
/**
 * قائمة مفصلة للمدفوعات مع أنواعها
 * Detailed Payments List with Types
 */

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    header('Location: ../../dashboard/');
    exit();
}

global $conn;

// معالجة حذف الدفعة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_payment'])) {
    if (verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $payment_id = intval($_POST['payment_id'] ?? 0);
        
        if ($payment_id > 0) {
            try {
                $conn->begin_transaction();
                
                // جلب بيانات الدفعة
                $payment_query = "SELECT * FROM student_payments WHERE id = ?";
                $stmt = $conn->prepare($payment_query);
                $stmt->bind_param('i', $payment_id);
                $stmt->execute();
                $payment_data = $stmt->get_result()->fetch_assoc();
                $stmt->close();
                
                if ($payment_data) {
                    // حذف الدفعة
                    $delete_query = "DELETE FROM student_payments WHERE id = ?";
                    $stmt = $conn->prepare($delete_query);
                    $stmt->bind_param('i', $payment_id);
                    
                    if ($stmt->execute()) {
                        $conn->commit();
                        $_SESSION['success_message'] = "تم حذف الدفعة رقم {$payment_data['receipt_number']} بنجاح";
                    } else {
                        throw new Exception('فشل في حذف الدفعة');
                    }
                    $stmt->close();
                } else {
                    throw new Exception('الدفعة غير موجودة');
                }
                
            } catch (Exception $e) {
                $conn->rollback();
                $_SESSION['error_message'] = 'فشل في حذف الدفعة: ' . $e->getMessage();
            }
        }
    }
    
    header('Location: index.php');
    exit();
}

// فلاتر البحث
$search = clean_input($_GET['search'] ?? '');
$filter_type = clean_input($_GET['type'] ?? '');
$filter_method = clean_input($_GET['method'] ?? '');
$filter_date_from = clean_input($_GET['date_from'] ?? '');
$filter_date_to = clean_input($_GET['date_to'] ?? '');
$filter_student = clean_input($_GET['student'] ?? '');
$filter_status = clean_input($_GET['status'] ?? '');

// بناء الاستعلام
$where_conditions = ["1=1"];
$params = [];
$param_types = "";

// البحث العام
if (!empty($search)) {
    $where_conditions[] = "(u.full_name LIKE ? OR s.student_id LIKE ? OR sp.receipt_number LIKE ? OR sp.payment_reference LIKE ?)";
    $search_term = "%$search%";
    $params = array_merge($params, [$search_term, $search_term, $search_term, $search_term]);
    $param_types .= "ssss";
}

if (!empty($filter_type)) {
    $where_conditions[] = "sp.payment_type = ?";
    $params[] = $filter_type;
    $param_types .= "s";
}

if (!empty($filter_method)) {
    $where_conditions[] = "sp.payment_method = ?";
    $params[] = $filter_method;
    $param_types .= "s";
}

if (!empty($filter_status)) {
    $where_conditions[] = "sp.status = ?";
    $params[] = $filter_status;
    $param_types .= "s";
}

if (!empty($filter_date_from)) {
    $where_conditions[] = "sp.payment_date >= ?";
    $params[] = $filter_date_from;
    $param_types .= "s";
}

if (!empty($filter_date_to)) {
    $where_conditions[] = "sp.payment_date <= ?";
    $params[] = $filter_date_to;
    $param_types .= "s";
}

if (!empty($filter_student)) {
    $where_conditions[] = "(u.full_name LIKE ? OR s.student_id LIKE ?)";
    $search_term = "%$filter_student%";
    $params[] = $search_term;
    $params[] = $search_term;
    $param_types .= "ss";
}

$where_clause = implode(" AND ", $where_conditions);

// التصفح
$page = intval($_GET['page'] ?? 1);
$per_page = 20;
$offset = ($page - 1) * $per_page;

// عدد النتائج الإجمالي
$count_query = "
    SELECT COUNT(*) as total
    FROM student_payments sp
    JOIN students s ON sp.student_id = s.id
    JOIN users u ON s.user_id = u.id
    WHERE $where_clause
";

$count_stmt = $conn->prepare($count_query);
if (!empty($params)) {
    $count_stmt->bind_param($param_types, ...$params);
}
$count_stmt->execute();
$total_records = $count_stmt->get_result()->fetch_assoc()['total'];
$total_pages = ceil($total_records / $per_page);
$count_stmt->close();

// الاستعلام الرئيسي
$payments_query = "
    SELECT 
        sp.id,
        sp.payment_reference,
        sp.amount,
        sp.payment_date,
        sp.payment_method,
        sp.payment_type,
        sp.receipt_number,
        sp.notes,
        sp.status,
        u.full_name as student_name,
        s.student_id as student_number,
        c.class_name,
        ft.type_name as fee_type_name,
        sf.final_amount as fee_amount,
        pu.full_name as processed_by_name
    FROM student_payments sp
    JOIN students s ON sp.student_id = s.id
    JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    LEFT JOIN student_fees sf ON sp.student_fee_id = sf.id
    LEFT JOIN fee_types ft ON sf.fee_type_id = ft.id
    LEFT JOIN users pu ON sp.processed_by = pu.id
    WHERE $where_clause
    ORDER BY sp.payment_date DESC, sp.id DESC
    LIMIT ? OFFSET ?
";

$payments_stmt = $conn->prepare($payments_query);
$params[] = $per_page;
$params[] = $offset;
$param_types .= "ii";

if (!empty($params)) {
    $payments_stmt->bind_param($param_types, ...$params);
}
$payments_stmt->execute();
$payments_result = $payments_stmt->get_result();

// إحصائيات
$stats_query = "
    SELECT 
        COUNT(*) as total_payments,
        SUM(sp.amount) as total_amount,
        COUNT(CASE WHEN sp.payment_method = 'cash' THEN 1 END) as cash_count,
        SUM(CASE WHEN sp.payment_method = 'cash' THEN sp.amount ELSE 0 END) as cash_amount,
        COUNT(CASE WHEN sp.payment_method = 'deposit_slip' THEN 1 END) as deposit_count,
        SUM(CASE WHEN sp.payment_method = 'deposit_slip' THEN sp.amount ELSE 0 END) as deposit_amount,
        COUNT(CASE WHEN sp.payment_method = 'bank_transfer' THEN 1 END) as transfer_count,
        SUM(CASE WHEN sp.payment_method = 'bank_transfer' THEN sp.amount ELSE 0 END) as transfer_amount,
        COUNT(CASE WHEN sp.payment_method = 'check' THEN 1 END) as check_count,
        SUM(CASE WHEN sp.payment_method = 'check' THEN sp.amount ELSE 0 END) as check_amount,
        COUNT(CASE WHEN sp.payment_method = 'card' THEN 1 END) as card_count,
        SUM(CASE WHEN sp.payment_method = 'card' THEN sp.amount ELSE 0 END) as card_amount
    FROM student_payments sp
    JOIN students s ON sp.student_id = s.id
    JOIN users u ON s.user_id = u.id
    WHERE $where_clause
";

// إزالة المعاملات الخاصة بالتصفح من الإحصائيات
$stats_params = array_slice($params, 0, -2);
$stats_param_types = substr($param_types, 0, -2);

$stats_stmt = $conn->prepare($stats_query);
if (!empty($stats_params)) {
    $stats_stmt->bind_param($stats_param_types, ...$stats_params);
}
$stats_stmt->execute();
$stats = $stats_stmt->get_result()->fetch_assoc();
$stats_stmt->close();

$page_title = 'قائمة المدفوعات المفصلة';
include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">قائمة المدفوعات المفصلة</h1>
            <p class="text-muted">عرض تفصيلي لجميع المدفوعات مع أنواعها</p>
        </div>
        <div>
            <a href="../index.php" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>العودة للمالية
            </a>
            <a href="add.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة دفعة جديدة
            </a>
        </div>
    </div>

    <!-- رسائل النجاح والخطأ -->
    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h4 class="mb-0"><?php echo number_format($stats['total_payments'] ?? 0); ?></h4>
                    <small>إجمالي المدفوعات</small>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h5 class="mb-0"><?php echo number_format($stats['total_amount'] ?? 0, 2); ?></h5>
                    <small>إجمالي المبلغ (ر.س)</small>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h6 class="mb-0"><?php echo number_format($stats['cash_count'] ?? 0); ?></h6>
                    <small>💵 نقدي</small>
                    <div style="font-size: 11px;"><?php echo number_format($stats['cash_amount'] ?? 0, 0); ?> ر.س</div>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-warning text-dark">
                <div class="card-body text-center">
                    <h6 class="mb-0"><?php echo number_format($stats['deposit_count'] ?? 0); ?></h6>
                    <small>🧾 إيداع نقدي</small>
                    <div style="font-size: 11px;"><?php echo number_format($stats['deposit_amount'] ?? 0, 0); ?> ر.س</div>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-secondary text-white">
                <div class="card-body text-center">
                    <h6 class="mb-0"><?php echo number_format($stats['transfer_count'] ?? 0); ?></h6>
                    <small>🏦 تحويل بنكي</small>
                    <div style="font-size: 11px;"><?php echo number_format($stats['transfer_amount'] ?? 0, 0); ?> ر.س</div>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-dark text-white">
                <div class="card-body text-center">
                    <h6 class="mb-0"><?php echo number_format($stats['check_count'] ?? 0); ?></h6>
                    <small>📝 شيك</small>
                    <div style="font-size: 11px;"><?php echo number_format($stats['check_amount'] ?? 0, 0); ?> ر.س</div>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-filter me-2"></i>فلاتر البحث
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" action="" class="row g-3">
                <div class="col-md-2">
                    <label for="search" class="form-label">
                        <i class="fas fa-search me-1"></i>البحث
                    </label>
                    <input type="text" class="form-control" id="search" name="search"
                           value="<?php echo htmlspecialchars($search); ?>"
                           placeholder="البحث في المدفوعات...">
                </div>

                <div class="col-md-2">
                    <label class="form-label">نوع الدفعة</label>
                    <select class="form-select" name="type">
                        <option value="">جميع الأنواع</option>
                        <option value="دفعة رسوم التسجيل" <?php echo $filter_type === 'دفعة رسوم التسجيل' ? 'selected' : ''; ?>>رسوم التسجيل</option>
                        <option value="دفعة رسوم الكتب" <?php echo $filter_type === 'دفعة رسوم الكتب' ? 'selected' : ''; ?>>رسوم الكتب</option>
                        <option value="دفعة رسوم النشاطات" <?php echo $filter_type === 'دفعة رسوم النشاطات' ? 'selected' : ''; ?>>رسوم النشاطات</option>
                        <option value="دفعة رسوم النقل" <?php echo $filter_type === 'دفعة رسوم النقل' ? 'selected' : ''; ?>>رسوم النقل</option>
                        <option value="دفعة رسوم دراسية" <?php echo $filter_type === 'دفعة رسوم دراسية' ? 'selected' : ''; ?>>رسوم دراسية</option>
                    </select>
                </div>

                <div class="col-md-2">
                    <label class="form-label">طريقة الدفع</label>
                    <select class="form-select" name="method">
                        <option value="">جميع الطرق</option>
                        <option value="cash" <?php echo $filter_method === 'cash' ? 'selected' : ''; ?>>💵 نقدي</option>
                        <option value="deposit_slip" <?php echo $filter_method === 'deposit_slip' ? 'selected' : ''; ?>>🧾 قسيمة إيداع نقدية</option>
                        <option value="bank_transfer" <?php echo $filter_method === 'bank_transfer' ? 'selected' : ''; ?>>🏦 تحويل بنكي</option>
                        <option value="check" <?php echo $filter_method === 'check' ? 'selected' : ''; ?>>📝 شيك</option>
                        <option value="card" <?php echo $filter_method === 'card' ? 'selected' : ''; ?>>💳 بطاقة ائتمان</option>
                    </select>
                </div>

                <div class="col-md-2">
                    <label class="form-label">الحالة</label>
                    <select class="form-select" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="pending" <?php echo $filter_status === 'pending' ? 'selected' : ''; ?>>معلقة</option>
                        <option value="confirmed" <?php echo $filter_status === 'confirmed' ? 'selected' : ''; ?>>مؤكدة</option>
                        <option value="cancelled" <?php echo $filter_status === 'cancelled' ? 'selected' : ''; ?>>ملغية</option>
                    </select>
                </div>

                <div class="col-md-2">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" name="date_from" value="<?php echo htmlspecialchars($filter_date_from); ?>">
                </div>

                <div class="col-md-2">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" name="date_to" value="<?php echo htmlspecialchars($filter_date_to); ?>">
                </div>

                <div class="col-md-2">
                    <label class="form-label">الطالب</label>
                    <input type="text" class="form-control" name="student" value="<?php echo htmlspecialchars($filter_student); ?>" placeholder="اسم أو رقم الطالب">
                </div>

                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>بحث
                        </button>
                    </div>
                </div>

                <?php if (!empty($search) || !empty($filter_type) || !empty($filter_method) || !empty($filter_date_from) || !empty($filter_date_to) || !empty($filter_student) || !empty($filter_status)): ?>
                <div class="col-12">
                    <div class="text-end">
                        <a href="?" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-times me-1"></i>مسح الفلاتر
                        </a>
                    </div>
                </div>
                <?php endif; ?>
            </form>
        </div>
    </div>

    <!-- قائمة المدفوعات -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>قائمة المدفوعات
                    <span class="badge bg-primary ms-2"><?php echo $total_records; ?></span>
                </h5>
                <div>
                    <button class="btn btn-success btn-sm" onclick="exportToExcel()">
                        <i class="fas fa-file-excel me-1"></i>تصدير Excel
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php if ($payments_result->num_rows > 0): ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>رقم الإيصال</th>
                            <th>الطالب</th>
                            <th>نوع الدفعة</th>
                            <th>المبلغ</th>
                            <th>طريقة الدفع</th>
                            <th>التاريخ</th>
                            <th>الحالة</th>
                            <th>معالج بواسطة</th>
                            <th class="no-print">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while ($payment = $payments_result->fetch_assoc()): ?>
                        <tr>
                            <td>
                                <strong><?php echo htmlspecialchars($payment['receipt_number']); ?></strong>
                                <?php if (!empty($payment['payment_reference'])): ?>
                                    <br><small class="text-muted"><?php echo htmlspecialchars($payment['payment_reference']); ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <strong><?php echo htmlspecialchars($payment['student_name']); ?></strong>
                                <br><small class="text-muted">ID: <?php echo htmlspecialchars($payment['student_number']); ?></small>
                                <?php if (!empty($payment['class_name'])): ?>
                                    <br><small class="text-muted"><?php echo htmlspecialchars($payment['class_name']); ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-info"><?php echo htmlspecialchars($payment['payment_type'] ?? 'دفعة عامة'); ?></span>
                                <?php if (!empty($payment['fee_type_name'])): ?>
                                    <br><small class="text-muted"><?php echo htmlspecialchars($payment['fee_type_name']); ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <strong class="text-success">
                                    <?php echo number_format($payment['amount'], 2); ?> ر.س
                                </strong>
                            </td>
                            <td>
                                <?php
                                $method_icons = [
                                    'cash' => 'fas fa-money-bill text-success',
                                    'deposit_slip' => 'fas fa-receipt text-info',
                                    'bank_transfer' => 'fas fa-university text-primary',
                                    'check' => 'fas fa-money-check text-warning',
                                    'card' => 'fas fa-credit-card text-danger'
                                ];
                                $method_names = [
                                    'cash' => 'نقدي',
                                    'deposit_slip' => 'قسيمة إيداع نقدية',
                                    'bank_transfer' => 'تحويل بنكي',
                                    'check' => 'شيك',
                                    'card' => 'بطاقة ائتمان'
                                ];
                                $icon = $method_icons[$payment['payment_method']] ?? 'fas fa-question text-muted';
                                $method_name = $method_names[$payment['payment_method']] ?? $payment['payment_method'];
                                ?>
                                <i class="<?php echo $icon; ?> me-1"></i>
                                <?php echo $method_name; ?>
                            </td>
                            <td>
                                <?php echo date('Y-m-d', strtotime($payment['payment_date'])); ?>
                                <br><small class="text-muted"><?php echo date('H:i', strtotime($payment['payment_date'])); ?></small>
                            </td>
                            <td>
                                <?php
                                $status_badges = [
                                    'pending' => 'bg-warning text-dark',
                                    'confirmed' => 'bg-success',
                                    'cancelled' => 'bg-danger'
                                ];
                                $status_names = [
                                    'pending' => 'معلقة',
                                    'confirmed' => 'مؤكدة',
                                    'cancelled' => 'ملغية'
                                ];
                                $badge_class = $status_badges[$payment['status']] ?? 'bg-secondary';
                                $status_name = $status_names[$payment['status']] ?? $payment['status'];
                                ?>
                                <span class="badge <?php echo $badge_class; ?>"><?php echo $status_name; ?></span>
                            </td>
                            <td>
                                <small><?php echo htmlspecialchars($payment['processed_by_name'] ?? 'غير محدد'); ?></small>
                            </td>
                            <td class="no-print">
                                <div class="btn-group btn-group-sm">
                                    <a href="view.php?id=<?php echo $payment['id']; ?>"
                                       class="btn btn-outline-info" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="receipt_improved.php?id=<?php echo $payment['id']; ?>"
                                       class="btn btn-outline-primary" title="طباعة الإيصال" target="_blank">
                                        <i class="fas fa-receipt"></i>
                                    </a>
                                    <?php if (check_permission('admin')): ?>
                                    <button type="button" class="btn btn-outline-danger"
                                            title="حذف الدفعة"
                                            onclick="deletePayment(<?php echo $payment['id']; ?>, '<?php echo htmlspecialchars($payment['receipt_number']); ?>')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>

            <!-- التصفح -->
            <?php if ($total_pages > 1): ?>
            <nav aria-label="Payments pagination" class="mt-4">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo ($page - 1); ?>&search=<?php echo urlencode($search); ?>&type=<?php echo urlencode($filter_type); ?>&method=<?php echo urlencode($filter_method); ?>&status=<?php echo urlencode($filter_status); ?>&date_from=<?php echo urlencode($filter_date_from); ?>&date_to=<?php echo urlencode($filter_date_to); ?>&student=<?php echo urlencode($filter_student); ?>">
                                السابق
                            </a>
                        </li>
                    <?php endif; ?>

                    <?php
                    $start = max(1, $page - 2);
                    $end = min($total_pages, $page + 2);

                    for ($i = $start; $i <= $end; $i++):
                    ?>
                        <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&type=<?php echo urlencode($filter_type); ?>&method=<?php echo urlencode($filter_method); ?>&status=<?php echo urlencode($filter_status); ?>&date_from=<?php echo urlencode($filter_date_from); ?>&date_to=<?php echo urlencode($filter_date_to); ?>&student=<?php echo urlencode($filter_student); ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                    <?php endfor; ?>

                    <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo ($page + 1); ?>&search=<?php echo urlencode($search); ?>&type=<?php echo urlencode($filter_type); ?>&method=<?php echo urlencode($filter_method); ?>&status=<?php echo urlencode($filter_status); ?>&date_from=<?php echo urlencode($filter_date_from); ?>&date_to=<?php echo urlencode($filter_date_to); ?>&student=<?php echo urlencode($filter_student); ?>">
                                التالي
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
            <?php endif; ?>

            <?php else: ?>
            <div class="text-center py-4">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد مدفوعات</h5>
                <p class="text-muted">لم يتم العثور على مدفوعات تطابق معايير البحث</p>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// حذف الدفعة
function deletePayment(paymentId, receiptNumber) {
    if (confirm(`هل أنت متأكد من حذف الدفعة رقم: ${receiptNumber}؟\n\nتحذير: هذا الإجراء لا يمكن التراجع عنه!`)) {
        // إنشاء نموذج مخفي للحذف
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '';

        const deleteInput = document.createElement('input');
        deleteInput.type = 'hidden';
        deleteInput.name = 'delete_payment';
        deleteInput.value = '1';

        const idInput = document.createElement('input');
        idInput.type = 'hidden';
        idInput.name = 'payment_id';
        idInput.value = paymentId;

        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = '<?php echo generate_csrf_token(); ?>';

        form.appendChild(deleteInput);
        form.appendChild(idInput);
        form.appendChild(csrfInput);
        document.body.appendChild(form);
        form.submit();
    }
}

// تصدير إلى Excel
function exportToExcel() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.open('export_payments.php?' + params.toString(), '_blank');
}

// البحث التلقائي
document.getElementById('search').addEventListener('input', function() {
    clearTimeout(this.searchTimeout);
    this.searchTimeout = setTimeout(() => {
        this.form.submit();
    }, 500);
});
</script>

<?php include_once '../../includes/footer.php'; ?>
