<?php
// صفحة عرض المستخدمين
require_once '../includes/header.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
check_session();

// جلب المستخدمين مع معلومات الإداريين
$users = [];
if ($conn) {
    $query = "
        SELECT u.*, s.id as admin_id
        FROM users u
        LEFT JOIN staff s ON u.id = s.user_id AND u.role = 'staff'
        ORDER BY u.created_at DESC
    ";
    $result = $conn->query($query);
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $users[] = $row;
        }
    }
}
?>
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2>المستخدمون</h2>
        <a href="add.php" class="btn btn-primary">إضافة مستخدم</a>
    </div>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>الاسم الكامل</th>
                <th>البريد الإلكتروني</th>
                <th>الدور</th>
                <th>الحالة</th>
                <th>معلومات إضافية</th>
                <th>إجراءات</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($users as $user): ?>
            <tr>
                <td><?= htmlspecialchars($user['full_name']) ?></td>
                <td><?= htmlspecialchars($user['email']) ?></td>
                <td>
                    <?= htmlspecialchars($user['role']) ?>
                    <?php if ($user['role'] === 'staff'): ?>
                        <span class="badge bg-info">إداري</span>
                    <?php endif; ?>
                </td>
                <td>
                    <?php
                    $status_class = $user['status'] === 'active' ? 'success' : 'secondary';
                    ?>
                    <span class="badge bg-<?= $status_class ?>"><?= htmlspecialchars($user['status']) ?></span>
                </td>
                <td>
                    <?php if ($user['role'] === 'staff' && $user['admin_id']): ?>
                        <a href="../administrators/view.php?id=<?= $user['admin_id'] ?>" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-user-tie"></i> عرض بيانات الإداري
                        </a>
                    <?php elseif ($user['role'] === 'staff' && !$user['admin_id']): ?>
                        <span class="text-warning">
                            <i class="fas fa-exclamation-triangle"></i> بيانات الإداري غير مكتملة
                        </span>
                    <?php else: ?>
                        -
                    <?php endif; ?>
                </td>
                <td>
                    <a href="view.php?id=<?= $user['id'] ?>" class="btn btn-info btn-sm">عرض</a>
                    <a href="edit.php?id=<?= $user['id'] ?>" class="btn btn-warning btn-sm">تعديل</a>
                    <button type="button" class="btn btn-danger btn-sm" onclick="deleteUser(<?= $user['id'] ?>, '<?= htmlspecialchars($user['full_name'], ENT_QUOTES) ?>')">حذف</button>
                </td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>
<script>
function deleteUser(id, name) {
    if (confirm('هل أنت متأكد من حذف المستخدم: ' + name + '؟\nسيتم حذف جميع البيانات المرتبطة به.\nهذا الإجراء لا يمكن التراجع عنه.')) {
        // إرسال طلب GET للحذف (ملف delete.php يتعامل مع GET)
        window.location.href = 'delete.php?id=' + id;
    }
}
</script>

<?php require_once '../includes/footer.php'; ?>