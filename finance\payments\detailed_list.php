<?php
/**
 * قائمة مفصلة للمدفوعات مع أنواعها
 * Detailed Payments List with Types
 */

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    header('Location: ../../dashboard/');
    exit();
}

global $conn;

// فلاتر البحث
$filter_type = clean_input($_GET['type'] ?? '');
$filter_method = clean_input($_GET['method'] ?? '');
$filter_date_from = clean_input($_GET['date_from'] ?? '');
$filter_date_to = clean_input($_GET['date_to'] ?? '');
$filter_student = clean_input($_GET['student'] ?? '');

// بناء الاستعلام
$where_conditions = ["sp.status = 'confirmed'"];
$params = [];
$param_types = "";

if (!empty($filter_type)) {
    $where_conditions[] = "sp.payment_type = ?";
    $params[] = $filter_type;
    $param_types .= "s";
}

if (!empty($filter_method)) {
    $where_conditions[] = "sp.payment_method = ?";
    $params[] = $filter_method;
    $param_types .= "s";
}

if (!empty($filter_date_from)) {
    $where_conditions[] = "sp.payment_date >= ?";
    $params[] = $filter_date_from;
    $param_types .= "s";
}

if (!empty($filter_date_to)) {
    $where_conditions[] = "sp.payment_date <= ?";
    $params[] = $filter_date_to;
    $param_types .= "s";
}

if (!empty($filter_student)) {
    $where_conditions[] = "(u.full_name LIKE ? OR s.student_id LIKE ?)";
    $search_term = "%$filter_student%";
    $params[] = $search_term;
    $params[] = $search_term;
    $param_types .= "ss";
}

$where_clause = implode(" AND ", $where_conditions);

// الاستعلام الرئيسي
$payments_query = "
    SELECT 
        sp.id,
        sp.payment_reference,
        sp.amount,
        sp.payment_date,
        sp.payment_method,
        sp.payment_type,
        sp.receipt_number,
        sp.notes,
        u.full_name as student_name,
        s.student_id as student_number,
        c.class_name,
        ft.type_name as fee_type_name,
        sf.final_amount as fee_amount,
        pu.full_name as processed_by_name
    FROM student_payments sp
    JOIN students s ON sp.student_id = s.id
    JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    LEFT JOIN student_fees sf ON sp.student_fee_id = sf.id
    LEFT JOIN fee_types ft ON sf.fee_type_id = ft.id
    LEFT JOIN users pu ON sp.processed_by = pu.id
    WHERE $where_clause
    ORDER BY sp.payment_date DESC, sp.id DESC
";

$payments_stmt = $conn->prepare($payments_query);
if (!empty($params)) {
    $payments_stmt->bind_param($param_types, ...$params);
}
$payments_stmt->execute();
$payments_result = $payments_stmt->get_result();

// إحصائيات
$stats_query = "
    SELECT 
        COUNT(*) as total_payments,
        SUM(sp.amount) as total_amount,
        COUNT(CASE WHEN sp.payment_type = 'دفعة رسوم التسجيل' THEN 1 END) as registration_count,
        SUM(CASE WHEN sp.payment_type = 'دفعة رسوم التسجيل' THEN sp.amount ELSE 0 END) as registration_amount,
        COUNT(CASE WHEN sp.payment_type = 'دفعة رسوم الكتب' THEN 1 END) as books_count,
        SUM(CASE WHEN sp.payment_type = 'دفعة رسوم الكتب' THEN sp.amount ELSE 0 END) as books_amount,
        COUNT(CASE WHEN sp.payment_type = 'دفعة رسوم النشاطات' THEN 1 END) as activities_count,
        SUM(CASE WHEN sp.payment_type = 'دفعة رسوم النشاطات' THEN sp.amount ELSE 0 END) as activities_amount,
        COUNT(CASE WHEN sp.payment_type = 'دفعة رسوم النقل' THEN 1 END) as transport_count,
        SUM(CASE WHEN sp.payment_type = 'دفعة رسوم النقل' THEN sp.amount ELSE 0 END) as transport_amount,
        COUNT(CASE WHEN sp.payment_type = 'دفعة رسوم دراسية' THEN 1 END) as tuition_count,
        SUM(CASE WHEN sp.payment_type = 'دفعة رسوم دراسية' THEN sp.amount ELSE 0 END) as tuition_amount
    FROM student_payments sp
    WHERE $where_clause
";

$stats_stmt = $conn->prepare($stats_query);
if (!empty($params)) {
    $stats_stmt->bind_param($param_types, ...$params);
}
$stats_stmt->execute();
$stats = $stats_stmt->get_result()->fetch_assoc();

$page_title = 'قائمة المدفوعات المفصلة';
include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">قائمة المدفوعات المفصلة</h1>
            <p class="text-muted">عرض تفصيلي لجميع المدفوعات مع أنواعها</p>
        </div>
        <div>
            <a href="../index.php" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>العودة للمالية
            </a>
            <a href="../fee_types/" class="btn btn-primary">
                <i class="fas fa-tags me-2"></i>إدارة أنواع الرسوم
            </a>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h4 class="mb-0"><?php echo number_format($stats['total_payments'] ?? 0); ?></h4>
                    <small>إجمالي المدفوعات</small>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h5 class="mb-0"><?php echo number_format($stats['total_amount'] ?? 0, 2); ?></h5>
                    <small>إجمالي المبلغ (<?php echo get_system_setting('currency_symbol', 'ر.س'); ?>)</small>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h6 class="mb-0"><?php echo number_format($stats['registration_count'] ?? 0); ?></h6>
                    <small>رسوم التسجيل</small>
                    <div style="font-size: 11px;"><?php echo number_format($stats['registration_amount'] ?? 0, 0); ?> <?php echo get_system_setting('currency_symbol', 'ر.س'); ?></div>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-warning text-dark">
                <div class="card-body text-center">
                    <h6 class="mb-0"><?php echo number_format($stats['books_count'] ?? 0); ?></h6>
                    <small>رسوم الكتب</small>
                    <div style="font-size: 11px;"><?php echo number_format($stats['books_amount'] ?? 0, 0); ?> <?php echo get_system_setting('currency_symbol', 'ر.س'); ?></div>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-secondary text-white">
                <div class="card-body text-center">
                    <h6 class="mb-0"><?php echo number_format($stats['activities_count'] ?? 0); ?></h6>
                    <small>رسوم النشاطات</small>
                    <div style="font-size: 11px;"><?php echo number_format($stats['activities_amount'] ?? 0, 0); ?> <?php echo get_system_setting('currency_symbol', 'ر.س'); ?></div>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-dark text-white">
                <div class="card-body text-center">
                    <h6 class="mb-0"><?php echo number_format($stats['transport_count'] ?? 0); ?></h6>
                    <small>رسوم النقل</small>
                    <div style="font-size: 11px;"><?php echo number_format($stats['transport_amount'] ?? 0, 0); ?> <?php echo get_system_setting('currency_symbol', 'ر.س'); ?></div>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-filter me-2"></i>فلاتر البحث
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" action="">
                <div class="row">
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label">نوع الدفعة</label>
                            <select class="form-select" name="type">
                                <option value="">جميع الأنواع</option>
                                <option value="دفعة رسوم التسجيل" <?php echo $filter_type === 'دفعة رسوم التسجيل' ? 'selected' : ''; ?>>رسوم التسجيل</option>
                                <option value="دفعة رسوم الكتب" <?php echo $filter_type === 'دفعة رسوم الكتب' ? 'selected' : ''; ?>>رسوم الكتب</option>
                                <option value="دفعة رسوم النشاطات" <?php echo $filter_type === 'دفعة رسوم النشاطات' ? 'selected' : ''; ?>>رسوم النشاطات</option>
                                <option value="دفعة رسوم النقل" <?php echo $filter_type === 'دفعة رسوم النقل' ? 'selected' : ''; ?>>رسوم النقل</option>
                                <option value="دفعة رسوم دراسية" <?php echo $filter_type === 'دفعة رسوم دراسية' ? 'selected' : ''; ?>>رسوم دراسية</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="mb-3">
                            <label class="form-label">طريقة الدفع</label>
                            <select class="form-select" name="method">
                                <option value="">جميع الطرق</option>
                                <option value="cash" <?php echo $filter_method === 'cash' ? 'selected' : ''; ?>>نقدي</option>
                                <option value="bank_transfer" <?php echo $filter_method === 'bank_transfer' ? 'selected' : ''; ?>>تحويل بنكي</option>
                                <option value="check" <?php echo $filter_method === 'check' ? 'selected' : ''; ?>>شيك</option>
                                <option value="card" <?php echo $filter_method === 'card' ? 'selected' : ''; ?>>بطاقة</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="mb-3">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" name="date_from" value="<?php echo htmlspecialchars($filter_date_from); ?>">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="mb-3">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" name="date_to" value="<?php echo htmlspecialchars($filter_date_to); ?>">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="mb-3">
                            <label class="form-label">الطالب</label>
                            <input type="text" class="form-control" name="student" value="<?php echo htmlspecialchars($filter_student); ?>" placeholder="اسم أو رقم الطالب">
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="mb-3">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary d-block">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <?php if (!empty($filter_type) || !empty($filter_method) || !empty($filter_date_from) || !empty($filter_date_to) || !empty($filter_student)): ?>
                <div class="text-end">
                    <a href="?" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times me-1"></i>مسح الفلاتر
                    </a>
                </div>
                <?php endif; ?>
            </form>
        </div>
    </div>

    <!-- قائمة المدفوعات -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>قائمة المدفوعات
                    <span class="badge bg-primary ms-2"><?php echo $payments_result->num_rows; ?></span>
                </h5>
                <div>
                    <button class="btn btn-success btn-sm" onclick="exportToExcel()">
                        <i class="fas fa-file-excel me-1"></i>تصدير Excel
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php if ($payments_result->num_rows > 0): ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>رقم الإيصال</th>
                            <th>الطالب</th>
                            <th>نوع الدفعة</th>
                            <th>المبلغ</th>
                            <th>طريقة الدفع</th>
                            <th>التاريخ</th>
                            <th>معالج بواسطة</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while ($payment = $payments_result->fetch_assoc()): ?>
                        <tr>
                            <td>
                                <strong><?php echo htmlspecialchars($payment['receipt_number']); ?></strong>
                                <br><small class="text-muted"><?php echo htmlspecialchars($payment['payment_reference']); ?></small>
                            </td>
                            <td>
                                <strong><?php echo htmlspecialchars($payment['student_name']); ?></strong>
                                <br><small class="text-muted">ID: <?php echo htmlspecialchars($payment['student_number']); ?></small>
                                <?php if (!empty($payment['class_name'])): ?>
                                    <br><small class="text-muted"><?php echo htmlspecialchars($payment['class_name']); ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-info"><?php echo htmlspecialchars($payment['payment_type'] ?? 'دفعة عامة'); ?></span>
                                <?php if (!empty($payment['fee_type_name'])): ?>
                                    <br><small class="text-muted"><?php echo htmlspecialchars($payment['fee_type_name']); ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <strong class="text-success">
                                    <?php echo number_format($payment['amount'], 2); ?> 
                                    <?php echo get_system_setting('currency_symbol', 'ر.س'); ?>
                                </strong>
                            </td>
                            <td>
                                <?php
                                $method_badges = [
                                    'cash' => 'bg-success',
                                    'bank_transfer' => 'bg-primary',
                                    'check' => 'bg-warning text-dark',
                                    'card' => 'bg-info'
                                ];
                                $method_names = [
                                    'cash' => 'نقدي',
                                    'bank_transfer' => 'تحويل بنكي',
                                    'check' => 'شيك',
                                    'card' => 'بطاقة'
                                ];
                                $badge_class = $method_badges[$payment['payment_method']] ?? 'bg-secondary';
                                $method_name = $method_names[$payment['payment_method']] ?? $payment['payment_method'];
                                ?>
                                <span class="badge <?php echo $badge_class; ?>"><?php echo $method_name; ?></span>
                            </td>
                            <td>
                                <?php echo date('Y-m-d', strtotime($payment['payment_date'])); ?>
                                <br><small class="text-muted"><?php echo date('H:i', strtotime($payment['payment_date'])); ?></small>
                            </td>
                            <td>
                                <small><?php echo htmlspecialchars($payment['processed_by_name'] ?? 'غير محدد'); ?></small>
                            </td>
                            <td>
                                <?php if (!empty($payment['notes'])): ?>
                                    <small><?php echo htmlspecialchars($payment['notes']); ?></small>
                                <?php else: ?>
                                    <small class="text-muted">-</small>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
            <?php else: ?>
            <div class="text-center py-4">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد مدفوعات</h5>
                <p class="text-muted">لم يتم العثور على مدفوعات تطابق معايير البحث</p>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function exportToExcel() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.open('export_payments.php?' + params.toString(), '_blank');
}
</script>

<?php include_once '../../includes/footer.php'; ?>
