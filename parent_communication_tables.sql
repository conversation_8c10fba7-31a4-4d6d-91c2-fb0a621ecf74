-- جداول نظام إدارة التواصل مع أولياء الأمور
-- Parent Communication Management System Tables

-- تعطيل فحص المفاتيح الخارجية مؤقتاً
SET FOREIGN_KEY_CHECKS = 0;

-- جدول رسائل التواصل
CREATE TABLE IF NOT EXISTS parent_communications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    parent_phone VARCHAR(20),
    message_type ENUM('behavior', 'academic', 'attendance', 'general', 'emergency') DEFAULT 'general',
    subject VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    sent_via ENUM('whatsapp', 'sms', 'email', 'app') DEFAULT 'whatsapp',
    status ENUM('pending', 'sent', 'delivered', 'read', 'failed') DEFAULT 'pending',
    sent_by INT NOT NULL,
    sent_at TIMESTAMP NULL,
    delivered_at TIMESTAMP NULL,
    read_at TIMESTAMP NULL,
    whatsapp_message_id VARCHAR(100) NULL,
    error_message TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_student_id (student_id),
    INDEX idx_sent_by (sent_by),
    INDEX idx_status (status),
    INDEX idx_message_type (message_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول تقارير سلوك الطلاب
CREATE TABLE IF NOT EXISTS student_behavior_reports (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    report_date DATE NOT NULL,
    behavior_type ENUM('excellent', 'good', 'needs_improvement', 'concerning') NOT NULL,
    category ENUM('discipline', 'participation', 'homework', 'social', 'academic') NOT NULL,
    description TEXT NOT NULL,
    action_taken TEXT NULL,
    teacher_id INT NOT NULL,
    notify_parent BOOLEAN DEFAULT TRUE,
    parent_notified_at TIMESTAMP NULL,
    communication_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_student_id (student_id),
    INDEX idx_teacher_id (teacher_id),
    INDEX idx_report_date (report_date),
    INDEX idx_behavior_type (behavior_type),
    INDEX idx_category (category),
    INDEX idx_communication_id (communication_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول قوالب الرسائل
CREATE TABLE IF NOT EXISTS message_templates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    template_name VARCHAR(100) NOT NULL,
    category VARCHAR(50) NOT NULL,
    subject VARCHAR(200) NOT NULL,
    message_body TEXT NOT NULL,
    variables TEXT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    usage_count INT DEFAULT 0,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_category (category),
    INDEX idx_is_active (is_active),
    INDEX idx_created_by (created_by)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول إعدادات التواصل
CREATE TABLE IF NOT EXISTS communication_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NULL,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    updated_by INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_setting_key (setting_key),
    INDEX idx_is_active (is_active),
    INDEX idx_updated_by (updated_by)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول سجل الإشعارات التلقائية
CREATE TABLE IF NOT EXISTS auto_notifications_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    trigger_type ENUM('absence', 'late', 'behavior', 'academic', 'emergency') NOT NULL,
    student_id INT NOT NULL,
    triggered_by_id INT NULL,
    notification_sent BOOLEAN DEFAULT FALSE,
    communication_id INT NULL,
    error_message TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_student_id (student_id),
    INDEX idx_trigger_type (trigger_type),
    INDEX idx_created_at (created_at),
    INDEX idx_communication_id (communication_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج قوالب رسائل افتراضية
INSERT INTO message_templates (template_name, category, subject, message_body, variables, created_by) VALUES
('تهنئة سلوك ممتاز', 'behavior', 'تهنئة - سلوك ممتاز', 'السلام عليكم ولي أمر الطالب/ة {student_name}

يسعدنا إبلاغكم بأن ابنكم/ابنتكم أظهر سلوكاً ممتازاً اليوم في {category_name}.

{description}

نشكركم على تربيتكم الحسنة ونتمنى استمرار هذا التميز.

مع تحيات إدارة المدرسة', '["student_name", "category_name", "description"]', 1),

('تنبيه غياب', 'attendance', 'تنبيه غياب', 'السلام عليكم ولي أمر الطالب/ة {student_name}

نود إعلامكم بأن ابنكم/ابنتكم غائب اليوم {date} عن المدرسة.

إذا كان الغياب لظرف طارئ، يرجى التواصل معنا.

مع تحيات إدارة المدرسة', '["student_name", "date"]', 1),

('تحسين مطلوب', 'behavior', 'يحتاج تحسين', 'السلام عليكم ولي أمر الطالب/ة {student_name}

نود لفت انتباهكم إلى أن ابنكم/ابنتكم يحتاج إلى تحسين في {category_name}.

{description}

الإجراء المتخذ: {action_taken}

نرجو التعاون معنا لمساعدة الطالب على التحسن.

مع تحيات إدارة المدرسة', '["student_name", "category_name", "description", "action_taken"]', 1),

('إشعار أكاديمي', 'academic', 'إشعار أكاديمي', 'السلام عليكم ولي أمر الطالب/ة {student_name}

نود إعلامكم بالتالي حول الأداء الأكاديمي لابنكم/ابنتكم:

{description}

نرجو المتابعة مع الطالب ومساعدته على التحسن.

مع تحيات إدارة المدرسة', '["student_name", "description"]', 1),

('رسالة عامة', 'general', 'رسالة من المدرسة', 'السلام عليكم ولي أمر الطالب/ة {student_name}

{message}

مع تحيات إدارة المدرسة', '["student_name", "message"]', 1);

-- إدراج إعدادات افتراضية
INSERT INTO communication_settings (setting_key, setting_value, setting_type, description) VALUES
('whatsapp_api_url', '', 'string', 'رابط WhatsApp Business API'),
('whatsapp_access_token', '', 'string', 'رمز الوصول لـ WhatsApp API'),
('whatsapp_phone_number_id', '', 'string', 'معرف رقم الهاتف في WhatsApp Business'),
('sms_api_url', '', 'string', 'رابط خدمة الرسائل النصية'),
('sms_api_key', '', 'string', 'مفتاح API للرسائل النصية'),
('email_smtp_host', '', 'string', 'خادم SMTP للبريد الإلكتروني'),
('email_smtp_port', '587', 'number', 'منفذ SMTP'),
('email_username', '', 'string', 'اسم المستخدم للبريد الإلكتروني'),
('email_password', '', 'string', 'كلمة مرور البريد الإلكتروني'),
('auto_absence_notification', '1', 'boolean', 'إرسال إشعار تلقائي عند الغياب'),
('auto_behavior_notification', '1', 'boolean', 'إرسال إشعار تلقائي لتقارير السلوك'),
('default_message_priority', 'medium', 'string', 'أولوية الرسائل الافتراضية'),
('max_daily_messages_per_parent', '10', 'number', 'الحد الأقصى للرسائل اليومية لولي الأمر الواحد');

-- إعادة تفعيل فحص المفاتيح الخارجية
SET FOREIGN_KEY_CHECKS = 1;
