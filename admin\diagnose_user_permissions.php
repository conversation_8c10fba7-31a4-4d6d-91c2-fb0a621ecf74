<?php
/**
 * تشخيص مفصل لصلاحيات المستخدم
 * Detailed User Permissions Diagnosis
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

$user_email = '<EMAIL>';
$diagnosis_results = [];

// جلب بيانات المستخدم
$user_query = "SELECT * FROM users WHERE email = ?";
$user_stmt = $conn->prepare($user_query);
$user_stmt->bind_param("s", $user_email);
$user_stmt->execute();
$user = $user_stmt->get_result()->fetch_assoc();

if (!$user) {
    $diagnosis_results['user_exists'] = false;
    $diagnosis_results['error'] = "المستخدم غير موجود!";
} else {
    $diagnosis_results['user_exists'] = true;
    $diagnosis_results['user_info'] = $user;
    
    // 1. فحص الصلاحيات المحفوظة في قاعدة البيانات
    $permissions_query = "SELECT * FROM user_custom_permissions WHERE user_id = ? ORDER BY permission_key";
    $permissions_stmt = $conn->prepare($permissions_query);
    $permissions_stmt->bind_param("i", $user['id']);
    $permissions_stmt->execute();
    $permissions_result = $permissions_stmt->get_result();
    
    $stored_permissions = [];
    while ($perm = $permissions_result->fetch_assoc()) {
        $stored_permissions[] = $perm;
    }
    $diagnosis_results['stored_permissions'] = $stored_permissions;
    
    // 2. محاكاة جلسة المستخدم واختبار دالة has_permission
    $original_session = $_SESSION;
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['role'] = $user['role'];
    $_SESSION['email'] = $user['email'];
    
    $test_permissions = [
        'teacher_access',
        'staff_access',
        'students_view',
        'student_add',
        'student_edit',
        'student_affairs',
        'teachers_view',
        'staff_affairs',
        'classes_view',
        'subjects_view',
        'reports_view'
    ];
    
    $function_test_results = [];
    foreach ($test_permissions as $permission) {
        $has_permission = has_permission($permission);
        $function_test_results[$permission] = $has_permission;
    }
    $diagnosis_results['function_test'] = $function_test_results;
    
    // 3. اختبار مباشر لقاعدة البيانات
    $direct_db_test = [];
    foreach ($test_permissions as $permission) {
        $direct_query = "SELECT is_granted FROM user_custom_permissions WHERE user_id = ? AND permission_key = ? AND is_granted = 1";
        $direct_stmt = $conn->prepare($direct_query);
        $direct_stmt->bind_param("is", $user['id'], $permission);
        $direct_stmt->execute();
        $direct_result = $direct_stmt->get_result();
        $direct_db_test[$permission] = $direct_result->num_rows > 0;
    }
    $diagnosis_results['direct_db_test'] = $direct_db_test;
    
    // 4. فحص الصفحات الرئيسية
    $page_access_test = [];
    
    // محاكاة فحص صفحة الطلاب
    $students_access = !(!check_permission('teacher') && !check_permission('staff') && !has_permission('students_view') && !has_permission('student_affairs'));
    $page_access_test['students_page'] = $students_access;
    
    // محاكاة فحص صفحة المعلمين
    $teachers_access = !(!check_permission('admin') && !check_permission('staff') && !has_permission('teachers_view') && !has_permission('staff_affairs'));
    $page_access_test['teachers_page'] = $teachers_access;
    
    $diagnosis_results['page_access_test'] = $page_access_test;
    
    // 5. فحص دالة check_permission
    $check_permission_test = [];
    $check_permission_test['admin'] = check_permission('admin');
    $check_permission_test['teacher'] = check_permission('teacher');
    $check_permission_test['staff'] = check_permission('staff');
    $check_permission_test['student'] = check_permission('student');
    $diagnosis_results['check_permission_test'] = $check_permission_test;
    
    // استعادة الجلسة الأصلية
    $_SESSION = $original_session;
}

$page_title = 'تشخيص صلاحيات المستخدم';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-stethoscope me-2"></i><?php echo $page_title; ?></h2>
            <p class="text-muted">تشخيص مفصل لمشكلة عدم ظهور الصلاحيات للمستخدم: <?php echo $user_email; ?></p>
        </div>
        <a href="../settings/permissions.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة
        </a>
    </div>

    <?php if (!$diagnosis_results['user_exists']): ?>
        <div class="alert alert-danger">
            <h5><i class="fas fa-exclamation-triangle me-2"></i>خطأ!</h5>
            <p class="mb-0"><?php echo $diagnosis_results['error']; ?></p>
        </div>
    <?php else: ?>
        
        <!-- معلومات المستخدم -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5><i class="fas fa-user me-2"></i>معلومات المستخدم</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>الاسم:</strong><br>
                        <?php echo safe_html($diagnosis_results['user_info']['full_name']); ?>
                    </div>
                    <div class="col-md-3">
                        <strong>البريد الإلكتروني:</strong><br>
                        <?php echo safe_html($diagnosis_results['user_info']['email']); ?>
                    </div>
                    <div class="col-md-3">
                        <strong>الدور:</strong><br>
                        <span class="badge bg-warning"><?php echo safe_html($diagnosis_results['user_info']['role']); ?></span>
                    </div>
                    <div class="col-md-3">
                        <strong>الحالة:</strong><br>
                        <span class="badge bg-<?php echo $diagnosis_results['user_info']['status'] === 'active' ? 'success' : 'danger'; ?>">
                            <?php echo $diagnosis_results['user_info']['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- الصلاحيات المحفوظة -->
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5><i class="fas fa-database me-2"></i>الصلاحيات المحفوظة في قاعدة البيانات</h5>
            </div>
            <div class="card-body">
                <?php if (empty($diagnosis_results['stored_permissions'])): ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>مشكلة:</strong> لا توجد صلاحيات محفوظة لهذا المستخدم في قاعدة البيانات!
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الصلاحية</th>
                                    <th>ممنوحة</th>
                                    <th>منحت بواسطة</th>
                                    <th>تاريخ المنح</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($diagnosis_results['stored_permissions'] as $perm): ?>
                                    <tr>
                                        <td><code><?php echo safe_html($perm['permission_key']); ?></code></td>
                                        <td>
                                            <span class="badge bg-<?php echo $perm['is_granted'] ? 'success' : 'danger'; ?>">
                                                <?php echo $perm['is_granted'] ? 'نعم' : 'لا'; ?>
                                            </span>
                                        </td>
                                        <td><?php echo safe_html($perm['granted_by']); ?></td>
                                        <td><?php echo safe_html($perm['granted_at']); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- اختبار دالة has_permission -->
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5><i class="fas fa-code me-2"></i>اختبار دالة has_permission</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php foreach ($diagnosis_results['function_test'] as $permission => $result): ?>
                        <div class="col-md-4 mb-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <span><?php echo $permission; ?></span>
                                <span class="badge bg-<?php echo $result ? 'success' : 'danger'; ?>">
                                    <?php echo $result ? 'متاح' : 'مرفوض'; ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- اختبار قاعدة البيانات المباشر -->
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h5><i class="fas fa-database me-2"></i>اختبار قاعدة البيانات المباشر</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php foreach ($diagnosis_results['direct_db_test'] as $permission => $result): ?>
                        <div class="col-md-4 mb-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <span><?php echo $permission; ?></span>
                                <span class="badge bg-<?php echo $result ? 'success' : 'danger'; ?>">
                                    <?php echo $result ? 'موجود' : 'غير موجود'; ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- اختبار دالة check_permission -->
        <div class="card mb-4">
            <div class="card-header bg-secondary text-white">
                <h5><i class="fas fa-shield-alt me-2"></i>اختبار دالة check_permission</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php foreach ($diagnosis_results['check_permission_test'] as $role => $result): ?>
                        <div class="col-md-3 mb-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <span><?php echo $role; ?></span>
                                <span class="badge bg-<?php echo $result ? 'success' : 'danger'; ?>">
                                    <?php echo $result ? 'متاح' : 'مرفوض'; ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- اختبار الوصول للصفحات -->
        <div class="card mb-4">
            <div class="card-header bg-dark text-white">
                <h5><i class="fas fa-file me-2"></i>اختبار الوصول للصفحات</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php foreach ($diagnosis_results['page_access_test'] as $page => $result): ?>
                        <div class="col-md-6 mb-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <span><?php echo str_replace('_', ' ', $page); ?></span>
                                <span class="badge bg-<?php echo $result ? 'success' : 'danger'; ?>">
                                    <?php echo $result ? 'متاح' : 'مرفوض'; ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- التشخيص والحلول -->
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h5><i class="fas fa-diagnoses me-2"></i>التشخيص والحلول المقترحة</h5>
            </div>
            <div class="card-body">
                <?php
                $issues_found = [];
                $solutions = [];
                
                // فحص المشاكل
                if (empty($diagnosis_results['stored_permissions'])) {
                    $issues_found[] = "لا توجد صلاحيات محفوظة في قاعدة البيانات";
                    $solutions[] = "استخدم أداة إدارة الصلاحيات المخصصة لمنح صلاحيات للمستخدم";
                }
                
                $working_permissions = array_filter($diagnosis_results['function_test']);
                if (empty($working_permissions)) {
                    $issues_found[] = "دالة has_permission لا تعمل بشكل صحيح";
                    $solutions[] = "تحقق من دالة has_permission في ملف functions.php";
                }
                
                $working_check_permissions = array_filter($diagnosis_results['check_permission_test']);
                if (empty($working_check_permissions)) {
                    $issues_found[] = "دالة check_permission لا تعمل بشكل صحيح";
                    $solutions[] = "تحقق من دالة check_permission في ملف functions.php";
                }
                
                if ($diagnosis_results['user_info']['role'] !== 'staff') {
                    $issues_found[] = "دور المستخدم ليس 'staff' - الدور الحالي: " . $diagnosis_results['user_info']['role'];
                    $solutions[] = "غير دور المستخدم إلى 'staff' أو تأكد من أن الصفحات تدعم الدور الحالي";
                }
                ?>
                
                <?php if (empty($issues_found)): ?>
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>لا توجد مشاكل واضحة!</h6>
                        <p class="mb-0">جميع الاختبارات تشير إلى أن النظام يعمل بشكل صحيح. قد تحتاج لتسجيل خروج وإعادة دخول.</p>
                    </div>
                <?php else: ?>
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>المشاكل المكتشفة:</h6>
                        <ul class="mb-0">
                            <?php foreach ($issues_found as $issue): ?>
                                <li><?php echo $issue; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb me-2"></i>الحلول المقترحة:</h6>
                        <ol class="mb-0">
                            <?php foreach ($solutions as $solution): ?>
                                <li><?php echo $solution; ?></li>
                            <?php endforeach; ?>
                        </ol>
                    </div>
                <?php endif; ?>
                
                <div class="text-center mt-4">
                    <a href="custom_permissions_manager.php?user_id=<?php echo $diagnosis_results['user_info']['id']; ?>" class="btn btn-primary me-2">
                        <i class="fas fa-user-cog me-2"></i>إدارة صلاحيات هذا المستخدم
                    </a>
                    <a href="fix_user_permissions.php?user_id=<?php echo $diagnosis_results['user_info']['id']; ?>" class="btn btn-success me-2">
                        <i class="fas fa-tools me-2"></i>إصلاح صلاحيات المستخدم
                    </a>
                    <button class="btn btn-info" onclick="window.location.reload()">
                        <i class="fas fa-redo me-2"></i>إعادة التشخيص
                    </button>
                </div>
            </div>
        </div>

    <?php endif; ?>
</div>

<?php include_once '../includes/footer.php'; ?>
