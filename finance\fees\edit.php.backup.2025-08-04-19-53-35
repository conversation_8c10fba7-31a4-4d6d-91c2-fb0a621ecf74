<?php
require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/security.php';

// التحقق من الجلسة والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

$error_message = '';
$success_message = '';

// جلب معرف الرسم
$fee_id = intval($_GET['id'] ?? 0);

if ($fee_id <= 0) {
    header('Location: index.php?error=' . urlencode('معرف الرسم غير صحيح'));
    exit();
}

// جلب بيانات الرسم الحالية
$stmt = $conn->prepare("
    SELECT 
        sf.*,
        u.full_name as student_name,
        s.student_id as student_number,
        c.class_name,
        ft.type_name as fee_type_name
    FROM student_fees sf
    JOIN students s ON sf.student_id = s.id
    JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    LEFT JOIN fee_types ft ON sf.fee_type_id = ft.id
    WHERE sf.id = ?
");

$stmt->bind_param("i", $fee_id);
$stmt->execute();
$fee = $stmt->get_result()->fetch_assoc();

if (!$fee) {
    header('Location: index.php?error=' . urlencode('الرسم غير موجود'));
    exit();
}

// معالجة تعديل الرسم
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من رمز CSRF
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = __('invalid_request');
    } else {
        // جمع البيانات وتنظيفها
        $fee_type_id = intval($_POST['fee_type_id'] ?? $fee['fee_type_id']);
        $semester = clean_input($_POST['semester'] ?? $fee['semester']);
        $base_amount = floatval($_POST['base_amount'] ?? $fee['base_amount']);
        $discount_amount = floatval($_POST['discount_amount'] ?? $fee['discount_amount']);
        $due_date = clean_input($_POST['due_date'] ?? $fee['due_date']);
        $status = clean_input($_POST['status'] ?? $fee['status']);
        $notes = clean_input($_POST['notes'] ?? $fee['notes']);
        
        // التحقق من صحة البيانات
        if ($fee_type_id <= 0) {
            $error_message = __('invalid_fee_type');
        } elseif ($base_amount <= 0) {
            $error_message = __('invalid_amount');
        } elseif (empty($due_date)) {
            $error_message = __('invalid_due_date');
        } else {
            try {
                // بدء المعاملة
                $conn->begin_transaction();
                
                // حساب المبلغ النهائي
                $final_amount = $base_amount - $discount_amount;

                // للرسوم: لا نحتاج لحساب المتبقي - إما مدفوعة أو غير مدفوعة
                $remaining_amount = ($fee['status'] === 'paid') ? 0 : $final_amount;

                // تحديد الحالة بناءً على تاريخ الاستحقاق والحالة الحالية
                if ($fee['status'] === 'paid') {
                    $status = 'paid'; // لا نغير الحالة إذا كانت مدفوعة
                } elseif (strtotime($due_date) < time() && $status !== 'paid') {
                    $status = 'overdue';
                } else {
                    $status = 'pending';
                }
                
                // تحديث الرسم
                $update_stmt = $conn->prepare("
                    UPDATE student_fees 
                    SET fee_type_id = ?, semester = ?, base_amount = ?, discount_amount = ?, 
                        final_amount = ?, remaining_amount = ?, due_date = ?, status = ?, 
                        notes = ?, updated_at = NOW() 
                    WHERE id = ?
                ");
                
                if ($update_stmt) {
                    $update_stmt->bind_param("isddddsssi", $fee_type_id, $semester, $base_amount, $discount_amount, $final_amount, $remaining_amount, $due_date, $status, $notes, $fee_id);
                    
                    if ($update_stmt->execute()) {
                        $conn->commit();
                        header("Location: view.php?id=" . $fee_id . "&edit_success=1");
                        exit();
                    } else {
                        throw new Exception(__('database_error') . ': ' . $conn->error);
                    }
                } else {
                    throw new Exception(__('database_error') . ': ' . $conn->error);
                }
                
            } catch (Exception $e) {
                $conn->rollback();
                $error_message = $e->getMessage();
            }
        }
    }
}

// جلب أنواع الرسوم
$fee_types = $conn->query("SELECT * FROM fee_types WHERE status = 'active' ORDER BY type_name");

$page_title = __('edit_fee');
include_once '../../includes/header.php';
?>

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0"><i class="fas fa-edit me-2"></i><?php echo __('edit_fee'); ?></h4>
                        <div class="d-flex gap-2">
                            <a href="view.php?id=<?php echo $fee['id']; ?>" class="btn btn-light btn-sm">
                                <i class="fas fa-eye me-1"></i><?php echo __('view'); ?>
                            </a>
                            <a href="index.php" class="btn btn-light btn-sm">
                                <i class="fas fa-arrow-left me-1"></i><?php echo __('back'); ?>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                    </div>
                    <?php endif; ?>
                    
                    <!-- معلومات الطالب (للقراءة فقط) -->
                    <div class="card border-info mb-4">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0"><i class="fas fa-user-graduate me-2"></i><?php echo __('student_information'); ?> (<?php echo __('read_only'); ?>)</h6>
                        </div>
                        <div class="card-body bg-light">
                            <div class="row">
                                <div class="col-md-4">
                                    <strong><?php echo __('student_name'); ?>:</strong> <?php echo htmlspecialchars($fee['student_name']); ?>
                                </div>
                                <div class="col-md-4">
                                    <strong><?php echo __('student_number'); ?>:</strong> <?php echo htmlspecialchars($fee['student_number']); ?>
                                </div>
                                <div class="col-md-4">
                                    <strong><?php echo __('class'); ?>:</strong> <?php echo htmlspecialchars($fee['class_name'] ?? 'غير محدد'); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        
                        <!-- تفاصيل الرسم -->
                        <div class="card border-success mb-4">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0"><i class="fas fa-file-invoice-dollar me-2"></i><?php echo __('fee_details'); ?></h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label"><?php echo __('fee_type'); ?> <span class="text-danger">*</span></label>
                                            <select class="form-select" name="fee_type_id" required>
                                                <?php while ($fee_type = $fee_types->fetch_assoc()): ?>
                                                    <option value="<?php echo $fee_type['id']; ?>" <?php echo ($fee_type['id'] == $fee['fee_type_id']) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($fee_type['type_name']); ?>
                                                    </option>
                                                <?php endwhile; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label"><?php echo __('semester'); ?> <span class="text-danger">*</span></label>
                                            <select class="form-select" name="semester" required>
                                                <option value="first" <?php echo ($fee['semester'] == 'first') ? 'selected' : ''; ?>><?php echo __('first_semester'); ?></option>
                                                <option value="second" <?php echo ($fee['semester'] == 'second') ? 'selected' : ''; ?>><?php echo __('second_semester'); ?></option>
                                                <option value="annual" <?php echo ($fee['semester'] == 'annual') ? 'selected' : ''; ?>><?php echo __('annual'); ?></option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label"><?php echo __('base_amount'); ?> <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" name="base_amount" id="base_amount" min="0" step="0.01" value="<?php echo $fee['base_amount']; ?>" required onchange="calculateFinal()">
                                                <span class="input-group-text"><?php echo get_currency_symbol(); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label"><?php echo __('discount_amount'); ?></label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" name="discount_amount" id="discount_amount" min="0" step="0.01" value="<?php echo $fee['discount_amount']; ?>" onchange="calculateFinal()">
                                                <span class="input-group-text"><?php echo get_currency_symbol(); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label"><?php echo __('due_date'); ?> <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control" name="due_date" value="<?php echo date('Y-m-d', strtotime($fee['due_date'])); ?>" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- عرض المبلغ النهائي -->
                                <div class="alert alert-info" id="final_amount_alert">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span><strong><?php echo __('final_amount'); ?>:</strong></span>
                                        <span class="fs-5 fw-bold" id="final_amount"><?php echo number_format($fee['final_amount'], 2); ?> <?php echo get_currency_symbol(); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- حالة الرسم الحالية (للقراءة فقط) -->
                        <div class="card border-info mb-4">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i><?php echo __('current_fee_status'); ?> (<?php echo __('read_only'); ?>)</h6>
                            </div>
                            <div class="card-body bg-light">
                                <div class="text-center">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="card border-primary">
                                                <div class="card-body">
                                                    <h5 class="text-primary"><?php echo number_format($fee['final_amount'], 2); ?></h5>
                                                    <small class="text-muted"><?php echo __('current_fee_amount'); ?></small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="card border-<?php echo $fee['status'] == 'paid' ? 'success' : ($fee['status'] == 'overdue' ? 'danger' : 'warning'); ?>">
                                                <div class="card-body">
                                                    <span class="badge bg-<?php echo $fee['status'] == 'paid' ? 'success' : ($fee['status'] == 'overdue' ? 'danger' : 'warning'); ?> fs-6">
                                                        <?php echo __($fee['status']); ?>
                                                    </span>
                                                    <br>
                                                    <small class="text-muted"><?php echo __('current_status'); ?></small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <small class="text-muted"><?php echo get_currency_symbol(); ?></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- ملاحظات -->
                        <div class="mb-4">
                            <label class="form-label"><?php echo __('notes'); ?></label>
                            <textarea class="form-control" name="notes" rows="3" placeholder="<?php echo __('notes_placeholder'); ?>"><?php echo htmlspecialchars($fee['notes']); ?></textarea>
                        </div>
                        
                        <!-- أزرار التحكم -->
                        <div class="d-flex justify-content-between">
                            <a href="view.php?id=<?php echo $fee['id']; ?>" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i><?php echo __('back'); ?>
                            </a>
                            <button type="submit" class="btn btn-warning btn-lg">
                                <i class="fas fa-save me-2"></i><?php echo __('save_changes'); ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function calculateFinal() {
    const baseAmount = parseFloat(document.getElementById('base_amount').value) || 0;
    const discountAmount = parseFloat(document.getElementById('discount_amount').value) || 0;
    
    const finalAmount = baseAmount - discountAmount;
    const finalAmountSpan = document.getElementById('final_amount');
    
    finalAmountSpan.textContent = finalAmount.toFixed(2) + ' <?php echo get_currency_symbol(); ?>';
    
    // تغيير لون التنبيه
    const alert = document.getElementById('final_amount_alert');
    alert.className = 'alert ';
    if (discountAmount > 0) {
        alert.className += 'alert-success';
    } else {
        alert.className += 'alert-info';
    }
}

document.addEventListener('DOMContentLoaded', function() {
    calculateFinal();
});
</script>

<?php include_once '../../includes/footer.php'; ?>
