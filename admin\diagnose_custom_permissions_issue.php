<?php
/**
 * تشخيص شامل لمشكلة عدم عمل الصلاحيات المخصصة
 * Comprehensive Diagnosis of Custom Permissions Issue
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

$diagnosis = [];
$issues_found = [];
$solutions = [];

// 1. فحص وجود جدول الصلاحيات المخصصة
$diagnosis['table_exists'] = false;
try {
    $table_check = $conn->query("SHOW TABLES LIKE 'user_custom_permissions'");
    if ($table_check && $table_check->num_rows > 0) {
        $diagnosis['table_exists'] = true;
        
        // فحص بنية الجدول
        $structure = $conn->query("DESCRIBE user_custom_permissions");
        $columns = [];
        while ($row = $structure->fetch_assoc()) {
            $columns[] = $row['Field'];
        }
        $diagnosis['table_structure'] = $columns;
        
        // عد الصلاحيات الموجودة
        $count_result = $conn->query("SELECT COUNT(*) as total FROM user_custom_permissions");
        $diagnosis['permissions_count'] = $count_result->fetch_assoc()['total'];
        
    } else {
        $issues_found[] = "جدول user_custom_permissions غير موجود";
        $solutions[] = "استخدم أداة إعداد نظام الصلاحيات المخصصة";
    }
} catch (Exception $e) {
    $issues_found[] = "خطأ في فحص جدول الصلاحيات: " . $e->getMessage();
}

// 2. فحص دالة has_permission
$diagnosis['has_permission_function'] = false;
if (function_exists('has_permission')) {
    $diagnosis['has_permission_function'] = true;
    
    // اختبار الدالة مع مستخدم وهمي
    $original_session = $_SESSION;
    $_SESSION['user_id'] = 999; // مستخدم وهمي
    $_SESSION['role'] = 'staff';
    
    try {
        $test_result = has_permission('test_permission');
        $diagnosis['function_works'] = true;
    } catch (Exception $e) {
        $diagnosis['function_works'] = false;
        $issues_found[] = "دالة has_permission لا تعمل: " . $e->getMessage();
        $solutions[] = "تحقق من دالة has_permission في ملف functions.php";
    }
    
    $_SESSION = $original_session;
} else {
    $issues_found[] = "دالة has_permission غير موجودة";
    $solutions[] = "تأكد من تحميل ملف functions.php بشكل صحيح";
}

// 3. فحص المستخدمين الذين لديهم صلاحيات مخصصة
$diagnosis['users_with_permissions'] = [];
if ($diagnosis['table_exists']) {
    try {
        $users_query = "
            SELECT u.id, u.full_name, u.email, u.role, 
                   COUNT(ucp.id) as permissions_count
            FROM users u
            LEFT JOIN user_custom_permissions ucp ON u.id = ucp.user_id
            WHERE u.role != 'admin'
            GROUP BY u.id
            HAVING permissions_count > 0
            ORDER BY u.full_name
        ";
        $users_result = $conn->query($users_query);
        
        while ($user = $users_result->fetch_assoc()) {
            // جلب صلاحيات المستخدم
            $perms_query = "SELECT permission_key FROM user_custom_permissions WHERE user_id = ?";
            $perms_stmt = $conn->prepare($perms_query);
            $perms_stmt->bind_param("i", $user['id']);
            $perms_stmt->execute();
            $perms_result = $perms_stmt->get_result();
            
            $user_permissions = [];
            while ($perm = $perms_result->fetch_assoc()) {
                $user_permissions[] = $perm['permission_key'];
            }
            $user['permissions'] = $user_permissions;
            
            $diagnosis['users_with_permissions'][] = $user;
        }
    } catch (Exception $e) {
        $issues_found[] = "خطأ في جلب المستخدمين: " . $e->getMessage();
    }
}

// 4. اختبار الصلاحيات مع مستخدم حقيقي
$test_user_email = '<EMAIL>';
$diagnosis['test_user_results'] = [];

try {
    $user_query = "SELECT * FROM users WHERE email = ?";
    $user_stmt = $conn->prepare($user_query);
    $user_stmt->bind_param("s", $test_user_email);
    $user_stmt->execute();
    $test_user = $user_stmt->get_result()->fetch_assoc();
    
    if ($test_user) {
        $diagnosis['test_user_exists'] = true;
        $diagnosis['test_user_info'] = $test_user;
        
        // محاكاة جلسة المستخدم
        $original_session = $_SESSION;
        $_SESSION['user_id'] = $test_user['id'];
        $_SESSION['role'] = $test_user['role'];
        $_SESSION['email'] = $test_user['email'];
        
        // اختبار صلاحيات مختلفة
        $test_permissions = [
            'staff_access',
            'students_view',
            'student_affairs',
            'teachers_view',
            'staff_affairs',
            'classes_view',
            'reports_view'
        ];
        
        foreach ($test_permissions as $permission) {
            $has_perm = has_permission($permission);
            $diagnosis['test_user_results'][$permission] = $has_perm;
        }
        
        $_SESSION = $original_session;
    } else {
        $diagnosis['test_user_exists'] = false;
        $issues_found[] = "المستخدم التجريبي غير موجود: $test_user_email";
    }
} catch (Exception $e) {
    $issues_found[] = "خطأ في اختبار المستخدم: " . $e->getMessage();
}

// 5. فحص استخدام الصلاحيات في header.php
$diagnosis['header_usage'] = [];
$header_file = '../includes/header.php';
if (file_exists($header_file)) {
    $header_content = file_get_contents($header_file);
    
    // البحث عن استخدامات has_permission
    preg_match_all('/has_permission\([\'"]([^\'"]+)[\'"]\)/', $header_content, $matches);
    $diagnosis['header_usage']['has_permission_calls'] = $matches[1] ?? [];
    
    // البحث عن استخدامات check_permission
    preg_match_all('/check_permission\([\'"]([^\'"]+)[\'"]\)/', $header_content, $matches2);
    $diagnosis['header_usage']['check_permission_calls'] = $matches2[1] ?? [];
    
    if (empty($diagnosis['header_usage']['has_permission_calls'])) {
        $issues_found[] = "header.php لا يستخدم has_permission للصلاحيات المخصصة";
        $solutions[] = "تحديث header.php لاستخدام الصلاحيات المخصصة";
    }
}

// 6. فحص الصفحات الفردية
$diagnosis['page_usage'] = [];
$sample_pages = ['students/index.php', 'teachers/index.php', 'classes/index.php'];

foreach ($sample_pages as $page) {
    $page_path = "../$page";
    if (file_exists($page_path)) {
        $page_content = file_get_contents($page_path);
        
        $page_info = [
            'exists' => true,
            'uses_has_permission' => strpos($page_content, 'has_permission(') !== false,
            'uses_check_permission' => strpos($page_content, 'check_permission(') !== false
        ];
        
        if (!$page_info['uses_has_permission']) {
            $issues_found[] = "صفحة $page لا تستخدم has_permission";
            $solutions[] = "تحديث صفحة $page لاستخدام الصلاحيات المخصصة";
        }
        
        $diagnosis['page_usage'][$page] = $page_info;
    }
}

// تحديد المشاكل الرئيسية
if (empty($diagnosis['users_with_permissions'])) {
    $issues_found[] = "لا يوجد مستخدمون لديهم صلاحيات مخصصة";
    $solutions[] = "استخدم أداة إدارة الصلاحيات المخصصة لمنح صلاحيات للمستخدمين";
}

if (isset($diagnosis['test_user_results']) && empty(array_filter($diagnosis['test_user_results']))) {
    $issues_found[] = "المستخدم التجريبي لا يملك أي صلاحيات فعالة";
    $solutions[] = "استخدم أداة إصلاح صلاحيات المستخدم";
}

$page_title = 'تشخيص مشكلة الصلاحيات المخصصة';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-microscope me-2"></i><?php echo $page_title; ?></h2>
            <p class="text-muted">تشخيص شامل لمشكلة عدم عمل الصلاحيات المخصصة</p>
        </div>
        <a href="../settings/permissions.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة
        </a>
    </div>

    <!-- ملخص المشاكل -->
    <?php if (!empty($issues_found)): ?>
        <div class="alert alert-danger">
            <h5><i class="fas fa-exclamation-triangle me-2"></i>المشاكل المكتشفة:</h5>
            <ul class="mb-0">
                <?php foreach ($issues_found as $issue): ?>
                    <li><?php echo $issue; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php else: ?>
        <div class="alert alert-success">
            <h5><i class="fas fa-check-circle me-2"></i>لا توجد مشاكل واضحة!</h5>
            <p class="mb-0">جميع الفحوصات تشير إلى أن النظام يعمل بشكل صحيح.</p>
        </div>
    <?php endif; ?>

    <!-- الحلول المقترحة -->
    <?php if (!empty($solutions)): ?>
        <div class="alert alert-info">
            <h5><i class="fas fa-lightbulb me-2"></i>الحلول المقترحة:</h5>
            <ol class="mb-0">
                <?php foreach ($solutions as $solution): ?>
                    <li><?php echo $solution; ?></li>
                <?php endforeach; ?>
            </ol>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- فحص قاعدة البيانات -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5><i class="fas fa-database me-2"></i>فحص قاعدة البيانات</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <strong>وجود الجدول:</strong><br>
                            <span class="badge bg-<?php echo $diagnosis['table_exists'] ? 'success' : 'danger'; ?>">
                                <?php echo $diagnosis['table_exists'] ? 'موجود' : 'غير موجود'; ?>
                            </span>
                        </div>
                        <div class="col-6">
                            <strong>عدد الصلاحيات:</strong><br>
                            <span class="badge bg-info">
                                <?php echo $diagnosis['permissions_count'] ?? 0; ?>
                            </span>
                        </div>
                    </div>
                    
                    <?php if (isset($diagnosis['table_structure'])): ?>
                        <div class="mt-3">
                            <strong>أعمدة الجدول:</strong><br>
                            <small><?php echo implode(', ', $diagnosis['table_structure']); ?></small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- فحص الدوال -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-code me-2"></i>فحص الدوال</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <strong>دالة has_permission:</strong><br>
                            <span class="badge bg-<?php echo $diagnosis['has_permission_function'] ? 'success' : 'danger'; ?>">
                                <?php echo $diagnosis['has_permission_function'] ? 'موجودة' : 'غير موجودة'; ?>
                            </span>
                        </div>
                        <div class="col-6">
                            <strong>تعمل بشكل صحيح:</strong><br>
                            <span class="badge bg-<?php echo ($diagnosis['function_works'] ?? false) ? 'success' : 'warning'; ?>">
                                <?php echo ($diagnosis['function_works'] ?? false) ? 'نعم' : 'غير مؤكد'; ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- المستخدمون الذين لديهم صلاحيات -->
    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5><i class="fas fa-users me-2"></i>المستخدمون الذين لديهم صلاحيات مخصصة</h5>
        </div>
        <div class="card-body">
            <?php if (empty($diagnosis['users_with_permissions'])): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    لا يوجد مستخدمون لديهم صلاحيات مخصصة!
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>المستخدم</th>
                                <th>الدور</th>
                                <th>عدد الصلاحيات</th>
                                <th>الصلاحيات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($diagnosis['users_with_permissions'] as $user): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo safe_html($user['full_name']); ?></strong><br>
                                        <small><?php echo safe_html($user['email']); ?></small>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary"><?php echo $user['role']; ?></span>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary"><?php echo $user['permissions_count']; ?></span>
                                    </td>
                                    <td>
                                        <small><?php echo implode(', ', array_slice($user['permissions'], 0, 3)); ?>
                                        <?php if (count($user['permissions']) > 3): ?>
                                            <br>وأخرى...
                                        <?php endif; ?>
                                        </small>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- اختبار المستخدم -->
    <?php if (isset($diagnosis['test_user_results'])): ?>
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h5><i class="fas fa-user-check me-2"></i>اختبار المستخدم: <?php echo $test_user_email; ?></h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php foreach ($diagnosis['test_user_results'] as $permission => $has_perm): ?>
                        <div class="col-md-4 mb-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <span><?php echo $permission; ?></span>
                                <span class="badge bg-<?php echo $has_perm ? 'success' : 'danger'; ?>">
                                    <?php echo $has_perm ? 'متاح' : 'مرفوض'; ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- أدوات الإصلاح -->
    <div class="card">
        <div class="card-header bg-dark text-white">
            <h5><i class="fas fa-tools me-2"></i>أدوات الإصلاح</h5>
        </div>
        <div class="card-body">
            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                <?php if (!$diagnosis['table_exists']): ?>
                    <a href="setup_custom_permissions.php" class="btn btn-danger">
                        <i class="fas fa-database me-2"></i>إنشاء جداول الصلاحيات
                    </a>
                <?php endif; ?>
                
                <?php if (empty($diagnosis['users_with_permissions'])): ?>
                    <a href="custom_permissions_manager.php" class="btn btn-warning">
                        <i class="fas fa-user-cog me-2"></i>منح صلاحيات للمستخدمين
                    </a>
                <?php endif; ?>
                
                <a href="fix_user_permissions.php" class="btn btn-success">
                    <i class="fas fa-user-shield me-2"></i>إصلاح صلاحيات المستخدم
                </a>
                
                <a href="fix_custom_permissions_system.php" class="btn btn-primary">
                    <i class="fas fa-magic me-2"></i>إصلاح شامل للنظام
                </a>
                
                <button class="btn btn-info" onclick="window.location.reload()">
                    <i class="fas fa-redo me-2"></i>إعادة التشخيص
                </button>
            </div>
        </div>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
