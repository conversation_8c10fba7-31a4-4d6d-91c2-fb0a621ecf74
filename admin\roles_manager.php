<?php
/**
 * إدارة الأدوار المخصصة
 * Custom Roles Management
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/enhanced_permissions.php';

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من الصلاحيات
check_session();
if (defined('ENHANCED_PERMISSIONS_LOADED') && function_exists('enhanced_require_permission')) {
    enhanced_require_permission('settings_permissions', 'full');
} else {
    // العودة للتحقق التقليدي
    if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
        header('Location: ../dashboard/?error=access_denied');
        exit();
    }
}

$success_message = '';
$error_message = '';

// معالجة إضافة دور جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_role'])) {
    $role_name = clean_input($_POST['role_name']);
    $role_display_name = clean_input($_POST['role_display_name']);
    $role_description = clean_input($_POST['role_description']);
    $permissions = $_POST['permissions'] ?? [];
    
    if (!empty($role_name) && !empty($role_display_name)) {
        $conn->begin_transaction();
        try {
            // إضافة الدور
            $stmt = $conn->prepare("
                INSERT INTO custom_roles (role_name, role_display_name, role_description, is_system_role, created_by) 
                VALUES (?, ?, ?, 0, ?)
            ");
            $stmt->bind_param("sssi", $role_name, $role_display_name, $role_description, $_SESSION['user_id']);
            $stmt->execute();
            
            // إضافة صلاحيات الدور
            if (!empty($permissions)) {
                $perm_stmt = $conn->prepare("
                    INSERT INTO role_permissions (role_name, resource_key, permission_level, granted_by) 
                    VALUES (?, ?, ?, ?)
                ");
                
                foreach ($permissions as $resource_key => $permission_level) {
                    if (!empty($permission_level) && $permission_level !== 'none') {
                        $perm_stmt->bind_param("sssi", $role_name, $resource_key, $permission_level, $_SESSION['user_id']);
                        $perm_stmt->execute();
                    }
                }
            }
            
            $conn->commit();
            $success_message = "تم إضافة الدور بنجاح";
            
            // تسجيل في سجل التدقيق
            log_permission_audit('role_created', null, null, $role_name, $_SESSION['user_id'], "تم إنشاء دور جديد: $role_display_name");
            
        } catch (Exception $e) {
            $conn->rollback();
            $error_message = "خطأ في إضافة الدور: " . $e->getMessage();
        }
    } else {
        $error_message = "يرجى تعبئة جميع الحقول المطلوبة";
    }
}

// معالجة تعديل دور
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['edit_role'])) {
    $role_id = intval($_POST['role_id']);
    $role_display_name = clean_input($_POST['role_display_name']);
    $role_description = clean_input($_POST['role_description']);
    $permissions = $_POST['permissions'] ?? [];
    
    if ($role_id > 0 && !empty($role_display_name)) {
        $conn->begin_transaction();
        try {
            // تحديث بيانات الدور
            $stmt = $conn->prepare("
                UPDATE custom_roles 
                SET role_display_name = ?, role_description = ?, updated_at = NOW() 
                WHERE id = ? AND is_system_role = 0
            ");
            $stmt->bind_param("ssi", $role_display_name, $role_description, $role_id);
            $stmt->execute();
            
            // الحصول على اسم الدور
            $role_stmt = $conn->prepare("SELECT role_name FROM custom_roles WHERE id = ?");
            $role_stmt->bind_param("i", $role_id);
            $role_stmt->execute();
            $role_data = $role_stmt->get_result()->fetch_assoc();
            $role_name = $role_data['role_name'];
            
            // حذف الصلاحيات القديمة
            $delete_stmt = $conn->prepare("DELETE FROM role_permissions WHERE role_name = ?");
            $delete_stmt->bind_param("s", $role_name);
            $delete_stmt->execute();
            
            // إضافة الصلاحيات الجديدة
            if (!empty($permissions)) {
                $perm_stmt = $conn->prepare("
                    INSERT INTO role_permissions (role_name, resource_key, permission_level, granted_by) 
                    VALUES (?, ?, ?, ?)
                ");
                
                foreach ($permissions as $resource_key => $permission_level) {
                    if (!empty($permission_level) && $permission_level !== 'none') {
                        $perm_stmt->bind_param("sssi", $role_name, $resource_key, $permission_level, $_SESSION['user_id']);
                        $perm_stmt->execute();
                    }
                }
            }
            
            $conn->commit();
            $success_message = "تم تحديث الدور بنجاح";
            
            // تسجيل في سجل التدقيق
            log_permission_audit('role_updated', null, null, $role_name, $_SESSION['user_id'], "تم تحديث الدور: $role_display_name");
            
        } catch (Exception $e) {
            $conn->rollback();
            $error_message = "خطأ في تحديث الدور: " . $e->getMessage();
        }
    }
}

// معالجة حذف دور
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_role'])) {
    $role_id = intval($_POST['role_id']);
    
    if ($role_id > 0) {
        $conn->begin_transaction();
        try {
            // التحقق من عدم وجود مستخدمين يستخدمون هذا الدور
            $check_stmt = $conn->prepare("
                SELECT COUNT(*) as count FROM users u 
                JOIN custom_roles cr ON u.role = cr.role_name 
                WHERE cr.id = ?
            ");
            $check_stmt->bind_param("i", $role_id);
            $check_stmt->execute();
            $count = $check_stmt->get_result()->fetch_assoc()['count'];
            
            if ($count > 0) {
                throw new Exception("لا يمكن حذف الدور لأنه مستخدم من قبل $count مستخدم");
            }
            
            // الحصول على اسم الدور
            $role_stmt = $conn->prepare("SELECT role_name, role_display_name FROM custom_roles WHERE id = ? AND is_system_role = 0");
            $role_stmt->bind_param("i", $role_id);
            $role_stmt->execute();
            $role_data = $role_stmt->get_result()->fetch_assoc();
            
            if (!$role_data) {
                throw new Exception("الدور غير موجود أو لا يمكن حذفه");
            }
            
            // حذف صلاحيات الدور
            $delete_perms = $conn->prepare("DELETE FROM role_permissions WHERE role_name = ?");
            $delete_perms->bind_param("s", $role_data['role_name']);
            $delete_perms->execute();
            
            // حذف الدور
            $delete_role = $conn->prepare("DELETE FROM custom_roles WHERE id = ? AND is_system_role = 0");
            $delete_role->bind_param("i", $role_id);
            $delete_role->execute();
            
            $conn->commit();
            $success_message = "تم حذف الدور بنجاح";
            
            // تسجيل في سجل التدقيق
            log_permission_audit('role_deleted', null, $role_data['role_name'], null, $_SESSION['user_id'], "تم حذف الدور: " . $role_data['role_display_name']);
            
        } catch (Exception $e) {
            $conn->rollback();
            $error_message = "خطأ في حذف الدور: " . $e->getMessage();
        }
    }
}

// جلب الأدوار
$roles_query = "
    SELECT cr.*, 
           (SELECT COUNT(*) FROM users u WHERE u.role = cr.role_name) as users_count,
           (SELECT COUNT(*) FROM role_permissions rp WHERE rp.role_name = cr.role_name) as permissions_count
    FROM custom_roles cr 
    ORDER BY cr.is_system_role DESC, cr.role_display_name
";
$roles_result = $conn->query($roles_query);

// جلب الموارد
$resources_query = "
    SELECT resource_key, resource_name, resource_type, resource_description, icon
    FROM system_resources 
    WHERE is_active = 1 
    ORDER BY resource_type, sort_order, resource_name
";
$resources_result = $conn->query($resources_query);
$system_resources = [];
while ($row = $resources_result->fetch_assoc()) {
    $system_resources[] = $row;
}

$page_title = 'إدارة الأدوار المخصصة';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-user-tag me-2 text-primary"></i><?php echo $page_title; ?></h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="../settings/">الإعدادات</a></li>
                    <li class="breadcrumb-item"><a href="enhanced_permissions_manager.php">إدارة الصلاحيات</a></li>
                    <li class="breadcrumb-item active">إدارة الأدوار</li>
                </ol>
            </nav>
        </div>
        <div>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRoleModal">
                <i class="fas fa-plus me-1"></i>إضافة دور جديد
            </button>
            <a href="enhanced_permissions_manager.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>العودة للصلاحيات
            </a>
        </div>
    </div>

    <!-- رسائل التنبيه -->
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- قائمة الأدوار -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-list me-2"></i>الأدوار المتاحة</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="rolesTable">
                    <thead>
                        <tr>
                            <th>اسم الدور</th>
                            <th>الوصف</th>
                            <th>النوع</th>
                            <th>عدد المستخدمين</th>
                            <th>عدد الصلاحيات</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while ($role = $roles_result->fetch_assoc()): ?>
                        <tr>
                            <td>
                                <strong><?php echo htmlspecialchars($role['role_display_name']); ?></strong>
                                <br><small class="text-muted"><?php echo htmlspecialchars($role['role_name']); ?></small>
                            </td>
                            <td><?php echo htmlspecialchars($role['role_description'] ?: 'لا يوجد وصف'); ?></td>
                            <td>
                                <span class="badge bg-<?php echo $role['is_system_role'] ? 'warning' : 'info'; ?>">
                                    <?php echo $role['is_system_role'] ? 'دور النظام' : 'دور مخصص'; ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-primary"><?php echo $role['users_count']; ?> مستخدم</span>
                            </td>
                            <td>
                                <span class="badge bg-success"><?php echo $role['permissions_count']; ?> صلاحية</span>
                            </td>
                            <td><?php echo date('Y-m-d', strtotime($role['created_at'])); ?></td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-info" 
                                            onclick="viewRolePermissions(<?php echo $role['id']; ?>)">
                                        <i class="fas fa-eye"></i> عرض
                                    </button>
                                    <?php if (!$role['is_system_role']): ?>
                                        <button type="button" class="btn btn-outline-primary" 
                                                onclick="editRole(<?php echo $role['id']; ?>)">
                                            <i class="fas fa-edit"></i> تعديل
                                        </button>
                                        <?php if ($role['users_count'] == 0): ?>
                                            <button type="button" class="btn btn-outline-danger" 
                                                    onclick="deleteRole(<?php echo $role['id']; ?>)">
                                                <i class="fas fa-trash"></i> حذف
                                            </button>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>
    <!-- مودال إضافة دور جديد -->
<div class="modal fade" id="addRoleModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة دور جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="add_role" value="1">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="role_name" class="form-label">اسم الدور (بالإنجليزية) *</label>
                                <input type="text" class="form-control" name="role_name" id="role_name"
                                       pattern="[a-z_]+" title="أحرف إنجليزية صغيرة وشرطة سفلية فقط" required>
                                <small class="text-muted">مثال: financial_manager</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="role_display_name" class="form-label">اسم الدور للعرض *</label>
                                <input type="text" class="form-control" name="role_display_name" id="role_display_name" required>
                                <small class="text-muted">مثال: مدير مالي</small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="role_description" class="form-label">وصف الدور</label>
                        <textarea class="form-control" name="role_description" id="role_description" rows="2"></textarea>
                    </div>

                    <hr>

                    <h6>صلاحيات الدور</h6>
                    <div id="addRolePermissions">
                        <!-- سيتم ملء هذا القسم بالجافا سكريبت -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة الدور</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- مودال تعديل دور -->
<div class="modal fade" id="editRoleModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل الدور</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="edit_role" value="1">
                    <input type="hidden" name="role_id" id="edit_role_id">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_role_display_name" class="form-label">اسم الدور للعرض *</label>
                                <input type="text" class="form-control" name="role_display_name" id="edit_role_display_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اسم الدور (بالإنجليزية)</label>
                                <input type="text" class="form-control" id="edit_role_name" readonly>
                                <small class="text-muted">لا يمكن تغيير اسم الدور</small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_role_description" class="form-label">وصف الدور</label>
                        <textarea class="form-control" name="role_description" id="edit_role_description" rows="2"></textarea>
                    </div>

                    <hr>

                    <h6>صلاحيات الدور</h6>
                    <div id="editRolePermissions">
                        <!-- سيتم ملء هذا القسم بالجافا سكريبت -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- مودال عرض صلاحيات الدور -->
<div class="modal fade" id="viewRoleModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">عرض صلاحيات الدور</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="viewRolePermissions">
                    <!-- سيتم ملء هذا القسم بالجافا سكريبت -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

</div>
</div>

<script>
// بيانات الموارد
const systemResources = <?php echo json_encode($system_resources); ?>;
const permissionLevels = {
    'none': 'بدون صلاحية',
    'read': 'قراءة فقط',
    'write': 'قراءة وكتابة',
    'full': 'صلاحية كاملة'
};

// تهيئة الصفحة
$(document).ready(function() {
    // تهيئة DataTable
    $('#rolesTable').DataTable({
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json'
        },
        pageLength: 25,
        order: [[0, 'asc']],
        columnDefs: [
            { orderable: false, targets: [6] }
        ]
    });

    // بناء نموذج الصلاحيات لإضافة دور جديد
    buildPermissionsForm('addRolePermissions', {});
});

// بناء نموذج الصلاحيات
function buildPermissionsForm(containerId, rolePermissions = {}) {
    const container = document.getElementById(containerId);

    // تجميع الموارد حسب النوع
    const resourcesByType = {};
    systemResources.forEach(resource => {
        if (!resourcesByType[resource.resource_type]) {
            resourcesByType[resource.resource_type] = [];
        }
        resourcesByType[resource.resource_type].push(resource);
    });

    // إنشاء تبويبات للأنواع المختلفة
    const tabsHtml = `
        <ul class="nav nav-tabs" id="${containerId}Tabs" role="tablist">
            ${Object.keys(resourcesByType).map((type, index) => `
                <li class="nav-item" role="presentation">
                    <button class="nav-link ${index === 0 ? 'active' : ''}"
                            id="${containerId}-${type}-tab" data-bs-toggle="tab" data-bs-target="#${containerId}-${type}-pane"
                            type="button" role="tab">
                        ${getTypeDisplayName(type)}
                    </button>
                </li>
            `).join('')}
        </ul>
        <div class="tab-content" id="${containerId}TabContent">
            ${Object.entries(resourcesByType).map(([type, resources], index) => `
                <div class="tab-pane fade ${index === 0 ? 'show active' : ''}"
                     id="${containerId}-${type}-pane" role="tabpanel">
                    <div class="mt-3">
                        ${resources.map(resource => `
                            <div class="row mb-2 align-items-center">
                                <div class="col-md-6">
                                    <label class="form-label">
                                        <i class="${resource.icon || 'fas fa-cog'} me-2"></i>
                                        ${resource.resource_name}
                                    </label>
                                    <small class="text-muted d-block">${resource.resource_description || ''}</small>
                                </div>
                                <div class="col-md-6">
                                    <select class="form-select form-select-sm"
                                            name="permissions[${resource.resource_key}]">
                                        <option value="">-- بدون صلاحية --</option>
                                        ${Object.entries(permissionLevels).filter(([level]) => level !== 'none').map(([level, name]) => `
                                            <option value="${level}"
                                                    ${rolePermissions[resource.resource_key] === level ? 'selected' : ''}>
                                                ${name}
                                            </option>
                                        `).join('')}
                                    </select>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `).join('')}
        </div>
    `;

    container.innerHTML = tabsHtml;
}

// عرض صلاحيات الدور
function viewRolePermissions(roleId) {
    fetch(`get_role_permissions.php?role_id=${roleId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                buildViewPermissions(data.role, data.permissions);
                const modal = new bootstrap.Modal(document.getElementById('viewRoleModal'));
                modal.show();
            } else {
                alert('حدث خطأ في جلب صلاحيات الدور');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في جلب صلاحيات الدور');
        });
}

// بناء عرض الصلاحيات
function buildViewPermissions(role, permissions) {
    const container = document.getElementById('viewRolePermissions');

    let html = `
        <div class="mb-3">
            <h6>${role.role_display_name}</h6>
            <p class="text-muted">${role.role_description || 'لا يوجد وصف'}</p>
        </div>
        <hr>
    `;

    if (Object.keys(permissions).length === 0) {
        html += '<p class="text-muted">لا توجد صلاحيات مخصصة لهذا الدور</p>';
    } else {
        // تجميع الصلاحيات حسب النوع
        const permissionsByType = {};
        Object.entries(permissions).forEach(([resourceKey, level]) => {
            const resource = systemResources.find(r => r.resource_key === resourceKey);
            if (resource) {
                if (!permissionsByType[resource.resource_type]) {
                    permissionsByType[resource.resource_type] = [];
                }
                permissionsByType[resource.resource_type].push({
                    resource: resource,
                    level: level
                });
            }
        });

        Object.entries(permissionsByType).forEach(([type, items]) => {
            html += `
                <h6>${getTypeDisplayName(type)}</h6>
                <div class="row">
                    ${items.map(item => `
                        <div class="col-md-6 mb-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <span>
                                    <i class="${item.resource.icon || 'fas fa-cog'} me-2"></i>
                                    ${item.resource.resource_name}
                                </span>
                                <span class="badge bg-${getLevelColor(item.level)}">
                                    ${permissionLevels[item.level]}
                                </span>
                            </div>
                        </div>
                    `).join('')}
                </div>
                <hr>
            `;
        });
    }

    container.innerHTML = html;
}

// تعديل دور
function editRole(roleId) {
    fetch(`get_role_permissions.php?role_id=${roleId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('edit_role_id').value = roleId;
                document.getElementById('edit_role_name').value = data.role.role_name;
                document.getElementById('edit_role_display_name').value = data.role.role_display_name;
                document.getElementById('edit_role_description').value = data.role.role_description || '';

                buildPermissionsForm('editRolePermissions', data.permissions);

                const modal = new bootstrap.Modal(document.getElementById('editRoleModal'));
                modal.show();
            } else {
                alert('حدث خطأ في جلب بيانات الدور');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في جلب بيانات الدور');
        });
}

// حذف دور
function deleteRole(roleId) {
    if (confirm('هل أنت متأكد من حذف هذا الدور؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="delete_role" value="1">
            <input type="hidden" name="role_id" value="${roleId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// الحصول على اسم النوع للعرض
function getTypeDisplayName(type) {
    const typeNames = {
        'page': 'الصفحات',
        'action': 'الإجراءات',
        'data': 'البيانات',
        'report': 'التقارير',
        'feature': 'الميزات'
    };
    return typeNames[type] || type;
}

// الحصول على لون المستوى
function getLevelColor(level) {
    const colors = {
        'read': 'info',
        'write': 'warning',
        'full': 'success'
    };
    return colors[level] || 'secondary';
}
</script>

<?php include_once '../includes/footer.php'; ?>
