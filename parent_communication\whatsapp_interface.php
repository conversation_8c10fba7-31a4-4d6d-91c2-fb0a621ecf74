<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('teacher')) {
    header('Location: ../dashboard/');
    exit();
}

global $conn;

// جلب جهات الاتصال (أولياء الأمور)
$contacts_query = "
    SELECT DISTINCT
        s.id as student_id,
        s.student_id as student_number,
        u.full_name as student_name,
        s.parent_phone,
        c.class_name,
        g.grade_name,
        (SELECT COUNT(*) FROM parent_communications pc WHERE pc.student_id = s.id) as message_count,
        (SELECT MAX(pc.created_at) FROM parent_communications pc WHERE pc.student_id = s.id) as last_message_time,
        (SELECT pc.subject FROM parent_communications pc WHERE pc.student_id = s.id ORDER BY pc.created_at DESC LIMIT 1) as last_message_subject
    FROM students s
    JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    LEFT JOIN grades g ON c.grade_id = g.id
    WHERE s.status = 'active' 
    AND s.parent_phone IS NOT NULL 
    AND s.parent_phone != ''
    ORDER BY last_message_time DESC, u.full_name ASC
";

$contacts_result = $conn->query($contacts_query);
$contacts = [];
if ($contacts_result) {
    while ($row = $contacts_result->fetch_assoc()) {
        $contacts[] = $row;
    }
}

// جلب المحادثة المحددة
$selected_student_id = isset($_GET['student_id']) ? intval($_GET['student_id']) : 0;
$conversation = [];
$selected_contact = null;

if ($selected_student_id > 0) {
    // جلب معلومات جهة الاتصال المحددة
    foreach ($contacts as $contact) {
        if ($contact['student_id'] == $selected_student_id) {
            $selected_contact = $contact;
            break;
        }
    }
    
    // جلب رسائل المحادثة
    $conversation_query = "
        SELECT 
            pc.*,
            sender.full_name as sender_name
        FROM parent_communications pc
        JOIN users sender ON pc.sent_by = sender.id
        WHERE pc.student_id = ?
        ORDER BY pc.created_at ASC
    ";
    
    $stmt = $conn->prepare($conversation_query);
    $stmt->bind_param('i', $selected_student_id);
    $stmt->execute();
    $conversation_result = $stmt->get_result();
    
    while ($row = $conversation_result->fetch_assoc()) {
        $conversation[] = $row;
    }
    $stmt->close();
}

$page_title = __('whatsapp_interface');
require_once '../includes/header.php';
?>

<!-- تضمين ملف CSS الخاص بواجهة واتساب -->
<link rel="stylesheet" href="assets/whatsapp-style.css">

<style>
.whatsapp-container {
    height: calc(100vh - 120px);
    background: #f0f2f5;
}

.chat-sidebar {
    background: white;
    border-right: 1px solid #e9ecef;
    height: 100%;
    overflow-y: auto;
}

.chat-header {
    background: #00a884;
    color: white;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
}

.search-box {
    padding: 10px 15px;
    border-bottom: 1px solid #e9ecef;
}

.contact-item {
    padding: 12px 15px;
    border-bottom: 1px solid #f0f2f5;
    cursor: pointer;
    transition: background-color 0.2s;
}

.contact-item:hover {
    background-color: #f5f6fa;
}

.contact-item.active {
    background-color: #e3f2fd;
}

.contact-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #00a884;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 18px;
}

.contact-info {
    flex: 1;
    margin-left: 12px;
}

.contact-name {
    font-weight: 600;
    color: #111b21;
    margin-bottom: 2px;
}

.contact-last-message {
    color: #667781;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.contact-time {
    color: #667781;
    font-size: 12px;
    white-space: nowrap;
}

.message-count {
    background: #00a884;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 12px;
    min-width: 20px;
    text-align: center;
}

.chat-main {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #efeae2;
}

.chat-main-header {
    background: white;
    padding: 10px 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><defs><pattern id="pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23d1d7db" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23pattern)"/></svg>');
}

.message {
    margin-bottom: 15px;
    display: flex;
}

.message.sent {
    justify-content: flex-end;
}

.message.received {
    justify-content: flex-start;
}

.message-bubble {
    max-width: 70%;
    padding: 8px 12px;
    border-radius: 8px;
    position: relative;
}

.message.sent .message-bubble {
    background: #d9fdd3;
    border-bottom-right-radius: 2px;
}

.message.received .message-bubble {
    background: white;
    border-bottom-left-radius: 2px;
}

.message-content {
    margin-bottom: 5px;
    line-height: 1.4;
}

.message-time {
    font-size: 11px;
    color: #667781;
    text-align: right;
}

.message-status {
    display: inline-block;
    margin-left: 5px;
}

.chat-input {
    background: white;
    padding: 10px 20px;
    border-top: 1px solid #e9ecef;
}

.empty-chat {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #667781;
}

.empty-chat i {
    font-size: 80px;
    margin-bottom: 20px;
    opacity: 0.3;
}

.status-icon {
    color: #53bdeb;
}

.status-icon.delivered {
    color: #53bdeb;
}

.status-icon.read {
    color: #53bdeb;
}

.status-icon.failed {
    color: #f15c6d;
}
</style>

<div class="container-fluid p-0">
    <div class="whatsapp-container">
        <div class="row g-0 h-100">
            <!-- الشريط الجانبي - قائمة جهات الاتصال -->
            <div class="col-md-4 col-lg-3">
                <div class="chat-sidebar">
                    <!-- رأس الشريط الجانبي -->
                    <div class="chat-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fab fa-whatsapp me-2"></i>
                                <?php echo __('parent_contacts'); ?>
                            </h5>
                            <div class="dropdown">
                                <button class="btn btn-link text-white p-0" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item" href="send_message.php">
                                        <i class="fas fa-plus me-2"></i><?php echo __('new_message'); ?>
                                    </a></li>
                                    <li><a class="dropdown-item" href="index.php">
                                        <i class="fas fa-chart-bar me-2"></i><?php echo __('communication_dashboard'); ?>
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- مربع البحث -->
                    <div class="search-box">
                        <div class="input-group">
                            <span class="input-group-text bg-light border-end-0">
                                <i class="fas fa-search text-muted"></i>
                            </span>
                            <input type="text" class="form-control border-start-0 bg-light" 
                                   placeholder="<?php echo __('search_contacts'); ?>" id="searchContacts">
                        </div>
                    </div>

                    <!-- قائمة جهات الاتصال -->
                    <div class="contacts-list">
                        <?php if (!empty($contacts)): ?>
                            <?php foreach ($contacts as $contact): ?>
                                <div class="contact-item <?php echo $contact['student_id'] == $selected_student_id ? 'active' : ''; ?>"
                                     onclick="selectContact(<?php echo $contact['student_id']; ?>)"
                                     data-name="<?php echo strtolower($contact['student_name']); ?>"
                                     data-phone="<?php echo $contact['parent_phone']; ?>">
                                    <div class="d-flex align-items-center">
                                        <div class="contact-avatar">
                                            <?php echo mb_substr($contact['student_name'], 0, 1, 'UTF-8'); ?>
                                        </div>
                                        <div class="contact-info">
                                            <div class="contact-name"><?php echo safe_html($contact['student_name']); ?></div>
                                            <div class="contact-last-message">
                                                <?php if ($contact['last_message_subject']): ?>
                                                    <?php echo safe_html($contact['last_message_subject']); ?>
                                                <?php else: ?>
                                                    <?php echo safe_html($contact['parent_phone']); ?>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="contact-meta text-end">
                                            <?php if ($contact['last_message_time']): ?>
                                                <div class="contact-time">
                                                    <?php echo date('H:i', strtotime($contact['last_message_time'])); ?>
                                                </div>
                                            <?php endif; ?>
                                            <?php if ($contact['message_count'] > 0): ?>
                                                <div class="message-count mt-1">
                                                    <?php echo $contact['message_count']; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="text-center py-5">
                                <i class="fas fa-address-book fa-3x text-muted mb-3"></i>
                                <p class="text-muted"><?php echo __('no_contacts_found'); ?></p>
                                <a href="send_message.php" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus me-1"></i><?php echo __('send_first_message'); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- المنطقة الرئيسية - المحادثة -->
            <div class="col-md-8 col-lg-9">
                <div class="chat-main">
                    <?php if ($selected_contact): ?>
                        <!-- رأس المحادثة -->
                        <div class="chat-main-header">
                            <div class="contact-avatar me-3">
                                <?php echo mb_substr($selected_contact['student_name'], 0, 1, 'UTF-8'); ?>
                            </div>
                            <div class="flex-grow-1">
                                <div class="fw-bold"><?php echo safe_html($selected_contact['student_name']); ?></div>
                                <div class="text-muted small">
                                    <i class="fas fa-phone me-1"></i><?php echo safe_html($selected_contact['parent_phone']); ?>
                                    <?php if ($selected_contact['class_name']): ?>
                                        • <?php echo safe_html($selected_contact['class_name']); ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="dropdown">
                                <button class="btn btn-link text-muted p-0" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item" href="send_message.php?student_id=<?php echo $selected_contact['student_id']; ?>">
                                        <i class="fas fa-paper-plane me-2"></i><?php echo __('send_message'); ?>
                                    </a></li>
                                    <li><a class="dropdown-item" href="../students/view.php?id=<?php echo $selected_contact['student_id']; ?>">
                                        <i class="fas fa-user me-2"></i><?php echo __('student_info'); ?>
                                    </a></li>
                                </ul>
                            </div>
                        </div>

                        <!-- منطقة الرسائل -->
                        <div class="chat-messages" id="chatMessages">
                            <?php if (!empty($conversation)): ?>
                                <?php foreach ($conversation as $message): ?>
                                    <div class="message sent">
                                        <div class="message-bubble">
                                            <div class="message-content">
                                                <strong><?php echo safe_html($message['subject']); ?></strong><br>
                                                <?php echo nl2br(safe_html($message['message'])); ?>
                                            </div>
                                            <div class="message-time">
                                                <?php echo date('H:i', strtotime($message['created_at'])); ?>
                                                <span class="message-status">
                                                    <?php
                                                    $status_icons = [
                                                        'pending' => 'fas fa-clock',
                                                        'sent' => 'fas fa-check',
                                                        'delivered' => 'fas fa-check-double',
                                                        'read' => 'fas fa-check-double status-icon read',
                                                        'failed' => 'fas fa-exclamation-triangle status-icon failed'
                                                    ];
                                                    $icon = $status_icons[$message['status']] ?? 'fas fa-question';
                                                    ?>
                                                    <i class="<?php echo $icon; ?> status-icon"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                                    <p class="text-muted"><?php echo __('no_messages_yet'); ?></p>
                                    <a href="send_message.php?student_id=<?php echo $selected_contact['student_id']; ?>" 
                                       class="btn btn-primary">
                                        <i class="fas fa-paper-plane me-2"></i><?php echo __('start_conversation'); ?>
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- منطقة إدخال الرسالة -->
                        <div class="chat-input">
                            <form id="quickMessageForm" class="d-flex gap-2">
                                <input type="hidden" name="student_id" value="<?php echo $selected_contact['student_id']; ?>">
                                <input type="hidden" name="message_type" value="general">
                                <input type="hidden" name="priority" value="medium">
                                <input type="hidden" name="send_via" value="whatsapp">

                                <input type="text" name="subject" class="form-control"
                                       placeholder="<?php echo __('message_subject'); ?>..." required>
                                <input type="text" name="message" class="form-control flex-grow-1"
                                       placeholder="<?php echo __('type_message'); ?>..." required>
                                <button type="submit" class="btn btn-success" id="sendButton">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </form>
                        </div>
                    <?php else: ?>
                        <!-- حالة عدم اختيار محادثة -->
                        <div class="empty-chat">
                            <i class="fab fa-whatsapp"></i>
                            <h4><?php echo __('whatsapp_interface'); ?></h4>
                            <p><?php echo __('select_contact'); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// البحث في جهات الاتصال
document.getElementById('searchContacts').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const contacts = document.querySelectorAll('.contact-item');
    
    contacts.forEach(contact => {
        const name = contact.dataset.name;
        const phone = contact.dataset.phone;
        
        if (name.includes(searchTerm) || phone.includes(searchTerm)) {
            contact.style.display = 'block';
        } else {
            contact.style.display = 'none';
        }
    });
});

// اختيار جهة اتصال
function selectContact(studentId) {
    window.location.href = 'whatsapp_interface.php?student_id=' + studentId;
}

// تمرير تلقائي للرسائل
function scrollToBottom() {
    const chatMessages = document.getElementById('chatMessages');
    if (chatMessages) {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
}

// تمرير عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    scrollToBottom();
});

// إرسال سريع للرسائل
document.getElementById('quickMessageForm')?.addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const sendButton = document.getElementById('sendButton');
    const originalText = sendButton.innerHTML;

    // تعطيل الزر وإظهار التحميل
    sendButton.disabled = true;
    sendButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

    fetch('send_message_ajax.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إضافة الرسالة للواجهة
            addMessageToChat(data);

            // مسح النموذج
            this.reset();

            // إظهار رسالة نجاح
            showNotification('تم إرسال الرسالة بنجاح', 'success');
        } else {
            showNotification(data.error || 'فشل في إرسال الرسالة', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('حدث خطأ في الإرسال', 'error');
    })
    .finally(() => {
        // إعادة تفعيل الزر
        sendButton.disabled = false;
        sendButton.innerHTML = originalText;
    });
});

// دالة إضافة رسالة للمحادثة
function addMessageToChat(data) {
    const chatMessages = document.getElementById('chatMessages');
    if (!chatMessages) return;

    const messageHtml = `
        <div class="message sent" style="opacity: 0; transform: translateY(20px);">
            <div class="message-bubble">
                <div class="message-content">
                    <strong>${data.subject || ''}</strong><br>
                    ${data.message.replace(/\n/g, '<br>')}
                </div>
                <div class="message-time">
                    ${new Date().toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'})}
                    <span class="message-status">
                        <i class="fas fa-check status-icon"></i>
                    </span>
                </div>
            </div>
        </div>
    `;

    chatMessages.insertAdjacentHTML('beforeend', messageHtml);

    // تأثير الظهور
    const newMessage = chatMessages.lastElementChild;
    setTimeout(() => {
        newMessage.style.transition = 'all 0.3s ease';
        newMessage.style.opacity = '1';
        newMessage.style.transform = 'translateY(0)';
    }, 100);

    // تمرير للأسفل
    scrollToBottom();
}

// دالة إظهار الإشعارات
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : 'success'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // إزالة تلقائية بعد 5 ثوان
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// تحديث الرسائل كل 30 ثانية
setInterval(function() {
    if (window.location.search.includes('student_id=')) {
        // يمكن إضافة AJAX لتحديث الرسائل هنا
    }
}, 30000);
</script>

<!-- تضمين ملف JavaScript الخاص بواجهة واتساب -->
<script src="assets/whatsapp-interface.js"></script>

<?php require_once '../includes/footer.php'; ?>
