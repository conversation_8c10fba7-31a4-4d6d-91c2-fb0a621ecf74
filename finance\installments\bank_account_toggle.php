<?php
/**
 * تبديل حالة نشاط الحساب البنكي
 * Toggle Bank Account Active Status
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    header('Location: ../../dashboard/');
    exit();
}

// التحقق من وجود المعاملات المطلوبة
if (!isset($_GET['id']) || !is_numeric($_GET['id']) || !isset($_GET['action'])) {
    header('Location: bank_accounts.php?error=' . urlencode('معاملات غير صالحة'));
    exit();
}

$account_id = intval($_GET['id']);
$action = clean_input($_GET['action']);

// التحقق من صحة الإجراء
if (!in_array($action, ['activate', 'deactivate'])) {
    header('Location: bank_accounts.php?error=' . urlencode('إجراء غير صالح'));
    exit();
}

try {
    // جلب بيانات الحساب للتحقق من وجوده
    $stmt = $conn->prepare("SELECT id, bank_name, account_number, is_active FROM bank_accounts WHERE id = ?");
    $stmt->bind_param("i", $account_id);
    $stmt->execute();
    $account = $stmt->get_result()->fetch_assoc();

    if (!$account) {
        header('Location: bank_accounts.php?error=' . urlencode('الحساب البنكي غير موجود'));
        exit();
    }

    // تحديد الحالة الجديدة
    $new_status = ($action === 'activate') ? 1 : 0;
    
    // التحقق من أن الحالة ستتغير فعلاً
    if ($account['is_active'] == $new_status) {
        $status_text = $new_status ? 'مفعل' : 'غير مفعل';
        header('Location: bank_accounts.php?error=' . urlencode("الحساب $status_text بالفعل"));
        exit();
    }

    // إذا كان الإجراء هو إلغاء التفعيل، تحقق من استخدام الحساب في أقساط نشطة
    if ($action === 'deactivate') {
        $usage_check = $conn->prepare("
            SELECT COUNT(*) as count 
            FROM student_installments 
            WHERE bank_account_id = ? AND status IN ('pending', 'partial')
        ");
        $usage_check->bind_param("i", $account_id);
        $usage_check->execute();
        $active_usage = $usage_check->get_result()->fetch_assoc()['count'];

        if ($active_usage > 0) {
            header('Location: bank_accounts.php?error=' . urlencode("لا يمكن إلغاء تفعيل الحساب لأنه مستخدم في $active_usage قسط نشط"));
            exit();
        }
    }

    // تحديث حالة الحساب
    $update_stmt = $conn->prepare("UPDATE bank_accounts SET is_active = ?, updated_at = NOW() WHERE id = ?");
    $update_stmt->bind_param("ii", $new_status, $account_id);

    if ($update_stmt->execute()) {
        // تحديد رسالة النجاح
        $action_text = ($action === 'activate') ? 'تفعيل' : 'إلغاء تفعيل';
        $success_message = "تم $action_text الحساب البنكي (" . $account['bank_name'] . " - " . $account['account_number'] . ") بنجاح";
        
        // تسجيل النشاط (إذا كان لديك نظام تسجيل الأنشطة)
        if (function_exists('log_activity')) {
            log_activity($_SESSION['user_id'], 'toggle_bank_account', 'bank_accounts', $account_id, null, [
                'action' => $action,
                'bank_name' => $account['bank_name'],
                'account_number' => $account['account_number'],
                'new_status' => $new_status
            ]);
        }

        header('Location: bank_accounts.php?success=' . urlencode($success_message));
        exit();
    } else {
        header('Location: bank_accounts.php?error=' . urlencode('خطأ في تحديث حالة الحساب: ' . $conn->error));
        exit();
    }

} catch (Exception $e) {
    header('Location: bank_accounts.php?error=' . urlencode('خطأ في العملية: ' . $e->getMessage()));
    exit();
}

$conn->close();
?>
