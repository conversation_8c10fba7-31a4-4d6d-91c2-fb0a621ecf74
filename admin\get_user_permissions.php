<?php
/**
 * API للحصول على صلاحيات المستخدم
 * User Permissions API
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/enhanced_permissions.php';

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من الصلاحيات
check_session();
require_permission('settings_permissions', 'read');

// تعيين نوع المحتوى
header('Content-Type: application/json; charset=utf-8');

try {
    $user_id = intval($_GET['user_id'] ?? 0);
    
    if ($user_id <= 0) {
        throw new Exception('معرف المستخدم غير صحيح');
    }
    
    // التحقق من وجود المستخدم
    $user = get_user_data($user_id);
    if (!$user) {
        throw new Exception('المستخدم غير موجود');
    }
    
    // الحصول على الصلاحيات المخصصة
    $custom_permissions = get_user_custom_permissions($user_id);
    
    // الحصول على صلاحيات الدور الافتراضية
    $role_permissions = get_role_default_permissions($user['role']);
    
    // إرسال الاستجابة
    echo json_encode([
        'success' => true,
        'user' => [
            'id' => $user['id'],
            'name' => $user['full_name'],
            'role' => $user['role']
        ],
        'permissions' => $custom_permissions,
        'role_permissions' => $role_permissions
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * الحصول على الصلاحيات المخصصة للمستخدم
 */
function get_user_custom_permissions($user_id) {
    global $conn;
    
    $permissions = [];
    
    try {
        $stmt = $conn->prepare("
            SELECT resource_key, permission_level 
            FROM user_custom_permissions 
            WHERE user_id = ? AND is_granted = 1
            AND (expires_at IS NULL OR expires_at > NOW())
        ");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        while ($row = $result->fetch_assoc()) {
            $permissions[$row['resource_key']] = $row['permission_level'];
        }
        
    } catch (Exception $e) {
        error_log("Error getting user custom permissions: " . $e->getMessage());
    }
    
    return $permissions;
}

/**
 * الحصول على صلاحيات الدور الافتراضية
 */
function get_role_default_permissions($role_name) {
    $role_config = DEFAULT_ROLE_PERMISSIONS[$role_name] ?? null;
    
    if (!$role_config) {
        return [];
    }
    
    $permissions = [];
    
    // إذا كان الدور له صلاحية على جميع الموارد
    if (in_array('*', $role_config['resources'])) {
        // الحصول على جميع الموارد من قاعدة البيانات
        global $conn;
        $result = $conn->query("SELECT resource_key FROM system_resources WHERE is_active = 1");
        
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $permissions[$row['resource_key']] = $role_config['level'];
            }
        }
    } else {
        // تعيين مستوى الصلاحية للموارد المحددة
        foreach ($role_config['resources'] as $resource_key) {
            $permissions[$resource_key] = $role_config['level'];
        }
    }
    
    return $permissions;
}
?>
