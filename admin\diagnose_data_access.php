<?php
/**
 * تشخيص مشكلة الوصول للبيانات
 * Diagnose Data Access Issues
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

$user_email = '<EMAIL>';
$diagnosis = [];

// جلب بيانات المستخدم
$user_query = "SELECT * FROM users WHERE email = ?";
$user_stmt = $conn->prepare($user_query);
$user_stmt->bind_param("s", $user_email);
$user_stmt->execute();
$user = $user_stmt->get_result()->fetch_assoc();

if (!$user) {
    die("❌ المستخدم غير موجود!");
}

$diagnosis['user'] = $user;

// محاكاة جلسة المستخدم للاختبار
$original_session = $_SESSION;
$_SESSION['user_id'] = $user['id'];
$_SESSION['role'] = $user['role'];
$_SESSION['email'] = $user['email'];

// اختبار الوصول للبيانات المختلفة
$data_tests = [
    'classes' => [
        'table' => 'classes',
        'query' => 'SELECT * FROM classes WHERE status = "active" LIMIT 5',
        'permission' => 'classes_view',
        'name' => 'الفصول'
    ],
    'subjects' => [
        'table' => 'subjects', 
        'query' => 'SELECT * FROM subjects WHERE status = "active" LIMIT 5',
        'permission' => 'subjects_view',
        'name' => 'المواد'
    ],
    'students' => [
        'table' => 'students',
        'query' => 'SELECT * FROM students WHERE status = "active" LIMIT 5', 
        'permission' => 'students_view',
        'name' => 'الطلاب'
    ],
    'teachers' => [
        'table' => 'teachers',
        'query' => 'SELECT t.*, u.full_name FROM teachers t LEFT JOIN users u ON t.user_id = u.id LIMIT 5',
        'permission' => 'teachers_view', 
        'name' => 'المعلمين'
    ],
    'exams' => [
        'table' => 'exams',
        'query' => 'SELECT * FROM exams LIMIT 5',
        'permission' => 'exams_view',
        'name' => 'الامتحانات'
    ]
];

foreach ($data_tests as $key => $test) {
    $diagnosis['tests'][$key] = [
        'name' => $test['name'],
        'permission' => $test['permission'],
        'has_permission' => has_permission($test['permission']),
        'table_exists' => false,
        'data_count' => 0,
        'can_access' => false,
        'sample_data' => [],
        'error' => null
    ];
    
    try {
        // فحص وجود الجدول
        $table_check = $conn->query("SHOW TABLES LIKE '{$test['table']}'");
        $diagnosis['tests'][$key]['table_exists'] = $table_check->num_rows > 0;
        
        if ($diagnosis['tests'][$key]['table_exists']) {
            // عد البيانات
            $count_query = "SELECT COUNT(*) as count FROM {$test['table']}";
            $count_result = $conn->query($count_query);
            $diagnosis['tests'][$key]['data_count'] = $count_result->fetch_assoc()['count'];
            
            // اختبار الوصول للبيانات
            if ($diagnosis['tests'][$key]['has_permission']) {
                $data_result = $conn->query($test['query']);
                if ($data_result) {
                    $diagnosis['tests'][$key]['can_access'] = true;
                    while ($row = $data_result->fetch_assoc()) {
                        $diagnosis['tests'][$key]['sample_data'][] = array_slice($row, 0, 3, true);
                    }
                }
            }
        }
    } catch (Exception $e) {
        $diagnosis['tests'][$key]['error'] = $e->getMessage();
    }
}

// فحص صلاحيات إضافية مهمة
$additional_permissions = [
    'classes_manage' => 'إدارة الفصول',
    'subjects_manage' => 'إدارة المواد', 
    'students_manage' => 'إدارة الطلاب',
    'teachers_manage' => 'إدارة المعلمين',
    'exams_manage' => 'إدارة الامتحانات',
    'student_data_access' => 'الوصول لبيانات الطلاب',
    'teacher_data_access' => 'الوصول لبيانات المعلمين'
];

$diagnosis['additional_permissions'] = [];
foreach ($additional_permissions as $perm => $name) {
    $diagnosis['additional_permissions'][$perm] = [
        'name' => $name,
        'has_permission' => has_permission($perm)
    ];
}

// فحص الدور والصلاحيات الأساسية
$diagnosis['role_check'] = [
    'current_role' => $user['role'],
    'is_admin' => $user['role'] === 'admin',
    'is_teacher' => $user['role'] === 'teacher',
    'is_staff' => $user['role'] === 'staff',
    'basic_permissions' => [
        'admin' => check_permission('admin'),
        'teacher' => check_permission('teacher'),
        'staff' => check_permission('staff')
    ]
];

// استعادة الجلسة الأصلية
$_SESSION = $original_session;

$page_title = 'تشخيص مشكلة الوصول للبيانات';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-stethoscope me-2"></i><?php echo $page_title; ?></h2>
            <p class="text-muted">تشخيص مشكلة: الصفحات تظهر لكن البيانات فارغة</p>
        </div>
        <a href="../settings/permissions.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة
        </a>
    </div>

    <!-- معلومات المستخدم -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5><i class="fas fa-user me-2"></i>المستخدم: <?php echo $user_email; ?></h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <strong>الاسم:</strong><br>
                    <?php echo htmlspecialchars($user['full_name']); ?>
                </div>
                <div class="col-md-3">
                    <strong>الدور:</strong><br>
                    <span class="badge bg-<?php 
                        echo match($user['role']) {
                            'admin' => 'danger',
                            'teacher' => 'success', 
                            'staff' => 'warning',
                            default => 'secondary'
                        };
                    ?>">
                        <?php echo get_role_name_arabic($user['role']); ?>
                    </span>
                </div>
                <div class="col-md-3">
                    <strong>صلاحيات الدور:</strong><br>
                    <?php foreach ($diagnosis['role_check']['basic_permissions'] as $role => $has): ?>
                        <?php if ($has): ?>
                            <span class="badge bg-success me-1"><?php echo $role; ?></span>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
                <div class="col-md-3">
                    <strong>الحالة:</strong><br>
                    <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : 'danger'; ?>">
                        <?php echo $user['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- نتائج اختبار الوصول للبيانات -->
    <div class="card mb-4">
        <div class="card-header bg-warning text-dark">
            <h5><i class="fas fa-database me-2"></i>اختبار الوصول للبيانات</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>البيانات</th>
                            <th>الجدول موجود</th>
                            <th>عدد السجلات</th>
                            <th>صلاحية العرض</th>
                            <th>يمكن الوصول</th>
                            <th>عينة البيانات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($diagnosis['tests'] as $key => $test): ?>
                            <tr>
                                <td>
                                    <strong><?php echo $test['name']; ?></strong>
                                    <br><small class="text-muted"><?php echo $test['permission']; ?></small>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $test['table_exists'] ? 'success' : 'danger'; ?>">
                                        <?php echo $test['table_exists'] ? 'موجود' : 'مفقود'; ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $test['data_count'] > 0 ? 'primary' : 'secondary'; ?>">
                                        <?php echo $test['data_count']; ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $test['has_permission'] ? 'success' : 'danger'; ?>">
                                        <?php echo $test['has_permission'] ? 'متاح' : 'مرفوض'; ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $test['can_access'] ? 'success' : 'danger'; ?>">
                                        <?php echo $test['can_access'] ? 'نعم' : 'لا'; ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if (!empty($test['sample_data'])): ?>
                                        <button class="btn btn-outline-info btn-sm" 
                                                data-bs-toggle="collapse" 
                                                data-bs-target="#data_<?php echo $key; ?>">
                                            عرض عينة
                                        </button>
                                        <div class="collapse mt-2" id="data_<?php echo $key; ?>">
                                            <div class="bg-light p-2 rounded small">
                                                <?php foreach (array_slice($test['sample_data'], 0, 2) as $row): ?>
                                                    <div class="mb-1">
                                                        <?php foreach ($row as $field => $value): ?>
                                                            <span class="badge bg-secondary me-1">
                                                                <?php echo $field; ?>: <?php echo htmlspecialchars(substr($value, 0, 15)); ?>
                                                            </span>
                                                        <?php endforeach; ?>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    <?php elseif ($test['error']): ?>
                                        <span class="text-danger small">خطأ: <?php echo $test['error']; ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">لا توجد بيانات</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- الصلاحيات الإضافية -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h6><i class="fas fa-key me-2"></i>صلاحيات الإدارة</h6>
                </div>
                <div class="card-body">
                    <?php foreach ($diagnosis['additional_permissions'] as $perm => $info): ?>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span><?php echo $info['name']; ?></span>
                            <span class="badge bg-<?php echo $info['has_permission'] ? 'success' : 'danger'; ?>">
                                <?php echo $info['has_permission'] ? 'متاح' : 'مرفوض'; ?>
                            </span>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h6><i class="fas fa-diagnoses me-2"></i>التشخيص والحل</h6>
                </div>
                <div class="card-body">
                    <?php
                    $issues = [];
                    $solutions = [];
                    
                    foreach ($diagnosis['tests'] as $key => $test) {
                        if (!$test['table_exists']) {
                            $issues[] = "جدول {$test['name']} غير موجود";
                            $solutions[] = "إنشاء جدول {$test['name']}";
                        } elseif ($test['data_count'] == 0) {
                            $issues[] = "لا توجد بيانات في {$test['name']}";
                            $solutions[] = "إضافة بيانات إلى {$test['name']}";
                        } elseif (!$test['has_permission']) {
                            $issues[] = "لا توجد صلاحية لعرض {$test['name']}";
                            $solutions[] = "منح صلاحية {$test['permission']}";
                        } elseif (!$test['can_access']) {
                            $issues[] = "لا يمكن الوصول لبيانات {$test['name']}";
                            $solutions[] = "فحص استعلامات قاعدة البيانات";
                        }
                    }
                    ?>
                    
                    <?php if (!empty($issues)): ?>
                        <h6 class="text-danger">المشاكل المكتشفة:</h6>
                        <ul class="small text-danger">
                            <?php foreach ($issues as $issue): ?>
                                <li><?php echo $issue; ?></li>
                            <?php endforeach; ?>
                        </ul>
                        
                        <h6 class="text-success">الحلول المقترحة:</h6>
                        <ul class="small text-success">
                            <?php foreach ($solutions as $solution): ?>
                                <li><?php echo $solution; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    <?php else: ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            لا توجد مشاكل واضحة. المستخدم يجب أن يرى البيانات.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- أدوات الإصلاح -->
    <div class="card mt-4">
        <div class="card-header bg-secondary text-white">
            <h5><i class="fas fa-tools me-2"></i>أدوات الإصلاح</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <a href="fix_data_permissions.php" class="btn btn-primary w-100">
                        <i class="fas fa-key me-2"></i>إصلاح صلاحيات البيانات
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="check_existing_data.php" class="btn btn-info w-100">
                        <i class="fas fa-search me-2"></i>فحص البيانات الموجودة
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="debug_user_permissions.php" class="btn btn-warning w-100">
                        <i class="fas fa-bug me-2"></i>تشخيص مفصل
                    </a>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-secondary w-100" onclick="window.location.reload()">
                        <i class="fas fa-redo me-2"></i>إعادة التشخيص
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
