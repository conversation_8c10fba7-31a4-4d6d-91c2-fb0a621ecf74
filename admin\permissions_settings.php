<?php
/**
 * إعدادات نظام الصلاحيات
 * Permissions System Settings
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// تحديد المسار الصحيح للعودة
$return_path = '../settings/';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

$success_message = '';
$error_message = '';

// معالجة حفظ الإعدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_settings'])) {
    $settings = [
        'permissions_enabled' => isset($_POST['permissions_enabled']) ? '1' : '0',
        'default_user_role' => clean_input($_POST['default_user_role']),
        'permission_inheritance' => isset($_POST['permission_inheritance']) ? '1' : '0',
        'audit_permissions' => isset($_POST['audit_permissions']) ? '1' : '0',
        'session_timeout' => intval($_POST['session_timeout']),
        'auto_logout_inactive' => isset($_POST['auto_logout_inactive']) ? '1' : '0',
        'require_permission_reason' => isset($_POST['require_permission_reason']) ? '1' : '0',
        'permission_expiry_warning' => intval($_POST['permission_expiry_warning'])
    ];
    
    $conn->begin_transaction();
    try {
        foreach ($settings as $key => $value) {
            $stmt = $conn->prepare("
                INSERT INTO system_settings (setting_key, setting_value) 
                VALUES (?, ?) 
                ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
            ");
            $stmt->bind_param("ss", $key, $value);
            $stmt->execute();
        }
        
        $conn->commit();
        $success_message = "تم حفظ إعدادات الصلاحيات بنجاح";
    } catch (Exception $e) {
        $conn->rollback();
        $error_message = "خطأ في حفظ الإعدادات: " . $e->getMessage();
    }
}

// جلب الإعدادات الحالية
$current_settings = [];
$settings_query = "SELECT setting_key, setting_value FROM system_settings WHERE setting_key LIKE 'permissions_%' OR setting_key IN ('default_user_role', 'session_timeout', 'auto_logout_inactive', 'require_permission_reason', 'permission_expiry_warning')";
$settings_result = $conn->query($settings_query);

while ($row = $settings_result->fetch_assoc()) {
    $current_settings[$row['setting_key']] = $row['setting_value'];
}

// القيم الافتراضية
$defaults = [
    'permissions_enabled' => '1',
    'default_user_role' => 'staff',
    'permission_inheritance' => '1',
    'audit_permissions' => '1',
    'session_timeout' => '3600',
    'auto_logout_inactive' => '0',
    'require_permission_reason' => '0',
    'permission_expiry_warning' => '7'
];

foreach ($defaults as $key => $default_value) {
    if (!isset($current_settings[$key])) {
        $current_settings[$key] = $default_value;
    }
}

// إحصائيات الصلاحيات
$stats_query = "
    SELECT 
        (SELECT COUNT(*) FROM users WHERE status = 'active') as total_users,
        (SELECT COUNT(*) FROM user_custom_permissions WHERE is_granted = 1) as custom_permissions,
        (SELECT COUNT(*) FROM system_resources WHERE is_active = 1) as active_resources,
        (SELECT COUNT(*) FROM permissions_audit_log WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)) as recent_changes
";
$stats = $conn->query($stats_query)->fetch_assoc();

$page_title = 'إعدادات نظام الصلاحيات';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-shield-alt me-2"></i><?php echo $page_title; ?></h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="../admin/">الإدارة</a></li>
                    <li class="breadcrumb-item active">إعدادات الصلاحيات</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- رسائل التنبيه -->
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- إحصائيات سريعة -->
        <div class="col-12 mb-4">
            <div class="row">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo $stats['total_users']; ?></h4>
                                    <p class="mb-0">إجمالي المستخدمين</p>
                                </div>
                                <i class="fas fa-users fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo $stats['custom_permissions']; ?></h4>
                                    <p class="mb-0">الصلاحيات المخصصة</p>
                                </div>
                                <i class="fas fa-key fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo $stats['active_resources']; ?></h4>
                                    <p class="mb-0">موارد النظام النشطة</p>
                                </div>
                                <i class="fas fa-sitemap fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo $stats['recent_changes']; ?></h4>
                                    <p class="mb-0">تغييرات آخر 30 يوم</p>
                                </div>
                                <i class="fas fa-history fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- نموذج الإعدادات -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-cog me-2"></i>إعدادات النظام</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <!-- الإعدادات الأساسية -->
                        <div class="mb-4">
                            <h6 class="text-primary mb-3">الإعدادات الأساسية</h6>
                            
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="permissions_enabled" 
                                       name="permissions_enabled" <?php echo $current_settings['permissions_enabled'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="permissions_enabled">
                                    <strong>تفعيل نظام الصلاحيات المخصص</strong>
                                    <br><small class="text-muted">تفعيل أو إلغاء تفعيل نظام الصلاحيات المتقدم</small>
                                </label>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">الدور الافتراضي للمستخدمين الجدد</label>
                                <select class="form-select" name="default_user_role">
                                    <option value="staff" <?php echo $current_settings['default_user_role'] === 'staff' ? 'selected' : ''; ?>>موظف</option>
                                    <option value="teacher" <?php echo $current_settings['default_user_role'] === 'teacher' ? 'selected' : ''; ?>>معلم</option>
                                    <option value="student" <?php echo $current_settings['default_user_role'] === 'student' ? 'selected' : ''; ?>>طالب</option>
                                    <option value="parent" <?php echo $current_settings['default_user_role'] === 'parent' ? 'selected' : ''; ?>>ولي أمر</option>
                                </select>
                            </div>

                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="permission_inheritance" 
                                       name="permission_inheritance" <?php echo $current_settings['permission_inheritance'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="permission_inheritance">
                                    <strong>وراثة الصلاحيات من الدور الأساسي</strong>
                                    <br><small class="text-muted">السماح للمستخدمين بوراثة صلاحيات دورهم الأساسي بالإضافة للصلاحيات المخصصة</small>
                                </label>
                            </div>
                        </div>

                        <!-- إعدادات الأمان -->
                        <div class="mb-4">
                            <h6 class="text-primary mb-3">إعدادات الأمان</h6>
                            
                            <div class="mb-3">
                                <label class="form-label">مدة انتهاء الجلسة (بالثواني)</label>
                                <input type="number" class="form-control" name="session_timeout" 
                                       value="<?php echo $current_settings['session_timeout']; ?>" min="300" max="86400">
                                <div class="form-text">القيمة الافتراضية: 3600 ثانية (ساعة واحدة)</div>
                            </div>

                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="auto_logout_inactive" 
                                       name="auto_logout_inactive" <?php echo $current_settings['auto_logout_inactive'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="auto_logout_inactive">
                                    <strong>تسجيل خروج تلقائي للمستخدمين غير النشطين</strong>
                                    <br><small class="text-muted">تسجيل خروج المستخدمين تلقائياً عند عدم النشاط</small>
                                </label>
                            </div>
                        </div>

                        <!-- إعدادات المراجعة والتسجيل -->
                        <div class="mb-4">
                            <h6 class="text-primary mb-3">المراجعة والتسجيل</h6>
                            
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="audit_permissions" 
                                       name="audit_permissions" <?php echo $current_settings['audit_permissions'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="audit_permissions">
                                    <strong>تسجيل تغييرات الصلاحيات</strong>
                                    <br><small class="text-muted">تسجيل جميع التغييرات التي تطرأ على صلاحيات المستخدمين</small>
                                </label>
                            </div>

                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="require_permission_reason" 
                                       name="require_permission_reason" <?php echo $current_settings['require_permission_reason'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="require_permission_reason">
                                    <strong>طلب سبب عند تغيير الصلاحيات</strong>
                                    <br><small class="text-muted">إجبار المدير على كتابة سبب عند منح أو إلغاء الصلاحيات</small>
                                </label>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">تحذير انتهاء الصلاحيات (بالأيام)</label>
                                <input type="number" class="form-control" name="permission_expiry_warning" 
                                       value="<?php echo $current_settings['permission_expiry_warning']; ?>" min="1" max="30">
                                <div class="form-text">إرسال تحذير قبل انتهاء الصلاحيات المؤقتة</div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="<?php echo $return_path; ?>" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>العودة للإعدادات
                            </a>
                            <button type="submit" name="save_settings" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ الإعدادات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- روابط سريعة -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-link me-2"></i>إدارة الصلاحيات</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="permissions_manager.php" class="btn btn-outline-primary">
                            <i class="fas fa-users me-2"></i>إدارة صلاحيات المستخدمين
                        </a>
                        <a href="system_resources.php" class="btn btn-outline-success">
                            <i class="fas fa-sitemap me-2"></i>إدارة موارد النظام
                        </a>
                        <a href="permissions_audit.php" class="btn btn-outline-info">
                            <i class="fas fa-history me-2"></i>سجل تغييرات الصلاحيات
                        </a>
                        <a href="update_permissions_system.php" class="btn btn-outline-warning">
                            <i class="fas fa-database me-2"></i>تحديث نظام الصلاحيات
                        </a>
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle me-2"></i>معلومات مهمة</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <strong>تنبيه:</strong> تغيير إعدادات الصلاحيات قد يؤثر على وصول المستخدمين للنظام. تأكد من الإعدادات قبل الحفظ.
                    </div>
                    <div class="alert alert-info">
                        <strong>ملاحظة:</strong> المدير (admin) له وصول كامل لجميع أجزاء النظام بغض النظر عن الإعدادات.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
