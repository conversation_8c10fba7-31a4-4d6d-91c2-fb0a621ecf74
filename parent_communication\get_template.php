<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('teacher')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'غير مصرح']);
    exit();
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'طريقة طلب غير مدعومة']);
    exit();
}

global $conn;

$template_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($template_id <= 0) {
    echo json_encode(['success' => false, 'error' => 'معرف القالب مطلوب']);
    exit();
}

try {
    // جلب بيانات القالب
    $template_query = "
        SELECT 
            mt.*,
            u.full_name as creator_name
        FROM message_templates mt
        JOIN users u ON mt.created_by = u.id
        WHERE mt.id = ?
    ";
    
    $stmt = $conn->prepare($template_query);
    $stmt->bind_param('i', $template_id);
    $stmt->execute();
    $template = $stmt->get_result()->fetch_assoc();
    $stmt->close();
    
    if (!$template) {
        echo json_encode(['success' => false, 'error' => 'القالب غير موجود']);
        exit();
    }
    
    // إرجاع البيانات
    echo json_encode([
        'success' => true,
        'template' => $template
    ]);
    
} catch (Exception $e) {
    error_log("Error in get_template.php: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'error' => 'حدث خطأ في جلب بيانات القالب'
    ]);
}
?>
