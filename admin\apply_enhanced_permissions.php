<?php
/**
 * تطبيق نظام الصلاحيات المحسن
 * Apply Enhanced Permissions System
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/?error=access_denied');
    exit();
}

$success_message = '';
$error_message = '';
$step = intval($_GET['step'] ?? 1);

// معالجة تطبيق النظام
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['apply_system'])) {
    $conn->begin_transaction();
    
    try {
        // الخطوة 1: تشغيل ملف تحديث قاعدة البيانات
        $sql_file = '../database/update_permissions_system.sql';
        if (file_exists($sql_file)) {
            $sql_content = file_get_contents($sql_file);
            
            // تقسيم الاستعلامات
            $queries = explode(';', $sql_content);
            
            foreach ($queries as $query) {
                $query = trim($query);
                if (!empty($query) && !preg_match('/^(SET|START|COMMIT)/', $query)) {
                    $conn->query($query);
                }
            }
        }
        
        // الخطوة 2: إدراج البيانات الأساسية
        insert_default_permissions_data();
        
        // الخطوة 3: ترحيل البيانات الموجودة
        migrate_existing_permissions();
        
        $conn->commit();
        $success_message = "تم تطبيق نظام الصلاحيات المحسن بنجاح!";
        $step = 4; // الانتقال لخطوة التأكيد
        
    } catch (Exception $e) {
        $conn->rollback();
        $error_message = "خطأ في تطبيق النظام: " . $e->getMessage();
    }
}

/**
 * إدراج البيانات الأساسية
 */
function insert_default_permissions_data() {
    global $conn;
    
    // إدراج الأدوار الأساسية
    $roles = [
        ['admin', 'مدير النظام', 'مدير عام للنظام مع جميع الصلاحيات', 1],
        ['teacher', 'معلم', 'معلم في المدرسة', 1],
        ['student', 'طالب', 'طالب في المدرسة', 1],
        ['staff', 'موظف إداري', 'موظف إداري في المدرسة', 1],
        ['financial_manager', 'مدير مالي', 'مسؤول عن الشؤون المالية', 1],
        ['academic_supervisor', 'مشرف أكاديمي', 'مشرف على الشؤون الأكاديمية', 1],
        ['librarian', 'أمين مكتبة', 'مسؤول عن المكتبة', 1],
        ['nurse', 'ممرض/ة', 'مسؤول عن الصحة المدرسية', 1],
        ['security', 'أمن', 'مسؤول عن الأمن', 1],
        ['maintenance', 'صيانة', 'مسؤول عن الصيانة', 1],
        ['parent', 'ولي أمر', 'ولي أمر طالب', 1]
    ];
    
    $role_stmt = $conn->prepare("
        INSERT IGNORE INTO custom_roles 
        (role_name, role_display_name, role_description, is_system_role, created_by) 
        VALUES (?, ?, ?, ?, 1)
    ");
    
    foreach ($roles as $role) {
        $role_stmt->bind_param("sssi", $role[0], $role[1], $role[2], $role[3]);
        $role_stmt->execute();
    }
    
    // إدراج موارد النظام الأساسية
    $resources = [
        // صفحات إدارة المستخدمين
        ['page', 'users_view', 'عرض المستخدمين', 'صفحة عرض قائمة المستخدمين', '/users/', NULL, 'fas fa-users', 10],
        ['page', 'users_add', 'إضافة مستخدم', 'صفحة إضافة مستخدم جديد', '/users/add.php', 'users_view', 'fas fa-user-plus', 11],
        ['page', 'users_edit', 'تعديل مستخدم', 'صفحة تعديل بيانات المستخدم', '/users/edit.php', 'users_view', 'fas fa-user-edit', 12],
        ['page', 'users_delete', 'حذف مستخدم', 'صفحة حذف المستخدم', '/users/delete.php', 'users_view', 'fas fa-user-times', 13],
        
        // صفحات إدارة الطلاب
        ['page', 'students_view', 'عرض الطلاب', 'صفحة عرض قائمة الطلاب', '/students/', NULL, 'fas fa-graduation-cap', 20],
        ['page', 'students_add', 'إضافة طالب', 'صفحة إضافة طالب جديد', '/students/add.php', 'students_view', 'fas fa-user-graduate', 21],
        ['page', 'students_edit', 'تعديل طالب', 'صفحة تعديل بيانات الطالب', '/students/edit.php', 'students_view', 'fas fa-edit', 22],
        ['page', 'students_delete', 'حذف طالب', 'صفحة حذف الطالب', '/students/delete.php', 'students_view', 'fas fa-trash', 23],
        
        // صفحات إدارة المعلمين
        ['page', 'teachers_view', 'عرض المعلمين', 'صفحة عرض قائمة المعلمين', '/teachers/', NULL, 'fas fa-chalkboard-teacher', 30],
        ['page', 'teachers_add', 'إضافة معلم', 'صفحة إضافة معلم جديد', '/teachers/add.php', 'teachers_view', 'fas fa-user-plus', 31],
        ['page', 'teachers_edit', 'تعديل معلم', 'صفحة تعديل بيانات المعلم', '/teachers/edit.php', 'teachers_view', 'fas fa-edit', 32],
        ['page', 'teachers_delete', 'حذف معلم', 'صفحة حذف المعلم', '/teachers/delete.php', 'teachers_view', 'fas fa-trash', 33],
        
        // صفحات النظام المالي
        ['page', 'finance_view', 'عرض المالية', 'صفحة عرض البيانات المالية', '/finance/', NULL, 'fas fa-money-bill-wave', 70],
        ['page', 'finance_fees', 'إدارة الرسوم', 'صفحة إدارة رسوم الطلاب', '/finance/fees/', 'finance_view', 'fas fa-dollar-sign', 71],
        ['page', 'finance_payments', 'إدارة المدفوعات', 'صفحة إدارة المدفوعات', '/finance/payments/', 'finance_view', 'fas fa-credit-card', 72],
        
        // صفحات الحضور والغياب
        ['page', 'attendance_view', 'عرض الحضور', 'صفحة عرض سجلات الحضور', '/attendance/', NULL, 'fas fa-calendar-check', 60],
        ['page', 'attendance_take', 'أخذ الحضور', 'صفحة تسجيل الحضور', '/attendance/take_attendance.php', 'attendance_view', 'fas fa-check', 61],
        
        // صفحات الامتحانات
        ['page', 'exams_view', 'عرض الامتحانات', 'صفحة عرض قائمة الامتحانات', '/exams/', NULL, 'fas fa-file-alt', 80],
        ['page', 'exams_add', 'إضافة امتحان', 'صفحة إضافة امتحان جديد', '/exams/add.php', 'exams_view', 'fas fa-plus', 81],
        ['page', 'exams_grade', 'تصحيح الامتحانات', 'صفحة تصحيح ورصد الدرجات', '/exams/grade.php', 'exams_view', 'fas fa-star', 83],
        
        // صفحات التقارير
        ['page', 'reports_view', 'عرض التقارير', 'صفحة عرض التقارير العامة', '/reports/', NULL, 'fas fa-chart-bar', 90],
        ['page', 'reports_students', 'تقارير الطلاب', 'صفحة تقارير الطلاب', '/reports/students.php', 'reports_view', 'fas fa-users', 91],
        ['page', 'reports_financial', 'التقارير المالية', 'صفحة التقارير المالية', '/reports/financial.php', 'reports_view', 'fas fa-money-bill', 92],
        
        // صفحات الإعدادات
        ['page', 'settings_view', 'عرض الإعدادات', 'صفحة عرض إعدادات النظام', '/settings/', NULL, 'fas fa-cog', 100],
        ['page', 'settings_permissions', 'إدارة الصلاحيات', 'صفحة إدارة الصلاحيات والأدوار', '/settings/permissions.php', 'settings_view', 'fas fa-key', 101]
    ];
    
    $resource_stmt = $conn->prepare("
        INSERT IGNORE INTO system_resources 
        (resource_type, resource_key, resource_name, resource_description, resource_path, parent_resource, icon, sort_order) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    foreach ($resources as $resource) {
        $resource_stmt->bind_param("sssssssi", ...$resource);
        $resource_stmt->execute();
    }
}

/**
 * ترحيل البيانات الموجودة
 */
function migrate_existing_permissions() {
    global $conn;
    
    // ترحيل الصلاحيات المخصصة الموجودة
    try {
        $old_permissions = $conn->query("
            SELECT user_id, permission_key, is_granted 
            FROM user_custom_permissions 
            WHERE resource_key IS NULL
        ");
        
        if ($old_permissions && $old_permissions->num_rows > 0) {
            $update_stmt = $conn->prepare("
                UPDATE user_custom_permissions 
                SET resource_key = ?, permission_level = 'full' 
                WHERE user_id = ? AND permission_key = ?
            ");
            
            while ($perm = $old_permissions->fetch_assoc()) {
                $update_stmt->bind_param("sis", $perm['permission_key'], $perm['user_id'], $perm['permission_key']);
                $update_stmt->execute();
            }
        }
    } catch (Exception $e) {
        // تجاهل الأخطاء في حالة عدم وجود البيانات القديمة
    }
}

$page_title = 'تطبيق نظام الصلاحيات المحسن';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-rocket me-2 text-success"></i><?php echo $page_title; ?></h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="../settings/">الإعدادات</a></li>
                    <li class="breadcrumb-item active">تطبيق النظام المحسن</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- رسائل التنبيه -->
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- خطوات التطبيق -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-list-ol me-2"></i>خطوات تطبيق النظام المحسن</h5>
                </div>
                <div class="card-body">
                    <!-- شريط التقدم -->
                    <div class="progress mb-4">
                        <div class="progress-bar bg-success" role="progressbar" 
                             style="width: <?php echo ($step / 4) * 100; ?>%" 
                             aria-valuenow="<?php echo $step; ?>" aria-valuemin="0" aria-valuemax="4">
                            الخطوة <?php echo $step; ?> من 4
                        </div>
                    </div>

                    <?php if ($step == 1): ?>
                        <!-- الخطوة 1: التحضير -->
                        <div class="step-content">
                            <h6><i class="fas fa-info-circle me-2 text-info"></i>الخطوة 1: التحضير والتحقق</h6>
                            <p>سيتم تطبيق نظام الصلاحيات المحسن الذي يتضمن:</p>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>إنشاء جداول الصلاحيات المحسنة</li>
                                        <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>إدراج الأدوار الأساسية</li>
                                        <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>إدراج موارد النظام</li>
                                        <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>نظام الصلاحيات المخصصة</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>سجل تدقيق الصلاحيات</li>
                                        <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>واجهة إدارة محسنة</li>
                                        <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>دعم مستويات الصلاحيات</li>
                                        <li class="list-group-item"><i class="fas fa-check text-success me-2"></i>ترحيل البيانات الموجودة</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="alert alert-warning mt-3">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>تحذير:</strong> يُنصح بأخذ نسخة احتياطية من قاعدة البيانات قبل المتابعة.
                            </div>
                            
                            <div class="text-center mt-4">
                                <a href="?step=2" class="btn btn-primary btn-lg">
                                    <i class="fas fa-arrow-right me-2"></i>المتابعة للخطوة التالية
                                </a>
                            </div>
                        </div>

                    <?php elseif ($step == 2): ?>
                        <!-- الخطوة 2: التأكيد -->
                        <div class="step-content">
                            <h6><i class="fas fa-exclamation-triangle me-2 text-warning"></i>الخطوة 2: تأكيد التطبيق</h6>
                            <p>هل أنت متأكد من تطبيق نظام الصلاحيات المحسن؟</p>
                            
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                سيتم الاحتفاظ بجميع البيانات الموجودة وترحيلها للنظام الجديد.
                            </div>
                            
                            <form method="POST">
                                <input type="hidden" name="apply_system" value="1">
                                <div class="text-center">
                                    <a href="?step=1" class="btn btn-secondary me-2">
                                        <i class="fas fa-arrow-left me-2"></i>العودة
                                    </a>
                                    <button type="submit" class="btn btn-success btn-lg">
                                        <i class="fas fa-rocket me-2"></i>تطبيق النظام المحسن
                                    </button>
                                </div>
                            </form>
                        </div>

                    <?php elseif ($step == 4): ?>
                        <!-- الخطوة 4: اكتمال التطبيق -->
                        <div class="step-content text-center">
                            <div class="mb-4">
                                <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                            </div>
                            <h4 class="text-success">تم تطبيق النظام بنجاح!</h4>
                            <p class="lead">نظام الصلاحيات المحسن جاهز للاستخدام الآن.</p>
                            
                            <div class="row mt-4">
                                <div class="col-md-4">
                                    <a href="enhanced_permissions_manager.php" class="btn btn-primary btn-lg w-100">
                                        <i class="fas fa-shield-alt me-2"></i>إدارة الصلاحيات
                                    </a>
                                </div>
                                <div class="col-md-4">
                                    <a href="roles_manager.php" class="btn btn-info btn-lg w-100">
                                        <i class="fas fa-user-tag me-2"></i>إدارة الأدوار
                                    </a>
                                </div>
                                <div class="col-md-4">
                                    <a href="enhanced_permissions_audit.php" class="btn btn-success btn-lg w-100">
                                        <i class="fas fa-history me-2"></i>سجل التدقيق
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
