<?php
/**
 * مثال على صفحة حضور العاملين مع نظام الصلاحيات الجديد
 * Example Staff Attendance Page with New Permissions System
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من تسجيل الدخول
check_session();

// التحقق من صلاحية عرض حضور العاملين
if (!can_view_staff_attendance()) {
    $error_message = "ليس لديك صلاحية للوصول إلى حضور العاملين";
    if ($_SESSION['role'] === 'student_affairs_admin') {
        $error_message .= ". يمكنك الوصول إلى <a href='student_attendance_example.php'>حضور الطلاب</a> بدلاً من ذلك.";
    }
    
    include_once '../includes/header.php';
    ?>
    <div class="container-fluid">
        <div class="alert alert-danger">
            <h4><i class="fas fa-exclamation-triangle me-2"></i>غير مصرح</h4>
            <p><?php echo $error_message; ?></p>
            <a href="../dashboard/" class="btn btn-primary">العودة للرئيسية</a>
        </div>
    </div>
    <?php
    include_once '../includes/footer.php';
    exit();
}

// جلب بيانات العاملين (مثال)
$staff_query = "
    SELECT u.id, u.full_name, u.email, u.role, u.phone
    FROM users u
    WHERE u.status = 'active' AND u.role IN ('teacher', 'staff', 'financial_manager', 'student_affairs_admin', 'staff_affairs_admin')
    ORDER BY u.role, u.full_name
    LIMIT 20
";
$staff_result = $conn->query($staff_query);

$page_title = 'حضور العاملين - ' . get_role_name_arabic($_SESSION['role']);
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-users me-2"></i><?php echo $page_title; ?></h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="index.php">الحضور والغياب</a></li>
                    <li class="breadcrumb-item active">حضور العاملين</li>
                </ol>
            </nav>
        </div>
        <div>
            <?php if (can_manage_staff_attendance()): ?>
                <button class="btn btn-primary me-2">
                    <i class="fas fa-plus me-2"></i>تسجيل حضور
                </button>
            <?php endif; ?>
            
            <?php if (has_permission('staff_attendance_reports')): ?>
                <button class="btn btn-success">
                    <i class="fas fa-chart-line me-2"></i>التقارير
                </button>
            <?php endif; ?>
        </div>
    </div>

    <!-- معلومات الدور والصلاحيات -->
    <div class="alert alert-warning">
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-user-tie me-2"></i>دورك الحالي:</h6>
                <span class="badge bg-warning text-dark"><?php echo get_role_name_arabic($_SESSION['role']); ?></span>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-key me-2"></i>صلاحياتك:</h6>
                <div>
                    <?php if (can_view_staff_attendance()): ?>
                        <span class="badge bg-success me-1">عرض حضور العاملين</span>
                    <?php endif; ?>
                    
                    <?php if (can_manage_staff_attendance()): ?>
                        <span class="badge bg-warning me-1">إدارة حضور العاملين</span>
                    <?php endif; ?>
                    
                    <?php if (has_permission('staff_attendance_reports')): ?>
                        <span class="badge bg-info me-1">تقارير العاملين</span>
                    <?php endif; ?>
                    
                    <?php if (can_access_staff_data()): ?>
                        <span class="badge bg-secondary me-1">بيانات العاملين</span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول العاملين -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-table me-2"></i>قائمة العاملين</h5>
        </div>
        <div class="card-body">
            <?php if ($staff_result && $staff_result->num_rows > 0): ?>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>الدور</th>
                                <th>البريد الإلكتروني</th>
                                <th>الهاتف</th>
                                <th>الحالة</th>
                                <?php if (can_manage_staff_attendance()): ?>
                                    <th>الإجراءات</th>
                                <?php endif; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($staff = $staff_result->fetch_assoc()): ?>
                                <tr>
                                    <td>
                                        <?php if (can_access_staff_data()): ?>
                                            <strong><?php echo htmlspecialchars($staff['full_name']); ?></strong>
                                        <?php else: ?>
                                            <span class="text-muted">غير مصرح بعرض الاسم</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo match($staff['role']) {
                                                'teacher' => 'success',
                                                'staff' => 'info',
                                                'financial_manager' => 'warning',
                                                'student_affairs_admin' => 'primary',
                                                'staff_affairs_admin' => 'secondary',
                                                default => 'dark'
                                            };
                                        ?>">
                                            <?php echo get_role_name_arabic($staff['role']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if (can_access_staff_data()): ?>
                                            <?php echo htmlspecialchars($staff['email']); ?>
                                        <?php else: ?>
                                            <span class="text-muted">***</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (can_access_staff_data()): ?>
                                            <?php echo htmlspecialchars($staff['phone'] ?? 'غير محدد'); ?>
                                        <?php else: ?>
                                            <span class="text-muted">***</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">حاضر</span>
                                    </td>
                                    <?php if (can_manage_staff_attendance()): ?>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" title="تعديل الحضور">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                        </td>
                                    <?php endif; ?>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-user-tie fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد بيانات عاملين</h5>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- معلومات إضافية حسب الدور -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-info-circle me-2"></i>معلومات دورك</h6>
                </div>
                <div class="card-body">
                    <?php
                    $role = $_SESSION['role'];
                    switch ($role) {
                        case 'admin':
                            echo '<p class="text-success">كمدير للنظام، يمكنك الوصول إلى جميع بيانات الطلاب والعاملين.</p>';
                            echo '<a href="student_attendance_example.php" class="btn btn-outline-primary btn-sm">عرض حضور الطلاب</a>';
                            break;
                        case 'staff_affairs_admin':
                            echo '<p class="text-warning">كمدير لشؤون العاملين، يمكنك الوصول إلى بيانات العاملين فقط.</p>';
                            echo '<p class="text-muted small">لا يمكنك الوصول إلى بيانات الطلاب.</p>';
                            break;
                        default:
                            echo '<p class="text-muted">تم منحك صلاحية خاصة لعرض حضور العاملين.</p>';
                    }
                    ?>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-shield-alt me-2"></i>الفصل بين الصلاحيات</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info alert-sm">
                        <h6><i class="fas fa-check-circle me-2"></i>النظام يعمل بشكل صحيح!</h6>
                        <ul class="small mb-0">
                            <li>مدير شؤون الطلاب: يرى الطلاب فقط</li>
                            <li>مدير شؤون العاملين: يرى العاملين فقط</li>
                            <li>المدير العام: يرى الجميع</li>
                            <li>البيانات محمية حسب الدور</li>
                        </ul>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <?php if (can_view_student_attendance()): ?>
                            <a href="student_attendance_example.php" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-user-graduate me-2"></i>حضور الطلاب
                            </a>
                        <?php else: ?>
                            <button class="btn btn-outline-secondary btn-sm" disabled>
                                <i class="fas fa-lock me-2"></i>حضور الطلاب (غير مصرح)
                            </button>
                        <?php endif; ?>
                        
                        <a href="../dashboard/" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- اختبار الصلاحيات -->
    <div class="card mt-4">
        <div class="card-header">
            <h6><i class="fas fa-vial me-2"></i>اختبار الصلاحيات</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-success">الصلاحيات المتاحة لك:</h6>
                    <ul class="list-group list-group-flush">
                        <?php if (can_view_staff_attendance()): ?>
                            <li class="list-group-item text-success">
                                <i class="fas fa-check me-2"></i>عرض حضور العاملين
                            </li>
                        <?php endif; ?>
                        
                        <?php if (can_manage_staff_attendance()): ?>
                            <li class="list-group-item text-success">
                                <i class="fas fa-check me-2"></i>إدارة حضور العاملين
                            </li>
                        <?php endif; ?>
                        
                        <?php if (can_access_staff_data()): ?>
                            <li class="list-group-item text-success">
                                <i class="fas fa-check me-2"></i>الوصول لبيانات العاملين
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="text-danger">الصلاحيات غير المتاحة:</h6>
                    <ul class="list-group list-group-flush">
                        <?php if (!can_view_student_attendance()): ?>
                            <li class="list-group-item text-danger">
                                <i class="fas fa-times me-2"></i>عرض حضور الطلاب
                            </li>
                        <?php endif; ?>
                        
                        <?php if (!can_access_student_data()): ?>
                            <li class="list-group-item text-danger">
                                <i class="fas fa-times me-2"></i>الوصول لبيانات الطلاب
                            </li>
                        <?php endif; ?>
                        
                        <?php if (!$_SESSION['role'] === 'admin'): ?>
                            <li class="list-group-item text-muted">
                                <i class="fas fa-info-circle me-2"></i>صلاحيات المدير العام
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// إضافة تأثيرات تفاعلية
document.addEventListener('DOMContentLoaded', function() {
    // تمييز الأدوار بألوان مختلفة
    const roleBadges = document.querySelectorAll('.badge');
    roleBadges.forEach(badge => {
        badge.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.1)';
            this.style.transition = 'transform 0.2s ease';
        });
        badge.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
});
</script>

<?php include_once '../includes/footer.php'; ?>
