<?php
require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/security.php';

// التحقق من الجلسة والصلاحيات
check_session();
if (!check_permission('admin')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit();
}

// التحقق من وجود معرف القسط
$installment_id = intval($_GET['id'] ?? 0);

if ($installment_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'معرف القسط غير صحيح']);
    exit();
}

try {
    // جلب بيانات القسط مع بيانات الطالب
    $stmt = $conn->prepare("
        SELECT
            si.id,
            si.installment_number,
            si.total_amount,
            si.paid_amount,
            si.due_date,
            si.status,
            si.notes,
            si.fee_type_id,
            u.full_name as student_name,
            s.student_id,
            c.class_name,
            ft.type_name as fee_type_name
        FROM student_installments si
        JOIN students s ON si.student_id = s.id
        JOIN users u ON s.user_id = u.id
        LEFT JOIN classes c ON s.class_id = c.id
        LEFT JOIN fee_types ft ON si.fee_type_id = ft.id
        WHERE si.id = ?
    ");
    
    if (!$stmt) {
        throw new Exception('خطأ في إعداد الاستعلام: ' . $conn->error);
    }
    
    $stmt->bind_param("i", $installment_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'القسط غير موجود']);
        exit();
    }
    
    $installment = $result->fetch_assoc();
    
    // تحويل المبالغ إلى أرقام عشرية
    $installment['total_amount'] = floatval($installment['total_amount']);
    $installment['paid_amount'] = floatval($installment['paid_amount']);
    $installment['remaining_amount'] = $installment['total_amount'] - $installment['paid_amount'];
    
    // إرجاع البيانات
    echo json_encode([
        'success' => true,
        'installment' => $installment
    ]);
    
} catch (Exception $e) {
    error_log("Error in get_installment_data.php: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'خطأ في جلب البيانات: ' . $e->getMessage()
    ]);
}
?>
