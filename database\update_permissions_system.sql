-- نظام الصلاحيات المحسن والمتكامل
-- Enhanced and Integrated Permissions System
-- تاريخ الإنشاء: 2025-08-05

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- ===================================
-- 1. إنشاء جدول الأدوار المخصصة
-- ===================================

CREATE TABLE IF NOT EXISTS `custom_roles` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `role_name` varchar(50) NOT NULL,
    `role_display_name` varchar(100) NOT NULL,
    `role_description` text DEFAULT NULL,
    `is_system_role` tinyint(1) DEFAULT 0,
    `is_active` tinyint(1) DEFAULT 1,
    `created_by` int(10) UNSIGNED NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `role_name` (`role_name`),
    KEY `is_active` (`is_active`),
    KEY `created_by` (`created_by`),
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================
-- 2. إنشاء جدول موارد النظام
-- ===================================

CREATE TABLE IF NOT EXISTS `system_resources` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `resource_type` enum('page','action','data','report','feature') NOT NULL,
    `resource_key` varchar(100) NOT NULL,
    `resource_name` varchar(255) NOT NULL,
    `resource_description` text DEFAULT NULL,
    `resource_path` varchar(255) DEFAULT NULL,
    `parent_resource` varchar(100) DEFAULT NULL,
    `icon` varchar(100) DEFAULT NULL,
    `sort_order` int(11) DEFAULT 0,
    `is_active` tinyint(1) DEFAULT 1,
    `is_system_resource` tinyint(1) DEFAULT 1,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `resource_key` (`resource_key`),
    KEY `resource_type` (`resource_type`),
    KEY `is_active` (`is_active`),
    KEY `parent_resource` (`parent_resource`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================
-- 3. إنشاء جدول صلاحيات الأدوار
-- ===================================

CREATE TABLE IF NOT EXISTS `role_permissions` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `role_name` varchar(50) NOT NULL,
    `resource_key` varchar(100) NOT NULL,
    `permission_level` enum('none','read','write','full') DEFAULT 'read',
    `is_granted` tinyint(1) DEFAULT 1,
    `granted_by` int(10) UNSIGNED NOT NULL,
    `granted_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `role_resource` (`role_name`, `resource_key`),
    KEY `role_name` (`role_name`),
    KEY `resource_key` (`resource_key`),
    KEY `granted_by` (`granted_by`),
    FOREIGN KEY (`granted_by`) REFERENCES `users`(`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================
-- 4. إنشاء جدول الصلاحيات المخصصة للمستخدمين
-- ===================================

CREATE TABLE IF NOT EXISTS `user_custom_permissions` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(10) UNSIGNED NOT NULL,
    `resource_key` varchar(100) NOT NULL,
    `permission_level` enum('none','read','write','full') DEFAULT 'read',
    `is_granted` tinyint(1) DEFAULT 1,
    `granted_by` int(10) UNSIGNED NOT NULL,
    `granted_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `expires_at` timestamp NULL DEFAULT NULL,
    `notes` text DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `user_resource` (`user_id`, `resource_key`),
    KEY `user_id` (`user_id`),
    KEY `resource_key` (`resource_key`),
    KEY `granted_by` (`granted_by`),
    KEY `expires_at` (`expires_at`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`granted_by`) REFERENCES `users`(`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================
-- 5. إنشاء جدول سجل تدقيق الصلاحيات
-- ===================================

CREATE TABLE IF NOT EXISTS `permissions_audit_log` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(10) UNSIGNED NOT NULL,
    `action_type` enum('role_changed','permission_granted','permission_revoked','login','logout','access_denied') NOT NULL,
    `resource_key` varchar(100) DEFAULT NULL,
    `old_value` varchar(255) DEFAULT NULL,
    `new_value` varchar(255) DEFAULT NULL,
    `changed_by` int(10) UNSIGNED DEFAULT NULL,
    `ip_address` varchar(45) DEFAULT NULL,
    `user_agent` text DEFAULT NULL,
    `notes` text DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `user_id` (`user_id`),
    KEY `action_type` (`action_type`),
    KEY `changed_by` (`changed_by`),
    KEY `created_at` (`created_at`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`changed_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================
-- 6. تحديث جدول المستخدمين لدعم الأدوار المخصصة
-- ===================================

-- إضافة عمود للدور المخصص
ALTER TABLE `users` 
ADD COLUMN `custom_role_id` int(11) DEFAULT NULL AFTER `role`,
ADD KEY `custom_role_id` (`custom_role_id`);

-- إضافة قيد خارجي للدور المخصص
-- ALTER TABLE `users` 
-- ADD FOREIGN KEY (`custom_role_id`) REFERENCES `custom_roles`(`id`) ON DELETE SET NULL;

-- ===================================
-- 7. إدراج الأدوار الأساسية
-- ===================================

INSERT IGNORE INTO `custom_roles` (`role_name`, `role_display_name`, `role_description`, `is_system_role`, `created_by`) VALUES
('admin', 'مدير النظام', 'مدير عام للنظام مع جميع الصلاحيات', 1, 1),
('teacher', 'معلم', 'معلم في المدرسة', 1, 1),
('student', 'طالب', 'طالب في المدرسة', 1, 1),
('staff', 'موظف إداري', 'موظف إداري في المدرسة', 1, 1),
('financial_manager', 'مدير مالي', 'مسؤول عن الشؤون المالية', 1, 1),
('academic_supervisor', 'مشرف أكاديمي', 'مشرف على الشؤون الأكاديمية', 1, 1),
('librarian', 'أمين مكتبة', 'مسؤول عن المكتبة', 1, 1),
('nurse', 'ممرض/ة', 'مسؤول عن الصحة المدرسية', 1, 1),
('security', 'أمن', 'مسؤول عن الأمن', 1, 1),
('maintenance', 'صيانة', 'مسؤول عن الصيانة', 1, 1),
('parent', 'ولي أمر', 'ولي أمر طالب', 1, 1);

-- ===================================
-- 8. إدراج موارد النظام الأساسية
-- ===================================

INSERT IGNORE INTO `system_resources` (`resource_type`, `resource_key`, `resource_name`, `resource_description`, `resource_path`, `parent_resource`, `icon`, `sort_order`) VALUES
-- صفحات إدارة المستخدمين
('page', 'users_view', 'عرض المستخدمين', 'صفحة عرض قائمة المستخدمين', '/users/', NULL, 'fas fa-users', 10),
('page', 'users_add', 'إضافة مستخدم', 'صفحة إضافة مستخدم جديد', '/users/add.php', 'users_view', 'fas fa-user-plus', 11),
('page', 'users_edit', 'تعديل مستخدم', 'صفحة تعديل بيانات المستخدم', '/users/edit.php', 'users_view', 'fas fa-user-edit', 12),
('page', 'users_delete', 'حذف مستخدم', 'صفحة حذف المستخدم', '/users/delete.php', 'users_view', 'fas fa-user-times', 13),

-- صفحات إدارة الطلاب
('page', 'students_view', 'عرض الطلاب', 'صفحة عرض قائمة الطلاب', '/students/', NULL, 'fas fa-graduation-cap', 20),
('page', 'students_add', 'إضافة طالب', 'صفحة إضافة طالب جديد', '/students/add.php', 'students_view', 'fas fa-user-graduate', 21),
('page', 'students_edit', 'تعديل طالب', 'صفحة تعديل بيانات الطالب', '/students/edit.php', 'students_view', 'fas fa-edit', 22),
('page', 'students_delete', 'حذف طالب', 'صفحة حذف الطالب', '/students/delete.php', 'students_view', 'fas fa-trash', 23),
('page', 'students_import', 'استيراد طلاب', 'صفحة استيراد بيانات الطلاب', '/students/import.php', 'students_view', 'fas fa-file-import', 24),

-- صفحات إدارة المعلمين
('page', 'teachers_view', 'عرض المعلمين', 'صفحة عرض قائمة المعلمين', '/teachers/', NULL, 'fas fa-chalkboard-teacher', 30),
('page', 'teachers_add', 'إضافة معلم', 'صفحة إضافة معلم جديد', '/teachers/add.php', 'teachers_view', 'fas fa-user-plus', 31),
('page', 'teachers_edit', 'تعديل معلم', 'صفحة تعديل بيانات المعلم', '/teachers/edit.php', 'teachers_view', 'fas fa-edit', 32),
('page', 'teachers_delete', 'حذف معلم', 'صفحة حذف المعلم', '/teachers/delete.php', 'teachers_view', 'fas fa-trash', 33),

-- صفحات إدارة الفصول
('page', 'classes_view', 'عرض الفصول', 'صفحة عرض قائمة الفصول', '/classes/', NULL, 'fas fa-door-open', 40),
('page', 'classes_add', 'إضافة فصل', 'صفحة إضافة فصل جديد', '/classes/add.php', 'classes_view', 'fas fa-plus', 41),
('page', 'classes_edit', 'تعديل فصل', 'صفحة تعديل بيانات الفصل', '/classes/edit.php', 'classes_view', 'fas fa-edit', 42),
('page', 'classes_delete', 'حذف فصل', 'صفحة حذف الفصل', '/classes/delete.php', 'classes_view', 'fas fa-trash', 43),

-- صفحات إدارة المواد
('page', 'subjects_view', 'عرض المواد', 'صفحة عرض قائمة المواد', '/subjects/', NULL, 'fas fa-book', 50),
('page', 'subjects_add', 'إضافة مادة', 'صفحة إضافة مادة جديدة', '/subjects/add.php', 'subjects_view', 'fas fa-plus', 51),
('page', 'subjects_edit', 'تعديل مادة', 'صفحة تعديل بيانات المادة', '/subjects/edit.php', 'subjects_view', 'fas fa-edit', 52),
('page', 'subjects_delete', 'حذف مادة', 'صفحة حذف المادة', '/subjects/delete.php', 'subjects_view', 'fas fa-trash', 53),

-- صفحات الحضور والغياب
('page', 'attendance_view', 'عرض الحضور', 'صفحة عرض سجلات الحضور', '/attendance/', NULL, 'fas fa-calendar-check', 60),
('page', 'attendance_take', 'أخذ الحضور', 'صفحة تسجيل الحضور', '/attendance/take_attendance.php', 'attendance_view', 'fas fa-check', 61),
('page', 'attendance_reports', 'تقارير الحضور', 'صفحة تقارير الحضور والغياب', '/attendance/reports.php', 'attendance_view', 'fas fa-chart-bar', 62),

-- صفحات النظام المالي
('page', 'finance_view', 'عرض المالية', 'صفحة عرض البيانات المالية', '/finance/', NULL, 'fas fa-money-bill-wave', 70),
('page', 'finance_fees', 'إدارة الرسوم', 'صفحة إدارة رسوم الطلاب', '/finance/fees/', 'finance_view', 'fas fa-dollar-sign', 71),
('page', 'finance_payments', 'إدارة المدفوعات', 'صفحة إدارة المدفوعات', '/finance/payments/', 'finance_view', 'fas fa-credit-card', 72),
('page', 'finance_reports', 'التقارير المالية', 'صفحة التقارير المالية', '/finance/reports/', 'finance_view', 'fas fa-chart-line', 73),

-- صفحات الامتحانات
('page', 'exams_view', 'عرض الامتحانات', 'صفحة عرض قائمة الامتحانات', '/exams/', NULL, 'fas fa-file-alt', 80),
('page', 'exams_add', 'إضافة امتحان', 'صفحة إضافة امتحان جديد', '/exams/add.php', 'exams_view', 'fas fa-plus', 81),
('page', 'exams_edit', 'تعديل امتحان', 'صفحة تعديل بيانات الامتحان', '/exams/edit.php', 'exams_view', 'fas fa-edit', 82),
('page', 'exams_grade', 'تصحيح الامتحانات', 'صفحة تصحيح ورصد الدرجات', '/exams/grade.php', 'exams_view', 'fas fa-star', 83),

-- صفحات التقارير
('page', 'reports_view', 'عرض التقارير', 'صفحة عرض التقارير العامة', '/reports/', NULL, 'fas fa-chart-bar', 90),
('page', 'reports_students', 'تقارير الطلاب', 'صفحة تقارير الطلاب', '/reports/students.php', 'reports_view', 'fas fa-users', 91),
('page', 'reports_financial', 'التقارير المالية', 'صفحة التقارير المالية', '/reports/financial.php', 'reports_view', 'fas fa-money-bill', 92),
('page', 'reports_attendance', 'تقارير الحضور', 'صفحة تقارير الحضور', '/reports/attendance.php', 'reports_view', 'fas fa-calendar', 93),

-- صفحات الإعدادات
('page', 'settings_view', 'عرض الإعدادات', 'صفحة عرض إعدادات النظام', '/settings/', NULL, 'fas fa-cog', 100),
('page', 'settings_permissions', 'إدارة الصلاحيات', 'صفحة إدارة الصلاحيات والأدوار', '/settings/permissions.php', 'settings_view', 'fas fa-key', 101),
('page', 'settings_system', 'إعدادات النظام', 'صفحة إعدادات النظام العامة', '/settings/system.php', 'settings_view', 'fas fa-tools', 102);

COMMIT;
