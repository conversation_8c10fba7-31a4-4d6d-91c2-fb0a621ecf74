<?php
/**
 * إصلاح سريع للأعمدة المفقودة
 * Quick Fix for Missing Columns
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'school_management';

echo "<h1>🔧 إصلاح سريع للأعمدة المفقودة</h1>";

try {
    $conn = new mysqli($host, $username, $password, $database);
    
    if ($conn->connect_error) {
        die("❌ فشل الاتصال: " . $conn->connect_error);
    }
    
    echo "<p>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // قائمة الأوامر المطلوبة
    $commands = [
        "إضافة عمود receipt_number" => "ALTER TABLE `student_installments` ADD COLUMN `receipt_number` varchar(50) DEFAULT NULL",
        "إضافة عمود bank_account_id" => "ALTER TABLE `student_installments` ADD COLUMN `bank_account_id` int(10) UNSIGNED DEFAULT NULL",
        "إضافة عمود iban" => "ALTER TABLE `bank_accounts` ADD COLUMN `iban` varchar(50) DEFAULT NULL",
        "إضافة فهرس receipt_number" => "ALTER TABLE `student_installments` ADD KEY `idx_receipt_number` (`receipt_number`)",
        "إضافة فهرس bank_account_id" => "ALTER TABLE `student_installments` ADD KEY `idx_bank_account_id` (`bank_account_id`)",
        "تحديث بيانات IBAN" => "UPDATE bank_accounts SET iban = CASE WHEN id = 1 THEN '**************************' WHEN id = 2 THEN '**************************' ELSE CONCAT('EG', LPAD(id, 2, '0'), '0003000', LPAD(account_number, 15, '0')) END WHERE iban IS NULL",
        "تحديث bank_account_id" => "UPDATE student_installments SET bank_account_id = 1 WHERE bank_account_id IS NULL"
    ];
    
    echo "<h2>📋 تنفيذ الأوامر:</h2>";
    
    foreach ($commands as $description => $sql) {
        echo "<p><strong>$description:</strong> ";
        
        if ($conn->query($sql)) {
            echo "<span style='color: green;'>✅ نجح</span></p>";
        } else {
            $error = $conn->error;
            if (strpos($error, 'Duplicate column name') !== false || 
                strpos($error, 'Duplicate key name') !== false ||
                strpos($error, 'already exists') !== false) {
                echo "<span style='color: orange;'>⚠️ موجود بالفعل</span></p>";
            } else {
                echo "<span style='color: red;'>❌ فشل: $error</span></p>";
            }
        }
    }
    
    // فحص النتائج
    echo "<h2>🔍 فحص النتائج:</h2>";
    
    // فحص أعمدة student_installments
    echo "<h3>جدول student_installments:</h3>";
    $result = $conn->query("SHOW COLUMNS FROM student_installments");
    if ($result) {
        echo "<ul>";
        while ($row = $result->fetch_assoc()) {
            $column = $row['Field'];
            $highlight = in_array($column, ['receipt_number', 'bank_account_id']) ? ' style="color: green; font-weight: bold;"' : '';
            echo "<li$highlight>$column ({$row['Type']})</li>";
        }
        echo "</ul>";
    }
    
    // فحص أعمدة bank_accounts
    echo "<h3>جدول bank_accounts:</h3>";
    $result = $conn->query("SHOW COLUMNS FROM bank_accounts");
    if ($result) {
        echo "<ul>";
        while ($row = $result->fetch_assoc()) {
            $column = $row['Field'];
            $highlight = ($column === 'iban') ? ' style="color: green; font-weight: bold;"' : '';
            echo "<li$highlight>$column ({$row['Type']})</li>";
        }
        echo "</ul>";
    }
    
    // اختبار الاستعلام
    echo "<h2>🧪 اختبار الاستعلام:</h2>";
    
    $test_query = "SELECT si.id, si.receipt_number, si.bank_account_id, ba.iban 
                   FROM student_installments si 
                   LEFT JOIN bank_accounts ba ON si.bank_account_id = ba.id 
                   LIMIT 1";
    
    $test_result = $conn->query($test_query);
    if ($test_result) {
        echo "<p style='color: green;'>✅ الاستعلام يعمل بنجاح!</p>";
        if ($test_result->num_rows > 0) {
            $row = $test_result->fetch_assoc();
            echo "<p>بيانات تجريبية: ID={$row['id']}, Receipt={$row['receipt_number']}, Bank Account={$row['bank_account_id']}, IBAN={$row['iban']}</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ الاستعلام فشل: " . $conn->error . "</p>";
    }
    
    echo "<hr>";
    echo "<h2>🎉 انتهى الإصلاح!</h2>";
    echo "<p>يمكنك الآن اختبار الصفحات:</p>";
    echo "<ul>";
    echo "<li><a href='finance/installments/receipt.php?id=1' target='_blank'>صفحة الإيصال</a></li>";
    echo "<li><a href='finance/installments/edit.php?id=1' target='_blank'>صفحة التعديل</a></li>";
    echo "<li><a href='finance/installments/bank_accounts.php' target='_blank'>الحسابات البنكية</a></li>";
    echo "</ul>";
    
    $conn->close();
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
    
    echo "<h3>الحل اليدوي:</h3>";
    echo "<p>افتح phpMyAdmin ونفذ هذه الأوامر واحداً تلو الآخر:</p>";
    echo "<pre style='background: #f0f0f0; padding: 10px; border-radius: 5px;'>";
    echo "ALTER TABLE `student_installments` ADD COLUMN `receipt_number` varchar(50) DEFAULT NULL;\n";
    echo "ALTER TABLE `student_installments` ADD COLUMN `bank_account_id` int(10) UNSIGNED DEFAULT NULL;\n";
    echo "ALTER TABLE `bank_accounts` ADD COLUMN `iban` varchar(50) DEFAULT NULL;\n";
    echo "UPDATE student_installments SET bank_account_id = 1 WHERE bank_account_id IS NULL;\n";
    echo "UPDATE bank_accounts SET iban = '**************************' WHERE id = 1;\n";
    echo "UPDATE bank_accounts SET iban = '**************************' WHERE id = 2;\n";
    echo "</pre>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 900px;
    margin: 20px auto;
    padding: 20px;
    background-color: #f9f9f9;
}
h1, h2, h3 {
    color: #333;
}
ul {
    background: white;
    padding: 15px;
    border-radius: 5px;
    border-left: 4px solid #007bff;
}
pre {
    font-family: 'Courier New', monospace;
    font-size: 14px;
}
</style>
