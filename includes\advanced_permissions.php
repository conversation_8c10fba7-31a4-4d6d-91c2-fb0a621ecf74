<?php
/**
 * نظام الصلاحيات المتقدم
 * Advanced Permissions System
 */

if (!defined('SYSTEM_INIT')) {
    define('SYSTEM_INIT', true);
}

/**
 * التحقق من صلاحية مخصصة للمستخدم
 */
function has_custom_permission($user_id, $permission_type, $permission_key) {
    global $conn;
    
    if (!$conn) return false;
    
    // التحقق من الصلاحية المخصصة
    $stmt = $conn->prepare("
        SELECT is_granted 
        FROM user_custom_permissions 
        WHERE user_id = ? AND permission_type = ? AND permission_key = ? 
        AND (expires_at IS NULL OR expires_at > NOW())
    ");
    $stmt->bind_param("iss", $user_id, $permission_type, $permission_key);
    $stmt->execute();
    $result = $stmt->get_result()->fetch_assoc();
    
    return $result ? (bool)$result['is_granted'] : false;
}

/**
 * التحقق من صلاحية الوصول لصفحة
 */
function can_access_page($page_key) {
    if (!is_logged_in()) return false;
    
    $user_id = $_SESSION['user_id'];
    $user_role = $_SESSION['role'];
    
    // المدير له وصول لجميع الصفحات
    if ($user_role === 'admin') return true;
    
    // التحقق من الصلاحية المخصصة
    if (has_custom_permission($user_id, 'page', $page_key)) {
        return true;
    }
    
    // التحقق من الصلاحيات الافتراضية حسب الدور
    return check_default_role_permission($user_role, 'page', $page_key);
}

/**
 * التحقق من صلاحية تنفيذ إجراء
 */
function can_perform_action($action_key, $context = null) {
    if (!is_logged_in()) return false;
    
    $user_id = $_SESSION['user_id'];
    $user_role = $_SESSION['role'];
    
    // المدير له صلاحية جميع الإجراءات
    if ($user_role === 'admin') return true;
    
    // التحقق من الصلاحية المخصصة
    if (has_custom_permission($user_id, 'action', $action_key)) {
        return true;
    }
    
    // التحقق من الصلاحيات الافتراضية حسب الدور
    return check_default_role_permission($user_role, 'action', $action_key);
}

/**
 * التحقق من صلاحية الوصول للبيانات
 */
function can_access_data($data_key, $data_context = null) {
    if (!is_logged_in()) return false;
    
    $user_id = $_SESSION['user_id'];
    $user_role = $_SESSION['role'];
    
    // المدير له وصول لجميع البيانات
    if ($user_role === 'admin') return true;
    
    // التحقق من الصلاحية المخصصة
    if (has_custom_permission($user_id, 'data', $data_key)) {
        return true;
    }
    
    // التحقق من السياق (مثل: هل البيانات تخص المستخدم نفسه؟)
    if ($data_context && isset($data_context['owner_id']) && $data_context['owner_id'] == $user_id) {
        return true;
    }
    
    // التحقق من الصلاحيات الافتراضية حسب الدور
    return check_default_role_permission($user_role, 'data', $data_key);
}

/**
 * التحقق من صلاحية الوصول للتقارير
 */
function can_access_report($report_key) {
    if (!is_logged_in()) return false;
    
    $user_id = $_SESSION['user_id'];
    $user_role = $_SESSION['role'];
    
    // المدير له وصول لجميع التقارير
    if ($user_role === 'admin') return true;
    
    // التحقق من الصلاحية المخصصة
    if (has_custom_permission($user_id, 'report', $report_key)) {
        return true;
    }
    
    // التحقق من الصلاحيات الافتراضية حسب الدور
    return check_default_role_permission($user_role, 'report', $report_key);
}

/**
 * التحقق من الصلاحيات الافتراضية حسب الدور
 */
function check_default_role_permission($role, $type, $key) {
    $default_permissions = [
        'financial_manager' => [
            'page' => ['finance', 'students', 'reports'],
            'action' => ['view', 'create', 'edit', 'export'],
            'data' => ['financial_data', 'student_data'],
            'report' => ['financial_reports', 'student_reports']
        ],
        'teacher' => [
            'page' => ['students', 'attendance', 'exams', 'communication'],
            'action' => ['view', 'create', 'edit'],
            'data' => ['class_data', 'own_data'],
            'report' => ['student_reports', 'attendance_reports', 'exam_reports']
        ],
        'staff' => [
            'page' => ['students', 'attendance'],
            'action' => ['view'],
            'data' => ['student_data'],
            'report' => ['student_reports']
        ],
        'student' => [
            'page' => ['attendance', 'exams', 'finance'],
            'action' => ['view'],
            'data' => ['own_data'],
            'report' => []
        ],
        'parent' => [
            'page' => ['students', 'attendance', 'exams', 'finance', 'communication'],
            'action' => ['view'],
            'data' => ['child_data'],
            'report' => []
        ]
    ];
    
    $role_permissions = $default_permissions[$role] ?? [];
    $type_permissions = $role_permissions[$type] ?? [];
    
    return in_array($key, $type_permissions);
}

/**
 * الحصول على جميع صلاحيات المستخدم
 */
function get_user_all_permissions($user_id) {
    global $conn;
    
    if (!$conn) return [];
    
    $permissions = [];
    
    // جلب الصلاحيات المخصصة
    $stmt = $conn->prepare("
        SELECT permission_type, permission_key, is_granted 
        FROM user_custom_permissions 
        WHERE user_id = ? AND (expires_at IS NULL OR expires_at > NOW())
    ");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    while ($row = $result->fetch_assoc()) {
        if ($row['is_granted']) {
            $permissions[$row['permission_type']][] = $row['permission_key'];
        }
    }
    
    return $permissions;
}

/**
 * منح صلاحية مخصصة لمستخدم
 */
function grant_custom_permission($user_id, $permission_type, $permission_key, $granted_by, $expires_at = null, $notes = null) {
    global $conn;
    
    if (!$conn) return false;
    
    $stmt = $conn->prepare("
        INSERT INTO user_custom_permissions 
        (user_id, permission_type, permission_key, is_granted, granted_by, expires_at, notes) 
        VALUES (?, ?, ?, 1, ?, ?, ?)
        ON DUPLICATE KEY UPDATE 
        is_granted = 1, granted_by = ?, granted_at = NOW(), expires_at = ?, notes = ?
    ");
    $stmt->bind_param("isssssiss", $user_id, $permission_type, $permission_key, $granted_by, $expires_at, $notes, $granted_by, $expires_at, $notes);
    
    $result = $stmt->execute();
    
    if ($result) {
        // تسجيل في سجل المراجعة
        log_permission_change($user_id, 'permission_granted', $permission_type, $permission_key, null, '1', $granted_by, $notes);
    }
    
    return $result;
}

/**
 * إلغاء صلاحية مخصصة من مستخدم
 */
function revoke_custom_permission($user_id, $permission_type, $permission_key, $revoked_by, $notes = null) {
    global $conn;
    
    if (!$conn) return false;
    
    $stmt = $conn->prepare("
        UPDATE user_custom_permissions 
        SET is_granted = 0, granted_by = ?, granted_at = NOW(), notes = ?
        WHERE user_id = ? AND permission_type = ? AND permission_key = ?
    ");
    $stmt->bind_param("isiss", $revoked_by, $notes, $user_id, $permission_type, $permission_key);
    
    $result = $stmt->execute();
    
    if ($result) {
        // تسجيل في سجل المراجعة
        log_permission_change($user_id, 'permission_revoked', $permission_type, $permission_key, '1', '0', $revoked_by, $notes);
    }
    
    return $result;
}

/**
 * تسجيل تغيير الصلاحيات في سجل المراجعة
 */
function log_permission_change($user_id, $action_type, $resource_type, $resource_key, $old_value, $new_value, $changed_by, $notes = null) {
    global $conn;
    
    if (!$conn) return false;
    
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    
    $stmt = $conn->prepare("
        INSERT INTO permissions_audit_log 
        (user_id, action_type, resource_type, resource_key, old_value, new_value, changed_by, ip_address, user_agent, notes) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    $stmt->bind_param("isssssssss", $user_id, $action_type, $resource_type, $resource_key, $old_value, $new_value, $changed_by, $ip_address, $user_agent, $notes);
    
    return $stmt->execute();
}

/**
 * التحقق من الصلاحية مع إعادة توجيه في حالة عدم التوفر
 */
function require_page_access($page_key, $redirect_url = '../dashboard/') {
    if (!can_access_page($page_key)) {
        header('Location: ' . $redirect_url);
        exit();
    }
}

/**
 * التحقق من صلاحية الإجراء مع رسالة خطأ
 */
function require_action_permission($action_key, $error_message = 'غير مسموح لك بتنفيذ هذا الإجراء') {
    if (!can_perform_action($action_key)) {
        http_response_code(403);
        die('<div class="alert alert-danger text-center mt-5">
                <i class="fas fa-lock fa-3x mb-3"></i>
                <h4>وصول مرفوض</h4>
                <p>' . htmlspecialchars($error_message) . '</p>
                <a href="../dashboard/" class="btn btn-primary">العودة للرئيسية</a>
             </div>');
    }
}

/**
 * فلترة البيانات حسب صلاحيات المستخدم
 */
function filter_data_by_permissions($data, $data_key, $owner_field = 'user_id') {
    if (!is_logged_in()) return [];
    
    $user_id = $_SESSION['user_id'];
    $user_role = $_SESSION['role'];
    
    // المدير يرى جميع البيانات
    if ($user_role === 'admin') return $data;
    
    // إذا كان لديه صلاحية رؤية جميع البيانات
    if (can_access_data('all_data')) return $data;
    
    // إذا كان لديه صلاحية رؤية بيانات محددة
    if (can_access_data($data_key)) return $data;
    
    // فلترة البيانات الشخصية فقط
    if (can_access_data('own_data')) {
        return array_filter($data, function($item) use ($owner_field, $user_id) {
            return isset($item[$owner_field]) && $item[$owner_field] == $user_id;
        });
    }
    
    return [];
}
?>
