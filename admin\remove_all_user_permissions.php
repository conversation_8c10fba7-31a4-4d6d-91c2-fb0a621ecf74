<?php
/**
 * حذف جميع الصلاحيات المخصصة للمستخدم
 * Remove All Custom Permissions for User
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

$user_id = isset($_GET['user_id']) ? (int)$_GET['user_id'] : null;
$user_email = '<EMAIL>';

// جلب بيانات المستخدم
if ($user_id) {
    $user_query = "SELECT * FROM users WHERE id = ?";
    $user_stmt = $conn->prepare($user_query);
    $user_stmt->bind_param("i", $user_id);
    $user_stmt->execute();
    $user = $user_stmt->get_result()->fetch_assoc();
} else {
    $user_query = "SELECT * FROM users WHERE email = ?";
    $user_stmt = $conn->prepare($user_query);
    $user_stmt->bind_param("s", $user_email);
    $user_stmt->execute();
    $user = $user_stmt->get_result()->fetch_assoc();
}

$results = [];
$success = true;

// معالجة حذف الصلاحيات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['remove_permissions']) && $user) {
    try {
        $results[] = "🚀 بدء حذف جميع الصلاحيات المخصصة للمستخدم: " . $user['full_name'];
        
        // عد الصلاحيات الحالية
        $count_query = "SELECT COUNT(*) as total FROM user_custom_permissions WHERE user_id = ?";
        $count_stmt = $conn->prepare($count_query);
        $count_stmt->bind_param("i", $user['id']);
        $count_stmt->execute();
        $current_count = $count_stmt->get_result()->fetch_assoc()['total'];
        
        $results[] = "📊 عدد الصلاحيات الحالية: $current_count";
        
        if ($current_count > 0) {
            // حذف جميع الصلاحيات المخصصة
            $delete_stmt = $conn->prepare("DELETE FROM user_custom_permissions WHERE user_id = ?");
            $delete_stmt->bind_param("i", $user['id']);
            
            if ($delete_stmt->execute()) {
                $deleted_count = $delete_stmt->affected_rows;
                $results[] = "✅ تم حذف $deleted_count صلاحية مخصصة";
                
                // التحقق من النتيجة
                $verify_stmt = $conn->prepare("SELECT COUNT(*) as remaining FROM user_custom_permissions WHERE user_id = ?");
                $verify_stmt->bind_param("i", $user['id']);
                $verify_stmt->execute();
                $remaining = $verify_stmt->get_result()->fetch_assoc()['remaining'];
                
                if ($remaining == 0) {
                    $results[] = "✅ تم حذف جميع الصلاحيات بنجاح";
                    $results[] = "📋 المستخدم الآن لا يملك أي صلاحيات مخصصة";
                    $results[] = "⚠️ لن تظهر أي أقسام في القائمة الجانبية للمستخدم";
                } else {
                    $results[] = "⚠️ تبقى $remaining صلاحية لم يتم حذفها";
                }
            } else {
                throw new Exception("فشل في حذف الصلاحيات: " . $conn->error);
            }
        } else {
            $results[] = "ℹ️ المستخدم لا يملك أي صلاحيات مخصصة للحذف";
        }
        
        $results[] = "\n🎉 تم حذف جميع الصلاحيات المخصصة بنجاح!";
        $results[] = "📋 يمكنك الآن منح صلاحيات جديدة حسب احتياجاتك";
        
    } catch (Exception $e) {
        $success = false;
        $results[] = "❌ خطأ: " . $e->getMessage();
    }
}

$page_title = 'حذف جميع الصلاحيات المخصصة';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-trash me-2"></i><?php echo $page_title; ?></h2>
            <p class="text-muted">حذف جميع الصلاحيات المخصصة للمستخدم: <?php echo $user ? safe_html($user['full_name']) : $user_email; ?></p>
        </div>
        <div>
            <a href="check_actual_user_permissions.php" class="btn btn-outline-info me-2">
                <i class="fas fa-user-check me-2"></i>فحص الصلاحيات الفعلية
            </a>
            <a href="../settings/permissions.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة
            </a>
        </div>
    </div>

    <?php if (!$user): ?>
        <div class="alert alert-danger">
            <h5><i class="fas fa-exclamation-triangle me-2"></i>خطأ!</h5>
            <p class="mb-0">المستخدم غير موجود!</p>
        </div>
    <?php else: ?>

        <!-- معلومات المستخدم -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5><i class="fas fa-user me-2"></i>معلومات المستخدم</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <strong>الاسم:</strong><br>
                        <?php echo safe_html($user['full_name']); ?>
                    </div>
                    <div class="col-md-4">
                        <strong>البريد الإلكتروني:</strong><br>
                        <?php echo safe_html($user['email']); ?>
                    </div>
                    <div class="col-md-4">
                        <strong>الدور:</strong><br>
                        <span class="badge bg-warning"><?php echo safe_html($user['role']); ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- نتائج الحذف -->
        <?php if (!empty($results)): ?>
            <div class="card mb-4">
                <div class="card-header <?php echo $success ? 'bg-success' : 'bg-danger'; ?> text-white">
                    <h5>
                        <i class="fas fa-<?php echo $success ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo $success ? 'تم الحذف بنجاح!' : 'فشل في الحذف'; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="bg-dark text-light p-3 rounded" style="font-family: monospace; white-space: pre-line; max-height: 400px; overflow-y: auto;">
<?php echo implode("\n", $results); ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- نموذج الحذف -->
        <?php if (empty($results) || !$success): ?>
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h5><i class="fas fa-trash me-2"></i>حذف جميع الصلاحيات المخصصة</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>تحذير مهم:</h6>
                        <p class="mb-2">هذا الإجراء سيقوم بحذف <strong>جميع</strong> الصلاحيات المخصصة لهذا المستخدم.</p>
                        <p class="mb-0">بعد الحذف، لن يتمكن المستخدم من الوصول لأي قسم في النظام حتى تمنحه صلاحيات جديدة.</p>
                    </div>

                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>ما سيحدث:</h6>
                        <ol>
                            <li><strong>حذف جميع الصلاحيات:</strong> إزالة كل الصلاحيات المخصصة من قاعدة البيانات</li>
                            <li><strong>إخفاء القائمة الجانبية:</strong> لن تظهر أي أقسام للمستخدم</li>
                            <li><strong>منع الوصول:</strong> لن يتمكن من الوصول لأي صفحة محمية</li>
                            <li><strong>إمكانية الاستعادة:</strong> يمكنك منح صلاحيات جديدة في أي وقت</li>
                        </ol>
                    </div>

                    <form method="POST">
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="confirmRemove" required>
                            <label class="form-check-label" for="confirmRemove">
                                <strong>أؤكد أنني أريد حذف جميع الصلاحيات المخصصة لهذا المستخدم</strong>
                            </label>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="check_actual_user_permissions.php" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" name="remove_permissions" class="btn btn-danger" id="removeButton" disabled>
                                <i class="fas fa-trash me-2"></i>حذف جميع الصلاحيات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        <?php endif; ?>

        <!-- الخطوات التالية -->
        <?php if ($success && !empty($results)): ?>
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-route me-2"></i>الخطوات التالية</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-success">
                        <h6><i class="fas fa-thumbs-up me-2"></i>تم حذف جميع الصلاحيات!</h6>
                        <p class="mb-0">المستخدم الآن لا يملك أي صلاحيات مخصصة.</p>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">يمكنك الآن:</h6>
                            <ul>
                                <li><strong>منح صلاحيات جديدة</strong> حسب احتياجاتك</li>
                                <li><strong>اختيار صلاحيات محددة</strong> فقط</li>
                                <li><strong>تجنب الصلاحيات غير المرغوبة</strong></li>
                                <li><strong>التحكم الكامل</strong> في ما يراه المستخدم</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-warning">حالة المستخدم الحالية:</h6>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">❌ لا توجد صلاحيات مخصصة</li>
                                <li class="list-group-item">❌ لن تظهر أقسام في القائمة</li>
                                <li class="list-group-item">❌ لا يمكن الوصول للصفحات</li>
                                <li class="list-group-item">✅ جاهز لصلاحيات جديدة</li>
                            </ul>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <a href="custom_permissions_manager.php?user_id=<?php echo $user['id']; ?>" class="btn btn-primary me-2">
                            <i class="fas fa-plus me-2"></i>منح صلاحيات جديدة
                        </a>
                        <a href="check_actual_user_permissions.php" class="btn btn-info me-2">
                            <i class="fas fa-user-check me-2"></i>فحص الصلاحيات الفعلية
                        </a>
                        <button class="btn btn-secondary" onclick="window.location.reload()">
                            <i class="fas fa-redo me-2"></i>إعادة الفحص
                        </button>
                    </div>
                </div>
            </div>
        <?php endif; ?>

    <?php endif; ?>
</div>

<script>
// تفعيل زر الحذف عند تأكيد الاختيار
document.getElementById('confirmRemove')?.addEventListener('change', function() {
    document.getElementById('removeButton').disabled = !this.checked;
});
</script>

<?php include_once '../includes/footer.php'; ?>
