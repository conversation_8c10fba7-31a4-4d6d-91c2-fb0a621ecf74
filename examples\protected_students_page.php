<?php
/**
 * مثال: صفحة الطلاب المحمية
 * Example: Protected Students Page
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/page_protection.php';

// حماية الصفحة - يتطلب صلاحية مشاهدة الطلاب
protect_page('students', 'view');

// الحصول على بيانات الطلاب (مثال)
$students = [];
try {
    $result = $conn->query("SELECT id, full_name, email, phone FROM users WHERE role = 'student' LIMIT 10");
    while ($row = $result->fetch_assoc()) {
        $students[] = $row;
    }
} catch (Exception $e) {
    // في حالة عدم وجود جدول المستخدمين، استخدم بيانات تجريبية
    $students = [
        ['id' => 1, 'full_name' => 'أحمد محمد', 'email' => '<EMAIL>', 'phone' => '123456789'],
        ['id' => 2, 'full_name' => 'فاطمة علي', 'email' => '<EMAIL>', 'phone' => '987654321'],
        ['id' => 3, 'full_name' => 'محمد حسن', 'email' => '<EMAIL>', 'phone' => '555666777']
    ];
}

$page_title = 'إدارة الطلاب - مثال محمي';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <!-- رأس الصفحة -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="fas fa-users me-2 text-primary"></i><?php echo $page_title; ?></h2>
                <p class="text-muted">مثال على صفحة محمية بالنظام البسيط للصلاحيات</p>
            </div>
            <div>
                <a href="../dashboard/" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>العودة
                </a>
            </div>
        </div>

        <!-- معلومات الصلاحيات -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-shield-alt me-2"></i>صلاحياتك في هذه الصفحة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>مشاهدة</span>
                                    <?php if (can_perform_action('students', 'view')): ?>
                                        <span class="badge bg-success">مسموح</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">مرفوض</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>إضافة</span>
                                    <?php if (can_perform_action('students', 'add')): ?>
                                        <span class="badge bg-success">مسموح</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">مرفوض</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>تعديل</span>
                                    <?php if (can_perform_action('students', 'edit')): ?>
                                        <span class="badge bg-success">مسموح</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">مرفوض</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>حذف</span>
                                    <?php if (can_perform_action('students', 'delete')): ?>
                                        <span class="badge bg-success">مسموح</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">مرفوض</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار العمليات المشروطة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-tools me-2"></i>العمليات المتاحة</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        echo render_action_buttons('students', [
                            'add' => [
                                'onclick' => 'alert("إضافة طالب جديد")',
                                'text' => 'إضافة طالب'
                            ],
                            'edit' => [
                                'onclick' => 'alert("تعديل بيانات الطالب")',
                                'text' => 'تعديل'
                            ],
                            'delete' => [
                                'onclick' => 'alert("حذف الطالب")',
                                'text' => 'حذف'
                            ]
                        ]);
                        ?>
                        
                        <?php if (!can_perform_action('students', 'add') && 
                                  !can_perform_action('students', 'edit') && 
                                  !can_perform_action('students', 'delete')): ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                لديك صلاحية المشاهدة فقط
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- قائمة الطلاب -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list me-2"></i>قائمة الطلاب</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($students)): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الرقم</th>
                                            <th>الاسم الكامل</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>الهاتف</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($students as $student): ?>
                                            <tr>
                                                <td><?php echo $student['id']; ?></td>
                                                <td><?php echo htmlspecialchars($student['full_name']); ?></td>
                                                <td><?php echo htmlspecialchars($student['email']); ?></td>
                                                <td><?php echo htmlspecialchars($student['phone']); ?></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <?php if (can_perform_action('students', 'view')): ?>
                                                            <button class="btn btn-outline-primary" onclick="viewStudent(<?php echo $student['id']; ?>)">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                        
                                                        <?php if (can_perform_action('students', 'edit')): ?>
                                                            <button class="btn btn-outline-warning" onclick="editStudent(<?php echo $student['id']; ?>)">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                        
                                                        <?php if (can_perform_action('students', 'delete')): ?>
                                                            <button class="btn btn-outline-danger" onclick="deleteStudent(<?php echo $student['id']; ?>)">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-users fa-3x mb-3"></i>
                                <p>لا توجد بيانات طلاب</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- نموذج مشروط -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-plus me-2"></i>إضافة طالب جديد</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        echo render_conditional_form('students', 'add', '
                            <form>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الاسم الكامل</label>
                                        <input type="text" class="form-control" placeholder="أدخل الاسم الكامل">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" placeholder="أدخل البريد الإلكتروني">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" placeholder="أدخل رقم الهاتف">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تاريخ الميلاد</label>
                                        <input type="date" class="form-control">
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-2"></i>حفظ الطالب
                                </button>
                            </form>
                        ');
                        ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات النظام -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle me-2"></i>معلومات النظام</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb me-2"></i>كيف يعمل النظام:</h6>
                            <ul class="mb-0">
                                <li><strong>المدير:</strong> له جميع الصلاحيات تلقائياً</li>
                                <li><strong>المستخدمون العاديون:</strong> يحتاجون صلاحيات مخصصة</li>
                                <li><strong>الحماية:</strong> تتم على مستوى الصفحة والعملية</li>
                                <li><strong>المرونة:</strong> يمكن إخفاء/إظهار المحتوى حسب الصلاحية</li>
                            </ul>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h6>الكود المستخدم:</h6>
                                <pre><code>// حماية الصفحة
protect_page('students', 'view');

// فحص صلاحية
if (can_perform_action('students', 'edit')) {
    // عرض زر التعديل
}</code></pre>
                            </div>
                            <div class="col-md-6">
                                <h6>المستخدم الحالي:</h6>
                                <ul>
                                    <li><strong>الاسم:</strong> <?php echo htmlspecialchars($_SESSION['username'] ?? 'غير محدد'); ?></li>
                                    <li><strong>الدور:</strong> <?php echo htmlspecialchars($_SESSION['role'] ?? 'غير محدد'); ?></li>
                                    <li><strong>الحالة:</strong> 
                                        <?php if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin'): ?>
                                            <span class="badge bg-success">مدير - جميع الصلاحيات</span>
                                        <?php else: ?>
                                            <span class="badge bg-info">مستخدم عادي - صلاحيات مخصصة</span>
                                        <?php endif; ?>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewStudent(id) {
            alert('عرض تفاصيل الطالب رقم: ' + id);
        }
        
        function editStudent(id) {
            alert('تعديل بيانات الطالب رقم: ' + id);
        }
        
        function deleteStudent(id) {
            if (confirm('هل أنت متأكد من حذف هذا الطالب؟')) {
                alert('تم حذف الطالب رقم: ' + id);
            }
        }
    </script>
</body>
</html>
