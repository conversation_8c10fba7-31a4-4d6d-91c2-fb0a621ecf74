-- تحديث جدول المستخدمين لإضافة الأدوار الجديدة
-- Update users table to add new roles

-- تحديث enum للأدوار في جدول المستخدمين
ALTER TABLE `users` 
MODIFY COLUMN `role` ENUM('admin','teacher','student','staff','parent','financial_manager','librarian','nurse','security','maintenance') NOT NULL;

-- إنشاء جدول الصلاحيات المخصصة (اختياري للمستقبل)
CREATE TABLE IF NOT EXISTS `user_permissions` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `user_id` INT(10) UNSIGNED NOT NULL,
    `permission` VARCHAR(100) NOT NULL,
    `granted` TINYINT(1) DEFAULT 1,
    `granted_by` INT(10) UNSIGNED NULL,
    `granted_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `expires_at` TIMESTAMP NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_user_permission` (`user_id`, `permission`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_permission` (`permission`),
    KEY `idx_granted_by` (`granted_by`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`granted_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول مجموعات الصلاحيات (اختياري للمستقبل)
CREATE TABLE IF NOT EXISTS `permission_groups` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `group_name` VARCHAR(100) NOT NULL,
    `description` TEXT NULL,
    `permissions` JSON NOT NULL,
    `is_active` TINYINT(1) DEFAULT 1,
    `created_by` INT(10) UNSIGNED NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_group_name` (`group_name`),
    KEY `idx_created_by` (`created_by`),
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج مجموعات صلاحيات افتراضية
INSERT IGNORE INTO `permission_groups` (`group_name`, `description`, `permissions`, `created_by`) VALUES
('مدير_مالي_كامل', 'صلاحيات كاملة للنظام المالي', 
 '["finance.view", "finance.create", "finance.edit", "finance.delete", "finance.view_reports", "finance.manage_fees", "finance.manage_expenses", "students.view", "reports.view", "reports.export"]', 
 1),
('معلم_أساسي', 'صلاحيات أساسية للمعلم', 
 '["students.view", "students.view_grades", "students.edit_grades", "attendance.view", "attendance.create", "attendance.edit", "exams.view", "exams.create", "exams.edit", "exams.grade", "reports.view", "communication.view", "communication.send"]', 
 1),
('موظف_استقبال', 'صلاحيات موظف الاستقبال', 
 '["students.view", "attendance.view", "communication.view", "reports.view"]', 
 1);

-- إنشاء جدول ربط المستخدمين بمجموعات الصلاحيات
CREATE TABLE IF NOT EXISTS `user_permission_groups` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `user_id` INT(10) UNSIGNED NOT NULL,
    `group_id` INT(11) NOT NULL,
    `assigned_by` INT(10) UNSIGNED NOT NULL,
    `assigned_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `expires_at` TIMESTAMP NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_user_group` (`user_id`, `group_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_group_id` (`group_id`),
    KEY `idx_assigned_by` (`assigned_by`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`group_id`) REFERENCES `permission_groups`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`assigned_by`) REFERENCES `users`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول سجل تغييرات الصلاحيات
CREATE TABLE IF NOT EXISTS `permission_audit_log` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `user_id` INT(10) UNSIGNED NOT NULL,
    `action` ENUM('role_changed', 'permission_granted', 'permission_revoked', 'group_assigned', 'group_removed') NOT NULL,
    `old_value` VARCHAR(255) NULL,
    `new_value` VARCHAR(255) NULL,
    `changed_by` INT(10) UNSIGNED NOT NULL,
    `ip_address` VARCHAR(45) NULL,
    `user_agent` TEXT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_changed_by` (`changed_by`),
    KEY `idx_action` (`action`),
    KEY `idx_created_at` (`created_at`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`changed_by`) REFERENCES `users`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة بعض المستخدمين التجريبيين بالأدوار الجديدة (اختياري)
-- INSERT INTO `users` (`full_name`, `username`, `email`, `password`, `role`, `status`) VALUES
-- ('مدير مالي تجريبي', 'financial_manager', '<EMAIL>', '$2y$10$example_hash', 'financial_manager', 'active'),
-- ('أمين مكتبة تجريبي', 'librarian', '<EMAIL>', '$2y$10$example_hash', 'librarian', 'active');

-- إنشاء views مفيدة لعرض الصلاحيات
CREATE OR REPLACE VIEW `user_permissions_view` AS
SELECT 
    u.id as user_id,
    u.full_name,
    u.username,
    u.email,
    u.role,
    u.status,
    GROUP_CONCAT(DISTINCT up.permission) as custom_permissions,
    GROUP_CONCAT(DISTINCT pg.group_name) as permission_groups
FROM users u
LEFT JOIN user_permissions up ON u.id = up.user_id AND up.granted = 1 AND (up.expires_at IS NULL OR up.expires_at > NOW())
LEFT JOIN user_permission_groups upg ON u.id = upg.user_id AND (upg.expires_at IS NULL OR upg.expires_at > NOW())
LEFT JOIN permission_groups pg ON upg.group_id = pg.id AND pg.is_active = 1
WHERE u.status != 'deleted'
GROUP BY u.id, u.full_name, u.username, u.email, u.role, u.status;

-- إنشاء stored procedure لتحديث صلاحيات المستخدم مع تسجيل التغييرات
DELIMITER //
CREATE OR REPLACE PROCEDURE UpdateUserRole(
    IN p_user_id INT,
    IN p_new_role VARCHAR(50),
    IN p_changed_by INT,
    IN p_ip_address VARCHAR(45)
)
BEGIN
    DECLARE old_role VARCHAR(50);
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- الحصول على الدور القديم
    SELECT role INTO old_role FROM users WHERE id = p_user_id;
    
    -- تحديث الدور
    UPDATE users SET role = p_new_role WHERE id = p_user_id;
    
    -- تسجيل التغيير
    INSERT INTO permission_audit_log (user_id, action, old_value, new_value, changed_by, ip_address)
    VALUES (p_user_id, 'role_changed', old_role, p_new_role, p_changed_by, p_ip_address);
    
    COMMIT;
END //
DELIMITER ;

-- إنشاء function للتحقق من صلاحية المستخدم
DELIMITER //
CREATE OR REPLACE FUNCTION HasUserPermission(
    p_user_id INT,
    p_permission VARCHAR(100)
) RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE user_role VARCHAR(50);
    DECLARE has_custom_permission INT DEFAULT 0;
    DECLARE has_group_permission INT DEFAULT 0;
    
    -- الحصول على دور المستخدم
    SELECT role INTO user_role FROM users WHERE id = p_user_id AND status = 'active';
    
    -- المدير له جميع الصلاحيات
    IF user_role = 'admin' THEN
        RETURN TRUE;
    END IF;
    
    -- التحقق من الصلاحيات المخصصة
    SELECT COUNT(*) INTO has_custom_permission
    FROM user_permissions 
    WHERE user_id = p_user_id 
    AND permission = p_permission 
    AND granted = 1 
    AND (expires_at IS NULL OR expires_at > NOW());
    
    IF has_custom_permission > 0 THEN
        RETURN TRUE;
    END IF;
    
    -- التحقق من صلاحيات المجموعات
    SELECT COUNT(*) INTO has_group_permission
    FROM user_permission_groups upg
    JOIN permission_groups pg ON upg.group_id = pg.id
    WHERE upg.user_id = p_user_id
    AND pg.is_active = 1
    AND JSON_CONTAINS(pg.permissions, JSON_QUOTE(p_permission))
    AND (upg.expires_at IS NULL OR upg.expires_at > NOW());
    
    IF has_group_permission > 0 THEN
        RETURN TRUE;
    END IF;
    
    RETURN FALSE;
END //
DELIMITER ;
