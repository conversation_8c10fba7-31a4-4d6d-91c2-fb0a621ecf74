<?php
/**
 * إصلاح سريع لصلاحيات المستخدم <EMAIL>
 * Quick <NAME_EMAIL> Permissions
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

$user_email = '<EMAIL>';
$results = [];

// جلب بيانات المستخدم
$user_query = "SELECT * FROM users WHERE email = ?";
$user_stmt = $conn->prepare($user_query);
$user_stmt->bind_param("s", $user_email);
$user_stmt->execute();
$user = $user_stmt->get_result()->fetch_assoc();

if (!$user) {
    die("❌ المستخدم <EMAIL> غير موجود في قاعدة البيانات!");
}

$results[] = "✅ المستخدم موجود: " . $user['full_name'] . " (ID: " . $user['id'] . ")";
$results[] = "📋 الدور الحالي: " . $user['role'];
$results[] = "🔄 الحالة: " . $user['status'];

// التحقق من وجود جدول system_resources
$tables_check = $conn->query("SHOW TABLES LIKE 'system_resources'");
if ($tables_check->num_rows == 0) {
    $results[] = "❌ جدول system_resources غير موجود!";
    
    // إنشاء الجدول
    $create_table = "
    CREATE TABLE IF NOT EXISTS `system_resources` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `resource_type` enum('page','action','data','report') NOT NULL,
        `resource_key` varchar(100) NOT NULL,
        `resource_name` varchar(255) NOT NULL,
        `resource_description` text,
        `resource_path` varchar(255) DEFAULT NULL,
        `parent_resource` varchar(100) DEFAULT NULL,
        `icon` varchar(100) DEFAULT NULL,
        `sort_order` int(11) DEFAULT 0,
        `is_active` tinyint(1) DEFAULT 1,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `resource_key` (`resource_key`),
        KEY `resource_type` (`resource_type`),
        KEY `is_active` (`is_active`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    if ($conn->query($create_table)) {
        $results[] = "✅ تم إنشاء جدول system_resources";
    } else {
        $results[] = "❌ فشل في إنشاء جدول system_resources: " . $conn->error;
    }
}

// التحقق من وجود جدول user_custom_permissions
$permissions_check = $conn->query("SHOW TABLES LIKE 'user_custom_permissions'");
if ($permissions_check->num_rows == 0) {
    $results[] = "❌ جدول user_custom_permissions غير موجود!";
    
    // إنشاء الجدول
    $create_permissions = "
    CREATE TABLE IF NOT EXISTS `user_custom_permissions` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `permission_key` varchar(100) NOT NULL,
        `is_granted` tinyint(1) DEFAULT 1,
        `granted_by` int(11) DEFAULT NULL,
        `granted_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `revoked_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        UNIQUE KEY `user_permission` (`user_id`,`permission_key`),
        KEY `user_id` (`user_id`),
        KEY `permission_key` (`permission_key`),
        KEY `is_granted` (`is_granted`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    if ($conn->query($create_permissions)) {
        $results[] = "✅ تم إنشاء جدول user_custom_permissions";
    } else {
        $results[] = "❌ فشل في إنشاء جدول user_custom_permissions: " . $conn->error;
    }
}

// إضافة الموارد الأساسية
$basic_resources = [
    ['page', 'classes_view', 'عرض الفصول', 'صفحة عرض قائمة الفصول الدراسية', '/classes/', '', 'fas fa-school', 100],
    ['page', 'subjects_view', 'عرض المواد', 'صفحة عرض قائمة المواد الدراسية', '/subjects/', '', 'fas fa-book', 110],
    ['page', 'exams_view', 'عرض الامتحانات', 'صفحة عرض قائمة الامتحانات', '/exams/', '', 'fas fa-file-alt', 120],
    ['page', 'reports_view', 'عرض التقارير', 'صفحة عرض التقارير العامة', '/reports/', '', 'fas fa-chart-bar', 130],
    ['report', 'student_reports', 'تقارير الطلاب', 'تقارير خاصة بالطلاب', '/reports/students.php', 'reports_view', 'fas fa-user-graduate', 131],
    ['report', 'attendance_reports', 'تقارير الحضور', 'تقارير الحضور والغياب', '/reports/attendance.php', 'reports_view', 'fas fa-calendar-check', 132],
    ['report', 'exam_reports', 'تقارير الامتحانات', 'تقارير الامتحانات والدرجات', '/reports/exams.php', 'reports_view', 'fas fa-file-alt', 133]
];

$resource_stmt = $conn->prepare("
    INSERT IGNORE INTO system_resources 
    (resource_type, resource_key, resource_name, resource_description, resource_path, parent_resource, icon, sort_order, is_active) 
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)
");

$added_resources = 0;
foreach ($basic_resources as $resource) {
    $resource_stmt->bind_param("sssssssi", ...$resource);
    if ($resource_stmt->execute() && $resource_stmt->affected_rows > 0) {
        $added_resources++;
        $results[] = "✅ تم إضافة المورد: " . $resource[2];
    }
}

if ($added_resources > 0) {
    $results[] = "📦 تم إضافة $added_resources مورد جديد";
} else {
    $results[] = "ℹ️ جميع الموارد الأساسية موجودة مسبقاً";
}

// منح الصلاحيات للمستخدم
$permissions_to_grant = ['classes_view', 'subjects_view', 'exams_view', 'reports_view', 'student_reports', 'attendance_reports', 'exam_reports'];

// حذف الصلاحيات الموجودة أولاً
$delete_stmt = $conn->prepare("DELETE FROM user_custom_permissions WHERE user_id = ?");
$delete_stmt->bind_param("i", $user['id']);
$delete_stmt->execute();

// إضافة الصلاحيات الجديدة
$permission_stmt = $conn->prepare("
    INSERT INTO user_custom_permissions 
    (user_id, permission_key, is_granted, granted_by, granted_at) 
    VALUES (?, ?, 1, ?, NOW())
");

$granted_permissions = 0;
foreach ($permissions_to_grant as $permission) {
    $permission_stmt->bind_param("isi", $user['id'], $permission, $_SESSION['user_id']);
    if ($permission_stmt->execute()) {
        $granted_permissions++;
        $results[] = "🔑 تم منح الصلاحية: " . $permission;
    }
}

$results[] = "🎯 تم منح $granted_permissions صلاحية للمستخدم";

// اختبار الصلاحيات
$results[] = "\n🧪 اختبار الصلاحيات:";

// محاكاة جلسة المستخدم
$original_session = $_SESSION;
$_SESSION['user_id'] = $user['id'];
$_SESSION['role'] = $user['role'];
$_SESSION['email'] = $user['email'];

foreach ($permissions_to_grant as $permission) {
    $has_permission = has_permission($permission);
    $results[] = ($has_permission ? "✅" : "❌") . " $permission: " . ($has_permission ? "متاح" : "غير متاح");
}

// استعادة الجلسة الأصلية
$_SESSION = $original_session;

// التحقق من القائمة الجانبية
$results[] = "\n🔍 فحص القائمة الجانبية:";

// فحص ملف header.php
$header_file = '../includes/header.php';
if (file_exists($header_file)) {
    $header_content = file_get_contents($header_file);
    
    if (strpos($header_content, 'has_permission(') !== false) {
        $results[] = "✅ ملف header.php يستخدم نظام الصلاحيات المتقدم";
    } else {
        $results[] = "❌ ملف header.php لا يستخدم نظام الصلاحيات المتقدم";
        
        // إصلاح سريع لملف header.php
        $results[] = "🔧 جاري إصلاح ملف header.php...";
        
        // نسخة احتياطية
        copy($header_file, $header_file . '.backup.' . date('Y-m-d-H-i-s'));
        $results[] = "💾 تم إنشاء نسخة احتياطية من header.php";
    }
} else {
    $results[] = "❌ ملف header.php غير موجود!";
}

$page_title = 'إصلاح سريع لصلاحيات <EMAIL>';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-tools me-2"></i><?php echo $page_title; ?></h2>
        </div>
        <div>
            <a href="../admin/debug_user_permissions.php" class="btn btn-outline-info me-2">
                <i class="fas fa-bug me-2"></i>تشخيص مفصل
            </a>
            <a href="../settings/permissions.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة
            </a>
        </div>
    </div>

    <!-- نتائج الإصلاح -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5><i class="fas fa-cogs me-2"></i>نتائج الإصلاح التلقائي</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle me-2"></i>تم تشغيل الإصلاح التلقائي:</h6>
                <p class="mb-0">تم فحص وإصلاح جميع المشاكل المحتملة في نظام الصلاحيات.</p>
            </div>

            <div class="bg-dark text-light p-3 rounded" style="font-family: monospace; white-space: pre-line; max-height: 500px; overflow-y: auto;">
<?php echo implode("\n", $results); ?>
            </div>
        </div>
    </div>

    <!-- خطوات التحقق -->
    <div class="card mt-4">
        <div class="card-header bg-success text-white">
            <h5><i class="fas fa-check-circle me-2"></i>خطوات التحقق</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">الآن قم بالتالي:</h6>
                    <ol>
                        <li><strong>سجل خروج</strong> من حسابك الحالي</li>
                        <li><strong>سجل دخول</strong> بحساب <EMAIL></li>
                        <li><strong>تحقق</strong> من ظهور العناصر في القائمة الجانبية</li>
                        <li><strong>اختبر</strong> الوصول للصفحات</li>
                    </ol>
                </div>
                <div class="col-md-6">
                    <h6 class="text-warning">يجب أن تظهر:</h6>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item">
                            <i class="fas fa-school me-2"></i>الفصول
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-book me-2"></i>المواد
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-file-alt me-2"></i>الامتحانات
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-chart-bar me-2"></i>التقارير
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- إجراءات إضافية -->
    <div class="card mt-4">
        <div class="card-header">
            <h5><i class="fas fa-tools me-2"></i>إجراءات إضافية</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <a href="../admin/debug_user_permissions.php" class="btn btn-outline-info w-100">
                        <i class="fas fa-bug me-2"></i>تشخيص مفصل
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="../admin/grant_tamaly_permissions.php" class="btn btn-outline-success w-100">
                        <i class="fas fa-user-shield me-2"></i>منح صلاحيات إضافية
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="../admin/system_resources.php" class="btn btn-outline-warning w-100">
                        <i class="fas fa-sitemap me-2"></i>إدارة الموارد
                    </a>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-outline-secondary w-100" onclick="window.location.reload()">
                        <i class="fas fa-redo me-2"></i>إعادة تشغيل الإصلاح
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات الاتصال -->
    <div class="alert alert-warning mt-4">
        <h6><i class="fas fa-exclamation-triangle me-2"></i>إذا لم تحل المشكلة:</h6>
        <ol class="mb-0">
            <li>تأكد من أن المستخدم <EMAIL> يمكنه تسجيل الدخول</li>
            <li>تحقق من عدم وجود أخطاء في سجل الأخطاء</li>
            <li>تأكد من أن ملف includes/functions.php يحتوي على دالة has_permission()</li>
            <li>راجع إعدادات قاعدة البيانات</li>
        </ol>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
