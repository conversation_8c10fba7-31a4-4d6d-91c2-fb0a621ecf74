-- تحديث قاعدة البيانات لنظام إدارة التواصل مع أولياء الأمور
-- تاريخ التحديث: 2025-08-03

-- إن<PERSON>اء جدول قوالب الرسائل إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS `message_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `template_name` varchar(255) NOT NULL,
  `message_type` enum('general','behavior','academic','attendance','emergency') NOT NULL DEFAULT 'general',
  `subject` varchar(255) NOT NULL,
  `message_content` text NOT NULL,
  `variables` json DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMAR<PERSON> KEY (`id`),
  KEY `idx_template_type` (`message_type`),
  KEY `idx_template_active` (`is_active`),
  KEY `fk_template_creator` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول تقارير السلوك إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS `student_behavior_reports` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `report_date` date NOT NULL,
  `behavior_type` enum('excellent','good','needs_improvement','concerning') NOT NULL,
  `category` enum('discipline','participation','homework','social','academic') NOT NULL,
  `description` text NOT NULL,
  `action_taken` text DEFAULT NULL,
  `teacher_id` int(11) NOT NULL,
  `notify_parent` tinyint(1) NOT NULL DEFAULT 0,
  `communication_id` int(11) DEFAULT NULL,
  `parent_notified_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_student_behavior` (`student_id`),
  KEY `idx_behavior_date` (`report_date`),
  KEY `idx_behavior_type` (`behavior_type`),
  KEY `fk_behavior_teacher` (`teacher_id`),
  KEY `fk_behavior_communication` (`communication_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- التحقق من وجود جدول parent_communications وإنشاؤه إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS `parent_communications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `parent_phone` varchar(20) NOT NULL,
  `message_type` enum('general','behavior','academic','attendance','emergency') NOT NULL DEFAULT 'general',
  `subject` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `priority` enum('low','medium','high','urgent') NOT NULL DEFAULT 'medium',
  `sent_via` enum('whatsapp','sms','email') NOT NULL DEFAULT 'whatsapp',
  `status` enum('pending','sent','delivered','read','failed') NOT NULL DEFAULT 'pending',
  `sent_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `sent_at` timestamp NULL DEFAULT NULL,
  `delivered_at` timestamp NULL DEFAULT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_student_communication` (`student_id`),
  KEY `idx_communication_status` (`status`),
  KEY `idx_communication_type` (`message_type`),
  KEY `idx_communication_date` (`created_at`),
  KEY `fk_communication_sender` (`sent_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة القيود الخارجية إذا لم تكن موجودة
-- للتحقق من وجود القيد قبل إضافته
SET @constraint_exists = (
    SELECT COUNT(*)
    FROM information_schema.TABLE_CONSTRAINTS
    WHERE CONSTRAINT_SCHEMA = DATABASE()
    AND TABLE_NAME = 'message_templates'
    AND CONSTRAINT_NAME = 'fk_template_creator'
);

SET @sql = IF(@constraint_exists = 0,
    'ALTER TABLE `message_templates` ADD CONSTRAINT `fk_template_creator` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE',
    'SELECT "Constraint fk_template_creator already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- إضافة القيود للجدول student_behavior_reports
SET @constraint_exists = (
    SELECT COUNT(*)
    FROM information_schema.TABLE_CONSTRAINTS
    WHERE CONSTRAINT_SCHEMA = DATABASE()
    AND TABLE_NAME = 'student_behavior_reports'
    AND CONSTRAINT_NAME = 'fk_behavior_student'
);

SET @sql = IF(@constraint_exists = 0,
    'ALTER TABLE `student_behavior_reports` ADD CONSTRAINT `fk_behavior_student` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE',
    'SELECT "Constraint fk_behavior_student already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- إضافة القيود للجدول parent_communications
SET @constraint_exists = (
    SELECT COUNT(*)
    FROM information_schema.TABLE_CONSTRAINTS
    WHERE CONSTRAINT_SCHEMA = DATABASE()
    AND TABLE_NAME = 'parent_communications'
    AND CONSTRAINT_NAME = 'fk_communication_student'
);

SET @sql = IF(@constraint_exists = 0,
    'ALTER TABLE `parent_communications` ADD CONSTRAINT `fk_communication_student` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE CASCADE',
    'SELECT "Constraint fk_communication_student already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @constraint_exists = (
    SELECT COUNT(*)
    FROM information_schema.TABLE_CONSTRAINTS
    WHERE CONSTRAINT_SCHEMA = DATABASE()
    AND TABLE_NAME = 'parent_communications'
    AND CONSTRAINT_NAME = 'fk_communication_sender'
);

SET @sql = IF(@constraint_exists = 0,
    'ALTER TABLE `parent_communications` ADD CONSTRAINT `fk_communication_sender` FOREIGN KEY (`sent_by`) REFERENCES `users` (`id`) ON DELETE CASCADE',
    'SELECT "Constraint fk_communication_sender already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- إدراج بيانات تجريبية لقوالب الرسائل
INSERT IGNORE INTO `message_templates` (`id`, `template_name`, `message_type`, `subject`, `message_content`, `variables`, `created_by`, `is_active`, `created_at`) VALUES
(1, 'تهنئة سلوك ممتاز', 'behavior', 'تهنئة - سلوك ممتاز', 'نود إعلامكم بأن الطالب/ة {student_name} أظهر سلوكاً ممتازاً اليوم في {class_name}.\n\nنشكركم على تربيتكم الحسنة وندعوكم لمواصلة التشجيع.', '["student_name", "class_name"]', 1, 1, '2025-08-03 15:00:00'),
(2, 'تنبيه غياب', 'attendance', 'تنبيه غياب', 'الطالب/ة {student_name} غائب اليوم {date}.\n\nيرجى التواصل مع المدرسة في حالة وجود ظروف خاصة.', '["student_name", "date"]', 1, 1, '2025-08-03 15:00:00'),
(3, 'إعلان عام', 'general', 'إعلان مهم', 'إعلان مهم لأولياء أمور طلاب {class_name}:\n\n{announcement_content}\n\nشكراً لتعاونكم.', '["class_name", "announcement_content"]', 1, 1, '2025-08-03 15:00:00'),
(4, 'تحسين مطلوب', 'behavior', 'يحتاج تحسين', 'نود إعلامكم بأن الطالب/ة {student_name} يحتاج إلى تحسين في {category}.\n\nنرجو التعاون معنا لمساعدة الطالب على التحسن.', '["student_name", "category"]', 1, 1, '2025-08-03 15:00:00'),
(5, 'تقرير أكاديمي', 'academic', 'تقرير أكاديمي', 'تقرير عن الأداء الأكاديمي للطالب/ة {student_name} في {subject}:\n\n{performance_details}\n\nنرجو المتابعة مع الطالب.', '["student_name", "subject", "performance_details"]', 1, 1, '2025-08-03 15:00:00');

-- التحقق من وجود عمود parent_phone في جدول students وإضافته إذا لم يكن موجوداً
SET @column_exists = (
    SELECT COUNT(*)
    FROM information_schema.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'students'
    AND COLUMN_NAME = 'parent_phone'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE `students` ADD COLUMN `parent_phone` varchar(20) DEFAULT NULL AFTER `parent_name`',
    'SELECT "Column parent_phone already exists in students table" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- إضافة فهرس للبحث السريع في أرقام الهواتف
SET @index_exists = (
    SELECT COUNT(*)
    FROM information_schema.STATISTICS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'students'
    AND INDEX_NAME = 'idx_parent_phone'
);

SET @sql = IF(@index_exists = 0,
    'ALTER TABLE `students` ADD INDEX `idx_parent_phone` (`parent_phone`)',
    'SELECT "Index idx_parent_phone already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- رسالة تأكيد
SELECT 'تم تحديث قاعدة البيانات بنجاح لنظام إدارة التواصل مع أولياء الأمور' as 'نتيجة التحديث';
