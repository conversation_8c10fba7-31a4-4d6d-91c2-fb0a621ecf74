-- تحديث نظام الأدوار لدعم شؤون الطلاب والعاملين
-- Update Roles System to Support Student and Staff Affairs

-- 1. تحديث قيم الأدوار الموجودة
UPDATE users SET role = 'student_affairs_admin' 
WHERE role = 'admin' AND id IN (
    -- ضع هنا معرفات المستخدمين الذين يديرون شؤون الطلاب
    -- مثال: SELECT id FROM users WHERE full_name LIKE '%شؤون الطلاب%'
);

UPDATE users SET role = 'staff_affairs_admin' 
WHERE role = 'admin' AND id IN (
    -- ضع هنا معرفات المستخدمين الذين يديرون شؤون العاملين
    -- مثال: SELECT id FROM users WHERE full_name LIKE '%شؤون العاملين%'
);

-- 2. إضافة موارد النظام الجديدة للحضور والغياب
INSERT INTO system_resources (resource_type, resource_key, resource_name, resource_description, icon, sort_order, is_active) VALUES
-- موارد حضور الطلاب
('page', 'student_attendance_view', 'عرض حضور الطلاب', 'صفحة عرض حضور وغياب الطلاب', 'fas fa-user-graduate', 10, 1),
('page', 'student_attendance_manage', 'إدارة حضور الطلاب', 'صفحة إدارة وتعديل حضور الطلاب', 'fas fa-edit', 11, 1),
('action', 'student_attendance_add', 'تسجيل حضور طالب', 'إضافة تسجيل حضور جديد للطلاب', 'fas fa-plus', 12, 1),
('action', 'student_attendance_edit', 'تعديل حضور طالب', 'تعديل تسجيل حضور موجود للطلاب', 'fas fa-edit', 13, 1),
('action', 'student_attendance_delete', 'حذف تسجيل حضور طالب', 'حذف تسجيل حضور للطلاب', 'fas fa-trash', 14, 1),
('report', 'student_attendance_reports', 'تقارير حضور الطلاب', 'تقارير وإحصائيات حضور الطلاب', 'fas fa-chart-bar', 15, 1),

-- موارد حضور العاملين (المعلمين والإداريين)
('page', 'staff_attendance_view', 'عرض حضور العاملين', 'صفحة عرض حضور وغياب العاملين', 'fas fa-users', 20, 1),
('page', 'staff_attendance_manage', 'إدارة حضور العاملين', 'صفحة إدارة وتعديل حضور العاملين', 'fas fa-user-tie', 21, 1),
('action', 'staff_attendance_add', 'تسجيل حضور عامل', 'إضافة تسجيل حضور جديد للعاملين', 'fas fa-plus', 22, 1),
('action', 'staff_attendance_edit', 'تعديل حضور عامل', 'تعديل تسجيل حضور موجود للعاملين', 'fas fa-edit', 23, 1),
('action', 'staff_attendance_delete', 'حذف تسجيل حضور عامل', 'حذف تسجيل حضور للعاملين', 'fas fa-trash', 24, 1),
('report', 'staff_attendance_reports', 'تقارير حضور العاملين', 'تقارير وإحصائيات حضور العاملين', 'fas fa-chart-line', 25, 1),

-- موارد بيانات الطلاب
('data', 'student_personal_data', 'البيانات الشخصية للطلاب', 'الوصول لبيانات الطلاب الشخصية', 'fas fa-id-card', 30, 1),
('data', 'student_academic_data', 'البيانات الأكاديمية للطلاب', 'الوصول لبيانات الطلاب الأكاديمية', 'fas fa-graduation-cap', 31, 1),

-- موارد بيانات العاملين
('data', 'staff_personal_data', 'البيانات الشخصية للعاملين', 'الوصول لبيانات العاملين الشخصية', 'fas fa-address-card', 40, 1),
('data', 'staff_employment_data', 'بيانات التوظيف للعاملين', 'الوصول لبيانات توظيف العاملين', 'fas fa-briefcase', 41, 1);

-- 3. تعيين الصلاحيات الافتراضية للأدوار الجديدة

-- صلاحيات مدير شؤون الطلاب
INSERT INTO user_custom_permissions (user_id, permission_key, is_granted, granted_by, granted_at)
SELECT u.id, 'student_attendance_view', 1, 1, NOW()
FROM users u WHERE u.role = 'student_affairs_admin';

INSERT INTO user_custom_permissions (user_id, permission_key, is_granted, granted_by, granted_at)
SELECT u.id, 'student_attendance_manage', 1, 1, NOW()
FROM users u WHERE u.role = 'student_affairs_admin';

INSERT INTO user_custom_permissions (user_id, permission_key, is_granted, granted_by, granted_at)
SELECT u.id, 'student_attendance_add', 1, 1, NOW()
FROM users u WHERE u.role = 'student_affairs_admin';

INSERT INTO user_custom_permissions (user_id, permission_key, is_granted, granted_by, granted_at)
SELECT u.id, 'student_attendance_edit', 1, 1, NOW()
FROM users u WHERE u.role = 'student_affairs_admin';

INSERT INTO user_custom_permissions (user_id, permission_key, is_granted, granted_by, granted_at)
SELECT u.id, 'student_attendance_reports', 1, 1, NOW()
FROM users u WHERE u.role = 'student_affairs_admin';

INSERT INTO user_custom_permissions (user_id, permission_key, is_granted, granted_by, granted_at)
SELECT u.id, 'student_personal_data', 1, 1, NOW()
FROM users u WHERE u.role = 'student_affairs_admin';

INSERT INTO user_custom_permissions (user_id, permission_key, is_granted, granted_by, granted_at)
SELECT u.id, 'student_academic_data', 1, 1, NOW()
FROM users u WHERE u.role = 'student_affairs_admin';

-- صلاحيات مدير شؤون العاملين
INSERT INTO user_custom_permissions (user_id, permission_key, is_granted, granted_by, granted_at)
SELECT u.id, 'staff_attendance_view', 1, 1, NOW()
FROM users u WHERE u.role = 'staff_affairs_admin';

INSERT INTO user_custom_permissions (user_id, permission_key, is_granted, granted_by, granted_at)
SELECT u.id, 'staff_attendance_manage', 1, 1, NOW()
FROM users u WHERE u.role = 'staff_affairs_admin';

INSERT INTO user_custom_permissions (user_id, permission_key, is_granted, granted_by, granted_at)
SELECT u.id, 'staff_attendance_add', 1, 1, NOW()
FROM users u WHERE u.role = 'staff_affairs_admin';

INSERT INTO user_custom_permissions (user_id, permission_key, is_granted, granted_by, granted_at)
SELECT u.id, 'staff_attendance_edit', 1, 1, NOW()
FROM users u WHERE u.role = 'staff_affairs_admin';

INSERT INTO user_custom_permissions (user_id, permission_key, is_granted, granted_by, granted_at)
SELECT u.id, 'staff_attendance_reports', 1, 1, NOW()
FROM users u WHERE u.role = 'staff_affairs_admin';

INSERT INTO user_custom_permissions (user_id, permission_key, is_granted, granted_by, granted_at)
SELECT u.id, 'staff_personal_data', 1, 1, NOW()
FROM users u WHERE u.role = 'staff_affairs_admin';

INSERT INTO user_custom_permissions (user_id, permission_key, is_granted, granted_by, granted_at)
SELECT u.id, 'staff_employment_data', 1, 1, NOW()
FROM users u WHERE u.role = 'staff_affairs_admin';

-- 4. تسجيل التغييرات في سجل المراجعة
INSERT INTO permissions_audit_log (user_id, action_type, target_type, target_id, old_value, new_value, ip_address, user_agent, created_at)
VALUES 
(1, 'system_update', 'roles_system', NULL, 'basic_roles', 'detailed_roles_with_affairs', '127.0.0.1', 'System Update', NOW()),
(1, 'bulk_permission_grant', 'student_affairs_admin', NULL, NULL, 'student_attendance_permissions', '127.0.0.1', 'System Update', NOW()),
(1, 'bulk_permission_grant', 'staff_affairs_admin', NULL, NULL, 'staff_attendance_permissions', '127.0.0.1', 'System Update', NOW());

-- 5. إضافة تعليق للمراجعة
INSERT INTO system_settings (setting_key, setting_value, updated_by, updated_at)
VALUES ('roles_system_updated', '1', 1, NOW())
ON DUPLICATE KEY UPDATE 
setting_value = '1', 
updated_by = 1, 
updated_at = NOW();
