<?php
/**
 * إضافة إداري جديد
 * Add New Administrator
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    redirect_to('../dashboard/');
}

$page_title = __('add_administrator');

$success_message = '';
$error_message = '';

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // جمع البيانات
    $full_name = trim($_POST['full_name'] ?? '');
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $password = trim($_POST['password'] ?? '');
    $confirm_password = trim($_POST['confirm_password'] ?? '');
    $employee_id = trim($_POST['employee_id'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $address = trim($_POST['address'] ?? '');
    $date_of_birth = trim($_POST['date_of_birth'] ?? '');
    $gender = trim($_POST['gender'] ?? '');
    $nationality = trim($_POST['nationality'] ?? '');
    $national_id = trim($_POST['national_id'] ?? '');
    $qualification = trim($_POST['qualification'] ?? '');
    $position = trim($_POST['position'] ?? '');
    $department = trim($_POST['department'] ?? '');
    $experience_years = intval($_POST['experience_years'] ?? 0);
    $hire_date = trim($_POST['hire_date'] ?? '');
    $salary = floatval($_POST['salary'] ?? 0);
    $bank_account = trim($_POST['bank_account'] ?? '');
    $emergency_contact_name = trim($_POST['emergency_contact_name'] ?? '');
    $emergency_contact_phone = trim($_POST['emergency_contact_phone'] ?? '');
    $status = trim($_POST['status'] ?? 'active');

    // التحقق من صحة البيانات
    $errors = [];

    if (empty($full_name)) {
        $errors[] = __('full_name_required');
    }

    if (empty($username)) {
        $errors[] = __('username_required');
    } elseif (strlen($username) < 3) {
        $errors[] = __('username_min_length');
    }

    if (empty($email)) {
        $errors[] = __('email_required');
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = __('invalid_email');
    }

    if (empty($password)) {
        $errors[] = __('password_required');
    } elseif (strlen($password) < 6) {
        $errors[] = __('password_min_length');
    }

    if ($password !== $confirm_password) {
        $errors[] = __('passwords_not_match');
    }

    // التحقق من عدم تكرار البيانات
    if (!empty($username)) {
        $check_stmt = $conn->prepare("SELECT id FROM users WHERE username = ?");
        $check_stmt->bind_param("s", $username);
        $check_stmt->execute();
        if ($check_stmt->get_result()->num_rows > 0) {
            $errors[] = __('username_exists');
        }
    }

    if (!empty($email)) {
        $check_stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
        $check_stmt->bind_param("s", $email);
        $check_stmt->execute();
        if ($check_stmt->get_result()->num_rows > 0) {
            $errors[] = __('email_exists');
        }
    }

    if (!empty($employee_id)) {
        $check_stmt = $conn->prepare("SELECT id FROM staff WHERE employee_id = ?");
        $check_stmt->bind_param("s", $employee_id);
        $check_stmt->execute();
        if ($check_stmt->get_result()->num_rows > 0) {
            $errors[] = __('employee_id_exists');
        }
    }

    if (!empty($national_id)) {
        $check_stmt = $conn->prepare("SELECT id FROM staff WHERE national_id = ?");
        $check_stmt->bind_param("s", $national_id);
        $check_stmt->execute();
        if ($check_stmt->get_result()->num_rows > 0) {
            $errors[] = __('national_id_exists');
        }
    }

    // إذا لم توجد أخطاء، قم بإضافة الإداري
    if (empty($errors)) {
        try {
            $conn->begin_transaction();

            // إضافة المستخدم
            $hashed_password = hash_password($password);
            $user_stmt = $conn->prepare("
                INSERT INTO users (username, email, password, full_name, phone, role, status, created_at) 
                VALUES (?, ?, ?, ?, ?, 'staff', ?, NOW())
            ");
            if (!$user_stmt) throw new Exception($conn->error);
            $user_stmt->bind_param("ssssss", $username, $email, $hashed_password, $full_name, $phone, $status);
            $user_stmt->execute();
            $user_id = $conn->insert_id;

            // إضافة الإداري
            $admin_stmt = $conn->prepare("
                INSERT INTO staff (
                    user_id, employee_id, phone, address, date_of_birth, gender, nationality, national_id,
                    qualification, position, department, experience_years, hire_date, salary, bank_account,
                    emergency_contact_name, emergency_contact_phone, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            if (!$admin_stmt) throw new Exception($conn->error);
            $admin_stmt->bind_param(
                "issssssssssissdsss",
                $user_id, $employee_id, $phone, $address, $date_of_birth, $gender, $nationality, $national_id,
                $qualification, $position, $department, $experience_years, $hire_date, $salary, $bank_account,
                $emergency_contact_name, $emergency_contact_phone, $status
            );
            $admin_stmt->execute();
            $admin_row_id = $conn->insert_id;

            $conn->commit();
            
            // تسجيل النشاط
            log_activity($_SESSION['user_id'], 'add_administrator', 'administrators', $admin_row_id, null, [
                'administrator_name' => $full_name,
                'employee_id' => $employee_id
            ]);

            // إرسال إشعار
            add_notification($user_id, __('welcome_to_system'), __('your_account_created_successfully'), 'success');

            $_SESSION['success_message'] = __('administrator_added_successfully');
            header('Location: index.php');
            exit();

        } catch (Exception $e) {
            $conn->rollback();
            log_error("Error adding administrator: " . $e->getMessage());
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                $error_message = __('error_occurred') . '<br>' . htmlspecialchars($e->getMessage());
            } else {
                $error_message = __('error_occurred');
            }
        }
    } else {
        $error_message = implode('<br>', $errors);
    }
}

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-user-plus text-primary me-2"></i>
                <?php echo __('add_administrator'); ?>
            </h2>
            <p class="text-muted mb-0"><?php echo __('add_new_administrator_to_system'); ?></p>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_administrators'); ?>
            </a>
        </div>
    </div>

    <!-- Messages -->
    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Add Administrator Form -->
    <div class="card">
        <div class="card-body">
            <form method="POST" class="needs-validation" novalidate>
                <div class="row">
                    <!-- معلومات الحساب -->
                    <div class="col-md-6">
                        <h5 class="mb-3">
                            <i class="fas fa-user me-2"></i><?php echo __('account_information'); ?>
                        </h5>
                        
                        <div class="mb-3">
                            <label for="full_name" class="form-label"><?php echo __('full_name'); ?> <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="full_name" name="full_name" 
                                   value="<?php echo htmlspecialchars($full_name ?? ''); ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="username" class="form-label"><?php echo __('username'); ?> <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="username" name="username" 
                                   value="<?php echo htmlspecialchars($username ?? ''); ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label"><?php echo __('email'); ?> <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label"><?php echo __('password'); ?> <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>

                        <div class="mb-3">
                            <label for="confirm_password" class="form-label"><?php echo __('confirm_password'); ?> <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>

                        <div class="mb-3">
                            <label for="status" class="form-label"><?php echo __('status'); ?></label>
                            <select class="form-select" id="status" name="status">
                                <option value="active" <?php echo ($status ?? 'active') === 'active' ? 'selected' : ''; ?>><?php echo __('active'); ?></option>
                                <option value="inactive" <?php echo ($status ?? '') === 'inactive' ? 'selected' : ''; ?>><?php echo __('inactive'); ?></option>
                            </select>
                        </div>
                    </div>

                    <!-- المعلومات الشخصية -->
                    <div class="col-md-6">
                        <h5 class="mb-3">
                            <i class="fas fa-id-card me-2"></i><?php echo __('personal_information'); ?>
                        </h5>

                        <div class="mb-3">
                            <label for="employee_id" class="form-label"><?php echo __('employee_id'); ?></label>
                            <input type="text" class="form-control" id="employee_id" name="employee_id" 
                                   value="<?php echo htmlspecialchars($employee_id ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="national_id" class="form-label"><?php echo __('national_id'); ?></label>
                            <input type="text" class="form-control" id="national_id" name="national_id" 
                                   value="<?php echo htmlspecialchars($national_id ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="phone" class="form-label"><?php echo __('phone'); ?></label>
                            <input type="text" class="form-control" id="phone" name="phone" 
                                   value="<?php echo htmlspecialchars($phone ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="date_of_birth" class="form-label"><?php echo __('date_of_birth'); ?></label>
                            <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" 
                                   value="<?php echo htmlspecialchars($date_of_birth ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="gender" class="form-label"><?php echo __('gender'); ?></label>
                            <select class="form-select" id="gender" name="gender">
                                <option value=""><?php echo __('select_gender'); ?></option>
                                <option value="male" <?php echo ($gender ?? '') === 'male' ? 'selected' : ''; ?>><?php echo __('male'); ?></option>
                                <option value="female" <?php echo ($gender ?? '') === 'female' ? 'selected' : ''; ?>><?php echo __('female'); ?></option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="nationality" class="form-label"><?php echo __('nationality'); ?></label>
                            <input type="text" class="form-control" id="nationality" name="nationality" 
                                   value="<?php echo htmlspecialchars($nationality ?? ''); ?>">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- المعلومات الوظيفية -->
                    <div class="col-md-6">
                        <h5 class="mb-3">
                            <i class="fas fa-briefcase me-2"></i><?php echo __('job_information'); ?>
                        </h5>

                        <div class="mb-3">
                            <label for="position" class="form-label"><?php echo __('position'); ?></label>
                            <input type="text" class="form-control" id="position" name="position" 
                                   value="<?php echo htmlspecialchars($position ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="department" class="form-label"><?php echo __('department'); ?></label>
                            <input type="text" class="form-control" id="department" name="department" 
                                   value="<?php echo htmlspecialchars($department ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="qualification" class="form-label"><?php echo __('qualification'); ?></label>
                            <input type="text" class="form-control" id="qualification" name="qualification" 
                                   value="<?php echo htmlspecialchars($qualification ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="experience_years" class="form-label"><?php echo __('experience_years'); ?></label>
                            <input type="number" class="form-control" id="experience_years" name="experience_years" 
                                   value="<?php echo htmlspecialchars($experience_years ?? 0); ?>" min="0">
                        </div>

                        <div class="mb-3">
                            <label for="hire_date" class="form-label"><?php echo __('hire_date'); ?></label>
                            <input type="date" class="form-control" id="hire_date" name="hire_date" 
                                   value="<?php echo htmlspecialchars($hire_date ?? ''); ?>">
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="col-md-6">
                        <h5 class="mb-3">
                            <i class="fas fa-info-circle me-2"></i><?php echo __('additional_information'); ?>
                        </h5>

                        <div class="mb-3">
                            <label for="salary" class="form-label"><?php echo __('salary'); ?></label>
                            <input type="number" class="form-control" id="salary" name="salary" 
                                   value="<?php echo htmlspecialchars($salary ?? 0); ?>" min="0" step="0.01">
                        </div>

                        <div class="mb-3">
                            <label for="bank_account" class="form-label"><?php echo __('bank_account'); ?></label>
                            <input type="text" class="form-control" id="bank_account" name="bank_account" 
                                   value="<?php echo htmlspecialchars($bank_account ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="emergency_contact_name" class="form-label"><?php echo __('emergency_contact_name'); ?></label>
                            <input type="text" class="form-control" id="emergency_contact_name" name="emergency_contact_name" 
                                   value="<?php echo htmlspecialchars($emergency_contact_name ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="emergency_contact_phone" class="form-label"><?php echo __('emergency_contact_phone'); ?></label>
                            <input type="text" class="form-control" id="emergency_contact_phone" name="emergency_contact_phone" 
                                   value="<?php echo htmlspecialchars($emergency_contact_phone ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label"><?php echo __('address'); ?></label>
                            <textarea class="form-control" id="address" name="address" rows="3"><?php echo htmlspecialchars($address ?? ''); ?></textarea>
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="row">
                    <div class="col-12">
                        <hr>
                        <div class="d-flex justify-content-end gap-2">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i><?php echo __('add_administrator'); ?>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
