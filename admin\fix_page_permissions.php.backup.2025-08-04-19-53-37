<?php
/**
 * إصلاح صلاحيات الصفحات لدعم الإداري
 * Fix Page Permissions to Support Staff Role
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

$results = [];
$success = true;

// قائمة الصفحات التي تحتاج إصلاح
$pages_to_fix = [
    'students/index.php' => [
        'old_check' => "if (!check_permission('teacher')) {",
        'new_check' => "if (!check_permission('teacher') && !check_permission('staff') && !has_permission('students_view') && !has_permission('student_affairs')) {",
        'description' => 'صفحة الطلاب الرئيسية'
    ],
    'students/add.php' => [
        'old_check' => "if (!check_permission('teacher')) {",
        'new_check' => "if (!check_permission('teacher') && !check_permission('staff') && !has_permission('student_add') && !has_permission('student_affairs')) {",
        'description' => 'صفحة إضافة طالب'
    ],
    'students/edit.php' => [
        'old_check' => "if (!check_permission('teacher')) {",
        'new_check' => "if (!check_permission('teacher') && !check_permission('staff') && !has_permission('student_edit') && !has_permission('student_affairs')) {",
        'description' => 'صفحة تعديل الطالب'
    ],
    'students/view.php' => [
        'old_check' => "if (!check_permission('teacher')) {",
        'new_check' => "if (!check_permission('teacher') && !check_permission('staff') && !has_permission('students_view') && !has_permission('student_affairs')) {",
        'description' => 'صفحة عرض الطالب'
    ],
    'teachers/index.php' => [
        'old_check' => "if (!check_permission('admin')) {",
        'new_check' => "if (!check_permission('admin') && !check_permission('staff') && !has_permission('teachers_view') && !has_permission('staff_affairs')) {",
        'description' => 'صفحة المعلمين الرئيسية'
    ],
    'classes/index.php' => [
        'old_check' => "if (!check_permission('teacher')) {",
        'new_check' => "if (!check_permission('teacher') && !check_permission('staff') && !has_permission('classes_view')) {",
        'description' => 'صفحة الفصول الرئيسية'
    ],
    'subjects/index.php' => [
        'old_check' => "if (!check_permission('teacher')) {",
        'new_check' => "if (!check_permission('teacher') && !check_permission('staff') && !has_permission('subjects_view')) {",
        'description' => 'صفحة المواد الرئيسية'
    ]
];

// معالجة الإصلاح
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fix_pages'])) {
    try {
        $results[] = "🚀 بدء إصلاح صلاحيات الصفحات";
        
        $fixed_count = 0;
        $skipped_count = 0;
        
        foreach ($pages_to_fix as $file_path => $fix_info) {
            $full_path = "../$file_path";
            
            if (!file_exists($full_path)) {
                $results[] = "⚠️ الملف غير موجود: $file_path";
                $skipped_count++;
                continue;
            }
            
            // قراءة محتوى الملف
            $content = file_get_contents($full_path);
            
            if ($content === false) {
                $results[] = "❌ فشل في قراءة الملف: $file_path";
                $skipped_count++;
                continue;
            }
            
            // التحقق من وجود النص القديم
            if (strpos($content, $fix_info['old_check']) === false) {
                $results[] = "ℹ️ الملف لا يحتاج إصلاح: $file_path";
                $skipped_count++;
                continue;
            }
            
            // إنشاء نسخة احتياطية
            $backup_path = $full_path . '.backup.' . date('Y-m-d-H-i-s');
            if (!copy($full_path, $backup_path)) {
                $results[] = "❌ فشل في إنشاء نسخة احتياطية: $file_path";
                $skipped_count++;
                continue;
            }
            
            // استبدال النص
            $new_content = str_replace($fix_info['old_check'], $fix_info['new_check'], $content);
            
            // كتابة المحتوى الجديد
            if (file_put_contents($full_path, $new_content) !== false) {
                $results[] = "✅ تم إصلاح: {$fix_info['description']} ($file_path)";
                $fixed_count++;
            } else {
                $results[] = "❌ فشل في كتابة الملف: $file_path";
                $skipped_count++;
            }
        }
        
        $results[] = "\n📊 ملخص العملية:";
        $results[] = "✅ تم إصلاح $fixed_count ملف";
        $results[] = "⚠️ تم تخطي $skipped_count ملف";
        
        if ($fixed_count > 0) {
            $results[] = "\n🎉 تم إصلاح صلاحيات الصفحات بنجاح!";
            $results[] = "📋 الآن يمكن للإداري الوصول للبيانات في جميع الصفحات";
        }
        
    } catch (Exception $e) {
        $success = false;
        $results[] = "❌ خطأ: " . $e->getMessage();
    }
}

$page_title = 'إصلاح صلاحيات الصفحات للإداري';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-file-code me-2"></i><?php echo $page_title; ?></h2>
            <p class="text-muted">إصلاح صلاحيات الصفحات لتدعم دور الإداري</p>
        </div>
        <a href="../settings/permissions.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة
        </a>
    </div>

    <!-- معلومات المشكلة -->
    <div class="alert alert-warning">
        <h6><i class="fas fa-exclamation-triangle me-2"></i>المشكلة المكتشفة:</h6>
        <p class="mb-2">صفحات النظام (الطلاب، المعلمين، الفصول، إلخ) تتحقق من صلاحية <code>teacher</code> فقط، ولا تدعم دور <code>staff</code> (الإداري).</p>
        <p class="mb-0"><strong>النتيجة:</strong> الإداري يرى القائمة الجانبية لكن لا يرى البيانات في الصفحات.</p>
    </div>

    <!-- نتائج الإصلاح -->
    <?php if (!empty($results)): ?>
        <div class="card mb-4">
            <div class="card-header <?php echo $success ? 'bg-success' : 'bg-danger'; ?> text-white">
                <h5>
                    <i class="fas fa-<?php echo $success ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                    <?php echo $success ? 'تم الإصلاح بنجاح!' : 'فشل في الإصلاح'; ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="bg-dark text-light p-3 rounded" style="font-family: monospace; white-space: pre-line;">
<?php echo implode("\n", $results); ?>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- قائمة الصفحات المراد إصلاحها -->
    <?php if (empty($results) || !$success): ?>
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5><i class="fas fa-list me-2"></i>الصفحات المراد إصلاحها</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>ما سيتم عمله:</h6>
                    <p class="mb-0">سيتم تحديث فحص الصلاحيات في الصفحات التالية لتدعم دور الإداري والصلاحيات المخصصة:</p>
                </div>

                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>الصفحة</th>
                                <th>الوصف</th>
                                <th>الفحص الحالي</th>
                                <th>الفحص الجديد</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($pages_to_fix as $file_path => $fix_info): ?>
                                <tr>
                                    <td><code><?php echo $file_path; ?></code></td>
                                    <td><?php echo $fix_info['description']; ?></td>
                                    <td><small class="text-danger"><?php echo htmlspecialchars($fix_info['old_check']); ?></small></td>
                                    <td><small class="text-success"><?php echo htmlspecialchars($fix_info['new_check']); ?></small></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <div class="alert alert-warning">
                    <h6><i class="fas fa-shield-alt me-2"></i>الأمان:</h6>
                    <ul class="mb-0">
                        <li>سيتم إنشاء نسخة احتياطية من كل ملف قبل التعديل</li>
                        <li>يمكن التراجع عن التغييرات باستخدام النسخ الاحتياطية</li>
                        <li>التعديل يضيف صلاحيات جديدة ولا يحذف الموجودة</li>
                    </ul>
                </div>

                <form method="POST">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="confirmFix" required>
                        <label class="form-check-label" for="confirmFix">
                            <strong>أؤكد أنني أريد إصلاح صلاحيات الصفحات لدعم دور الإداري</strong>
                        </label>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="../settings/permissions.php" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" name="fix_pages" class="btn btn-primary" id="fixButton" disabled>
                            <i class="fas fa-file-code me-2"></i>إصلاح الصفحات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    <?php endif; ?>

    <!-- تعليمات الاختبار -->
    <?php if ($success && !empty($results)): ?>
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5><i class="fas fa-clipboard-check me-2"></i>اختبار النتيجة</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <h6><i class="fas fa-thumbs-up me-2"></i>تم الإصلاح!</h6>
                    <p class="mb-0">الآن يجب أن تعمل جميع الصفحات مع دور الإداري.</p>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">خطوات الاختبار:</h6>
                        <ol>
                            <li><strong>سجل خروج</strong> من حسابك الحالي</li>
                            <li><strong>سجل دخول</strong> بحساب الإداري</li>
                            <li><strong>اذهب لصفحة الطلاب</strong> - يجب أن تظهر البيانات</li>
                            <li><strong>اختبر الصفحات الأخرى</strong> - المعلمين، الفصول، إلخ</li>
                        </ol>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-success">يجب أن تعمل الآن:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">📋 صفحة الطلاب - عرض قائمة الطلاب</li>
                            <li class="list-group-item">➕ إضافة طالب جديد</li>
                            <li class="list-group-item">✏️ تعديل بيانات الطلاب</li>
                            <li class="list-group-item">👨‍🏫 صفحة المعلمين</li>
                            <li class="list-group-item">🏫 صفحة الفصول</li>
                            <li class="list-group-item">📚 صفحة المواد</li>
                        </ul>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <a href="../students/" class="btn btn-info me-2" target="_blank">
                        <i class="fas fa-user-graduate me-2"></i>اختبار صفحة الطلاب
                    </a>
                    <a href="../teachers/" class="btn btn-success me-2" target="_blank">
                        <i class="fas fa-chalkboard-teacher me-2"></i>اختبار صفحة المعلمين
                    </a>
                    <button class="btn btn-secondary" onclick="window.location.reload()">
                        <i class="fas fa-redo me-2"></i>إعادة الإصلاح
                    </button>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- معلومات تقنية -->
    <div class="card mt-4">
        <div class="card-header bg-secondary text-white">
            <h6><i class="fas fa-code me-2"></i>التفاصيل التقنية</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-danger">المشكلة:</h6>
                    <ul class="small">
                        <li>الصفحات تتحقق من <code>check_permission('teacher')</code> فقط</li>
                        <li>لا تدعم <code>check_permission('staff')</code></li>
                        <li>لا تدعم الصلاحيات المخصصة مثل <code>has_permission('students_view')</code></li>
                        <li>النتيجة: الإداري لا يرى البيانات</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="text-success">الحل:</h6>
                    <ul class="small">
                        <li>إضافة <code>check_permission('staff')</code></li>
                        <li>إضافة الصلاحيات المخصصة</li>
                        <li>إضافة صلاحيات شئون الطلاب والعاملين</li>
                        <li>الحفاظ على الصلاحيات الموجودة</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تفعيل زر الإصلاح عند تأكيد الاختيار
document.getElementById('confirmFix')?.addEventListener('change', function() {
    document.getElementById('fixButton').disabled = !this.checked;
});
</script>

<?php include_once '../includes/footer.php'; ?>
