<?php
require_once '../includes/header.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';
check_session();
if (!check_permission('teacher')) {
    header('Location: ../dashboard/');
    exit();
}

$class_id = intval($_GET['class_id'] ?? 0);
$subject_id = intval($_GET['subject_id'] ?? 0);
$date = clean_input($_GET['date'] ?? '');

$teacher_id = null;
if ($_SESSION['role'] === 'teacher') {
    $teacher_stmt = $conn->prepare("SELECT id FROM teachers WHERE user_id = ?");
    $teacher_stmt->bind_param("i", $_SESSION['user_id']);
    $teacher_stmt->execute();
    $teacher_result = $teacher_stmt->get_result();
    $teacher_data = $teacher_result->fetch_assoc();
    $teacher_id = $teacher_data['id'] ?? 0;
}
if ($teacher_id) {
    $classes_query = "
        SELECT DISTINCT c.id, c.class_name, c.grade_level 
        FROM classes c 
        JOIN teacher_assignments ta ON c.id = ta.class_id 
        WHERE ta.teacher_id = ? AND ta.status = 'active'
        ORDER BY c.grade_level, c.class_name
    ";
    $classes_stmt = $conn->prepare($classes_query);
    $classes_stmt->bind_param("i", $teacher_id);
    $classes_stmt->execute();
    $classes = $classes_stmt->get_result();
} else {
    $classes = $conn->query("SELECT id, class_name, grade_level FROM classes WHERE status = 'active' ORDER BY grade_level, class_name");
}
if ($teacher_id) {
    $subjects_query = "
        SELECT DISTINCT s.id, s.subject_name 
        FROM subjects s 
        JOIN teacher_assignments ta ON s.id = ta.subject_id 
        WHERE ta.teacher_id = ? AND ta.status = 'active'
        ORDER BY s.subject_name
    ";
    $subjects_stmt = $conn->prepare($subjects_query);
    $subjects_stmt->bind_param("i", $teacher_id);
    $subjects_stmt->execute();
    $subjects = $subjects_stmt->get_result();
} else {
    $subjects = $conn->query("SELECT id, subject_name FROM subjects WHERE status = 'active' ORDER BY subject_name");
}
$where = [];
$params = [];
$types = '';
if ($class_id) {
    $where[] = 's.class_id = ?';
    $params[] = $class_id;
    $types .= 'i';
}
if ($subject_id) {
    $where[] = 'a.subject_id = ?';
    $params[] = $subject_id;
    $types .= 'i';
}
if ($date) {
    $where[] = 'DATE(a.attendance_date) = ?';
    $params[] = $date;
    $types .= 's';
}
$where_sql = $where ? 'WHERE ' . implode(' AND ', $where) : '';
$query = "
    SELECT a.*, u.full_name as student_name, c.class_name, sub.subject_name
    FROM attendance a
    JOIN students s ON a.student_id = s.id
    JOIN users u ON s.user_id = u.id
    JOIN classes c ON s.class_id = c.id
    LEFT JOIN subjects sub ON a.subject_id = sub.id
    $where_sql
    ORDER BY c.class_name, u.full_name
";
$stmt = $conn->prepare($query);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$records = $stmt->get_result();
// إحصائيات
$stats = [
    'total' => 0,
    'present' => 0,
    'absent' => 0,
    'late' => 0,
    'excused' => 0
];
foreach ($records as $row) {
    $stats['total']++;
    $stats[$row['status']]++;
}
$records->data_seek(0);

// إضافة متغير لتحديد التبويب النشط (طلاب أو معلمين)
$active_tab = $_GET['tab'] ?? 'students';
?>
<ul class="nav nav-tabs mb-4" id="attendanceReportTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <a class="nav-link <?php echo $active_tab === 'students' ? 'active' : ''; ?>" href="?tab=students">
            <i class="fas fa-user-graduate me-1"></i> <?php echo __('students'); ?>
        </a>
    </li>
    <li class="nav-item" role="presentation">
        <a class="nav-link <?php echo $active_tab === 'teachers' ? 'active' : ''; ?>" href="?tab=teachers">
            <i class="fas fa-chalkboard-teacher me-1"></i> <?php echo __('teachers'); ?>
        </a>
    </li>
</ul>
<?php if ($active_tab === 'students'): ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('attendance_reports'); ?></h1>
            <p class="text-muted"><?php echo __('view_attendance_reports'); ?></p>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
            </a>
        </div>
    </div>
    <form method="GET" class="row mb-4">
        <div class="col-md-3">
            <label for="class_id" class="form-label"><?php echo __('class'); ?></label>
            <select class="form-select" id="class_id" name="class_id">
                <option value=""><?php echo __('all_classes'); ?></option>
                <?php while ($class = $classes->fetch_assoc()): ?>
                    <option value="<?php echo $class['id']; ?>" <?php echo ($class_id == $class['id']) ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($class['class_name']) . ' - ' . htmlspecialchars($class['grade_level']); ?>
                    </option>
                <?php endwhile; ?>
            </select>
        </div>
        <div class="col-md-3">
            <label for="subject_id" class="form-label"><?php echo __('subject'); ?></label>
            <select class="form-select" id="subject_id" name="subject_id">
                <option value=""><?php echo __('all_subjects'); ?></option>
                <?php while ($subject = $subjects->fetch_assoc()): ?>
                    <option value="<?php echo $subject['id']; ?>" <?php echo ($subject_id == $subject['id']) ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($subject['subject_name']); ?>
                    </option>
                <?php endwhile; ?>
            </select>
        </div>
        <div class="col-md-3">
            <label for="date" class="form-label"><?php echo __('date'); ?></label>
            <input type="date" class="form-control" id="date" name="date" value="<?php echo htmlspecialchars($date); ?>">
        </div>
        <div class="col-md-3 d-flex align-items-end">
            <button type="submit" class="btn btn-primary w-100">
                <i class="fas fa-search me-2"></i><?php echo __('search'); ?>
            </button>
        </div>
    </form>
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h5 class="card-title mb-2"><?php echo __('present'); ?></h5>
                    <p class="mb-0 fs-4 text-success"><?php echo $stats['present']; ?></p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h5 class="card-title mb-2"><?php echo __('absent'); ?></h5>
                    <p class="mb-0 fs-4 text-danger"><?php echo $stats['absent']; ?></p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h5 class="card-title mb-2"><?php echo __('late'); ?></h5>
                    <p class="mb-0 fs-4 text-warning"><?php echo $stats['late']; ?></p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h5 class="card-title mb-2"><?php echo __('excused'); ?></h5>
                    <p class="mb-0 fs-4 text-info"><?php echo $stats['excused']; ?></p>
                </div>
            </div>
        </div>
    </div>
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0"><?php echo __('attendance_records'); ?></h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th><?php echo __('student'); ?></th>
                            <th><?php echo __('class'); ?></th>
                            <th><?php echo __('subject'); ?></th>
                            <th><?php echo __('status'); ?></th>
                            <th><?php echo __('date'); ?></th>
                            <th><?php echo __('notes'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($records as $row): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($row['student_name']); ?></td>
                                <td><?php echo htmlspecialchars($row['class_name']); ?></td>
                                <td><?php echo htmlspecialchars($row['subject_name'] ?? __('general')); ?></td>
                                <td><?php echo __($row['status']); ?></td>
                                <td><?php echo htmlspecialchars(substr($row['attendance_date'], 0, 10)); ?></td>
                                <td><?php echo htmlspecialchars($row['notes']); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<?php else: ?>
<?php
// فلاتر تقارير المعلمين
$teacher_search = clean_input($_GET['teacher_search'] ?? '');
$teacher_status = clean_input($_GET['teacher_status'] ?? '');
$teacher_date = clean_input($_GET['teacher_date'] ?? '');
$teacher_where = [];
$teacher_params = [];
$teacher_types = '';
if (!empty($teacher_search)) {
    $teacher_where[] = 'u.full_name LIKE ?';
    $teacher_params[] = "%$teacher_search%";
    $teacher_types .= 's';
}
if (!empty($teacher_status)) {
    $teacher_where[] = 'a.status = ?';
    $teacher_params[] = $teacher_status;
    $teacher_types .= 's';
}
if (!empty($teacher_date)) {
    $teacher_where[] = 'DATE(a.attendance_date) = ?';
    $teacher_params[] = $teacher_date;
    $teacher_types .= 's';
}
$teacher_where_sql = $teacher_where ? 'WHERE ' . implode(' AND ', $teacher_where) : '';
$teacher_query = "
    SELECT a.*, u.full_name as teacher_name
    FROM teacher_attendance a
    JOIN teachers t ON a.teacher_id = t.id
    JOIN users u ON t.user_id = u.id
    $teacher_where_sql
    ORDER BY u.full_name
";
$teacher_stmt = $conn->prepare($teacher_query);
if (!empty($teacher_params)) {
    $teacher_stmt->bind_param($teacher_types, ...$teacher_params);
}
$teacher_stmt->execute();
$teacher_records = $teacher_stmt->get_result();
?>
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-chalkboard-teacher me-2"></i><?php echo __('teacher_attendance_report'); ?>
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3 mb-4">
            <input type="hidden" name="tab" value="teachers">
            <div class="col-md-4">
                <label for="teacher_search" class="form-label"><?php echo __('search'); ?></label>
                <input type="text" class="form-control" id="teacher_search" name="teacher_search" value="<?php echo htmlspecialchars($teacher_search); ?>" placeholder="<?php echo __('search_teacher'); ?>">
            </div>
            <div class="col-md-3">
                <label for="teacher_status" class="form-label"><?php echo __('status'); ?></label>
                <select class="form-select" id="teacher_status" name="teacher_status">
                    <option value=""><?php echo __('all_statuses'); ?></option>
                    <option value="present" <?php echo ($teacher_status == 'present') ? 'selected' : ''; ?>><?php echo __('present'); ?></option>
                    <option value="absent" <?php echo ($teacher_status == 'absent') ? 'selected' : ''; ?>><?php echo __('absent'); ?></option>
                    <option value="late" <?php echo ($teacher_status == 'late') ? 'selected' : ''; ?>><?php echo __('late'); ?></option>
                    <option value="excused" <?php echo ($teacher_status == 'excused') ? 'selected' : ''; ?>><?php echo __('excused'); ?></option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="teacher_date" class="form-label"><?php echo __('date'); ?></label>
                <input type="date" class="form-control" id="teacher_date" name="teacher_date" value="<?php echo htmlspecialchars($teacher_date); ?>">
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-2"></i><?php echo __('search'); ?>
                </button>
            </div>
        </form>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-dark">
                    <tr>
                        <th><?php echo __('teacher'); ?></th>
                        <th><?php echo __('status'); ?></th>
                        <th><?php echo __('date'); ?></th>
                        <th><?php echo __('check_in'); ?></th>
                        <th><?php echo __('check_out'); ?></th>
                        <th><?php echo __('notes'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ($teacher_records->num_rows > 0): ?>
                        <?php while ($row = $teacher_records->fetch_assoc()): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($row['teacher_name']); ?></td>
                                <td><?php echo __($row['status']); ?></td>
                                <td><?php echo htmlspecialchars($row['attendance_date']); ?></td>
                                <td><?php echo htmlspecialchars($row['check_in_time']); ?></td>
                                <td><?php echo htmlspecialchars($row['check_out_time']); ?></td>
                                <td><?php echo htmlspecialchars($row['notes']); ?></td>
                            </tr>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6" class="text-center text-muted py-4"><?php echo __('no_attendance_records'); ?></td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php endif; ?>
<?php require_once '../includes/footer.php'; ?> 