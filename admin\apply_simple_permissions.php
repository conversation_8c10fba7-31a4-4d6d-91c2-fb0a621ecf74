<?php
/**
 * تطبيق النظام البسيط للصلاحيات
 * Apply Simple Permissions System
 */

require_once '../includes/config.php';
require_once '../includes/database.php';

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من الصلاحيات (مدير فقط)
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/?error=access_denied');
    exit();
}

$success_message = '';
$error_message = '';
$steps_completed = [];

// تطبيق النظام
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['apply_system'])) {
    try {
        // الخطوة 1: إنشاء الجداول
        $sql_file = '../database/simple_permissions_system.sql';
        if (file_exists($sql_file)) {
            $sql_content = file_get_contents($sql_file);
            
            // تقسيم الاستعلامات
            $queries = array_filter(array_map('trim', explode(';', $sql_content)));
            
            foreach ($queries as $query) {
                if (!empty($query) && !preg_match('/^\s*--/', $query)) {
                    try {
                        $conn->query($query);
                    } catch (Exception $e) {
                        // تجاهل أخطاء الجداول الموجودة
                        if (strpos($e->getMessage(), 'already exists') === false) {
                            throw $e;
                        }
                    }
                }
            }
            $steps_completed[] = 'تم إنشاء جداول قاعدة البيانات';
        }
        
        // الخطوة 2: التحقق من وجود الجداول
        $required_tables = ['user_page_permissions', 'available_pages', 'permissions_log'];
        $tables_exist = true;
        
        foreach ($required_tables as $table) {
            $result = $conn->query("SHOW TABLES LIKE '$table'");
            if ($result->num_rows == 0) {
                $tables_exist = false;
                break;
            }
        }
        
        if ($tables_exist) {
            $steps_completed[] = 'تم التحقق من وجود جميع الجداول المطلوبة';
        } else {
            throw new Exception('فشل في إنشاء بعض الجداول المطلوبة');
        }
        
        // الخطوة 3: إدراج البيانات الأساسية إذا لم تكن موجودة
        $pages_count = $conn->query("SELECT COUNT(*) as count FROM available_pages")->fetch_assoc()['count'];
        
        if ($pages_count == 0) {
            $basic_pages = [
                ['students', 'إدارة الطلاب', '/students/', 'الطلاب', 'عرض وإدارة بيانات الطلاب', 1, 1, 1],
                ['attendance', 'الحضور والغياب', '/attendance/', 'الحضور', 'تسجيل ومتابعة حضور الطلاب', 1, 1, 0],
                ['grades', 'الدرجات', '/grades/', 'الدرجات', 'إدارة درجات الطلاب', 0, 1, 0],
                ['finance', 'الشؤون المالية', '/finance/', 'المالية', 'إدارة الرسوم والمدفوعات', 1, 1, 1],
                ['reports', 'التقارير', '/reports/', 'التقارير', 'عرض وطباعة التقارير', 0, 0, 0],
                ['users', 'إدارة المستخدمين', '/admin/users/', 'الإدارة', 'إدارة حسابات المستخدمين', 1, 1, 1],
                ['settings', 'الإعدادات', '/settings/', 'الإدارة', 'إعدادات النظام', 0, 1, 0],
                ['exams', 'الامتحانات', '/exams/', 'الامتحانات', 'إدارة الامتحانات والنتائج', 1, 1, 1],
                ['library', 'المكتبة', '/library/', 'المكتبة', 'إدارة الكتب والمراجع', 1, 1, 1],
                ['communication', 'التواصل', '/communication/', 'التواصل', 'الرسائل والإشعارات', 1, 0, 1]
            ];
            
            $stmt = $conn->prepare("
                INSERT INTO available_pages
                (page_name, page_title, page_path, category, description, requires_add, requires_edit, requires_delete)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");

            foreach ($basic_pages as $page) {
                $stmt->bind_param("sssssiiii", $page[0], $page[1], $page[2], $page[3], $page[4], $page[5], $page[6], $page[7]);
                $stmt->execute();
            }
            
            $steps_completed[] = 'تم إدراج البيانات الأساسية للصفحات المتاحة';
        } else {
            $steps_completed[] = 'البيانات الأساسية موجودة مسبقاً';
        }
        
        // الخطوة 4: تحميل النظام البسيط
        if (file_exists('../includes/simple_permissions.php')) {
            require_once '../includes/simple_permissions.php';
            
            if (defined('SIMPLE_PERMISSIONS_LOADED')) {
                $steps_completed[] = 'تم تحميل النظام البسيط للصلاحيات بنجاح';
            }
        }
        
        // الخطوة 5: اختبار الدوال الأساسية
        if (function_exists('check_page_permission') && 
            function_exists('get_available_pages') && 
            function_exists('grant_page_permission')) {
            $steps_completed[] = 'تم التحقق من عمل جميع الدوال الأساسية';
        }
        
        $success_message = 'تم تطبيق النظام البسيط للصلاحيات بنجاح!';
        
    } catch (Exception $e) {
        $error_message = 'خطأ في تطبيق النظام: ' . $e->getMessage();
    }
}

// فحص حالة النظام
$system_status = [];

// فحص الجداول
$required_tables = ['user_page_permissions', 'available_pages', 'permissions_log'];
foreach ($required_tables as $table) {
    try {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        $system_status['tables'][$table] = $result->num_rows > 0;
    } catch (Exception $e) {
        $system_status['tables'][$table] = false;
    }
}

// فحص الملفات
$required_files = [
    '../includes/simple_permissions.php',
    '../admin/simple_permissions_manager.php',
    '../admin/test_simple_permissions.php'
];

foreach ($required_files as $file) {
    $system_status['files'][basename($file)] = file_exists($file);
}

// فحص الدوال
if (file_exists('../includes/simple_permissions.php')) {
    require_once '../includes/simple_permissions.php';
    
    $required_functions = [
        'check_page_permission',
        'require_page_permission', 
        'get_user_page_permissions',
        'grant_page_permission',
        'get_available_pages'
    ];
    
    foreach ($required_functions as $function) {
        $system_status['functions'][$function] = function_exists($function);
    }
}

$page_title = 'تطبيق النظام البسيط للصلاحيات';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <!-- رأس الصفحة -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="fas fa-rocket me-2 text-success"></i><?php echo $page_title; ?></h2>
                <p class="text-muted">تطبيق الحل الأمثل للصلاحيات المخصصة مع الاحتفاظ بصلاحيات المدير</p>
            </div>
        </div>

        <!-- رسائل التنبيه -->
        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (!empty($steps_completed)): ?>
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle me-2"></i>الخطوات المكتملة:</h6>
                <ul class="mb-0">
                    <?php foreach ($steps_completed as $step): ?>
                        <li><?php echo $step; ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- حالة النظام -->
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-database me-2"></i>جداول قاعدة البيانات</h5>
                    </div>
                    <div class="card-body">
                        <?php if (isset($system_status['tables'])): ?>
                            <?php foreach ($system_status['tables'] as $table => $exists): ?>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span><?php echo $table; ?></span>
                                    <span class="badge bg-<?php echo $exists ? 'success' : 'danger'; ?>">
                                        <?php echo $exists ? 'موجود' : 'غير موجود'; ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-file me-2"></i>الملفات المطلوبة</h5>
                    </div>
                    <div class="card-body">
                        <?php if (isset($system_status['files'])): ?>
                            <?php foreach ($system_status['files'] as $file => $exists): ?>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span><?php echo $file; ?></span>
                                    <span class="badge bg-<?php echo $exists ? 'success' : 'danger'; ?>">
                                        <?php echo $exists ? 'موجود' : 'غير موجود'; ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-code me-2"></i>الدوال المطلوبة</h5>
                    </div>
                    <div class="card-body">
                        <?php if (isset($system_status['functions'])): ?>
                            <?php foreach ($system_status['functions'] as $function => $exists): ?>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span><code><?php echo $function; ?></code></span>
                                    <span class="badge bg-<?php echo $exists ? 'success' : 'danger'; ?>">
                                        <?php echo $exists ? 'موجودة' : 'غير موجودة'; ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="text-muted">لم يتم تحميل النظام بعد</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- تطبيق النظام -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-play me-2"></i>تطبيق النظام</h5>
                    </div>
                    <div class="card-body">
                        <?php 
                        $all_tables_exist = isset($system_status['tables']) && 
                                          !in_array(false, $system_status['tables']);
                        $all_files_exist = isset($system_status['files']) && 
                                         !in_array(false, $system_status['files']);
                        $all_functions_exist = isset($system_status['functions']) && 
                                             !in_array(false, $system_status['functions']);
                        
                        $system_ready = $all_tables_exist && $all_files_exist && $all_functions_exist;
                        ?>
                        
                        <?php if ($system_ready): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>النظام جاهز!</strong> تم تطبيق النظام البسيط للصلاحيات بنجاح.
                            </div>
                            
                            <div class="text-center">
                                <a href="simple_permissions_manager.php" class="btn btn-success btn-lg me-2">
                                    <i class="fas fa-shield-alt me-2"></i>بدء إدارة الصلاحيات
                                </a>
                                <a href="test_simple_permissions.php" class="btn btn-primary btn-lg">
                                    <i class="fas fa-vial me-2"></i>اختبار النظام
                                </a>
                            </div>
                            
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>النظام غير مكتمل.</strong> يحتاج إلى تطبيق المكونات المفقودة.
                            </div>
                            
                            <form method="POST">
                                <input type="hidden" name="apply_system" value="1">
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-rocket me-2"></i>تطبيق النظام الآن
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات النظام -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle me-2"></i>مزايا النظام البسيط</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>المزايا الرئيسية:</h6>
                                <ul>
                                    <li>نظام منفصل تماماً - لا تضارب</li>
                                    <li>الاحتفاظ بصلاحيات المدير الكاملة</li>
                                    <li>صلاحيات مخصصة للمستخدمين العاديين</li>
                                    <li>4 مستويات صلاحيات (مشاهدة، إضافة، تعديل، حذف)</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>سهولة الاستخدام:</h6>
                                <ul>
                                    <li>دوال بسيطة ومباشرة</li>
                                    <li>واجهة إدارة سهلة</li>
                                    <li>تطبيق فوري في أي صفحة</li>
                                    <li>سجل شامل للعمليات</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
