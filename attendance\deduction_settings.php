<?php
/**
 * إعدادات الخصم
 * Deduction Settings
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';
require_once '../includes/header.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

$user_id = $_SESSION['user_id'];

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'طلب غير صالح';
    } else {
        $action = $_POST['action'] ?? '';

        try {
            $conn->begin_transaction();

            switch ($action) {
                case 'add':
                    // إضافة إعداد جديد
                    $absence_type = clean_input($_POST['absence_type']);
                    $staff_role = clean_input($_POST['staff_role']);
                    $deduction_value = floatval($_POST['deduction_value']);
                    $deduction_type = clean_input($_POST['deduction_type']);
                    $max_allowed = !empty($_POST['max_allowed_per_month']) ? intval($_POST['max_allowed_per_month']) : null;
                    $requires_approval = isset($_POST['requires_approval']) ? 1 : 0;
                    $description = clean_input($_POST['description']);
                    $is_active = isset($_POST['is_active']) ? 1 : 0;

                    $stmt = $conn->prepare("
                        INSERT INTO deduction_settings
                        (absence_type, staff_role, deduction_value, deduction_type, max_allowed_per_month,
                         requires_approval, description, is_active, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
                    ");
                    $stmt->bind_param("ssdsisii", $absence_type, $staff_role, $deduction_value, $deduction_type,
                                    $max_allowed, $requires_approval, $description, $is_active);
                    $stmt->execute();
                    $success_message = 'تم إضافة إعداد الخصم بنجاح';
                    break;

                case 'update':
                    // تحديث إعداد موجود
                    $id = intval($_POST['id']);
                    $deduction_value = floatval($_POST['deduction_value']);
                    $deduction_type = clean_input($_POST['deduction_type']);
                    $max_allowed = !empty($_POST['max_allowed_per_month']) ? intval($_POST['max_allowed_per_month']) : null;
                    $requires_approval = isset($_POST['requires_approval']) ? 1 : 0;
                    $description = clean_input($_POST['description']);
                    $is_active = isset($_POST['is_active']) ? 1 : 0;

                    $stmt = $conn->prepare("
                        UPDATE deduction_settings
                        SET deduction_value = ?, deduction_type = ?, max_allowed_per_month = ?,
                            requires_approval = ?, description = ?, is_active = ?, updated_at = NOW()
                        WHERE id = ?
                    ");
                    $stmt->bind_param("dsisiii", $deduction_value, $deduction_type, $max_allowed,
                                    $requires_approval, $description, $is_active, $id);
                    $stmt->execute();
                    $success_message = 'تم تحديث إعداد الخصم بنجاح';
                    break;

                case 'delete':
                    // حذف إعداد
                    $id = intval($_POST['id']);
                    $stmt = $conn->prepare("DELETE FROM deduction_settings WHERE id = ?");
                    $stmt->bind_param("i", $id);
                    $stmt->execute();
                    $success_message = 'تم حذف إعداد الخصم بنجاح';
                    break;

                case 'toggle_status':
                    // تغيير حالة التفعيل
                    $id = intval($_POST['id']);
                    $stmt = $conn->prepare("UPDATE deduction_settings SET is_active = NOT is_active, updated_at = NOW() WHERE id = ?");
                    $stmt->bind_param("i", $id);
                    $stmt->execute();
                    $success_message = 'تم تغيير حالة الإعداد بنجاح';
                    break;
            }

            $conn->commit();

            // تسجيل النشاط
            log_activity($user_id, $action . '_deduction_setting', 'deduction_settings', $_POST['id'] ?? 0);

        } catch (Exception $e) {
            $conn->rollback();
            $error_message = 'حدث خطأ: ' . $e->getMessage();
        }
    }
}

// التحقق من وجود العمود staff_role وإضافته إذا لم يكن موجود
try {
    $check_column = $conn->query("SHOW COLUMNS FROM deduction_settings LIKE 'staff_role'");
    if ($check_column->num_rows == 0) {
        $conn->query("ALTER TABLE deduction_settings ADD COLUMN staff_role ENUM('teacher','staff','all') NOT NULL DEFAULT 'all' AFTER absence_type");
    }
} catch (Exception $e) {
    // تجاهل الخطأ إذا كان العمود موجود
}

// جلب الإعدادات الحالية
$settings_query = "SELECT * FROM deduction_settings ORDER BY staff_role, absence_type";
$settings_result = $conn->query($settings_query);
$settings = [];
if ($settings_result) {
    while ($row = $settings_result->fetch_assoc()) {
        $settings[] = $row;
    }
}

// أنواع الغياب المتاحة مع الأوصاف
$absence_types_info = [
    'unauthorized' => ['name' => 'غياب بدون عذر', 'default_value' => 100.00, 'default_type' => 'daily_rate'],
    'personal' => ['name' => 'غياب شخصي', 'default_value' => 50.00, 'default_type' => 'daily_rate'],
    'emergency' => ['name' => 'غياب طارئ', 'default_value' => 25.00, 'default_type' => 'percentage'],
    'sick' => ['name' => 'إجازة مرضية', 'default_value' => 0.00, 'default_type' => 'fixed']
];

// تحويل إلى التنسيق المطلوب للعرض
$default_settings = [];
foreach ($absence_types_info as $type => $info) {
    $default_settings[$type] = [
        'value' => $info['default_value'],
        'description' => $info['name'],
        'type' => $info['default_type']
    ];
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            إعدادات الخصم المتقدمة
                        </h5>
                        <div>
                            <a href="add_deduction_setting.php" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus me-2"></i>إضافة إعداد جديد
                            </a>
                            <a href="manage_absences.php" class="btn btn-secondary btn-sm">
                                <i class="fas fa-arrow-left me-2"></i>رجوع
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (isset($success_message)): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($error_message)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                        </div>
                    <?php endif; ?>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>معلومة:</strong> يمكنك إنشاء إعدادات خصم مختلفة للمعلمين والإداريين حسب نوع المخالفة. كل إعداد يمكن تخصيصه بمبلغ وشروط مختلفة.
                    </div>

                    <!-- جدول الإعدادات الحالية -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>نوع المخالفة</th>
                                    <th>الفئة المستهدفة</th>
                                    <th>نوع الخصم</th>
                                    <th>المبلغ</th>
                                    <th>الحد الأقصى شهرياً</th>
                                    <th>يتطلب موافقة</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($settings)): ?>
                                    <tr>
                                        <td colspan="8" class="text-center text-muted py-4">
                                            <i class="fas fa-inbox fa-2x mb-2"></i><br>
                                            لا توجد إعدادات خصم حالياً. اضغط على "إضافة إعداد جديد" لإنشاء إعداد.
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($settings as $setting): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($setting['description'] ?: $setting['absence_type']); ?></strong>
                                                <br><small class="text-muted"><?php echo $setting['absence_type']; ?></small>
                                            </td>
                                            <td>
                                                <?php
                                                $role_badge = '';
                                                switch($setting['staff_role'] ?? 'all') {
                                                    case 'teacher':
                                                        $role_badge = '<span class="badge bg-primary">المعلمين</span>';
                                                        break;
                                                    case 'staff':
                                                        $role_badge = '<span class="badge bg-success">الإداريين</span>';
                                                        break;
                                                    default:
                                                        $role_badge = '<span class="badge bg-secondary">الجميع</span>';
                                                }
                                                echo $role_badge;
                                                ?>
                                            </td>
                                            <td>
                                                <?php
                                                $type_text = '';
                                                switch($setting['deduction_type']) {
                                                    case 'fixed': $type_text = 'مبلغ ثابت'; break;
                                                    case 'percentage': $type_text = 'نسبة مئوية'; break;
                                                    case 'daily_rate': $type_text = 'معدل يومي'; break;
                                                    case 'hourly_rate': $type_text = 'معدل ساعي'; break;
                                                    default: $type_text = $setting['deduction_type'];
                                                }
                                                echo $type_text;
                                                ?>
                                            </td>
                                            <td>
                                                <strong class="text-danger">
                                                    <?php echo number_format($setting['deduction_value'], 2); ?>
                                                    <?php echo $setting['deduction_type'] === 'percentage' ? '%' : 'ج.م'; ?>
                                                </strong>
                                            </td>
                                            <td>
                                                <?php echo $setting['max_allowed_per_month'] ? $setting['max_allowed_per_month'] . ' مرة' : 'غير محدد'; ?>
                                            </td>
                                            <td>
                                                <?php if ($setting['requires_approval']): ?>
                                                    <span class="badge bg-warning">نعم</span>
                                                <?php else: ?>
                                                    <span class="badge bg-info">لا</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($setting['is_active']): ?>
                                                    <span class="badge bg-success">نشط</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">غير نشط</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="edit_deduction_setting.php?id=<?php echo $setting['id']; ?>"
                                                       class="btn btn-outline-primary" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-outline-<?php echo $setting['is_active'] ? 'warning' : 'success'; ?>"
                                                            onclick="toggleStatus(<?php echo $setting['id']; ?>)"
                                                            title="<?php echo $setting['is_active'] ? 'إلغاء التفعيل' : 'تفعيل'; ?>">
                                                        <i class="fas fa-<?php echo $setting['is_active'] ? 'pause' : 'play'; ?>"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-danger"
                                                            onclick="deleteSetting(<?php echo $setting['id']; ?>, '<?php echo htmlspecialchars($setting['description'] ?: $setting['absence_type']); ?>')"
                                                            title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>


                </div>
            </div>
        </div>
    </div>


</div>



<script>
// دالة تغيير حالة التفعيل
function toggleStatus(id) {
    if (confirm('هل أنت متأكد من تغيير حالة هذا الإعداد؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <?php echo csrf_token_field(); ?>
            <input type="hidden" name="action" value="toggle_status">
            <input type="hidden" name="id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// دالة حذف الإعداد
function deleteSetting(id, name) {
    if (confirm(`هل أنت متأكد من حذف إعداد "${name}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <?php echo csrf_token_field(); ?>
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php require_once '../includes/footer.php'; ?>
