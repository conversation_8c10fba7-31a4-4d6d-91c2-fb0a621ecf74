<?php
/**
 * حماية الصفحات باستخدام النظام البسيط
 * Page Protection using Simple Permissions System
 */

// منع الوصول المباشر
if (!defined('SYSTEM_INIT') && !defined('FUNCTIONS_LOADED')) {
    die('Direct access not allowed');
}

// تحميل النظام البسيط
if (file_exists(__DIR__ . '/simple_permissions.php')) {
    require_once __DIR__ . '/simple_permissions.php';
}

/**
 * حماية صفحة بالكامل
 * 
 * @param string $page_name اسم الصفحة
 * @param string $required_action العملية المطلوبة
 * @param string $redirect_url رابط إعادة التوجيه
 */
function protect_page($page_name, $required_action = 'view', $redirect_url = '../dashboard/') {
    // التحقق من تسجيل الدخول أولاً
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    if (!isset($_SESSION['user_id'])) {
        header('Location: ../login.php');
        exit();
    }
    
    // استخدام النظام البسيط للتحقق
    if (function_exists('require_page_permission')) {
        require_page_permission($page_name, $required_action, $redirect_url);
    } else {
        // احتياطي: التحقق من المدير فقط
        if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
            header('Location: ' . $redirect_url . '?error=access_denied');
            exit();
        }
    }
}

/**
 * حماية عملية معينة (إضافة، تعديل، حذف)
 * 
 * @param string $page_name اسم الصفحة
 * @param string $action العملية
 * @return bool
 */
function can_perform_action($page_name, $action) {
    if (function_exists('check_page_permission')) {
        return check_page_permission($page_name, $action);
    }
    
    // احتياطي: المدير فقط
    return isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
}

/**
 * عرض أزرار مشروطة حسب الصلاحيات
 * 
 * @param string $page_name اسم الصفحة
 * @param array $buttons الأزرار المطلوبة
 * @return string
 */
function render_action_buttons($page_name, $buttons = []) {
    $html = '<div class="action-buttons d-flex gap-2">';
    
    $default_buttons = [
        'add' => ['class' => 'btn-success', 'icon' => 'fas fa-plus', 'text' => 'إضافة'],
        'edit' => ['class' => 'btn-warning', 'icon' => 'fas fa-edit', 'text' => 'تعديل'],
        'delete' => ['class' => 'btn-danger', 'icon' => 'fas fa-trash', 'text' => 'حذف'],
        'view' => ['class' => 'btn-primary', 'icon' => 'fas fa-eye', 'text' => 'عرض']
    ];
    
    foreach ($buttons as $action => $config) {
        if (can_perform_action($page_name, $action)) {
            $btn_config = array_merge($default_buttons[$action] ?? [], $config);
            $html .= sprintf(
                '<button type="button" class="btn %s" onclick="%s">
                    <i class="%s me-1"></i>%s
                </button>',
                $btn_config['class'],
                $btn_config['onclick'] ?? '',
                $btn_config['icon'],
                $btn_config['text']
            );
        }
    }
    
    $html .= '</div>';
    return $html;
}

/**
 * إنشاء قائمة تنقل مشروطة
 * 
 * @param array $pages قائمة الصفحات
 * @return string
 */
function render_conditional_menu($pages = []) {
    $html = '<ul class="nav nav-pills flex-column">';
    
    foreach ($pages as $page_name => $config) {
        if (can_perform_action($page_name, 'view')) {
            $html .= sprintf(
                '<li class="nav-item">
                    <a class="nav-link" href="%s">
                        <i class="%s me-2"></i>%s
                    </a>
                </li>',
                $config['url'] ?? '#',
                $config['icon'] ?? 'fas fa-circle',
                $config['title'] ?? $page_name
            );
        }
    }
    
    $html .= '</ul>';
    return $html;
}

/**
 * فحص صلاحيات متعددة
 * 
 * @param array $permissions قائمة الصلاحيات
 * @param string $mode نوع الفحص (all أو any)
 * @return bool
 */
function check_multiple_permissions($permissions, $mode = 'any') {
    if (!function_exists('check_page_permission')) {
        return isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
    }
    
    $results = [];
    foreach ($permissions as $page_name => $action) {
        $results[] = check_page_permission($page_name, $action);
    }
    
    if ($mode === 'all') {
        return !in_array(false, $results);
    } else {
        return in_array(true, $results);
    }
}

/**
 * إنشاء تبويبات مشروطة
 * 
 * @param array $tabs قائمة التبويبات
 * @return string
 */
function render_conditional_tabs($tabs = []) {
    $nav_html = '<ul class="nav nav-tabs" role="tablist">';
    $content_html = '<div class="tab-content">';
    
    $first_tab = true;
    foreach ($tabs as $tab_id => $config) {
        $page_name = $config['page_name'] ?? $tab_id;
        $action = $config['action'] ?? 'view';
        
        if (can_perform_action($page_name, $action)) {
            $active_class = $first_tab ? 'active' : '';
            
            $nav_html .= sprintf(
                '<li class="nav-item">
                    <button class="nav-link %s" data-bs-toggle="tab" data-bs-target="#%s">
                        <i class="%s me-1"></i>%s
                    </button>
                </li>',
                $active_class,
                $tab_id,
                $config['icon'] ?? 'fas fa-circle',
                $config['title'] ?? $tab_id
            );
            
            $content_html .= sprintf(
                '<div class="tab-pane fade %s" id="%s">
                    %s
                </div>',
                $first_tab ? 'show active' : '',
                $tab_id,
                $config['content'] ?? ''
            );
            
            $first_tab = false;
        }
    }
    
    $nav_html .= '</ul>';
    $content_html .= '</div>';
    
    return $nav_html . $content_html;
}

/**
 * حماية AJAX requests
 * 
 * @param string $page_name اسم الصفحة
 * @param string $action العملية
 * @return array
 */
function check_ajax_permission($page_name, $action = 'view') {
    header('Content-Type: application/json');
    
    if (!isset($_SESSION['user_id'])) {
        http_response_code(401);
        return ['success' => false, 'message' => 'غير مسجل الدخول'];
    }
    
    if (!can_perform_action($page_name, $action)) {
        http_response_code(403);
        return ['success' => false, 'message' => 'غير مسموح لك بهذه العملية'];
    }
    
    return ['success' => true];
}

/**
 * إنشاء نموذج مشروط
 * 
 * @param string $page_name اسم الصفحة
 * @param string $action العملية
 * @param string $form_content محتوى النموذج
 * @return string
 */
function render_conditional_form($page_name, $action, $form_content) {
    if (!can_perform_action($page_name, $action)) {
        return '<div class="alert alert-warning">
                    <i class="fas fa-lock me-2"></i>
                    ليس لديك صلاحية لتنفيذ هذه العملية
                </div>';
    }
    
    return $form_content;
}

/**
 * عرض رسالة حسب الصلاحية
 * 
 * @param string $page_name اسم الصفحة
 * @param string $action العملية
 * @param string $allowed_content المحتوى المسموح
 * @param string $denied_message رسالة الرفض
 * @return string
 */
function conditional_content($page_name, $action, $allowed_content, $denied_message = null) {
    if (can_perform_action($page_name, $action)) {
        return $allowed_content;
    }
    
    if ($denied_message === null) {
        $denied_message = 'ليس لديك صلاحية للوصول لهذا المحتوى';
    }
    
    return '<div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>' . 
                htmlspecialchars($denied_message) . 
            '</div>';
}

// تعريف أن نظام حماية الصفحات محمل
define('PAGE_PROTECTION_LOADED', true);
?>
