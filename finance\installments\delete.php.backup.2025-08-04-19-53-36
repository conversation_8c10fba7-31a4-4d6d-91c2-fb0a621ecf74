<?php
require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/security.php';

// التحقق من الجلسة والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

$error_message = '';
$success_message = '';

// جلب معرف القسط
$installment_id = intval($_GET['id'] ?? 0);

if ($installment_id <= 0) {
    header('Location: index.php?error=' . urlencode('معرف القسط غير صحيح'));
    exit();
}

// جلب بيانات القسط للتأكيد
$stmt = $conn->prepare("
    SELECT 
        si.id,
        si.installment_number,
        si.total_amount,
        si.paid_amount,
        si.due_date,
        si.status,
        u.full_name as student_name,
        s.student_id,
        c.class_name
    FROM student_installments si
    JOIN students s ON si.student_id = s.id
    JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    WHERE si.id = ?
");

$stmt->bind_param("i", $installment_id);
$stmt->execute();
$installment = $stmt->get_result()->fetch_assoc();

if (!$installment) {
    header('Location: index.php?error=' . urlencode('القسط غير موجود'));
    exit();
}

// معالجة الحذف
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = __('invalid_request');
    } else {
        $confirm_delete = $_POST['confirm_delete'] ?? '';
        
        if ($confirm_delete !== 'DELETE') {
            $error_message = __('delete_confirmation_required');
        } else {
            try {
                // بدء المعاملة
                $conn->begin_transaction();
                
                // حذف المدفوعات المرتبطة أولاً (إذا وجدت)
                $delete_payments = $conn->prepare("DELETE FROM student_payments WHERE installment_id = ?");
                if ($delete_payments) {
                    $delete_payments->bind_param("i", $installment_id);
                    $delete_payments->execute();
                }
                
                // حذف القسط
                $delete_stmt = $conn->prepare("DELETE FROM student_installments WHERE id = ?");
                $delete_stmt->bind_param("i", $installment_id);
                
                if (!$delete_stmt->execute()) {
                    throw new Exception(__('database_error') . ': ' . $conn->error);
                }
                
                // تأكيد المعاملة
                $conn->commit();
                
                // إعادة توجيه مع رسالة نجاح
                header("Location: index.php?delete_success=1&student_name=" . urlencode($installment['student_name']) . "&installment_number=" . $installment['installment_number']);
                exit();
                
            } catch (Exception $e) {
                // إلغاء المعاملة
                $conn->rollback();
                $error_message = $e->getMessage();
                error_log("Delete installment error: " . $e->getMessage());
            }
        }
    }
}

$page_title = __('delete_installment');
include_once '../../includes/header.php';
?>

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm border-danger">
                <div class="card-header bg-danger text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i><?php echo __('delete_installment'); ?></h4>
                        <a href="index.php" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-left me-1"></i><?php echo __('back'); ?>
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                    </div>
                    <?php endif; ?>
                    
                    <!-- تحذير -->
                    <div class="alert alert-warning">
                        <h5 class="alert-heading"><i class="fas fa-exclamation-triangle me-2"></i><?php echo __('warning'); ?></h5>
                        <p class="mb-0"><?php echo __('delete_installment_warning'); ?></p>
                    </div>
                    
                    <!-- معلومات القسط المراد حذفه -->
                    <div class="card border-danger mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0 text-danger"><i class="fas fa-info-circle me-2"></i><?php echo __('installment_to_delete'); ?></h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td><strong><?php echo __('student'); ?>:</strong></td>
                                            <td><?php echo htmlspecialchars($installment['student_name']); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong><?php echo __('student_number'); ?>:</strong></td>
                                            <td><?php echo htmlspecialchars($installment['student_id']); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong><?php echo __('class'); ?>:</strong></td>
                                            <td><?php echo htmlspecialchars($installment['class_name'] ?? 'غير محدد'); ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td><strong><?php echo __('installment_number'); ?>:</strong></td>
                                            <td>#<?php echo $installment['installment_number']; ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong><?php echo __('total_amount'); ?>:</strong></td>
                                            <td><?php echo number_format($installment['total_amount'], 2); ?> <?php echo get_system_setting('currency_symbol', 'ر.س'); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong><?php echo __('paid_amount'); ?>:</strong></td>
                                            <td class="<?php echo $installment['paid_amount'] > 0 ? 'text-success fw-bold' : ''; ?>">
                                                <?php echo number_format($installment['paid_amount'], 2); ?> <?php echo get_system_setting('currency_symbol', 'ر.س'); ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong><?php echo __('status'); ?>:</strong></td>
                                            <td>
                                                <span class="badge bg-<?php echo $installment['status'] == 'paid' ? 'success' : ($installment['status'] == 'overdue' ? 'danger' : 'warning'); ?>">
                                                    <?php echo __($installment['status']); ?>
                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- تحذير إضافي إذا كان هناك مبلغ مدفوع -->
                    <?php if ($installment['paid_amount'] > 0): ?>
                    <div class="alert alert-danger">
                        <h6 class="alert-heading"><i class="fas fa-money-bill-wave me-2"></i><?php echo __('payment_warning'); ?></h6>
                        <p class="mb-0">
                            <?php echo __('installment_has_payments'); ?>: <strong><?php echo number_format($installment['paid_amount'], 2); ?> <?php echo get_system_setting('currency_symbol', 'ر.س'); ?></strong><br>
                            <?php echo __('delete_will_remove_payments'); ?>
                        </p>
                    </div>
                    <?php endif; ?>
                    
                    <!-- نموذج التأكيد -->
                    <form method="POST" action="" onsubmit="return confirmDelete()">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        
                        <div class="mb-4">
                            <label class="form-label text-danger"><strong><?php echo __('delete_confirmation_label'); ?></strong></label>
                            <input type="text" class="form-control" name="confirm_delete" placeholder="<?php echo __('type_delete_to_confirm'); ?>" required>
                            <small class="text-muted"><?php echo __('type_delete_instruction'); ?></small>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="index.php" class="btn btn-secondary btn-lg">
                                <i class="fas fa-arrow-left me-2"></i><?php echo __('cancel'); ?>
                            </a>
                            <button type="submit" class="btn btn-danger btn-lg">
                                <i class="fas fa-trash me-2"></i><?php echo __('delete_installment'); ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete() {
    const confirmText = document.querySelector('input[name="confirm_delete"]').value;
    if (confirmText !== 'DELETE') {
        alert('<?php echo __("please_type_delete_to_confirm"); ?>');
        return false;
    }
    
    return confirm('<?php echo __("final_delete_confirmation"); ?>');
}
</script>

<?php include_once '../../includes/footer.php'; ?>
