<?php
/**
 * سجل تغييرات الصلاحيات
 * Permissions Audit Log
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

// معاملات البحث والتصفية
$search_user = clean_input($_GET['search_user'] ?? '');
$filter_action = clean_input($_GET['filter_action'] ?? '');
$date_from = clean_input($_GET['date_from'] ?? '');
$date_to = clean_input($_GET['date_to'] ?? '');
$page = intval($_GET['page'] ?? 1);
$per_page = 20;
$offset = ($page - 1) * $per_page;

// بناء استعلام البحث
$where_conditions = [];
$params = [];
$types = '';

if (!empty($search_user)) {
    $where_conditions[] = "(u.full_name LIKE ? OR u.username LIKE ? OR u.email LIKE ?)";
    $search_term = "%$search_user%";
    $params = array_merge($params, [$search_term, $search_term, $search_term]);
    $types .= 'sss';
}

if (!empty($filter_action)) {
    $where_conditions[] = "pal.action_type = ?";
    $params[] = $filter_action;
    $types .= 's';
}

if (!empty($date_from)) {
    $where_conditions[] = "DATE(pal.created_at) >= ?";
    $params[] = $date_from;
    $types .= 's';
}

if (!empty($date_to)) {
    $where_conditions[] = "DATE(pal.created_at) <= ?";
    $params[] = $date_to;
    $types .= 's';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// عدد السجلات الإجمالي
$count_query = "
    SELECT COUNT(*) as total
    FROM permissions_audit_log pal
    JOIN users u ON pal.user_id = u.id
    JOIN users changer ON pal.changed_by = changer.id
    $where_clause
";

$count_stmt = $conn->prepare($count_query);
if (!empty($params)) {
    $count_stmt->bind_param($types, ...$params);
}
$count_stmt->execute();
$total_records = $count_stmt->get_result()->fetch_assoc()['total'];
$total_pages = ceil($total_records / $per_page);

// جلب السجلات
$audit_query = "
    SELECT 
        pal.*,
        u.full_name as user_name,
        u.username,
        changer.full_name as changed_by_name
    FROM permissions_audit_log pal
    JOIN users u ON pal.user_id = u.id
    JOIN users changer ON pal.changed_by = changer.id
    $where_clause
    ORDER BY pal.created_at DESC
    LIMIT ? OFFSET ?
";

$audit_stmt = $conn->prepare($audit_query);
$final_params = $params;
$final_params[] = $per_page;
$final_params[] = $offset;
$final_types = $types . 'ii';

if (!empty($params)) {
    $audit_stmt->bind_param($final_types, ...$final_params);
} else {
    $audit_stmt->bind_param('ii', $per_page, $offset);
}

$audit_stmt->execute();
$audit_result = $audit_stmt->get_result();

$page_title = 'سجل تغييرات الصلاحيات';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-history me-2"></i><?php echo $page_title; ?></h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="../admin/">الإدارة</a></li>
                    <li class="breadcrumb-item active">سجل الصلاحيات</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="card mb-4">
        <div class="card-header">
            <h5><i class="fas fa-filter me-2"></i>البحث والتصفية</h5>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">البحث في المستخدمين</label>
                    <input type="text" class="form-control" name="search_user" 
                           value="<?php echo htmlspecialchars($search_user); ?>" 
                           placeholder="اسم المستخدم أو البريد الإلكتروني">
                </div>
                <div class="col-md-2">
                    <label class="form-label">نوع الإجراء</label>
                    <select class="form-select" name="filter_action">
                        <option value="">جميع الإجراءات</option>
                        <option value="role_changed" <?php echo $filter_action === 'role_changed' ? 'selected' : ''; ?>>تغيير الدور</option>
                        <option value="permission_granted" <?php echo $filter_action === 'permission_granted' ? 'selected' : ''; ?>>منح صلاحية</option>
                        <option value="permission_revoked" <?php echo $filter_action === 'permission_revoked' ? 'selected' : ''; ?>>إلغاء صلاحية</option>
                        <option value="permission_modified" <?php echo $filter_action === 'permission_modified' ? 'selected' : ''; ?>>تعديل صلاحية</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" name="date_from" 
                           value="<?php echo htmlspecialchars($date_from); ?>">
                </div>
                <div class="col-md-2">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" name="date_to" 
                           value="<?php echo htmlspecialchars($date_to); ?>">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-2"></i>بحث
                    </button>
                    <a href="permissions_audit.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i>مسح
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول السجلات -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5><i class="fas fa-list me-2"></i>سجل التغييرات</h5>
            <span class="badge bg-info"><?php echo $total_records; ?> سجل</span>
        </div>
        <div class="card-body">
            <?php if ($audit_result->num_rows > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>التاريخ والوقت</th>
                                <th>المستخدم</th>
                                <th>نوع الإجراء</th>
                                <th>التفاصيل</th>
                                <th>تم بواسطة</th>
                                <th>عنوان IP</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($log = $audit_result->fetch_assoc()): ?>
                            <tr>
                                <td>
                                    <small class="text-muted">
                                        <?php echo date('Y-m-d H:i:s', strtotime($log['created_at'])); ?>
                                    </small>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                            <?php echo strtoupper(substr($log['user_name'], 0, 1)); ?>
                                        </div>
                                        <div>
                                            <strong><?php echo htmlspecialchars($log['user_name']); ?></strong>
                                            <br><small class="text-muted">@<?php echo htmlspecialchars($log['username']); ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-<?php 
                                        echo match($log['action_type']) {
                                            'role_changed' => 'primary',
                                            'permission_granted' => 'success',
                                            'permission_revoked' => 'danger',
                                            'permission_modified' => 'warning',
                                            default => 'secondary'
                                        };
                                    ?>">
                                        <?php 
                                        $action_names = [
                                            'role_changed' => 'تغيير الدور',
                                            'permission_granted' => 'منح صلاحية',
                                            'permission_revoked' => 'إلغاء صلاحية',
                                            'permission_modified' => 'تعديل صلاحية'
                                        ];
                                        echo $action_names[$log['action_type']] ?? $log['action_type'];
                                        ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($log['action_type'] === 'role_changed'): ?>
                                        <small>
                                            من: <code><?php echo htmlspecialchars($log['old_value']); ?></code><br>
                                            إلى: <code><?php echo htmlspecialchars($log['new_value']); ?></code>
                                        </small>
                                    <?php elseif ($log['resource_type'] && $log['resource_key']): ?>
                                        <small>
                                            النوع: <span class="badge badge-sm bg-light text-dark"><?php echo htmlspecialchars($log['resource_type']); ?></span><br>
                                            المورد: <code><?php echo htmlspecialchars($log['resource_key']); ?></code>
                                        </small>
                                    <?php endif; ?>
                                    
                                    <?php if ($log['notes']): ?>
                                        <br><small class="text-muted">
                                            <i class="fas fa-sticky-note me-1"></i>
                                            <?php echo htmlspecialchars($log['notes']); ?>
                                        </small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small><?php echo htmlspecialchars($log['changed_by_name']); ?></small>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?php echo htmlspecialchars($log['ip_address'] ?? 'غير معروف'); ?>
                                    </small>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>

                <!-- الترقيم -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="Page navigation" class="mt-3">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page - 1; ?>&<?php echo http_build_query($_GET); ?>">السابق</a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&<?php echo http_build_query($_GET); ?>"><?php echo $i; ?></a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page + 1; ?>&<?php echo http_build_query($_GET); ?>">التالي</a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-history fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد سجلات</h5>
                    <p class="text-muted">لم يتم العثور على سجلات تغييرات الصلاحيات</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
