<?php
require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/security.php';

// التحقق من الجلسة والصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    header('Location: ../../dashboard/');
    exit();
}

$error_message = '';
$success_message = '';

// جلب معرف الرسم
$fee_id = intval($_GET['id'] ?? 0);

if ($fee_id <= 0) {
    header('Location: index.php?error=' . urlencode('معرف الرسم غير صحيح'));
    exit();
}

// جلب بيانات الرسم للتأكيد
$stmt = $conn->prepare("
    SELECT 
        sf.*,
        u.full_name as student_name,
        s.student_id as student_number,
        c.class_name,
        ft.type_name as fee_type_name
    FROM student_fees sf
    JOIN students s ON sf.student_id = s.id
    JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    LEFT JOIN fee_types ft ON sf.fee_type_id = ft.id
    WHERE sf.id = ?
");

$stmt->bind_param("i", $fee_id);
$stmt->execute();
$fee = $stmt->get_result()->fetch_assoc();

if (!$fee) {
    header('Location: index.php?error=' . urlencode('الرسم غير موجود'));
    exit();
}

// التحقق من وجود مدفوعات مرتبطة
$payments_count = 0;
$payments_stmt = $conn->prepare("SELECT COUNT(*) as count FROM student_payments WHERE student_fee_id = ?");
if ($payments_stmt) {
    $payments_stmt->bind_param("i", $fee_id);
    $payments_stmt->execute();
    $payments_count = $payments_stmt->get_result()->fetch_assoc()['count'];
}

// معالجة الحذف
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = __('invalid_request');
    } else {
        $confirm_delete = $_POST['confirm_delete'] ?? '';
        
        if ($confirm_delete !== 'DELETE') {
            $error_message = __('delete_confirmation_required');
        } else {
            try {
                // بدء المعاملة
                $conn->begin_transaction();
                
                // حذف المدفوعات المرتبطة أولاً (إذا وجدت)
                if ($payments_count > 0) {
                    $delete_payments = $conn->prepare("DELETE FROM student_payments WHERE student_fee_id = ?");
                    if ($delete_payments) {
                        $delete_payments->bind_param("i", $fee_id);
                        $delete_payments->execute();
                    }
                }
                
                // حذف الرسم
                $delete_stmt = $conn->prepare("DELETE FROM student_fees WHERE id = ?");
                $delete_stmt->bind_param("i", $fee_id);
                
                if (!$delete_stmt->execute()) {
                    throw new Exception(__('database_error') . ': ' . $conn->error);
                }
                
                // تأكيد المعاملة
                $conn->commit();
                
                // إعادة توجيه مع رسالة نجاح
                header("Location: index.php?delete_success=1&student_name=" . urlencode($fee['student_name']) . "&fee_type=" . urlencode($fee['fee_type_name']));
                exit();
                
            } catch (Exception $e) {
                // إلغاء المعاملة
                $conn->rollback();
                $error_message = $e->getMessage();
                error_log("Delete fee error: " . $e->getMessage());
            }
        }
    }
}

$page_title = __('delete_fee');
include_once '../../includes/header.php';
?>

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm border-danger">
                <div class="card-header bg-danger text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i><?php echo __('delete_fee'); ?></h4>
                        <a href="index.php" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-left me-1"></i><?php echo __('back'); ?>
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                    </div>
                    <?php endif; ?>
                    
                    <!-- تحذير -->
                    <div class="alert alert-warning">
                        <h5 class="alert-heading"><i class="fas fa-exclamation-triangle me-2"></i><?php echo __('warning'); ?></h5>
                        <p class="mb-0"><?php echo __('delete_fee_warning'); ?></p>
                    </div>
                    
                    <!-- معلومات الرسم المراد حذفه -->
                    <div class="card border-danger mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0 text-danger"><i class="fas fa-info-circle me-2"></i><?php echo __('fee_to_delete'); ?></h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td><strong><?php echo __('student'); ?>:</strong></td>
                                            <td><?php echo htmlspecialchars($fee['student_name']); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong><?php echo __('student_number'); ?>:</strong></td>
                                            <td><?php echo htmlspecialchars($fee['student_number']); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong><?php echo __('class'); ?>:</strong></td>
                                            <td><?php echo htmlspecialchars($fee['class_name'] ?? 'غير محدد'); ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td><strong><?php echo __('fee_type'); ?>:</strong></td>
                                            <td><?php echo htmlspecialchars($fee['fee_type_name'] ?? __('general_fee')); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong><?php echo __('final_amount'); ?>:</strong></td>
                                            <td><?php echo number_format($fee['final_amount'], 2); ?> <?php echo get_system_setting('currency_symbol', 'ر.س'); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong><?php echo __('remaining_amount'); ?>:</strong></td>
                                            <td class="<?php echo $fee['remaining_amount'] > 0 ? 'text-danger fw-bold' : 'text-success'; ?>">
                                                <?php echo number_format($fee['remaining_amount'], 2); ?> <?php echo get_system_setting('currency_symbol', 'ر.س'); ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong><?php echo __('status'); ?>:</strong></td>
                                            <td>
                                                <?php
                                                $status_classes = [
                                                    'pending' => 'bg-warning',
                                                    'partial' => 'bg-info',
                                                    'paid' => 'bg-success',
                                                    'overdue' => 'bg-danger',
                                                    'cancelled' => 'bg-secondary'
                                                ];
                                                $status_class = $status_classes[$fee['status']] ?? 'bg-secondary';
                                                ?>
                                                <span class="badge <?php echo $status_class; ?>">
                                                    <?php echo __($fee['status']); ?>
                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- تحذير إضافي إذا كان هناك مدفوعات -->
                    <?php if ($payments_count > 0): ?>
                    <div class="alert alert-danger">
                        <h6 class="alert-heading"><i class="fas fa-money-bill-wave me-2"></i><?php echo __('payments_warning'); ?></h6>
                        <p class="mb-0">
                            <?php echo __('fee_has_payments'); ?>: <strong><?php echo $payments_count; ?></strong> <?php echo __('payments'); ?><br>
                            <?php echo __('delete_will_remove_payments'); ?>
                        </p>
                    </div>
                    <?php endif; ?>
                    
                    <!-- نموذج التأكيد -->
                    <form method="POST" action="" onsubmit="return confirmDelete()">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        
                        <div class="mb-4">
                            <label class="form-label text-danger"><strong><?php echo __('delete_confirmation_label'); ?></strong></label>
                            <input type="text" class="form-control" name="confirm_delete" placeholder="<?php echo __('type_delete_to_confirm'); ?>" required>
                            <small class="text-muted"><?php echo __('type_delete_instruction'); ?></small>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="index.php" class="btn btn-secondary btn-lg">
                                <i class="fas fa-arrow-left me-2"></i><?php echo __('cancel'); ?>
                            </a>
                            <button type="submit" class="btn btn-danger btn-lg">
                                <i class="fas fa-trash me-2"></i><?php echo __('delete_fee'); ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete() {
    const confirmText = document.querySelector('input[name="confirm_delete"]').value;
    if (confirmText !== 'DELETE') {
        alert('<?php echo __("please_type_delete_to_confirm"); ?>');
        return false;
    }
    
    return confirm('<?php echo __("final_delete_confirmation_fee"); ?>');
}
</script>

<?php include_once '../../includes/footer.php'; ?>
