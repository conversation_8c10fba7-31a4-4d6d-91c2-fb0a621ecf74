<?php
/**
 * استيراد المواد الدراسية من CSV
 * Import Subjects from CSV
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    header('Location: ../dashboard/');
    exit();
}

$error_message = '';
$success_message = '';
$import_report = [];

// معالجة رفع الملف
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['import_file'])) {
    if (!verify_csrf_token($_POST['csrf_token'])) {
        $error_message = 'رمز الأمان غير صحيح';
    } else {
        $file = $_FILES['import_file'];
        if ($file['error'] === UPLOAD_ERR_OK) {
            $ext = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            if (!in_array($ext, ['csv'])) {
                $error_message = 'نوع الملف غير مدعوم. يجب أن يكون الملف بصيغة CSV';
            } else {
                $handle = fopen($file['tmp_name'], 'r');
                if ($handle) {
                    // قراءة المحتوى وتحويل الترميز
                    $content = file_get_contents($file['tmp_name']);
                    
                    // إزالة BOM إذا كان موجوداً
                    $content = str_replace("\xEF\xBB\xBF", '', $content);
                    
                    // تحويل الترميز إلى UTF-8 إذا لزم الأمر
                    if (!mb_check_encoding($content, 'UTF-8')) {
                        $content = mb_convert_encoding($content, 'UTF-8', 'auto');
                    }
                    
                    // كتابة المحتوى المحسن إلى ملف مؤقت
                    $temp_file = tempnam(sys_get_temp_dir(), 'csv_import_subjects_');
                    file_put_contents($temp_file, $content);
                    
                    // إعادة فتح الملف المحسن
                    fclose($handle);
                    $handle = fopen($temp_file, 'r');
                    
                    $header = fgetcsv($handle);
                    $row_num = 1;
                    $added = 0;
                    $errors = 0;
                    
                    while (($row = fgetcsv($handle)) !== false) {
                        $row_num++;
                        $data = array_combine($header, $row);
                        $row_errors = [];
                        
                        // جمع البيانات
                        $subject_name = clean_input($data['subject_name'] ?? '');
                        $subject_code = clean_input($data['subject_code'] ?? '');
                        $department = clean_input($data['department'] ?? '');
                        $description = clean_input($data['description'] ?? '');
                        $credit_hours = intval($data['credit_hours'] ?? 1);
                        $status = clean_input($data['status'] ?? 'active');
                        
                        // التحقق من البيانات المطلوبة
                        if (empty($subject_name)) {
                            $row_errors[] = 'اسم المادة مطلوب';
                        }
                        
                        if (empty($subject_code)) {
                            $row_errors[] = 'كود المادة مطلوب';
                        }
                        
                        if (!in_array($status, ['active', 'inactive'])) {
                            $status = 'active';
                        }
                        
                        if ($credit_hours < 1) {
                            $credit_hours = 1;
                        }
                        
                        // التحقق من عدم تكرار كود المادة
                        if (!empty($subject_code)) {
                            $check_query = "SELECT id FROM subjects WHERE subject_code = ?";
                            $check_stmt = $conn->prepare($check_query);
                            $check_stmt->bind_param("s", $subject_code);
                            $check_stmt->execute();
                            if ($check_stmt->get_result()->num_rows > 0) {
                                $row_errors[] = 'كود المادة موجود مسبقاً';
                            }
                        }
                        
                        if (empty($row_errors)) {
                            $conn->begin_transaction();
                            try {
                                // إدراج المادة
                                $insert_query = "
                                    INSERT INTO subjects (
                                        subject_name, subject_code, department, description, 
                                        credit_hours, status, created_at
                                    ) VALUES (?, ?, ?, ?, ?, ?, NOW())
                                ";
                                
                                $insert_stmt = $conn->prepare($insert_query);
                                $insert_stmt->bind_param(
                                    "ssssiss",
                                    $subject_name, $subject_code, $department, $description,
                                    $credit_hours, $status
                                );
                                
                                $insert_stmt->execute();
                                $conn->commit();
                                $added++;
                                
                                $import_report[] = [
                                    'row' => $row_num,
                                    'status' => 'success',
                                    'message' => 'تم إضافة المادة بنجاح',
                                    'name' => $subject_name
                                ];
                                
                            } catch (Exception $e) {
                                $conn->rollback();
                                $errors++;
                                $import_report[] = [
                                    'row' => $row_num,
                                    'status' => 'error',
                                    'message' => $e->getMessage(),
                                    'name' => $subject_name
                                ];
                            }
                        } else {
                            $errors++;
                            $import_report[] = [
                                'row' => $row_num,
                                'status' => 'error',
                                'message' => implode('; ', $row_errors),
                                'name' => $subject_name
                            ];
                        }
                    }
                    
                    fclose($handle);
                    
                    // تنظيف الملف المؤقت
                    if (isset($temp_file) && file_exists($temp_file)) {
                        unlink($temp_file);
                    }
                    
                    $success_message = "تم استيراد {$added} مادة بنجاح. فشل في استيراد {$errors} مادة.";
                } else {
                    $error_message = 'فشل في فتح الملف';
                }
            }
        } else {
            $error_message = 'خطأ في رفع الملف';
        }
    }
}

$page_title = 'استيراد المواد الدراسية';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-upload me-2"></i>استيراد المواد الدراسية
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="index.php">المواد الدراسية</a></li>
                    <li class="breadcrumb-item active">استيراد</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
            </a>
        </div>
    </div>

    <!-- عرض الرسائل -->
    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- نموذج الاستيراد -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-file-csv me-2"></i>استيراد من ملف CSV
            </h5>
        </div>
        <div class="card-body">
            <!-- تعليمات الاستيراد -->
            <div class="alert alert-info">
                <h6 class="alert-heading">
                    <i class="fas fa-info-circle me-2"></i>تعليمات الاستيراد
                </h6>
                <ul class="mb-2">
                    <li>يجب أن يكون الملف بصيغة CSV مع ترميز UTF-8</li>
                    <li>الصف الأول يجب أن يحتوي على رؤوس الأعمدة</li>
                    <li>الأعمدة المطلوبة: subject_name, subject_code</li>
                    <li>الأعمدة الاختيارية: department, description, credit_hours, status</li>
                    <li>استخدم النموذج المرفق لضمان التوافق مع النظام</li>
                </ul>
                <div class="alert alert-warning mb-0">
                    <small>
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        <strong>ملاحظة:</strong> لضمان ظهور النصوص العربية بشكل صحيح، يُنصح بتحميل النموذج واستخدامه مباشرة.
                    </small>
                </div>
            </div>

            <!-- نموذج الرفع -->
            <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                <?php echo csrf_token_field(); ?>
                
                <div class="mb-3">
                    <label for="import_file" class="form-label">اختر ملف CSV <span class="text-danger">*</span></label>
                    <input type="file" class="form-control" id="import_file" name="import_file" accept=".csv" required>
                    <div class="form-text">الحد الأقصى لحجم الملف: 5MB</div>
                    <div class="invalid-feedback">يرجى اختيار ملف CSV</div>
                </div>
                
                <div class="d-flex justify-content-between">
                    <a href="generate_csv_sample.php" class="btn btn-outline-info">
                        <i class="fas fa-download me-2"></i>تحميل نموذج CSV
                    </a>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-upload me-2"></i>استيراد المواد
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- تقرير الاستيراد -->
    <?php if (!empty($import_report)): ?>
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list-alt me-2"></i>تقرير الاستيراد
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>الصف</th>
                                <th>اسم المادة</th>
                                <th>الحالة</th>
                                <th>الرسالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($import_report as $report): ?>
                                <tr>
                                    <td><?php echo $report['row']; ?></td>
                                    <td><?php echo htmlspecialchars($report['name']); ?></td>
                                    <td>
                                        <?php if ($report['status'] == 'success'): ?>
                                            <span class="badge bg-success">نجح</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">فشل</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($report['message']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
// التحقق من نوع الملف
document.getElementById('import_file').addEventListener('change', function() {
    const file = this.files[0];
    if (file) {
        const ext = file.name.split('.').pop().toLowerCase();
        if (ext !== 'csv') {
            alert('يرجى اختيار ملف CSV فقط');
            this.value = '';
            return;
        }
        
        // التحقق من حجم الملف (5MB)
        if (file.size > 5 * 1024 * 1024) {
            alert('حجم الملف كبير جداً. الحد الأقصى 5MB');
            this.value = '';
            return;
        }
    }
});

// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?php include_once '../includes/footer.php'; ?>
