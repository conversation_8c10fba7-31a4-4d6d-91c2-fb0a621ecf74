<?php
/**
 * English Language File
 * ملف الترجمة الإنجليزية
 */

// if (!defined('SYSTEM_INIT')) {
//     die('Direct access not allowed');
// }

// English texts
$lang = [
    // Basic titles
    'system_name' => 'School Management System',
    'welcome' => 'Welcome',
    'dashboard' => 'Dashboard',
    'home' => 'Home',
    'profile' => 'Profile',
    'settings' => 'Settings',
    'logout' => 'Logout',
    'login' => 'Login',
    'register' => 'Register',
    
    // Main menus
    'students' => 'Students',
    'teachers' => 'Teachers',
    'classes' => 'Classes',
    'subjects' => 'Subjects',
    'exams' => 'Exams',
    'grades' => 'Grades',
    'attendance' => 'Attendance',
    'reports' => 'Reports',
    'users' => 'Users',
    'staff' => 'Staff',
    
    // Actions
    'add' => 'Add',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'view' => 'View',
    'save' => 'Save',
    'cancel' => 'Cancel',
    'submit' => 'Submit',
    'search' => 'Search',
    'filter' => 'Filter',
    'export' => 'Export',
    'import' => 'Import',
    'print' => 'Print',
    'download' => 'Download',
    'upload' => 'Upload',
    'back' => 'Back',
    'next' => 'Next',
    'previous' => 'Previous',
    'continue' => 'Continue',
    'finish' => 'Finish',
    'close' => 'Close',
    'refresh' => 'Refresh',
    'reset' => 'Reset',
    'clear' => 'Clear',
    'select' => 'Select',
    'choose' => 'Choose',
    'browse' => 'Browse',
    'confirm' => 'Confirm',
    
    // Status
    'active' => 'Active',
    'inactive' => 'Inactive',
    'enabled' => 'Enabled',
    'disabled' => 'Disabled',
    'published' => 'Published',
    'draft' => 'Draft',
    'pending' => 'Pending',
    'approved' => 'Approved',
    'rejected' => 'Rejected',
    'completed' => 'Completed',
    'in_progress' => 'In Progress',
    'cancelled' => 'Cancelled',
    'suspended' => 'Suspended',
    
    // Roles and permissions
    'admin' => 'Administrator',
    'teacher' => 'Teacher',
    'student' => 'Student',
    'staff' => 'Staff',
    'parent' => 'Parent',
    'guest' => 'Guest',
    
    // User information
    'username' => 'Username',
    'password' => 'Password',
    'email' => 'Email',
    'full_name' => 'Full Name',
    'first_name' => 'First Name',
    'last_name' => 'Last Name',
    'phone' => 'Phone',
    'mobile' => 'Mobile',
    'address' => 'Address',
    'city' => 'City',
    'country' => 'Country',
    'nationality' => 'Nationality',
    'national_id' => 'National ID',
    'birth_date' => 'Birth Date',
    'age' => 'Age',
    'gender' => 'Gender',
    'male' => 'Male',
    'female' => 'Female',
    'photo' => 'Photo',
    'profile_picture' => 'Profile Picture',
    
    // Student information
    'student_number' => 'Student Number',
    'class' => 'Class',
    'grade_level' => 'Grade Level',
    'section' => 'Section',
    'enrollment_date' => 'Enrollment Date',
    'graduation_date' => 'Graduation Date',
    'parent_name' => 'Parent Name',
    'parent_phone' => 'Parent Phone',
    'parent_email' => 'Parent Email',
    'emergency_contact' => 'Emergency Contact',
    'medical_notes' => 'Medical Notes',
    
    // Teacher information
    'employee_number' => 'Employee Number',
    'specialization' => 'Specialization',
    'qualification' => 'Qualification',
    'hire_date' => 'Hire Date',
    'salary' => 'Salary',
    'department' => 'Department',
    'office_location' => 'Office Location',
    'experience_years' => 'Years of Experience',
    
    // Class information
    'class_name' => 'Class Name',
    'class_code' => 'Class Code',
    'room_number' => 'Room Number',
    'max_students' => 'Maximum Students',
    'current_students' => 'Current Students',
    'supervisor' => 'Supervisor',
    'academic_year' => 'Academic Year',
    'semester' => 'Semester',
    'first_semester' => 'First Semester',
    'second_semester' => 'Second Semester',
    
    // Subject information
    'subject_name' => 'Subject Name',
    'subject_code' => 'Subject Code',
    'description' => 'Description',
    'credit_hours' => 'Credit Hours',
    'is_required' => 'Required Subject',
    'textbook' => 'Textbook',
    
    // Exam information
    'exam_title' => 'Exam Title',
    'exam_description' => 'Exam Description',
    'exam_type' => 'Exam Type',
    'quiz' => 'Quiz',
    'midterm' => 'Midterm',
    'final' => 'Final Exam',
    'assignment' => 'Assignment',
    'total_marks' => 'Total Marks',
    'duration' => 'Duration',
    'start_time' => 'Start Time',
    'end_time' => 'End Time',
    'instructions' => 'Instructions',
    'questions' => 'Questions',
    'question' => 'Question',
    'answer' => 'Answer',
    'correct_answer' => 'Correct Answer',
    'marks' => 'Marks',
    'score' => 'Score',
    'percentage' => 'Percentage',
    'grade_letter' => 'Grade Letter',
    'pass' => 'Pass',
    'fail' => 'Fail',
    
    // Question types
    'multiple_choice' => 'Multiple Choice',
    'true_false' => 'True/False',
    'short_answer' => 'Short Answer',
    'essay' => 'Essay',
    'fill_blank' => 'Fill in the Blank',
    
    // Attendance
    'attendance_date' => 'Attendance Date',
    'attendance_status' => 'Attendance Status',
    'present' => 'Present',
    'absent' => 'Absent',
    'late' => 'Late',
    'excused' => 'Excused',
    'attendance_rate' => 'Attendance Rate',
    'smart_attendance' => 'Smart Attendance',
    'smart_attendance_description' => 'Smart attendance system - Staff on leave are automatically excluded and their attendance is not recorded',
    'smart_student_attendance_description' => 'Smart student attendance system - Quick and efficient attendance recording for all students',
    'smart_teacher_attendance_description' => 'Smart teacher attendance system - Teachers on leave are automatically excluded and their attendance is not recorded',
    'smart_admin_attendance_description' => 'Smart admin attendance system - Admins on leave are automatically excluded and their attendance is not recorded',
    'smart_staff_attendance' => 'Smart Staff Attendance',
    'smart_student_attendance' => 'Smart Student Attendance',
    'smart_teacher_attendance' => 'Smart Teacher Attendance',
    'smart_admin_attendance' => 'Smart Admin Attendance',
    'student_name' => 'Student Name',
    'back_to_list' => 'Back to List',
    'no_record' => 'No Record',
    'available' => 'Available',
    'search_by_name' => 'Search by Name',
    'type_to_search' => 'Type to search...',
    'clear_filter' => 'Clear Filter',
    'search_teacher' => 'Search for teacher...',
    'search_admin' => 'Search for admin...',
    'all_statuses' => 'All Statuses',
    'excluded_from_attendance' => 'Excluded from attendance',
    'students_attendance_info' => 'All students can be marked quickly and efficiently',
    'teachers_on_leave_excluded' => 'Teachers on leave are automatically excluded and their attendance is not recorded',
    'admins_on_leave_excluded' => 'Admins on leave are automatically excluded and their attendance is not recorded',
    'assessment' => 'Assessment',
    
    // Grades
    'excellent' => 'Excellent',
    'very_good' => 'Very Good',
    'good' => 'Good',
    'acceptable' => 'Acceptable',
    'weak' => 'Weak',
    'failed' => 'Failed',
    
    // Messages
    'success' => 'Success',
    'error' => 'Error',
    'warning' => 'Warning',
    'info' => 'Information',
    'loading' => 'Loading...',
    'no_data' => 'No data available',
    'no_results' => 'No results found',
    'please_wait' => 'Please wait...',
    'processing' => 'Processing...',
    
    // Confirmation messages
    'confirm_delete' => 'Are you sure you want to delete?',
    'confirm_action' => 'Are you sure about this action?',
    'cannot_undo' => 'This action cannot be undone',
    'are_you_sure' => 'Are you sure?',
    
    // Success messages
    'added_successfully' => 'Added successfully',
    'updated_successfully' => 'Updated successfully',
    'deleted_successfully' => 'Deleted successfully',
    'saved_successfully' => 'Saved successfully',
    'uploaded_successfully' => 'Uploaded successfully',
    'sent_successfully' => 'Sent successfully',
    
    // Error messages
    'invalid_data' => 'Invalid data',
    'required_field' => 'This field is required',
    'invalid_email' => 'Invalid email address',
    'invalid_phone' => 'Invalid phone number',
    'password_too_short' => 'Password is too short',
    'passwords_not_match' => 'Passwords do not match',
    'username_exists' => 'Username already exists',
    'email_exists' => 'Email already exists',
    'file_too_large' => 'File is too large',
    'invalid_file_type' => 'Invalid file type',
    'upload_failed' => 'Upload failed',
    'access_denied' => 'Access denied',
    'session_expired' => 'Session expired',
    'login_required' => 'Login required',
    'invalid_credentials' => 'Invalid credentials',
    'account_disabled' => 'Account disabled',
    'too_many_attempts' => 'Too many attempts, please try again later',
    
    // Dates and times
    'today' => 'Today',
    'yesterday' => 'Yesterday',
    'tomorrow' => 'Tomorrow',
    'this_week' => 'This Week',
    'this_month' => 'This Month',
    'this_year' => 'This Year',
    'date' => 'Date',
    'time' => 'Time',
    'datetime' => 'Date & Time',
    'created_at' => 'Created At',
    'updated_at' => 'Updated At',
    'last_login' => 'Last Login',
    
    // Days of the week
    'sunday' => 'Sunday',
    'monday' => 'Monday',
    'tuesday' => 'Tuesday',
    'wednesday' => 'Wednesday',
    'thursday' => 'Thursday',
    'friday' => 'Friday',
    'saturday' => 'Saturday',
    
    // Months
    'january' => 'January',
    'february' => 'February',
    'march' => 'March',
    'april' => 'April',
    'may' => 'May',
    'june' => 'June',
    'july' => 'July',
    'august' => 'August',
    'september' => 'September',
    'october' => 'October',
    'november' => 'November',
    'december' => 'December',
    
    // Statistics
    'statistics' => 'Statistics',
    'total' => 'Total',
    'count' => 'Count',
    'average' => 'Average',
    'minimum' => 'Minimum',
    'maximum' => 'Maximum',
    'percentage' => 'Percentage',
    'ratio' => 'Ratio',
    
    // Settings
    'general_settings' => 'General Settings',
    'system_settings' => 'System Settings',
    'user_settings' => 'User Settings',
    'privacy_settings' => 'Privacy Settings',
    'notification_settings' => 'Notification Settings',

    // Leave Management
    'sick_leave' => 'Sick Leave',
    'regular_leave' => 'Regular Leave',
    'emergency_leave' => 'Emergency Leave',
    'absence_with_deduction_leave' => 'Absence with Deduction',
    'absence_with_deduction' => 'Absence with Deduction',
    'record_absence_with_deduction' => 'Record Absence with Deduction',
    'manage_absences_with_deduction' => 'Manage Absences with Deduction',
    'deduction_amount' => 'Deduction Amount',
    'deduction_type' => 'Deduction Type',
    'daily_wage' => 'Daily Wage',
    'hourly_wage' => 'Hourly Wage',
    'fixed_amount' => 'Fixed Amount',
    'register_sick_leave' => 'Register Sick Leave',
    'register_regular_leave' => 'Register Regular Leave',
    'register_emergency_leave' => 'Register Emergency Leave',
    'register_absence_with_deduction_leave' => 'Register Absence with Deduction',
    'leave_status' => 'Leave Status',
    'available' => 'Available',
    'language' => 'Language',
    'theme' => 'Theme',
    'timezone' => 'Timezone',
    'currency' => 'Currency',
    'date_format' => 'Date Format',
    'time_format' => 'Time Format',
    
    // Themes
    'light_theme' => 'Light Theme',
    'dark_theme' => 'Dark Theme',
    'auto_theme' => 'Auto',
    
    // Notifications
    'notifications' => 'Notifications',
    'notification' => 'Notification',
    'unread_notifications' => 'Unread Notifications',
    'mark_as_read' => 'Mark as Read',
    'mark_all_read' => 'Mark All as Read',
    'no_notifications' => 'No notifications',
    
    // Reports
    'report' => 'Report',
    'generate_report' => 'Generate Report',
    'student_report' => 'Student Report',
    'class_report' => 'Class Report',
    'grade_report' => 'Grade Report',
    'attendance_report' => 'Attendance Report',
    'exam_report' => 'Exam Report',
    'monthly_report' => 'Monthly Report',
    'annual_report' => 'Annual Report',
    
    // Others
    'notes' => 'Notes',
    'comments' => 'Comments',
    'attachments' => 'Attachments',
    'files' => 'Files',
    'documents' => 'Documents',
    'images' => 'Images',
    'videos' => 'Videos',
    'audio' => 'Audio',
    'links' => 'Links',
    'tags' => 'Tags',
    'categories' => 'Categories',
    'priority' => 'Priority',
    'status' => 'Status',
    'type' => 'Type',
    'level' => 'Level',
    'rank' => 'Rank',
    'position' => 'Position',
    'title' => 'Title',
    'content' => 'Content',
    'summary' => 'Summary',
    'details' => 'Details',
    'more' => 'More',
    'less' => 'Less',
    'show_more' => 'Show More',
    'show_less' => 'Show Less',
    'expand' => 'Expand',
    'collapse' => 'Collapse',
    'full_screen' => 'Full Screen',
    'exit_full_screen' => 'Exit Full Screen',

    // Educational Stages
    'educational_stages' => 'Educational Stages',
    'stage' => 'Stage',
    'stages' => 'Stages',
    'add_stage' => 'Add Stage',
    'edit_stage' => 'Edit Stage',
    'view_stage' => 'View Stage',
    'delete_stage' => 'Delete Stage',
    'stage_name' => 'Stage Name',
    'stage_name_en' => 'Stage Name (English)',
    'stage_code' => 'Stage Code',
    'stage_description' => 'Stage Description',
    'sort_order' => 'Sort Order',
    'min_age' => 'Minimum Age',
    'max_age' => 'Maximum Age',
    'age_range' => 'Age Range',
    'duration_years' => 'Duration (Years)',
    'duration' => 'Duration',
    'years' => 'Years',
    'manage_educational_stages' => 'Manage Educational Stages',
    'add_new_educational_stage' => 'Add New Educational Stage',
    'back_to_stages' => 'Back to Stages',
    'stage_information' => 'Stage Information',
    'editing_stage' => 'Editing Stage',
    'total_stages' => 'Total Stages',
    'active_stages' => 'Active Stages',
    'inactive_stages' => 'Inactive Stages',
    'stages_list' => 'Stages List',
    'no_stages_found' => 'No Stages Found',
    'no_stages_message' => 'No educational stages have been added yet',
    'add_first_stage' => 'Add First Stage',
    'search_stages' => 'Search Stages',
    'all_statuses' => 'All Statuses',
    'clear_filters' => 'Clear Filters',
    'stage_statistics' => 'Stage Statistics',
    'stage_details' => 'Stage Details',
    'stage_dependencies_info' => 'This stage is linked to classes or subjects',
    'stage_has_dependencies_warning' => 'Warning: This stage is linked to other data',

    // Validation and Error Messages for Stages
    'stage_name_required' => 'Stage name is required',
    'stage_name_too_long' => 'Stage name is too long (maximum 100 characters)',
    'stage_name_en_too_long' => 'English stage name is too long (maximum 100 characters)',
    'stage_code_required' => 'Stage code is required',
    'stage_code_too_long' => 'Stage code is too long (maximum 20 characters)',
    'stage_code_invalid_format' => 'Stage code must contain only uppercase letters and numbers',
    'stage_code_exists' => 'Stage code already exists',
    'sort_order_required' => 'Sort order is required',
    'sort_order_exists' => 'Sort order already exists',
    'duration_years_required' => 'Duration years is required',
    'min_age_invalid' => 'Minimum age is invalid (0-25)',
    'max_age_invalid' => 'Maximum age is invalid (0-25)',
    'age_range_invalid' => 'Minimum age must be less than maximum age',
    'max_age_must_be_greater' => 'Maximum age must be greater than minimum age',
    'stage_added_successfully' => 'Stage added successfully',
    'stage_updated_successfully' => 'Stage updated successfully',
    'stage_deleted_successfully' => 'Stage deleted successfully',
    'delete_stage_confirmation' => 'Are you sure you want to delete this stage?',
    'delete_stage_warning' => 'Warning: All data related to this stage will be deleted',
    'deactivate_stage_confirmation' => 'Are you sure you want to deactivate this stage? This may affect related classes and subjects',
    'cannot_delete_stage_has_classes' => 'Cannot delete stage because it contains classes',
    'cannot_delete_stage_has_subjects' => 'Cannot delete stage because it contains subjects',
    'cannot_delete_stage_has_students' => 'Cannot delete stage because it contains students',

    // Help and Tips
    'enter_stage_name' => 'Enter stage name',
    'enter_stage_name_en' => 'Enter stage name in English',
    'enter_stage_code' => 'Enter stage code',
    'enter_stage_description' => 'Enter stage description',
    'optional' => 'Optional',
    'stage_code_help' => 'Use uppercase letters and numbers only (e.g., KG, PRI, MID)',
    'sort_order_help' => 'Order of the stage in the system (1 for first, 2 for second, etc.)',
    'duration_years_help' => 'Number of years this stage takes',
    'min_age_help' => 'Minimum age of students in this stage',
    'max_age_help' => 'Maximum age of students in this stage',
    'description_help' => 'Brief description of the educational stage and its objectives',
    'help_and_tips' => 'Help and Tips',
    'examples' => 'Examples',
    'stage_name_tip' => 'Choose a clear and understandable name for the educational stage',
    'stage_code_tip' => 'Short and distinctive code to easily identify the stage',
    'sort_order_tip' => 'Determines the order of appearance in lists and reports',
    'age_range_tip' => 'Helps classify students according to their ages',
    'kindergarten' => 'Kindergarten',
    'primary' => 'Primary',
    'middle' => 'Middle',
    'high' => 'High School',
    'save_stage' => 'Save Stage',
    'update_stage' => 'Update Stage',
    'ascending' => 'Ascending',
    'descending' => 'Descending',
    'order' => 'Order',
    'sort_by' => 'Sort By',
    'created_at' => 'Created At',
    'updated_at' => 'Updated At',
    'stage_id' => 'Stage ID',
    'actions' => 'Actions',
    'previous' => 'Previous',
    'next' => 'Next',
    'pagination' => 'Pagination',
    'total_classes' => 'Total Classes',
    'total_students' => 'Total Students',
    'quick_actions' => 'Quick Actions',
    'add_class' => 'Add Class',
    'add_subject' => 'Add Subject',
    'no_classes_found' => 'No Classes Found',
    'no_classes_in_stage' => 'No classes in this stage',
    'add_first_class' => 'Add First Class',
    'class_name' => 'Class Name',
    'grade_level' => 'Grade Level',
    'section' => 'Section',
    'capacity' => 'Capacity',
    'class_teacher' => 'Class Teacher',
    'room' => 'Room',
    'over_capacity' => 'Over Capacity',
    'not_assigned' => 'Not Assigned',
    'no_subjects_found' => 'No Subjects Found',
    'no_subjects_in_stage' => 'No subjects in this stage',
    'add_first_subject' => 'Add First Subject',
    'subject_name' => 'Subject Name',
    'subject_code' => 'Subject Code',
    'credit_hours' => 'Credit Hours',
    'department' => 'Department',
    'teachers' => 'Teachers',
    'stage_has_dependencies' => 'Stage has dependencies',
    'delete_confirmation' => 'Delete Confirmation',
    'cannot_delete_stage_has_dependencies' => 'Cannot delete stage because it has dependencies',
    'resolve_dependencies_message' => 'You must delete or move the related data first before deleting the stage',
    'view_dependencies' => 'View Dependencies',
    'suggestion' => 'Suggestion',
    'delete_dependencies_first' => 'Delete or move the related data first, then delete the stage',
    'educational_stage' => 'Educational Stage',
    'select_stage' => 'Select Stage',
    'room_number' => 'Room Number',
    'class_information' => 'Class Information',
    'add_new_class_info' => 'Add new class information',
    'back_to_list' => 'Back to List',
    'class_added_successfully' => 'Class added successfully',
    'error_occurred' => 'An error occurred',
    'add_new_subject' => 'Add new subject',
    'back_to_subjects' => 'Back to Subjects',
    'subject_information' => 'Subject Information',
    'subject_name_en' => 'Subject Name (English)',
    'enter_subject_name' => 'Enter subject name',
    'enter_subject_name_en' => 'Enter subject name in English',
    'enter_subject_code' => 'Enter subject code',
    'enter_subject_description' => 'Enter subject description',
    'enter_department' => 'Enter department name',
    'subject_code_help' => 'Use uppercase letters and numbers only (e.g., MATH101, ENG201)',
    'subject_code_invalid_format' => 'Subject code must contain only uppercase letters and numbers',
    'subject_code_exists' => 'Subject code already exists',
    'subject_added_successfully' => 'Subject added successfully',
    'please_fix_errors' => 'Please fix the errors first',
    'save_subject' => 'Save Subject',
    'subject_code_tip' => 'Short and distinctive code to easily identify the subject',
    'stage_subject_tip' => 'Choose the educational stage this subject belongs to',
    'credit_hours_tip' => 'Number of credit hours for this subject per week',
    'all_stages' => 'All Stages',
    'students_report' => 'Students Report',
    'search_student' => 'Search Student',
    'filter' => 'Filter',

    // School Grades
    'school_grades' => 'School Grades',
    'grade' => 'Grade',
    'grades' => 'Grades',
    'add_grade' => 'Add Grade',
    'edit_grade' => 'Edit Grade',
    'view_grade' => 'View Grade',
    'delete_grade' => 'Delete Grade',
    'grade_name' => 'Grade Name',
    'grade_name_en' => 'Grade Name (English)',
    'grade_code' => 'Grade Code',
    'grade_description' => 'Grade Description',
    'manage_school_grades' => 'Manage School Grades',
    'add_new_grade' => 'Add New Grade',
    'back_to_grades' => 'Back to Grades',
    'grade_information' => 'Grade Information',
    'editing_grade' => 'Editing Grade',
    'total_grades' => 'Total Grades',
    'active_grades' => 'Active Grades',
    'inactive_grades' => 'Inactive Grades',
    'grades_list' => 'Grades List',
    'no_grades_found' => 'No Grades Found',
    'no_grades_message' => 'No school grades have been added yet',
    'add_first_grade' => 'Add First Grade',
    'search_grades' => 'Search Grades',
    'stage_and_grade_order' => 'Stage and Grade Order',
    'grade_statistics' => 'Grade Statistics',
    'grade_details' => 'Grade Details',
    'grade_dependencies_info' => 'This grade is linked to classes',
    'grade_has_dependencies_warning' => 'Warning: This grade is linked to other data',

    // Validation and Error Messages for Grades
    'grade_name_required' => 'Grade name is required',
    'grade_name_too_long' => 'Grade name is too long (maximum 100 characters)',
    'grade_name_en_too_long' => 'English grade name is too long (maximum 100 characters)',
    'grade_code_required' => 'Grade code is required',
    'grade_code_too_long' => 'Grade code is too long (maximum 20 characters)',
    'grade_code_invalid_format' => 'Grade code must contain only uppercase letters and numbers',
    'grade_code_exists' => 'Grade code already exists',
    'grade_sort_order_required' => 'Grade sort order is required',
    'grade_sort_order_exists' => 'Sort order already exists in the same stage',
    'grade_added_successfully' => 'Grade added successfully',
    'grade_updated_successfully' => 'Grade updated successfully',
    'grade_deleted_successfully' => 'Grade deleted successfully',
    'delete_grade_confirmation' => 'Are you sure you want to delete this grade?',
    'delete_grade_warning' => 'Warning: All classes related to this grade will be deleted',
    'cannot_delete_grade_has_classes' => 'Cannot delete grade because it contains classes',

    // Help and Tips for Grades
    'enter_grade_name' => 'Enter grade name',
    'enter_grade_name_en' => 'Enter grade name in English',
    'enter_grade_code' => 'Enter grade code',
    'enter_grade_description' => 'Enter grade description',
    'grade_code_help' => 'Use uppercase letters and numbers only (e.g., G1, G2, PRE1)',
    'grade_sort_order_help' => 'Order of the grade within the educational stage (1 for first, 2 for second, etc.)',
    'grade_age_range_help' => 'Appropriate age range for this grade',
    'grade_stage_help' => 'Choose the educational stage this grade belongs to',
    'grade_name_tip' => 'Choose a clear and understandable name for the grade',
    'grade_code_tip' => 'Short and distinctive code to easily identify the grade',
    'grade_sort_order_tip' => 'Determines the order of appearance in lists and reports',
    'save_grade' => 'Save Grade',
    'update_grade' => 'Update Grade',
    'deactivate_grade_confirmation' => 'Are you sure you want to deactivate this grade? This may affect related classes',
    'grade_id' => 'Grade ID',
    'invalid_grade_id' => 'Invalid grade ID',
    'grade_not_found' => 'Grade not found',
    'confirm_delete_grade' => 'Confirm Grade Deletion',
    'back_to_grade' => 'Back to Grade',
    'grade_to_delete' => 'Grade to Delete',
    'delete_consequences' => 'Deletion Consequences',
    'grade_will_be_permanently_deleted' => 'The grade will be permanently deleted from the system',
    'action_cannot_be_undone' => 'This action cannot be undone',
    'grade_references_will_be_removed' => 'All references to this grade will be removed',
    'confirm_delete_understanding' => 'I understand the consequences of deleting this grade and want to proceed',
    'final_delete_confirmation' => 'Are you absolutely sure you want to delete this grade?',
    'delete_grade_error' => 'Error deleting grade',
    'no_classes_in_grade' => 'No classes in this grade yet',
    'add_first_class' => 'Add First Class',
    'view_all_classes' => 'View All Classes',
    'quick_actions' => 'Quick Actions',
    'additional_info' => 'Additional Information',
    'stage_hierarchy' => 'Stage Hierarchy',
    'average_students_per_class' => 'Average Students per Class',
    'from' => 'From',
    'up_to' => 'Up to',
    'years' => 'Years',
    'deleting' => 'Deleting',
    'please_wait' => 'Please wait',
    'cannot_delete' => 'Cannot Delete',
    'ok' => 'OK',
    'delete_not_confirmed' => 'Delete not confirmed',
    'cannot_delete_stage_has_dependencies' => 'Cannot delete this stage because it contains related classes or subjects',
    'invalid_stage_id' => 'Invalid stage ID',
    'stage_not_found' => 'Stage not found',
    'are_you_sure_delete_class' => 'Are you sure you want to delete this class?',
    'cannot_delete_class_with_students' => 'Cannot delete class because it contains students',
    'class_has_students_warning' => 'This class contains students',
    'students_count' => 'Students Count',
    'school_grade' => 'School Grade',
    'select_grade' => 'Select Grade',
    'select_grade_first' => 'Select School Grade',
    'select_stage_first' => 'Select Educational Stage First',
    'stages' => 'Stages',
    'grades' => 'Grades',
    'no_grades_found' => 'No grades found',
    'add_first_grade' => 'Add first grade',
    'no_subjects_found' => 'No subjects found',
    'add_first_subject' => 'Add first subject',
    'grade_statistics' => 'Grade Statistics',
    'total_grades' => 'Total Grades',
    'search_and_filter' => 'Search and Filter',
    'all_stages' => 'All Stages',
    'select_stage' => 'Select Stage',
    'teacher_name' => 'Teacher Name',
    'warning' => 'Warning',
    'delete_teacher_warning' => 'All data related to this teacher will be permanently deleted',
    'deleting' => 'Deleting',
    'please_wait' => 'Please wait',
    'success' => 'Success',
    'error' => 'Error',
    'ok' => 'OK',
    'phone_placeholder' => 'Example: 0501234567',
    'phone_format_help' => 'Enter a valid phone number',
    'phone_format_example' => 'Example: 0501234567 or +966501234567',
    'notes_placeholder' => 'Add any additional notes about the teacher...',
    'notes_help_text' => 'You can add notes about teacher performance or other useful information',

    // Student deletion translations
    'student_name' => 'Student Name',
    'are_you_sure_delete_student' => 'Are you sure you want to delete this student?',
    'delete_student_warning' => 'All data related to this student will be permanently deleted',
    'this_action_cannot_be_undone' => 'This action cannot be undone',

    // Additional required translations
    'print' => 'Print',
    'add_student' => 'Add Student',
    'import_students' => 'Import Students',
    'student_info' => 'Student Information',
    'contact_info' => 'Contact Information',
    'parent_info' => 'Parent Information',
    'photo' => 'Photo',
    'class' => 'Class',
    'last_login' => 'Last Login',
    'never_logged_in' => 'Never logged in',
    'actions' => 'Actions',
    'more_actions' => 'More Actions',
    'search_by_name_number' => 'Search by name or number',
    'all_classes' => 'All Classes',
    'active_students' => 'Active Students',
    'male_students' => 'Male Students',
    'female_students' => 'Female Students',
    'no_students_found' => 'No students found',
    'try_different_search' => 'Try a different search',
    'add_first_student' => 'Add first student',
    'manage_students_info' => 'Manage student information',
    'students_list' => 'Students List',

    // Teacher-specific translations
    'add_teacher' => 'Add Teacher',
    'import_teachers' => 'Import Teachers',
    'teacher_info' => 'Teacher Information',
    'manage_teachers_info' => 'Manage teacher information',
    'teachers_list' => 'Teachers List',
    'add_first_teacher' => 'Add first teacher',
    'no_teachers_found' => 'No teachers found',
    'try_different_search_teachers' => 'Try a different search for teachers',

    // Additional teacher translations
    'total_teachers' => 'Total Teachers',
    'active_teachers' => 'Active Teachers',
    'departments' => 'Departments',
    'avg_experience_years' => 'Average Experience Years',
    'all_departments' => 'All Departments',
    'employee_id' => 'Employee ID',
    'not_specified' => 'Not specified',
    'assignments' => 'Assignments',
    'are_you_sure_delete_teacher' => 'Are you sure you want to delete this teacher?',
    'specialization' => 'Specialization',
    'hire_date' => 'Hire Date',
    'years' => 'years',

    // Finance section
    'finance' => 'Finance',
    'fees_management' => 'Fees Management',
    'installments' => 'Installments',
    'payments' => 'Payments',
    'pay_installment' => 'Pay Installment',
    'installments_management' => 'Installments Management',
    'manage_student_installments' => 'Manage Student Installments',
    'total_installments' => 'Total Installments',
    'paid_installments' => 'Paid Installments',
    'pending_installments' => 'Pending Installments',
    'overdue_installments' => 'Overdue Installments',
    'collection_rate' => 'Collection Rate',
    'installments_list' => 'Installments List',
    'student' => 'Student',
    'choose_student' => 'Choose Student',
    'class' => 'Class',
    'installment' => 'Installment',
    'choose_installment' => 'Choose Installment',
    'total_amount' => 'Total Amount',
    'paid_amount' => 'Paid Amount',
    'remaining_amount' => 'Remaining Amount',
    'pay_amount' => 'Pay Amount',
    'payment_method' => 'Payment Method',
    'payment_date' => 'Payment Date',
    'notes' => 'Notes',
    'back' => 'Back',
    'pay' => 'Pay',
    'cash' => 'Cash',
    'bank_transfer' => 'Bank Transfer',
    'check' => 'Check',
    'card' => 'Card',
    'online' => 'Online',
    'installment_details' => 'Installment Details',
    'installment_number' => 'Installment Number',
    'due_date' => 'Due Date',
    'status' => 'Status',
    'payment_details' => 'Payment Details',
    'max_amount' => 'Maximum Amount',
    'payment_reference' => 'Payment Reference',
    'optional' => 'Optional',
    'payment_reference_help' => 'Check number or bank transfer reference',
    'payment_notes_placeholder' => 'Additional notes about the payment...',
    'financial_summary' => 'Financial Summary',
    'student_number' => 'Student Number',
    'total_paid' => 'Total Paid',
    'total_remaining' => 'Total Remaining',
    'payment_progress' => 'Payment Progress',
    'contact_info' => 'Contact Information',
    'parent_name' => 'Parent Name',
    'parent_phone' => 'Parent Phone',
    'amount_exceeds_remaining' => 'Amount exceeds remaining balance',
    'amount_must_be_positive' => 'Amount must be greater than zero',
    'pay_full_amount' => 'Pay Full Amount',
    'check_number' => 'Check Number',
    'enter_check_number' => 'Enter check number',
    'transfer_reference' => 'Transfer Reference',
    'enter_transfer_reference' => 'Enter transfer reference',
    'transaction_id' => 'Transaction ID',
    'enter_transaction_id' => 'Enter transaction ID',
    'payment_id' => 'Payment ID',
    'enter_payment_id' => 'Enter payment ID',
    'confirm_payment' => 'Confirm Payment',
    'amount' => 'Amount',
    'method' => 'Method',
    'continue_payment' => 'Do you want to continue?',
    'overdue' => 'Overdue',
    'no_pending_installments' => 'No Pending Installments',
    'no_pending_installments_desc' => 'There are no pending or unpaid installments for this student currently',
    'add_installment' => 'Add New Installment',
    'fee_type' => 'Fee Type',
    'tuition' => 'Tuition',
    'books' => 'Books',
    'transport' => 'Transport',
    'activities' => 'Activities',
    'other' => 'Other',
    'cancel' => 'Cancel',
    'invalid_student' => 'Invalid student',
    'invalid_installment_number' => 'Invalid installment number',
    'invalid_amount' => 'Invalid amount',
    'invalid_due_date' => 'Invalid due date',
    'installment_added_successfully' => 'Installment added successfully',
    'database_error' => 'Database error',
    'installment_number_exists' => 'Installment number already exists for this student',
    'installment_number_help' => 'Unique sequential number for the installment',
    'description_placeholder' => 'Detailed description of the installment (optional)',
    'paid_amount_help' => 'Amount already paid (optional)',
    'paid_amount_exceeds_total' => 'Paid amount cannot exceed total amount',
    'quick_payment' => 'Quick Payment',
    'pay_half_amount' => 'Pay Half Amount',
    'remaining_after_payment' => 'Remaining After Payment',
    'process_payment' => 'Process Payment',
    'invalid_installment' => 'Invalid installment',
    'installment_not_found' => 'Installment not found',
    'payment_processed_successfully' => 'Payment processed successfully',
    'view_receipt' => 'View Receipt',
    'installment_receipt' => 'Installment Receipt',
    'installment_payment_receipt' => 'Installment Payment Receipt',
    'student_information' => 'Student Information',
    'installment_information' => 'Installment Information',
    'receipt_number' => 'Receipt Number',
    'issue_date' => 'Issue Date',
    'payment_history' => 'Payment History',
    'receipt_generated_on' => 'Receipt generated on',
    'generated_by' => 'Generated by',
    'currency' => 'Currency',
    'reference' => 'Reference',
    'delete_confirmation_required' => 'Delete confirmation required',
    'installment_to_delete' => 'Installment to delete',
    'payment_warning' => 'Payment Warning',
    'installment_has_payments' => 'This installment has payments',
    'delete_will_remove_payments' => 'Deletion will remove all related payments',
    'delete_confirmation_label' => 'To confirm deletion, type "DELETE" in the field below',
    'type_delete_to_confirm' => 'Type DELETE to confirm',
    'type_delete_instruction' => 'You must type DELETE in capital letters to confirm',
    'please_type_delete_to_confirm' => 'Please type DELETE to confirm',
    'final_delete_confirmation' => 'Are you sure you want to delete this installment permanently?',
    'confirm_delete_installment' => 'Are you sure you want to delete this installment?',
    'installment_deleted_successfully' => 'Installment deleted successfully',

    // Administrators
    'administrators' => 'Administrators',
    'administrator' => 'Administrator',
    'add_administrator' => 'Add Administrator',
    'edit_administrator' => 'Edit Administrator',
    'delete_administrator' => 'Delete Administrator',
    'view_administrator' => 'View Administrator',
    'administrator_info' => 'Administrator Information',
    'administrator_details' => 'Administrator Details',
    'manage_administrators' => 'Manage Administrators',
    'total_administrators' => 'Total Administrators',
    'active_administrators' => 'Active Administrators',
    'search_administrators' => 'Search Administrators',
    'no_administrators_found' => 'No administrators found',
    'add_first_administrator' => 'Add First Administrator',
    'administrator_added_successfully' => 'Administrator added successfully',
    'administrator_updated_successfully' => 'Administrator updated successfully',
    'administrator_deleted_successfully' => 'Administrator deleted successfully',
    'import_administrators' => 'Import Administrators',
    'import_administrators_from_csv' => 'Import Administrators from CSV',
    'back_to_administrators' => 'Back to Administrators',
    'add_new_administrator_to_system' => 'Add new administrator to system',
    'position' => 'Position',
    'employment_status' => 'Employment Status',
    'on_leave' => 'On Leave',
    'terminated' => 'Terminated',
    'avg_experience_years' => 'Average Experience Years',
    'administrators_report' => 'Administrators Report',
    'search_administrator' => 'Search Administrator'
];

// Function to get translated text with variables
if (!function_exists('__f')) {
    function __f($key, $vars = [], $default = null) {
        global $lang;
        $text = $lang[$key] ?? $default;

        foreach ($vars as $var => $value) {
            $text = str_replace('{' . $var . '}', $value, $text);
        }

        return $text;
    }
}
?>
