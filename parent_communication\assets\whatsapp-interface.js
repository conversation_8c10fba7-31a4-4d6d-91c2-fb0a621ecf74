/**
 * واجهة واتساب - ملف JavaScript للتفاعل
 */

class WhatsAppInterface {
    constructor() {
        this.currentStudentId = null;
        this.searchTimeout = null;
        this.autoRefreshInterval = null;
        this.init();
    }

    init() {
        this.initializeEventListeners();
        this.initializeAutoRefresh();
        this.scrollToBottom();
        this.getCurrentStudentId();
    }

    initializeEventListeners() {
        // البحث في جهات الاتصال
        const searchInput = document.getElementById('searchContacts');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.searchContacts(e.target.value);
                }, 300);
            });
        }

        // إرسال الرسالة بالضغط على Enter
        const messageInputs = document.querySelectorAll('input[name="message"], input[name="subject"]');
        messageInputs.forEach(input => {
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    const form = input.closest('form');
                    if (form && this.validateForm(form)) {
                        form.submit();
                    }
                }
            });
        });

        // تحديد النص عند التركيز على حقل البحث
        if (searchInput) {
            searchInput.addEventListener('focus', () => {
                searchInput.select();
            });
        }

        // إضافة تأثيرات التحويم على جهات الاتصال
        this.initializeContactHoverEffects();

        // إضافة مؤشر الكتابة
        this.initializeTypingIndicator();
    }

    initializeContactHoverEffects() {
        const contacts = document.querySelectorAll('.contact-item');
        contacts.forEach(contact => {
            contact.addEventListener('mouseenter', () => {
                contact.style.transform = 'translateX(3px)';
            });

            contact.addEventListener('mouseleave', () => {
                contact.style.transform = 'translateX(0)';
            });
        });
    }

    initializeTypingIndicator() {
        const messageInput = document.querySelector('input[name="message"]');
        if (messageInput) {
            let typingTimer;
            
            messageInput.addEventListener('input', () => {
                this.showTypingIndicator();
                clearTimeout(typingTimer);
                typingTimer = setTimeout(() => {
                    this.hideTypingIndicator();
                }, 1000);
            });
        }
    }

    showTypingIndicator() {
        // يمكن إضافة مؤشر الكتابة هنا
        console.log('User is typing...');
    }

    hideTypingIndicator() {
        // إخفاء مؤشر الكتابة
        console.log('User stopped typing');
    }

    searchContacts(searchTerm) {
        const contacts = document.querySelectorAll('.contact-item');
        const normalizedSearch = searchTerm.toLowerCase().trim();
        
        let visibleCount = 0;
        
        contacts.forEach(contact => {
            const name = contact.dataset.name || '';
            const phone = contact.dataset.phone || '';
            
            const isVisible = name.includes(normalizedSearch) || 
                            phone.includes(normalizedSearch) ||
                            normalizedSearch === '';
            
            if (isVisible) {
                contact.style.display = 'block';
                contact.style.animation = 'fadeInUp 0.3s ease';
                visibleCount++;
            } else {
                contact.style.display = 'none';
            }
        });

        // إظهار رسالة عدم وجود نتائج
        this.toggleNoResultsMessage(visibleCount === 0 && normalizedSearch !== '');
    }

    toggleNoResultsMessage(show) {
        let noResultsMsg = document.getElementById('noSearchResults');
        
        if (show && !noResultsMsg) {
            noResultsMsg = document.createElement('div');
            noResultsMsg.id = 'noSearchResults';
            noResultsMsg.className = 'text-center py-4 text-muted';
            noResultsMsg.innerHTML = `
                <i class="fas fa-search fa-2x mb-2"></i>
                <p>لا توجد نتائج للبحث</p>
            `;
            
            const contactsList = document.querySelector('.contacts-list');
            if (contactsList) {
                contactsList.appendChild(noResultsMsg);
            }
        } else if (!show && noResultsMsg) {
            noResultsMsg.remove();
        }
    }

    selectContact(studentId) {
        // إضافة تأثير التحميل
        this.showLoadingIndicator();
        
        // تحديث الرابط
        const newUrl = `whatsapp_interface.php?student_id=${studentId}`;
        window.location.href = newUrl;
    }

    showLoadingIndicator() {
        const loadingHtml = `
            <div class="loading-overlay" id="loadingOverlay">
                <div class="loading-spinner">
                    <div class="spinner-border text-success" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحميل المحادثة...</p>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', loadingHtml);
    }

    hideLoadingIndicator() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.remove();
        }
    }

    scrollToBottom() {
        const chatMessages = document.getElementById('chatMessages');
        if (chatMessages) {
            chatMessages.scrollTop = chatMessages.scrollHeight;
            
            // تأثير سلس للتمرير
            chatMessages.style.scrollBehavior = 'smooth';
        }
    }

    getCurrentStudentId() {
        const urlParams = new URLSearchParams(window.location.search);
        this.currentStudentId = urlParams.get('student_id');
    }

    initializeAutoRefresh() {
        // تحديث تلقائي كل 30 ثانية إذا كان هناك محادثة مفتوحة
        if (this.currentStudentId) {
            this.autoRefreshInterval = setInterval(() => {
                this.refreshMessages();
            }, 30000);
        }
    }

    refreshMessages() {
        if (!this.currentStudentId) return;

        // يمكن إضافة AJAX لتحديث الرسائل هنا
        fetch(`get_messages.php?student_id=${this.currentStudentId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.updateMessagesDisplay(data.messages);
                }
            })
            .catch(error => {
                console.error('Error refreshing messages:', error);
            });
    }

    updateMessagesDisplay(messages) {
        const chatMessages = document.getElementById('chatMessages');
        if (!chatMessages) return;

        // تحديث الرسائل (يمكن تحسين هذا لاحقاً)
        // هذا مثال بسيط
        console.log('Messages updated:', messages);
    }

    validateForm(form) {
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                isValid = false;
                field.classList.add('is-invalid');
                
                // إزالة الكلاس بعد التصحيح
                field.addEventListener('input', () => {
                    if (field.value.trim()) {
                        field.classList.remove('is-invalid');
                    }
                }, { once: true });
            }
        });

        return isValid;
    }

    // دالة لإضافة رسالة جديدة بتأثير
    addNewMessage(messageData) {
        const chatMessages = document.getElementById('chatMessages');
        if (!chatMessages) return;

        const messageHtml = this.createMessageHTML(messageData);
        chatMessages.insertAdjacentHTML('beforeend', messageHtml);
        
        // تمرير للأسفل
        this.scrollToBottom();
        
        // تأثير ظهور
        const newMessage = chatMessages.lastElementChild;
        newMessage.style.opacity = '0';
        newMessage.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            newMessage.style.transition = 'all 0.3s ease';
            newMessage.style.opacity = '1';
            newMessage.style.transform = 'translateY(0)';
        }, 100);
    }

    createMessageHTML(messageData) {
        const time = new Date(messageData.created_at).toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });

        return `
            <div class="message sent">
                <div class="message-bubble">
                    <div class="message-content">
                        <strong>${messageData.subject}</strong><br>
                        ${messageData.message.replace(/\n/g, '<br>')}
                    </div>
                    <div class="message-time">
                        ${time}
                        <span class="message-status">
                            <i class="fas fa-clock status-icon"></i>
                        </span>
                    </div>
                </div>
            </div>
        `;
    }

    // تنظيف الموارد عند مغادرة الصفحة
    cleanup() {
        if (this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
        }
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
    }
}

// تهيئة الواجهة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    window.whatsappInterface = new WhatsAppInterface();
});

// تنظيف عند مغادرة الصفحة
window.addEventListener('beforeunload', function() {
    if (window.whatsappInterface) {
        window.whatsappInterface.cleanup();
    }
});

// دوال عامة للاستخدام في HTML
function selectContact(studentId) {
    if (window.whatsappInterface) {
        window.whatsappInterface.selectContact(studentId);
    }
}

// إضافة أنماط CSS للتحميل
const loadingStyles = `
    <style>
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            color: white;
        }
        
        .loading-spinner {
            text-align: center;
        }
        
        .contact-item {
            transition: transform 0.2s ease;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
`;

document.head.insertAdjacentHTML('beforeend', loadingStyles);
