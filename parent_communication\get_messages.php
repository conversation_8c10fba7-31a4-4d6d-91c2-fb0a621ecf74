<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('teacher')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'غير مصرح']);
    exit();
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'طريقة طلب غير مدعومة']);
    exit();
}

global $conn;

$student_id = isset($_GET['student_id']) ? intval($_GET['student_id']) : 0;
$last_message_id = isset($_GET['last_message_id']) ? intval($_GET['last_message_id']) : 0;

if ($student_id <= 0) {
    echo json_encode(['success' => false, 'error' => 'معرف الطالب مطلوب']);
    exit();
}

try {
    // جلب الرسائل الجديدة
    $messages_query = "
        SELECT 
            pc.*,
            sender.full_name as sender_name
        FROM parent_communications pc
        JOIN users sender ON pc.sent_by = sender.id
        WHERE pc.student_id = ?
    ";
    
    $params = [$student_id];
    $types = 'i';
    
    // إذا كان هناك معرف آخر رسالة، جلب الرسائل الأحدث فقط
    if ($last_message_id > 0) {
        $messages_query .= " AND pc.id > ?";
        $params[] = $last_message_id;
        $types .= 'i';
    }
    
    $messages_query .= " ORDER BY pc.created_at ASC";
    
    $stmt = $conn->prepare($messages_query);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $messages = [];
    while ($row = $result->fetch_assoc()) {
        $messages[] = [
            'id' => $row['id'],
            'subject' => $row['subject'],
            'message' => $row['message'],
            'message_type' => $row['message_type'],
            'priority' => $row['priority'],
            'status' => $row['status'],
            'sent_via' => $row['sent_via'],
            'sender_name' => $row['sender_name'],
            'created_at' => $row['created_at'],
            'sent_at' => $row['sent_at'],
            'delivered_at' => $row['delivered_at'],
            'read_at' => $row['read_at']
        ];
    }
    $stmt->close();
    
    // جلب معلومات الطالب
    $student_query = "
        SELECT 
            s.id,
            s.student_id as student_number,
            u.full_name as student_name,
            s.parent_phone,
            c.class_name
        FROM students s
        JOIN users u ON s.user_id = u.id
        LEFT JOIN classes c ON s.class_id = c.id
        WHERE s.id = ?
    ";
    
    $stmt = $conn->prepare($student_query);
    $stmt->bind_param('i', $student_id);
    $stmt->execute();
    $student = $stmt->get_result()->fetch_assoc();
    $stmt->close();
    
    if (!$student) {
        echo json_encode(['success' => false, 'error' => 'الطالب غير موجود']);
        exit();
    }
    
    // إحصائيات سريعة
    $stats_query = "
        SELECT 
            COUNT(*) as total_messages,
            COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent_messages,
            COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered_messages,
            COUNT(CASE WHEN status = 'read' THEN 1 END) as read_messages,
            COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_messages,
            MAX(created_at) as last_message_time
        FROM parent_communications 
        WHERE student_id = ?
    ";
    
    $stmt = $conn->prepare($stats_query);
    $stmt->bind_param('i', $student_id);
    $stmt->execute();
    $stats = $stmt->get_result()->fetch_assoc();
    $stmt->close();
    
    // إرجاع البيانات
    echo json_encode([
        'success' => true,
        'messages' => $messages,
        'student' => $student,
        'stats' => $stats,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    error_log("Error in get_messages.php: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'error' => 'حدث خطأ في جلب الرسائل'
    ]);
}
?>
