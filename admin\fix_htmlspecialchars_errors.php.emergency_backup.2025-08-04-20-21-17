<?php
/**
 * إصلاح أخطاء htmlspecialchars في النظام
 * Fix htmlspecialchars Errors in System
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

$results = [];
$success = true;

// دالة مساعدة آمنة
$safe_html_function = "
// دالة مساعدة لتجنب خطأ htmlspecialchars مع null
function safe_html(\$value, \$default = '') {
    return htmlspecialchars(\$value ?? \$default);
}";

// معالجة الإصلاح
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fix_htmlspecialchars'])) {
    try {
        $results[] = "🚀 بدء إصلاح أخطاء htmlspecialchars في النظام";
        
        // 1. إضافة الدالة المساعدة إلى functions.php
        $functions_file = '../includes/functions.php';
        $functions_content = file_get_contents($functions_file);
        
        // التحقق من وجود الدالة
        if (strpos($functions_content, 'function safe_html') === false) {
            // إضافة الدالة في نهاية الملف
            $functions_content .= "\n\n" . $safe_html_function;
            
            // إنشاء نسخة احتياطية
            $backup_file = $functions_file . '.backup.' . date('Y-m-d-H-i-s');
            copy($functions_file, $backup_file);
            
            // كتابة الملف المحدث
            if (file_put_contents($functions_file, $functions_content)) {
                $results[] = "✅ تم إضافة دالة safe_html إلى functions.php";
            } else {
                throw new Exception("فشل في كتابة ملف functions.php");
            }
        } else {
            $results[] = "ℹ️ دالة safe_html موجودة بالفعل في functions.php";
        }
        
        // 2. قائمة الملفات التي تحتاج إصلاح
        $files_to_fix = [
            'admin/debug_permissions_table.php',
            'admin/quick_fix_permissions.php',
            'admin/fix_permissions_compatibility.php',
            'admin/test_staff_access.php',
            'admin/system_permissions_summary.php',
            'admin/staff_permissions_manager.php'
        ];
        
        $fixed_files = 0;
        $skipped_files = 0;
        
        foreach ($files_to_fix as $file_path) {
            $full_path = "../$file_path";
            
            if (!file_exists($full_path)) {
                $results[] = "⚠️ الملف غير موجود: $file_path";
                $skipped_files++;
                continue;
            }
            
            $content = file_get_contents($full_path);
            if ($content === false) {
                $results[] = "❌ فشل في قراءة الملف: $file_path";
                $skipped_files++;
                continue;
            }
            
            $original_content = $content;
            
            // إضافة الدالة المساعدة إذا لم تكن موجودة
            if (strpos($content, 'function safe_html') === false) {
                // البحث عن مكان مناسب لإضافة الدالة
                if (preg_match('/\$user_email\s*=.*?;/', $content)) {
                    $content = preg_replace(
                        '/(\$user_email\s*=.*?;)/',
                        "$1\n\n" . $safe_html_function,
                        $content,
                        1
                    );
                }
            }
            
            // استبدال htmlspecialchars بـ safe_html في الحالات الشائعة
            $patterns = [
                '/htmlspecialchars\(\$([a-zA-Z_][a-zA-Z0-9_]*)\[\'([^\']+)\'\]\s*\?\?\s*\'([^\']*)\'\)/' => 'safe_html($\1[\'\2\'], \'\3\')',
                '/htmlspecialchars\(\$([a-zA-Z_][a-zA-Z0-9_]*)\[\'([^\']+)\'\]\s*\?\?\s*\'([^\']*)\'\)/' => 'safe_html($\1[\'\2\'], \'\3\')',
                '/htmlspecialchars\(\$([a-zA-Z_][a-zA-Z0-9_]*)\[\'([^\']+)\'\]\)/' => 'safe_html($\1[\'\2\'])',
                '/htmlspecialchars\(\$([a-zA-Z_][a-zA-Z0-9_]*)\)/' => 'safe_html($\1)',
                '/htmlspecialchars\(\$([a-zA-Z_][a-zA-Z0-9_]*)\[\'([^\']+)\'\]\s*\?\?\s*\$([a-zA-Z_][a-zA-Z0-9_]*)\[\'([^\']+)\'\]\s*\?\?\s*\'([^\']*)\'\)/' => 'safe_html($\1[\'\2\'] ?? $\3[\'\4\'], \'\5\')'
            ];
            
            $changes = 0;
            foreach ($patterns as $pattern => $replacement) {
                $new_content = preg_replace($pattern, $replacement, $content);
                if ($new_content !== $content) {
                    $content = $new_content;
                    $changes++;
                }
            }
            
            // حفظ الملف إذا تم تغييره
            if ($content !== $original_content) {
                // إنشاء نسخة احتياطية
                $backup_path = $full_path . '.backup.' . date('Y-m-d-H-i-s');
                copy($full_path, $backup_path);
                
                // كتابة المحتوى الجديد
                if (file_put_contents($full_path, $content)) {
                    $results[] = "✅ تم إصلاح: $file_path ($changes تغيير)";
                    $fixed_files++;
                } else {
                    $results[] = "❌ فشل في كتابة الملف: $file_path";
                    $skipped_files++;
                }
            } else {
                $results[] = "ℹ️ لا يحتاج إصلاح: $file_path";
                $skipped_files++;
            }
        }
        
        $results[] = "\n📊 ملخص العملية:";
        $results[] = "✅ تم إصلاح $fixed_files ملف";
        $results[] = "⚠️ تم تخطي $skipped_files ملف";
        
        if ($fixed_files > 0) {
            $results[] = "\n🎉 تم إصلاح أخطاء htmlspecialchars بنجاح!";
            $results[] = "📋 الآن يجب أن تعمل جميع الصفحات بدون أخطاء";
        } else {
            $results[] = "\nℹ️ لم تحتج أي ملفات للإصلاح";
        }
        
    } catch (Exception $e) {
        $success = false;
        $results[] = "❌ خطأ: " . $e->getMessage();
    }
}

$page_title = 'إصلاح أخطاء htmlspecialchars';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-code me-2"></i><?php echo $page_title; ?></h2>
            <p class="text-muted">إصلاح أخطاء "Passing null to htmlspecialchars" في النظام</p>
        </div>
        <a href="../settings/permissions.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة
        </a>
    </div>

    <!-- شرح المشكلة -->
    <div class="alert alert-warning">
        <h6><i class="fas fa-exclamation-triangle me-2"></i>المشكلة:</h6>
        <p class="mb-2">في PHP 8.1+، تظهر رسالة خطأ عند تمرير قيمة <code>null</code> لدالة <code>htmlspecialchars()</code>.</p>
        <p class="mb-0"><strong>الحل:</strong> استخدام دالة <code>safe_html()</code> التي تتعامل مع القيم الفارغة بأمان.</p>
    </div>

    <!-- نتائج الإصلاح -->
    <?php if (!empty($results)): ?>
        <div class="card mb-4">
            <div class="card-header <?php echo $success ? 'bg-success' : 'bg-danger'; ?> text-white">
                <h5>
                    <i class="fas fa-<?php echo $success ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                    <?php echo $success ? 'تم الإصلاح بنجاح!' : 'فشل في الإصلاح'; ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="bg-dark text-light p-3 rounded" style="font-family: monospace; white-space: pre-line; max-height: 400px; overflow-y: auto;">
<?php echo implode("\n", $results); ?>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- نموذج الإصلاح -->
    <?php if (empty($results) || !$success): ?>
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5><i class="fas fa-tools me-2"></i>إصلاح أخطاء htmlspecialchars</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>ما سيتم عمله:</h6>
                    <ol>
                        <li><strong>إضافة دالة safe_html:</strong> إلى ملف functions.php</li>
                        <li><strong>فحص الملفات:</strong> البحث عن استخدامات htmlspecialchars</li>
                        <li><strong>استبدال الكود:</strong> تحويل htmlspecialchars إلى safe_html</li>
                        <li><strong>إنشاء نسخ احتياطية:</strong> من جميع الملفات المعدلة</li>
                        <li><strong>اختبار النتيجة:</strong> التأكد من عدم وجود أخطاء</li>
                    </ol>
                </div>

                <div class="card mb-3">
                    <div class="card-header">
                        <h6><i class="fas fa-code me-2"></i>الدالة المساعدة الجديدة:</h6>
                    </div>
                    <div class="card-body">
                        <pre class="bg-light p-3 rounded"><code>function safe_html($value, $default = '') {
    return htmlspecialchars($value ?? $default);
}</code></pre>
                        <p class="small text-muted mb-0">هذه الدالة تتعامل مع القيم الفارغة (null) بأمان وتمنع ظهور رسائل الخطأ.</p>
                    </div>
                </div>

                <form method="POST">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="confirmFix" required>
                        <label class="form-check-label" for="confirmFix">
                            <strong>أؤكد أنني أريد إصلاح أخطاء htmlspecialchars في النظام</strong>
                        </label>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="../settings/permissions.php" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" name="fix_htmlspecialchars" class="btn btn-primary" id="fixButton" disabled>
                            <i class="fas fa-code me-2"></i>إصلاح الأخطاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    <?php endif; ?>

    <!-- معلومات إضافية -->
    <div class="card mt-4">
        <div class="card-header bg-info text-white">
            <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-danger">المشكلة:</h6>
                    <ul class="small">
                        <li>PHP 8.1+ يظهر تحذيرات عند تمرير null</li>
                        <li>htmlspecialchars() لا يقبل قيم فارغة</li>
                        <li>يسبب رسائل "Deprecated" في السجلات</li>
                        <li>قد يؤثر على أداء النظام</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="text-success">الحل:</h6>
                    <ul class="small">
                        <li>دالة safe_html() تتعامل مع null بأمان</li>
                        <li>استبدال تلقائي في جميع الملفات</li>
                        <li>نسخ احتياطية من الملفات المعدلة</li>
                        <li>حل متوافق مع جميع إصدارات PHP</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تفعيل زر الإصلاح عند تأكيد الاختيار
document.getElementById('confirmFix')?.addEventListener('change', function() {
    document.getElementById('fixButton').disabled = !this.checked;
});
</script>

<?php include_once '../includes/footer.php'; ?>
