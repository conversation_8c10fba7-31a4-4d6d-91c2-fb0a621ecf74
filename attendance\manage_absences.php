<?php
/**
 * إدارة الغياب بالخصم
 * Manage Absences with Deduction
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';
require_once '../includes/header.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

$user_id = $_SESSION['user_id'];

// التحقق من وجود الجداول المطلوبة
$tables_exist = true;
$missing_tables = [];

$required_tables = ['staff_absences_with_deduction', 'deduction_settings'];
foreach ($required_tables as $table) {
    $check = $conn->query("SHOW TABLES LIKE '$table'");
    if ($check->num_rows === 0) {
        $tables_exist = false;
        $missing_tables[] = $table;
    }
}

// إذا كانت الجداول مفقودة، عرض رسالة مع رابط الإصلاح
if (!$tables_exist) {
    echo '<div class="container-fluid mt-4">';
    echo '<div class="alert alert-danger">';
    echo '<h4><i class="fas fa-exclamation-triangle"></i> جداول مفقودة في قاعدة البيانات</h4>';
    echo '<p>الجداول التالية مفقودة من قاعدة البيانات:</p>';
    echo '<ul>';
    foreach ($missing_tables as $table) {
        echo '<li><code>' . $table . '</code></li>';
    }
    echo '</ul>';
    echo '<p><strong>الحل:</strong></p>';
    echo '<ol>';
    echo '<li>تأكد من استيراد ملف <code>database/school_management.sql</code> بالكامل</li>';
    echo '<li>أو استخدم أداة الإصلاح التلقائي أدناه</li>';
    echo '</ol>';
    echo '<div class="mt-3">';
    echo '<a href="fix_absence_data_display.php" class="btn btn-warning btn-lg me-2">';
    echo '<i class="fas fa-tools"></i> إصلاح تلقائي للجداول';
    echo '</a>';
    echo '<a href="check_absence_data.php" class="btn btn-info me-2">';
    echo '<i class="fas fa-search"></i> فحص قاعدة البيانات';
    echo '</a>';
    echo '<a href="../dashboard/" class="btn btn-secondary">';
    echo '<i class="fas fa-home"></i> العودة للوحة التحكم';
    echo '</a>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    require_once '../includes/footer.php';
    exit();
}

// التحقق من وجود الأعمدة المطلوبة في جدول staff_absences_with_deduction
if ($tables_exist) {
    $required_columns = ['processed_by', 'processed_at', 'recorded_by'];
    foreach ($required_columns as $column) {
        $check_column = $conn->query("SHOW COLUMNS FROM staff_absences_with_deduction LIKE '$column'");
        if ($check_column->num_rows === 0) {
            // إضافة العمود المفقود
            switch ($column) {
                case 'processed_by':
                    $conn->query("ALTER TABLE staff_absences_with_deduction ADD COLUMN processed_by INT(10) UNSIGNED NULL DEFAULT NULL AFTER approved_at");
                    $conn->query("ALTER TABLE staff_absences_with_deduction ADD KEY processed_by (processed_by)");
                    $conn->query("ALTER TABLE staff_absences_with_deduction ADD CONSTRAINT staff_absences_processed_by_foreign FOREIGN KEY (processed_by) REFERENCES users (id) ON DELETE SET NULL");
                    break;
                case 'processed_at':
                    $conn->query("ALTER TABLE staff_absences_with_deduction ADD COLUMN processed_at TIMESTAMP NULL DEFAULT NULL AFTER processed_by");
                    break;
                case 'recorded_by':
                    $conn->query("ALTER TABLE staff_absences_with_deduction ADD COLUMN recorded_by INT(10) UNSIGNED NULL DEFAULT NULL AFTER processed_at");
                    $conn->query("ALTER TABLE staff_absences_with_deduction ADD KEY recorded_by (recorded_by)");
                    $conn->query("ALTER TABLE staff_absences_with_deduction ADD CONSTRAINT staff_absences_recorded_by_foreign FOREIGN KEY (recorded_by) REFERENCES users (id) ON DELETE SET NULL");
                    break;
            }
        }
    }
}

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'طلب غير صالح';
    } else {
        $action = $_POST['action'] ?? '';
        $absence_id = intval($_POST['absence_id'] ?? 0);
        
        if ($action === 'process' || $action === 'cancel') {
            $status = $action === 'process' ? 'processed' : 'cancelled';
            $stmt = $conn->prepare("
                UPDATE staff_absences_with_deduction 
                SET status = ?, processed_by = ?, processed_at = NOW() 
                WHERE id = ?
            ");
            $stmt->bind_param("sii", $status, $user_id, $absence_id);
            
            if ($stmt->execute()) {
                $success_message = $action === 'process' ? 'تم معالجة الغياب بالخصم' : 'تم إلغاء الغياب بالخصم';
                
                // تسجيل النشاط
                log_activity($user_id, $action . '_absence_deduction', 'staff_absences_with_deduction', $absence_id);
            } else {
                $error_message = 'حدث خطأ أثناء تحديث الغياب';
            }
        }
    }
}

// معالجة التبويب
$tab = $_GET['tab'] ?? 'all';
$page_title = 'إدارة الغياب بالخصم';
$back_link = 'smart_attendance.php';

// تحديد الفئة بناءً على التبويب
switch ($tab) {
    case 'teachers':
        $role_filter = 'teacher';
        $page_title = 'إدارة غياب المعلمين بالخصم';
        $back_link = 'smart_attendance.php?tab=teachers&date=' . date('Y-m-d');
        break;
    case 'admins':
        $role_filter = 'staff';
        $page_title = 'إدارة غياب الإداريين بالخصم';
        $back_link = 'smart_attendance.php?tab=admins&date=' . date('Y-m-d');
        break;
    default:
        $role_filter = $_GET['role'] ?? 'all';
        $back_link = 'smart_attendance.php?date=' . date('Y-m-d');
        break;
}

// فلاتر البحث
$status_filter = $_GET['status'] ?? 'all';
$from_date = $_GET['from_date'] ?? date('Y-m-01'); // أول يوم في الشهر الحالي
$to_date = $_GET['to_date'] ?? date('Y-m-t'); // آخر يوم في الشهر الحالي
$employee_search = $_GET['employee_search'] ?? '';

$where_conditions = [];
$params = [];
$types = "";

if ($status_filter !== 'all') {
    $where_conditions[] = "sad.status = ?";
    $params[] = $status_filter;
    $types .= "s";
}

if (!empty($from_date)) {
    $where_conditions[] = "sad.absence_date >= ?";
    $params[] = $from_date;
    $types .= "s";
}

if (!empty($to_date)) {
    $where_conditions[] = "sad.absence_date <= ?";
    $params[] = $to_date;
    $types .= "s";
}

if (!empty($employee_search)) {
    $where_conditions[] = "u.full_name LIKE ?";
    $params[] = '%' . $employee_search . '%';
    $types .= "s";
}

// فلتر الدور
if ($role_filter !== 'all') {
    $where_conditions[] = "u.role = ?";
    $params[] = $role_filter;
    $types .= "s";
}

// إضافة شرط استبعاد مديري النظام فقط (الإداريون العاديون لهم دور 'staff')
$where_conditions[] = "u.role NOT IN ('system_admin')";

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

// جلب بيانات الغياب بالخصم
$query = "
    SELECT sad.*, u.full_name, u.role,
           processor.full_name as processed_by_name,
           recorder.full_name as recorded_by_name
    FROM staff_absences_with_deduction sad
    JOIN users u ON sad.user_id = u.id
    LEFT JOIN users processor ON sad.processed_by = processor.id
    LEFT JOIN users recorder ON sad.recorded_by = recorder.id
    $where_clause
    ORDER BY sad.absence_date DESC, sad.created_at DESC
";

$stmt = $conn->prepare($query);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$absences_result = $stmt->get_result();

// حساب الإحصائيات
$stats_query = "
    SELECT
        COUNT(*) as total_absences,
        SUM(CASE WHEN sad.status = 'pending' THEN 1 ELSE 0 END) as pending_absences,
        SUM(CASE WHEN sad.status = 'processed' THEN 1 ELSE 0 END) as processed_absences,
        SUM(CASE WHEN sad.status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_absences,
        SUM(CASE WHEN sad.status = 'processed' THEN sad.deduction_amount ELSE 0 END) as total_deductions,
        COUNT(DISTINCT sad.user_id) as unique_employees,
        SUM(sad.deduction_amount) as total_all_deductions
    FROM staff_absences_with_deduction sad
    JOIN users u ON sad.user_id = u.id
    $where_clause
";

$stats_stmt = $conn->prepare($stats_query);
if (!empty($params)) {
    $stats_stmt->bind_param($types, ...$params);
}
$stats_stmt->execute();
$stats_result = $stats_stmt->get_result();
$stats = $stats_result->fetch_assoc();

// حساب إحصائيات إضافية للموظفين
$employee_stats_query = "
    SELECT
        u.full_name,
        u.role,
        COUNT(*) as absence_days,
        SUM(sad.deduction_amount) as total_deduction
    FROM staff_absences_with_deduction sad
    JOIN users u ON sad.user_id = u.id
    $where_clause
    GROUP BY sad.user_id, u.full_name, u.role
    ORDER BY absence_days DESC, total_deduction DESC
";

$employee_stats_stmt = $conn->prepare($employee_stats_query);
if (!empty($params)) {
    $employee_stats_stmt->bind_param($types, ...$params);
}
$employee_stats_stmt->execute();
$employee_stats_result = $employee_stats_stmt->get_result();

// التأكد من وجود القيم وتعيين قيم افتراضية
$stats['total_absences'] = $stats['total_absences'] ?? 0;
$stats['processed_absences'] = $stats['processed_absences'] ?? 0;
$stats['total_deductions'] = $stats['total_deductions'] ?? 0.00;
$stats['unique_employees'] = $stats['unique_employees'] ?? 0;
$stats['total_all_deductions'] = $stats['total_all_deductions'] ?? 0.00;
?>

<div class="container-fluid">
    <!-- إحصائيات الغياب بالخصم -->
    <div class="row mb-4">
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-danger text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $stats['total_absences']; ?></h4>
                            <p class="mb-0 small">إجمالي أيام الغياب</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-times fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $stats['unique_employees']; ?></h4>
                            <p class="mb-0 small">عدد الموظفين</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $stats['pending_absences']; ?></h4>
                            <p class="mb-0 small">في الانتظار</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $stats['processed_absences']; ?></h4>
                            <p class="mb-0 small">تم المعالجة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo number_format((float)$stats['total_deductions'], 2); ?></h4>
                            <p class="mb-0 small">خصومات معالجة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-dark text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo number_format((float)$stats['total_all_deductions'], 2); ?></h4>
                            <p class="mb-0 small">إجمالي الخصومات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-user-times me-2"></i>
                            <?php echo $page_title; ?>
                        </h5>
                        <div>
                            <a href="deduction_settings.php" class="btn btn-warning btn-sm">
                                <i class="fas fa-cogs me-2"></i>إعدادات الخصم
                            </a>
                            <a href="reports_absence.php" class="btn btn-info btn-sm">
                                <i class="fas fa-chart-line me-2"></i>التقارير
                            </a>
                            <button type="button" class="btn btn-secondary btn-sm" onclick="goBackToAttendance()">
                                <i class="fas fa-arrow-left me-2"></i>رجوع
                            </button>
                        </div>
                    </div>
                </div>

                <!-- أزرار التبويب -->
                <div class="card-body border-bottom">
                    <div class="btn-group" role="group">
                        <a href="?tab=all<?php echo !empty($_GET['status']) ? '&status=' . $_GET['status'] : ''; ?><?php echo !empty($_GET['from_date']) ? '&from_date=' . $_GET['from_date'] : ''; ?><?php echo !empty($_GET['to_date']) ? '&to_date=' . $_GET['to_date'] : ''; ?>"
                           class="btn btn-<?php echo $tab === 'all' ? 'primary' : 'outline-primary'; ?>">
                            <i class="fas fa-users me-1"></i>الكل
                        </a>
                        <a href="?tab=teachers<?php echo !empty($_GET['status']) ? '&status=' . $_GET['status'] : ''; ?><?php echo !empty($_GET['from_date']) ? '&from_date=' . $_GET['from_date'] : ''; ?><?php echo !empty($_GET['to_date']) ? '&to_date=' . $_GET['to_date'] : ''; ?>"
                           class="btn btn-<?php echo $tab === 'teachers' ? 'primary' : 'outline-primary'; ?>">
                            <i class="fas fa-chalkboard-teacher me-1"></i>المعلمين
                        </a>
                        <a href="?tab=admins<?php echo !empty($_GET['status']) ? '&status=' . $_GET['status'] : ''; ?><?php echo !empty($_GET['from_date']) ? '&from_date=' . $_GET['from_date'] : ''; ?><?php echo !empty($_GET['to_date']) ? '&to_date=' . $_GET['to_date'] : ''; ?>"
                           class="btn btn-<?php echo $tab === 'admins' ? 'primary' : 'outline-primary'; ?>">
                            <i class="fas fa-user-tie me-1"></i>الإداريين
                        </a>
                    </div>
                </div>

                <div class="card-body">

                    <?php if (!$tables_exist): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-database me-2"></i>
                            <strong>خطأ في قاعدة البيانات:</strong> الجداول المطلوبة غير موجودة.
                            <br>الجداول المفقودة: <?php echo implode(', ', $missing_tables); ?>
                            <br><a href="../setup_absence_system.php" class="btn btn-warning btn-sm mt-2">
                                <i class="fas fa-tools me-2"></i>تشغيل ملف الإعداد
                            </a>
                        </div>
                    <?php endif; ?>

                    <!-- فلاتر البحث -->
                    <div class="alert alert-info mb-3">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> جميع سجلات الغياب بالخصم يتم تفعيلها مباشرة عند التسجيل.
                    </div>

                    <form method="GET" class="mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-filter me-2"></i>
                                    فلاتر البحث والتصفية
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <label for="from_date" class="form-label">
                                            <i class="fas fa-calendar-alt me-1"></i>
                                            من تاريخ
                                        </label>
                                        <input type="date" class="form-control" id="from_date" name="from_date" value="<?php echo $from_date; ?>">
                                    </div>
                                    <div class="col-lg-3 col-md-6 mb-3">
                                        <label for="to_date" class="form-label">
                                            <i class="fas fa-calendar-alt me-1"></i>
                                            إلى تاريخ
                                        </label>
                                        <input type="date" class="form-control" id="to_date" name="to_date" value="<?php echo $to_date; ?>">
                                    </div>
                                    <div class="col-lg-4 col-md-6 mb-3">
                                        <label for="employee_search" class="form-label">
                                            <i class="fas fa-user-search me-1"></i>
                                            البحث في الموظفين
                                        </label>
                                        <input type="text"
                                               class="form-control"
                                               id="employee_search"
                                               name="employee_search"
                                               value="<?php echo htmlspecialchars($employee_search ?? ''); ?>"
                                               placeholder="اكتب اسم الموظف أو جزء منه..."
                                               autocomplete="off">
                                        <div id="employee_suggestions" class="dropdown-menu" style="display: none; width: 100%;"></div>
                                    </div>
                                    <div class="col-lg-2 col-md-6 mb-3">
                                        <label for="status" class="form-label">
                                            <i class="fas fa-flag me-1"></i>
                                            الحالة
                                        </label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>جميع الحالات</option>
                                            <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>في الانتظار</option>
                                            <option value="processed" <?php echo $status_filter === 'processed' ? 'selected' : ''; ?>>تم المعالجة</option>
                                            <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>ملغي</option>
                                        </select>
                                    </div>

                                    <?php if ($tab === 'all'): ?>
                                    <div class="col-lg-2 col-md-6 mb-3">
                                        <label for="role" class="form-label">
                                            <i class="fas fa-users me-1"></i>
                                            نوع الموظف
                                        </label>
                                        <select class="form-select" id="role" name="role">
                                            <option value="all" <?php echo $role_filter === 'all' ? 'selected' : ''; ?>>جميع الموظفين</option>
                                            <option value="teacher" <?php echo $role_filter === 'teacher' ? 'selected' : ''; ?>>المعلمين فقط</option>
                                            <option value="staff" <?php echo $role_filter === 'staff' ? 'selected' : ''; ?>>الإداريين فقط</option>
                                        </select>
                                    </div>
                                    <?php else: ?>
                                    <input type="hidden" name="role" value="<?php echo htmlspecialchars($role_filter); ?>">
                                    <input type="hidden" name="tab" value="<?php echo htmlspecialchars($tab); ?>">
                                    <?php endif; ?>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="d-flex gap-2 flex-wrap">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-search me-2"></i>بحث وتصفية
                                            </button>
                                            <a href="manage_absences.php<?php echo $tab !== 'all' ? '?tab=' . $tab : ''; ?>" class="btn btn-secondary">
                                                <i class="fas fa-refresh me-2"></i>إعادة تعيين
                                            </a>
                                            <button type="button" class="btn btn-info" onclick="setCurrentMonth()">
                                                <i class="fas fa-calendar me-2"></i>الشهر الحالي
                                            </button>
                                            <button type="button" class="btn btn-warning" onclick="setLastMonth()">
                                                <i class="fas fa-calendar-minus me-2"></i>الشهر الماضي
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- إحصائيات الموظفين -->
                    <?php if ($employee_stats_result->num_rows > 0): ?>
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    إحصائيات الموظفين للفترة المحددة
                                    <?php if (!empty($from_date) && !empty($to_date)): ?>
                                        (من <?php echo $from_date; ?> إلى <?php echo $to_date; ?>)
                                    <?php endif; ?>
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>الموظف</th>
                                                <th>الدور</th>
                                                <th class="text-center">عدد أيام الغياب</th>
                                                <th class="text-center">إجمالي مبلغ الخصم</th>
                                                <th class="text-center">متوسط الخصم اليومي</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php while ($emp_stat = $employee_stats_result->fetch_assoc()): ?>
                                                <tr>
                                                    <td>
                                                        <strong><?php echo htmlspecialchars($emp_stat['full_name']); ?></strong>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-<?php echo $emp_stat['role'] === 'teacher' ? 'primary' : 'secondary'; ?>">
                                                            <?php echo $emp_stat['role'] === 'teacher' ? 'معلم' : 'إداري'; ?>
                                                        </span>
                                                    </td>
                                                    <td class="text-center">
                                                        <span class="badge bg-danger fs-6">
                                                            <?php echo $emp_stat['absence_days']; ?> يوم
                                                        </span>
                                                    </td>
                                                    <td class="text-center">
                                                        <strong class="text-danger">
                                                            <?php echo number_format($emp_stat['total_deduction'], 2); ?> ج.م
                                                        </strong>
                                                    </td>
                                                    <td class="text-center">
                                                        <span class="text-muted">
                                                            <?php echo number_format($emp_stat['total_deduction'] / $emp_stat['absence_days'], 2); ?> ج.م
                                                        </span>
                                                    </td>
                                                </tr>
                                            <?php endwhile; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- جدول الغياب بالخصم -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الموظف</th>
                                    <th>تاريخ الغياب</th>
                                    <th>السبب</th>
                                    <th>مبلغ الخصم</th>
                                    <th>نوع الخصم</th>
                                    <th>مسجل بواسطة</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($absences_result->num_rows > 0): ?>
                                    <?php while ($absence = $absences_result->fetch_assoc()): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($absence['full_name']); ?></strong>
                                                <br><small class="text-muted"><?php echo $absence['role']; ?></small>
                                            </td>
                                            <td><?php echo date('Y-m-d', strtotime($absence['absence_date'])); ?></td>
                                            <td>
                                                <?php if (!empty($absence['reason'])): ?>
                                                    <span class="text-truncate" style="max-width: 200px;" title="<?php echo htmlspecialchars($absence['reason'] ?? ''); ?>">
                                                        <?php echo htmlspecialchars(substr($absence['reason'] ?? '', 0, 50)) . (strlen($absence['reason'] ?? '') > 50 ? '...' : ''); ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted"><em>غير محدد</em></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <strong class="text-danger"><?php echo number_format((float)($absence['deduction_amount'] ?? 0), 2); ?> ج.م</strong>
                                            </td>
                                            <td>
                                                <?php
                                                $absence_types = [
                                                    'unauthorized' => 'غياب بدون عذر',
                                                    'personal' => 'غياب شخصي',
                                                    'emergency' => 'غياب طارئ',
                                                    'sick' => 'إجازة مرضية'
                                                ];
                                                echo $absence_types[$absence['absence_type']] ?? $absence['absence_type'];
                                                ?>
                                            </td>
                                            <td>
                                                <?php echo htmlspecialchars($absence['recorded_by_name'] ?? 'غير محدد'); ?>
                                            </td>
                                            <td>
                                                <?php echo date('Y-m-d H:i', strtotime($absence['created_at'])); ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="edit_absence.php?id=<?php echo $absence['id']; ?>"
                                                       class="btn btn-sm btn-outline-primary"
                                                       title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="delete_absence.php?id=<?php echo $absence['id']; ?>"
                                                       class="btn btn-sm btn-outline-danger"
                                                       title="حذف"
                                                       onclick="return confirm('هل أنت متأكد من حذف سجل الغياب بالخصم؟\n\nالموظف: <?php echo htmlspecialchars($absence['full_name'] ?? 'غير محدد'); ?>\nالتاريخ: <?php echo $absence['absence_date'] ?? 'غير محدد'; ?>\n\nتحذير: سيتم حذف السجل نهائياً من النظام وسيؤثر على سجل الحضور أيضاً.')">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="9" class="text-center">لا توجد سجلات غياب بالخصم</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<script>
// البحث الذكي للموظفين
document.getElementById('employee_search').addEventListener('input', function() {
    const searchTerm = this.value.trim();
    const suggestionsDiv = document.getElementById('employee_suggestions');

    if (searchTerm.length < 1) {
        suggestionsDiv.style.display = 'none';
        return;
    }

    // إرسال طلب AJAX للبحث
    fetch(`search_employees.php?term=${encodeURIComponent(searchTerm)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.employees.length > 0) {
                let html = '';
                data.employees.forEach(employee => {
                    html += `<a class="dropdown-item" href="#" onclick="selectEmployee('${employee.full_name}')">${employee.full_name} (${employee.role === 'teacher' ? 'معلم' : 'إداري'})</a>`;
                });
                suggestionsDiv.innerHTML = html;
                suggestionsDiv.style.display = 'block';
            } else {
                suggestionsDiv.style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            suggestionsDiv.style.display = 'none';
        });
});

// اختيار موظف من القائمة المنسدلة
function selectEmployee(name) {
    document.getElementById('employee_search').value = name;
    document.getElementById('employee_suggestions').style.display = 'none';
}

// إخفاء القائمة المنسدلة عند النقر خارجها
document.addEventListener('click', function(e) {
    if (!e.target.closest('#employee_search')) {
        document.getElementById('employee_suggestions').style.display = 'none';
    }
});

// تحديد الشهر الحالي
function setCurrentMonth() {
    const now = new Date();
    const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    document.getElementById('from_date').value = firstDay.toISOString().split('T')[0];
    document.getElementById('to_date').value = lastDay.toISOString().split('T')[0];
}

// تحديد الشهر الماضي
function setLastMonth() {
    const now = new Date();
    const firstDay = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const lastDay = new Date(now.getFullYear(), now.getMonth(), 0);

    document.getElementById('from_date').value = firstDay.toISOString().split('T')[0];
    document.getElementById('to_date').value = lastDay.toISOString().split('T')[0];
}

// التحقق من صحة التواريخ
document.getElementById('from_date').addEventListener('change', function() {
    const fromDate = new Date(this.value);
    const toDate = new Date(document.getElementById('to_date').value);

    if (fromDate > toDate) {
        document.getElementById('to_date').value = this.value;
    }
});

document.getElementById('to_date').addEventListener('change', function() {
    const fromDate = new Date(document.getElementById('from_date').value);
    const toDate = new Date(this.value);

    if (toDate < fromDate) {
        document.getElementById('from_date').value = this.value;
    }
});
</script>

<style>
#employee_suggestions {
    position: absolute;
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#employee_suggestions .dropdown-item {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
}

#employee_suggestions .dropdown-item:hover {
    background-color: #f8f9fa;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.table-dark th {
    background: linear-gradient(135deg, #495057 0%, #343a40 100%);
}

.badge {
    font-size: 0.8em;
}

.btn-group .btn {
    margin: 2px;
}

@media (max-width: 768px) {
    .d-flex.gap-2 {
        flex-direction: column;
    }

    .d-flex.gap-2 .btn {
        margin-bottom: 5px;
    }
}
</style>

<script>
// دالة الرجوع للحضور الذكي
function goBackToAttendance() {
    const urlParams = new URLSearchParams(window.location.search);
    const tab = urlParams.get('tab');
    const currentDate = new Date().toISOString().split('T')[0];

    let backUrl = 'smart_attendance.php?date=' + currentDate;

    if (tab === 'teachers') {
        backUrl = 'smart_attendance.php?tab=teachers&date=' + currentDate;
    } else if (tab === 'admins') {
        backUrl = 'smart_attendance.php?tab=admins&date=' + currentDate;
    }

    window.location.href = backUrl;
}
</script>

<?php require_once '../includes/footer.php'; ?>
