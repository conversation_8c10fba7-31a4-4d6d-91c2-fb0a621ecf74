<?php
/**
 * إدارة موارد النظام (الصفحات والإجراءات)
 * System Resources Management
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

$success_message = '';
$error_message = '';

// معالجة إضافة مورد جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_resource'])) {
    $resource_type = clean_input($_POST['resource_type']);
    $resource_key = clean_input($_POST['resource_key']);
    $resource_name = clean_input($_POST['resource_name']);
    $resource_description = clean_input($_POST['resource_description']);
    $resource_path = clean_input($_POST['resource_path']);
    $parent_resource = clean_input($_POST['parent_resource']);
    $icon = clean_input($_POST['icon']);
    $sort_order = intval($_POST['sort_order']);
    
    if (!empty($resource_type) && !empty($resource_key) && !empty($resource_name)) {
        $stmt = $conn->prepare("
            INSERT INTO system_resources 
            (resource_type, resource_key, resource_name, resource_description, resource_path, parent_resource, icon, sort_order) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->bind_param("sssssssi", $resource_type, $resource_key, $resource_name, $resource_description, $resource_path, $parent_resource, $icon, $sort_order);
        
        if ($stmt->execute()) {
            $success_message = "تم إضافة المورد بنجاح";
        } else {
            $error_message = "خطأ في إضافة المورد: " . $conn->error;
        }
    } else {
        $error_message = "يرجى ملء جميع الحقول المطلوبة";
    }
}

// معالجة تحديث مورد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_resource'])) {
    $resource_id = intval($_POST['resource_id']);
    $resource_name = clean_input($_POST['resource_name']);
    $resource_description = clean_input($_POST['resource_description']);
    $resource_path = clean_input($_POST['resource_path']);
    $icon = clean_input($_POST['icon']);
    $sort_order = intval($_POST['sort_order']);
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    
    if ($resource_id > 0 && !empty($resource_name)) {
        $stmt = $conn->prepare("
            UPDATE system_resources 
            SET resource_name = ?, resource_description = ?, resource_path = ?, icon = ?, sort_order = ?, is_active = ?
            WHERE id = ?
        ");
        $stmt->bind_param("ssssiii", $resource_name, $resource_description, $resource_path, $icon, $sort_order, $is_active, $resource_id);
        
        if ($stmt->execute()) {
            $success_message = "تم تحديث المورد بنجاح";
        } else {
            $error_message = "خطأ في تحديث المورد: " . $conn->error;
        }
    }
}

// معالجة حذف مورد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_resource'])) {
    $resource_id = intval($_POST['resource_id']);
    
    if ($resource_id > 0) {
        // التحقق من عدم وجود صلاحيات مرتبطة بهذا المورد
        $check_stmt = $conn->prepare("SELECT COUNT(*) as count FROM user_custom_permissions WHERE permission_key = (SELECT resource_key FROM system_resources WHERE id = ?)");
        $check_stmt->bind_param("i", $resource_id);
        $check_stmt->execute();
        $count = $check_stmt->get_result()->fetch_assoc()['count'];
        
        if ($count > 0) {
            $error_message = "لا يمكن حذف هذا المورد لأنه مرتبط بصلاحيات مستخدمين";
        } else {
            $stmt = $conn->prepare("DELETE FROM system_resources WHERE id = ?");
            $stmt->bind_param("i", $resource_id);
            
            if ($stmt->execute()) {
                $success_message = "تم حذف المورد بنجاح";
            } else {
                $error_message = "خطأ في حذف المورد: " . $conn->error;
            }
        }
    }
}

// جلب الموارد
$resources_query = "
    SELECT sr.*, 
           (SELECT COUNT(*) FROM user_custom_permissions ucp WHERE ucp.permission_key = sr.resource_key) as usage_count
    FROM system_resources sr 
    ORDER BY sr.resource_type, sr.sort_order, sr.resource_name
";
$resources_result = $conn->query($resources_query);

$page_title = 'إدارة موارد النظام';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-sitemap me-2"></i><?php echo $page_title; ?></h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="../admin/">الإدارة</a></li>
                    <li class="breadcrumb-item active">موارد النظام</li>
                </ol>
            </nav>
        </div>
        <a href="add_resource.php" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>إضافة مورد جديد
        </a>
    </div>

    <!-- رسائل التنبيه -->
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- جدول الموارد -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-list me-2"></i>موارد النظام</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>النوع</th>
                            <th>المفتاح</th>
                            <th>الاسم</th>
                            <th>الوصف</th>
                            <th>المسار</th>
                            <th>الاستخدام</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while ($resource = $resources_result->fetch_assoc()): ?>
                        <tr>
                            <td>
                                <span class="badge bg-<?php 
                                    echo match($resource['resource_type']) {
                                        'page' => 'primary',
                                        'action' => 'success',
                                        'data' => 'info',
                                        'report' => 'warning',
                                        default => 'secondary'
                                    };
                                ?>">
                                    <?php 
                                    $type_names = [
                                        'page' => 'صفحة',
                                        'action' => 'إجراء',
                                        'data' => 'بيانات',
                                        'report' => 'تقرير'
                                    ];
                                    echo $type_names[$resource['resource_type']] ?? $resource['resource_type'];
                                    ?>
                                </span>
                            </td>
                            <td><code><?php echo htmlspecialchars($resource['resource_key']); ?></code></td>
                            <td>
                                <?php if ($resource['icon']): ?>
                                    <i class="<?php echo htmlspecialchars($resource['icon']); ?> me-2"></i>
                                <?php endif; ?>
                                <?php echo htmlspecialchars($resource['resource_name']); ?>
                            </td>
                            <td><?php echo htmlspecialchars($resource['resource_description']); ?></td>
                            <td>
                                <?php if ($resource['resource_path']): ?>
                                    <small class="text-muted"><?php echo htmlspecialchars($resource['resource_path']); ?></small>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo $resource['usage_count'] > 0 ? 'success' : 'secondary'; ?>">
                                    <?php echo $resource['usage_count']; ?> مستخدم
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo $resource['is_active'] ? 'success' : 'secondary'; ?>">
                                    <?php echo $resource['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="edit_resource.php?id=<?php echo $resource['id']; ?>"
                                       class="btn btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <?php if ($resource['usage_count'] == 0): ?>
                                        <a href="delete_resource.php?id=<?php echo $resource['id']; ?>"
                                           class="btn btn-outline-danger">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>



<?php include_once '../includes/footer.php'; ?>
