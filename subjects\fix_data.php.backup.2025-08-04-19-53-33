<?php
/**
 * إصلاح بيانات المواد والفصول
 * Fix Subjects and Classes Data
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

check_session();
if (!check_permission('admin')) {
    die('ليس لديك صلاحية للوصول');
}

echo "<h2>إصلاح بيانات المواد والفصول</h2>";

// 1. فحص البيانات الحالية
echo "<h3>1. فحص البيانات الحالية:</h3>";

$subjects_count = $conn->query("SELECT COUNT(*) as count FROM subjects")->fetch_assoc()['count'];
$classes_count = $conn->query("SELECT COUNT(*) as count FROM classes")->fetch_assoc()['count'];
$assignments_count = $conn->query("SELECT COUNT(*) as count FROM teacher_assignments")->fetch_assoc()['count'];

echo "<p>عدد المواد: <strong>$subjects_count</strong></p>";
echo "<p>عدد الفصول: <strong>$classes_count</strong></p>";
echo "<p>عدد التكليفات: <strong>$assignments_count</strong></p>";

// 2. إضافة بيانات تجريبية إذا لم تكن موجودة
if ($assignments_count == 0) {
    echo "<h3>2. إضافة بيانات تجريبية:</h3>";
    
    // إضافة تكليفات تجريبية
    $sample_assignments = [
        [1, 1, 7, 'active', 4], // معلم 1، فصل 1، مادة 7 (رياضيات)
        [1, 2, 7, 'active', 4], // معلم 1، فصل 2، مادة 7 (رياضيات)
        [2, 1, 8, 'active', 3], // معلم 2، فصل 1، مادة 8 (علوم)
        [2, 2, 8, 'active', 3], // معلم 2، فصل 2، مادة 8 (علوم)
    ];
    
    $insert_query = "INSERT INTO teacher_assignments (teacher_id, class_id, subject_id, status, weekly_hours, academic_year_id, semester, assigned_at, created_at, updated_at) VALUES (?, ?, ?, ?, ?, 1, 'first', NOW(), NOW(), NOW())";
    $stmt = $conn->prepare($insert_query);
    
    foreach ($sample_assignments as $assignment) {
        $stmt->bind_param('iiisi', $assignment[0], $assignment[1], $assignment[2], $assignment[3], $assignment[4]);
        if ($stmt->execute()) {
            echo "<p style='color: green;'>✅ تم إضافة تكليف: معلم {$assignment[0]} → فصل {$assignment[1]} → مادة {$assignment[2]}</p>";
        } else {
            echo "<p style='color: red;'>❌ خطأ في إضافة تكليف: " . $stmt->error . "</p>";
        }
    }
    $stmt->close();
    
    echo "<p><strong>تم إضافة " . count($sample_assignments) . " تكليف تجريبي</strong></p>";
}

// 3. فحص الفصول المفقودة
echo "<h3>3. فحص الفصول:</h3>";

$missing_classes = [];
$check_classes = $conn->query("SELECT DISTINCT class_id FROM teacher_assignments WHERE status = 'active'");
while ($row = $check_classes->fetch_assoc()) {
    $class_id = $row['class_id'];
    $class_exists = $conn->query("SELECT id FROM classes WHERE id = $class_id")->num_rows;
    if ($class_exists == 0) {
        $missing_classes[] = $class_id;
    }
}

if (!empty($missing_classes)) {
    echo "<p style='color: orange;'>⚠️ فصول مفقودة: " . implode(', ', $missing_classes) . "</p>";
    
    // إضافة الفصول المفقودة مع فحص المفاتيح الخارجية
    foreach ($missing_classes as $class_id) {
        // فحص وجود grade_id و stage_id صالحين
        $valid_grade = $conn->query("SELECT id FROM grades LIMIT 1")->fetch_assoc();
        $valid_stage = $conn->query("SELECT id FROM educational_stages LIMIT 1")->fetch_assoc();

        if ($valid_grade && $valid_stage) {
            $insert_class = "INSERT INTO classes (id, class_name, section, grade_id, stage_id, status, created_at, updated_at) VALUES (?, ?, 'أ', ?, ?, 'active', NOW(), NOW())";
            $stmt = $conn->prepare($insert_class);
            $class_name = "الفصل $class_id";
            $stmt->bind_param('isii', $class_id, $class_name, $valid_grade['id'], $valid_stage['id']);
            if ($stmt->execute()) {
                echo "<p style='color: green;'>✅ تم إضافة الفصل: $class_name</p>";
            } else {
                echo "<p style='color: red;'>❌ خطأ في إضافة الفصل: " . $stmt->error . "</p>";
            }
            $stmt->close();
        } else {
            // إضافة بدون مفاتيح خارجية
            $insert_class = "INSERT INTO classes (id, class_name, section, grade_id, stage_id, status, created_at, updated_at) VALUES (?, ?, 'أ', NULL, NULL, 'active', NOW(), NOW())";
            $stmt = $conn->prepare($insert_class);
            $class_name = "الفصل $class_id";
            $stmt->bind_param('is', $class_id, $class_name);
            if ($stmt->execute()) {
                echo "<p style='color: green;'>✅ تم إضافة الفصل: $class_name (بدون مرحلة وصف)</p>";
            } else {
                echo "<p style='color: red;'>❌ خطأ في إضافة الفصل: " . $stmt->error . "</p>";
            }
            $stmt->close();
        }
    }
} else {
    echo "<p style='color: green;'>✅ جميع الفصول موجودة</p>";
}

// 4. اختبار الاستعلام بعد الإصلاح
echo "<h3>4. اختبار الاستعلام بعد الإصلاح:</h3>";

$test_subjects = [7, 8]; // رياضيات وعلوم
foreach ($test_subjects as $subject_id) {
    echo "<h4>المادة ID: $subject_id</h4>";
    
    $classes_query = "
        SELECT DISTINCT
            c.id,
            c.class_name,
            COALESCE(c.section, 'أ') as section,
            COALESCE(g.grade_name, 'غير محدد') as grade_name,
            COALESCE(es.stage_name, 'غير محدد') as stage_name,
            ta.teacher_id,
            COALESCE(ta.weekly_hours, 0) as weekly_hours
        FROM teacher_assignments ta
        INNER JOIN classes c ON ta.class_id = c.id
        LEFT JOIN grades g ON c.grade_id = g.id
        LEFT JOIN educational_stages es ON c.stage_id = es.id
        WHERE ta.subject_id = ? 
        AND ta.status = 'active'
        AND c.id IS NOT NULL
        ORDER BY c.id
    ";
    
    $stmt = $conn->prepare($classes_query);
    $stmt->bind_param('i', $subject_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #d4edda;'><th>Class ID</th><th>اسم الفصل</th><th>الشعبة</th><th>الصف</th><th>المرحلة</th><th>Teacher ID</th><th>ساعات</th></tr>";
        while ($class = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$class['id']}</td>";
            echo "<td>{$class['class_name']}</td>";
            echo "<td>{$class['section']}</td>";
            echo "<td>{$class['grade_name']}</td>";
            echo "<td>{$class['stage_name']}</td>";
            echo "<td>{$class['teacher_id']}</td>";
            echo "<td>{$class['weekly_hours']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p style='color: green;'>✅ تم العثور على " . $result->num_rows . " فصل للمادة $subject_id</p>";
    } else {
        echo "<p style='color: red;'>❌ لا توجد فصول للمادة $subject_id</p>";
    }
    $stmt->close();
}

// 5. روابط الاختبار
echo "<h3>5. روابط الاختبار:</h3>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px;'>";
echo "<p><a href='debug_classes.php?id=7' target='_blank'>🔍 تشخيص مادة الرياضيات (ID: 7)</a></p>";
echo "<p><a href='view.php?id=7' target='_blank'>👁️ عرض مادة الرياضيات</a></p>";
echo "<p><a href='debug_classes.php?id=8' target='_blank'>🔍 تشخيص مادة العلوم (ID: 8)</a></p>";
echo "<p><a href='view.php?id=8' target='_blank'>👁️ عرض مادة العلوم</a></p>";
echo "<p><a href='index.php' target='_blank'>📋 قائمة المواد الرئيسية</a></p>";
echo "</div>";

// 6. تعليمات
echo "<h3>6. التعليمات:</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;'>";
echo "<ol>";
echo "<li><strong>شغل هذا الملف أولاً</strong> لإصلاح البيانات</li>";
echo "<li><strong>استخدم ملف التشخيص</strong> للتحقق من البيانات</li>";
echo "<li><strong>اختبر صفحة العرض</strong> للتأكد من ظهور الفصول</li>";
echo "<li><strong>إذا لم تظهر الفصول</strong> تحقق من error.log</li>";
echo "</ol>";
echo "</div>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: right; }
th { background-color: #f0f0f0; }
h3 { color: #333; border-bottom: 2px solid #ddd; padding-bottom: 5px; margin-top: 30px; }
h4 { color: #666; margin-top: 20px; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
