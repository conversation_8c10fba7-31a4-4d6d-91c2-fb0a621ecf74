<?php
/**
 * اختبار وصول الإداري للبيانات
 * Test Staff Access to Data
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

$user_email = '<EMAIL>';
$test_results = [];

// جلب بيانات المستخدم
$user_query = "SELECT * FROM users WHERE email = ?";
$user_stmt = $conn->prepare($user_query);
$user_stmt->bind_param("s", $user_email);
$user_stmt->execute();
$user = $user_stmt->get_result()->fetch_assoc();

if (!$user) {
    die("❌ المستخدم غير موجود!");
}

// محاكاة جلسة المستخدم
$original_session = $_SESSION;
$_SESSION['user_id'] = $user['id'];
$_SESSION['role'] = $user['role'];
$_SESSION['email'] = $user['email'];

// اختبار الصلاحيات الأساسية
$test_results['basic_permissions'] = [
    'admin' => check_permission('admin'),
    'teacher' => check_permission('teacher'),
    'staff' => check_permission('staff'),
    'student' => check_permission('student')
];

// اختبار الصلاحيات المخصصة
$custom_permissions = [
    'student_affairs' => 'شئون الطلاب',
    'staff_affairs' => 'شئون العاملين',
    'students_view' => 'عرض الطلاب',
    'teachers_view' => 'عرض المعلمين',
    'classes_view' => 'عرض الفصول',
    'subjects_view' => 'عرض المواد',
    'student_add' => 'إضافة طالب',
    'teacher_add' => 'إضافة معلم',
    'reports_view' => 'عرض التقارير'
];

$test_results['custom_permissions'] = [];
foreach ($custom_permissions as $permission => $name) {
    $test_results['custom_permissions'][$permission] = [
        'name' => $name,
        'has_permission' => has_permission($permission)
    ];
}

// اختبار الوصول للبيانات
$data_tests = [
    'students' => 'SELECT COUNT(*) as count FROM students',
    'teachers' => 'SELECT COUNT(*) as count FROM teachers',
    'classes' => 'SELECT COUNT(*) as count FROM classes',
    'subjects' => 'SELECT COUNT(*) as count FROM subjects'
];

$test_results['data_access'] = [];
foreach ($data_tests as $table => $query) {
    try {
        $result = $conn->query($query);
        if ($result) {
            $count = $result->fetch_assoc()['count'];
            $test_results['data_access'][$table] = [
                'accessible' => true,
                'count' => $count,
                'error' => null
            ];
        } else {
            $test_results['data_access'][$table] = [
                'accessible' => false,
                'count' => 0,
                'error' => 'فشل في تنفيذ الاستعلام'
            ];
        }
    } catch (Exception $e) {
        $test_results['data_access'][$table] = [
            'accessible' => false,
            'count' => 0,
            'error' => $e->getMessage()
        ];
    }
}

// اختبار الوصول للصفحات (محاكاة)
$page_tests = [
    'students' => !(!check_permission('teacher') && !check_permission('staff') && !has_permission('students_view') && !has_permission('student_affairs')),
    'teachers' => !(!check_permission('admin') && !check_permission('staff') && !has_permission('teachers_view') && !has_permission('staff_affairs')),
    'classes' => !(!check_permission('teacher') && !check_permission('staff') && !has_permission('classes_view')),
    'subjects' => !(!check_permission('admin') && !check_permission('staff') && !has_permission('subjects_view'))
];

$test_results['page_access'] = $page_tests;

// استعادة الجلسة الأصلية
$_SESSION = $original_session;

$page_title = 'اختبار وصول الإداري للبيانات';
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-vial me-2"></i><?php echo $page_title; ?></h2>
            <p class="text-muted">اختبار شامل لوصول المستخدم: <?php echo $user['full_name']; ?> (<?php echo $user['email']; ?>)</p>
        </div>
        <a href="../settings/permissions.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة
        </a>
    </div>

    <!-- معلومات المستخدم -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5><i class="fas fa-user me-2"></i>معلومات المستخدم</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <strong>الاسم:</strong><br>
                    <?php echo htmlspecialchars($user['full_name']); ?>
                </div>
                <div class="col-md-3">
                    <strong>البريد الإلكتروني:</strong><br>
                    <?php echo htmlspecialchars($user['email']); ?>
                </div>
                <div class="col-md-3">
                    <strong>الدور:</strong><br>
                    <span class="badge bg-<?php 
                        echo match($user['role']) {
                            'admin' => 'danger',
                            'teacher' => 'success',
                            'staff' => 'warning',
                            'student' => 'info',
                            default => 'secondary'
                        };
                    ?>">
                        <?php echo get_role_name_arabic($user['role']); ?>
                    </span>
                </div>
                <div class="col-md-3">
                    <strong>الحالة:</strong><br>
                    <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : 'danger'; ?>">
                        <?php echo $user['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                    </span>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- الصلاحيات الأساسية -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h6><i class="fas fa-shield-alt me-2"></i>الصلاحيات الأساسية</h6>
                </div>
                <div class="card-body">
                    <?php foreach ($test_results['basic_permissions'] as $role => $has_permission): ?>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span><?php echo get_role_name_arabic($role); ?></span>
                            <span class="badge bg-<?php echo $has_permission ? 'success' : 'danger'; ?>">
                                <?php echo $has_permission ? 'متاح' : 'مرفوض'; ?>
                            </span>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- الصلاحيات المخصصة -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h6><i class="fas fa-key me-2"></i>الصلاحيات المخصصة</h6>
                </div>
                <div class="card-body">
                    <?php foreach ($test_results['custom_permissions'] as $permission => $info): ?>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span><?php echo $info['name']; ?></span>
                            <span class="badge bg-<?php echo $info['has_permission'] ? 'success' : 'danger'; ?>">
                                <?php echo $info['has_permission'] ? 'متاح' : 'مرفوض'; ?>
                            </span>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <!-- الوصول للبيانات -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h6><i class="fas fa-database me-2"></i>الوصول للبيانات</h6>
                </div>
                <div class="card-body">
                    <?php foreach ($test_results['data_access'] as $table => $info): ?>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>
                                <?php echo ucfirst($table); ?>
                                <?php if ($info['accessible']): ?>
                                    <small class="text-muted">(<?php echo $info['count']; ?> سجل)</small>
                                <?php endif; ?>
                            </span>
                            <span class="badge bg-<?php echo $info['accessible'] ? 'success' : 'danger'; ?>">
                                <?php echo $info['accessible'] ? 'متاح' : 'مرفوض'; ?>
                            </span>
                        </div>
                        <?php if (!$info['accessible'] && $info['error']): ?>
                            <div class="small text-danger mb-2">خطأ: <?php echo $info['error']; ?></div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- الوصول للصفحات -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h6><i class="fas fa-file me-2"></i>الوصول للصفحات</h6>
                </div>
                <div class="card-body">
                    <?php foreach ($test_results['page_access'] as $page => $accessible): ?>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>صفحة <?php echo ucfirst($page); ?></span>
                            <span class="badge bg-<?php echo $accessible ? 'success' : 'danger'; ?>">
                                <?php echo $accessible ? 'متاح' : 'مرفوض'; ?>
                            </span>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- ملخص النتائج -->
    <div class="card mt-4">
        <div class="card-header bg-primary text-white">
            <h5><i class="fas fa-chart-pie me-2"></i>ملخص النتائج</h5>
        </div>
        <div class="card-body">
            <?php
            $total_permissions = count($test_results['custom_permissions']);
            $granted_permissions = count(array_filter($test_results['custom_permissions'], function($p) { return $p['has_permission']; }));
            
            $total_data_access = count($test_results['data_access']);
            $accessible_data = count(array_filter($test_results['data_access'], function($d) { return $d['accessible']; }));
            
            $total_pages = count($test_results['page_access']);
            $accessible_pages = count(array_filter($test_results['page_access']));
            
            $has_staff_role = $test_results['basic_permissions']['staff'];
            ?>
            
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-<?php echo $has_staff_role ? 'success' : 'danger'; ?>">
                            <?php echo $has_staff_role ? '✅' : '❌'; ?>
                        </h4>
                        <p class="mb-0">دور الإداري</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-<?php echo $granted_permissions > 0 ? 'success' : 'danger'; ?>">
                            <?php echo $granted_permissions; ?>/<?php echo $total_permissions; ?>
                        </h4>
                        <p class="mb-0">الصلاحيات المخصصة</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-<?php echo $accessible_data == $total_data_access ? 'success' : 'warning'; ?>">
                            <?php echo $accessible_data; ?>/<?php echo $total_data_access; ?>
                        </h4>
                        <p class="mb-0">الوصول للبيانات</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-<?php echo $accessible_pages == $total_pages ? 'success' : 'warning'; ?>">
                            <?php echo $accessible_pages; ?>/<?php echo $total_pages; ?>
                        </h4>
                        <p class="mb-0">الوصول للصفحات</p>
                    </div>
                </div>
            </div>

            <div class="alert alert-<?php echo ($has_staff_role && $granted_permissions > 0 && $accessible_pages == $total_pages) ? 'success' : 'warning'; ?> mt-3">
                <h6><i class="fas fa-info-circle me-2"></i>التقييم العام:</h6>
                <?php if ($has_staff_role && $granted_permissions > 0 && $accessible_pages == $total_pages): ?>
                    <p class="mb-0">✅ <strong>ممتاز!</strong> المستخدم لديه جميع الصلاحيات المطلوبة ويمكنه الوصول لجميع البيانات والصفحات.</p>
                <?php elseif (!$has_staff_role): ?>
                    <p class="mb-0">❌ <strong>مشكلة:</strong> المستخدم ليس لديه دور "staff" (إداري). يجب تحديث الدور أولاً.</p>
                <?php elseif ($granted_permissions == 0): ?>
                    <p class="mb-0">⚠️ <strong>تحذير:</strong> المستخدم ليس لديه صلاحيات مخصصة. يجب منح الصلاحيات المطلوبة.</p>
                <?php else: ?>
                    <p class="mb-0">⚠️ <strong>تحذير:</strong> بعض الصلاحيات أو الصفحات غير متاحة. قد تحتاج لمراجعة الإعدادات.</p>
                <?php endif; ?>
            </div>

            <div class="text-center mt-3">
                <a href="setup_staff_permissions.php" class="btn btn-success me-2">
                    <i class="fas fa-cogs me-2"></i>إعداد صلاحيات الإداري
                </a>
                <a href="fix_page_permissions.php" class="btn btn-warning me-2">
                    <i class="fas fa-file-code me-2"></i>إصلاح صلاحيات الصفحات
                </a>
                <button class="btn btn-info" onclick="window.location.reload()">
                    <i class="fas fa-redo me-2"></i>إعادة الاختبار
                </button>
            </div>
        </div>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
