<?php
/**
 * تعديل الغياب بالخصم
 * Edit Absence with Deduction
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

$user_id = $_SESSION['user_id'];
$success_message = '';
$error_message = '';

// التحقق من وجود معرف السجل
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: manage_absences.php');
    exit();
}

$absence_id = intval($_GET['id']);

// جلب بيانات السجل
$stmt = $conn->prepare("
    SELECT 
        sad.id,
        sad.user_id,
        sad.absence_date,
        sad.reason,
        sad.deduction_amount,
        sad.deduction_type,
        u.full_name,
        u.role
    FROM staff_absences_with_deduction sad
    JOIN users u ON sad.user_id = u.id
    WHERE sad.id = ?
");

$stmt->bind_param("i", $absence_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header('Location: manage_absences.php');
    exit();
}

$absence = $result->fetch_assoc();

// معالجة التعديل
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $edit_date = clean_input($_POST['absence_date']);
    $edit_reason = clean_input($_POST['reason']);
    $edit_amount = floatval($_POST['deduction_amount']);
    $edit_type = clean_input($_POST['deduction_type']);
    
    if (!empty($edit_date) && $edit_amount > 0) {
        try {
            $conn->begin_transaction();
            
            // تحديث سجل الغياب بالخصم
            $update_stmt = $conn->prepare("
                UPDATE staff_absences_with_deduction 
                SET absence_date = ?, reason = ?, deduction_amount = ?, deduction_type = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $update_stmt->bind_param("ssdsi", $edit_date, $edit_reason, $edit_amount, $edit_type, $absence_id);
            
            if ($update_stmt->execute()) {
                // تحديث سجل الحضور المرتبط
                $note = '🔴 غياب بالخصم - مبلغ الخصم: ' . $edit_amount . ' ج.م';
                if (!empty($edit_reason)) {
                    $note .= ' - السبب: ' . $edit_reason;
                }
                $note .= ' - تم التعديل: ' . date('Y-m-d H:i:s');
                
                if ($absence['role'] === 'teacher') {
                    // تحديث حضور المعلم
                    $update_teacher_attendance = $conn->prepare("
                        UPDATE teacher_attendance ta
                        JOIN teachers t ON ta.teacher_id = t.id
                        SET ta.attendance_date = ?, ta.notes = ?, ta.updated_at = NOW()
                        WHERE t.user_id = ? AND ta.status = 'absence_with_deduction'
                    ");
                    $update_teacher_attendance->bind_param("ssi", $edit_date, $note, $absence['user_id']);
                    $update_teacher_attendance->execute();
                } else {
                    // تحديث حضور الإداري
                    $update_admin_attendance = $conn->prepare("
                        UPDATE admin_attendance 
                        SET attendance_date = ?, notes = ?, updated_at = NOW()
                        WHERE admin_id = ? AND status = 'absence_with_deduction'
                    ");
                    $update_admin_attendance->bind_param("ssi", $edit_date, $note, $absence['user_id']);
                    $update_admin_attendance->execute();
                }
                
                $conn->commit();
                $success_message = 'تم تعديل سجل الغياب بالخصم بنجاح';
                
                // تحديث البيانات المعروضة
                $absence['absence_date'] = $edit_date;
                $absence['reason'] = $edit_reason;
                $absence['deduction_amount'] = $edit_amount;
                $absence['deduction_type'] = $edit_type;
                
            } else {
                throw new Exception('فشل في تحديث السجل');
            }
        } catch (Exception $e) {
            $conn->rollback();
            $error_message = 'خطأ في التعديل: ' . $e->getMessage();
        }
    } else {
        $error_message = 'يرجى ملء جميع الحقول المطلوبة بشكل صحيح';
    }
}

// تضمين الهيدر بعد معالجة جميع عمليات إعادة التوجيه
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-edit text-primary me-2"></i>
                    تعديل الغياب بالخصم
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="manage_absences.php">إدارة الغياب</a></li>
                        <li class="breadcrumb-item active">تعديل</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user me-2"></i>
                        تعديل غياب الموظف: <?php echo htmlspecialchars($absence['full_name']); ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($success_message)): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($error_message)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST" class="needs-validation" novalidate>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="absence_date" class="form-label">
                                        <i class="fas fa-calendar me-1"></i>
                                        تاريخ الغياب <span class="text-danger">*</span>
                                    </label>
                                    <input type="date" 
                                           class="form-control" 
                                           id="absence_date" 
                                           name="absence_date" 
                                           value="<?php echo $absence['absence_date']; ?>" 
                                           required>
                                    <div class="invalid-feedback">
                                        يرجى اختيار تاريخ الغياب
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="deduction_amount" class="form-label">
                                        <i class="fas fa-money-bill me-1"></i>
                                        مبلغ الخصم (ج.م) <span class="text-danger">*</span>
                                    </label>
                                    <input type="number" 
                                           class="form-control" 
                                           id="deduction_amount" 
                                           name="deduction_amount" 
                                           value="<?php echo $absence['deduction_amount']; ?>"
                                           step="0.01" 
                                           min="0" 
                                           required>
                                    <div class="invalid-feedback">
                                        يرجى إدخال مبلغ الخصم
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="deduction_type" class="form-label">
                                        <i class="fas fa-tags me-1"></i>
                                        نوع الخصم <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="deduction_type" name="deduction_type" required>
                                        <option value="">اختر نوع الخصم</option>
                                        <option value="daily_wage" <?php echo $absence['deduction_type'] === 'daily_wage' ? 'selected' : ''; ?>>خصم يومي كامل</option>
                                        <option value="hourly_wage" <?php echo $absence['deduction_type'] === 'hourly_wage' ? 'selected' : ''; ?>>خصم بالساعة</option>
                                        <option value="fixed_amount" <?php echo $absence['deduction_type'] === 'fixed_amount' ? 'selected' : ''; ?>>مبلغ ثابت</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        يرجى اختيار نوع الخصم
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-info-circle me-1"></i>
                                        معلومات الموظف
                                    </label>
                                    <div class="form-control-plaintext bg-light p-2 rounded">
                                        <strong>الاسم:</strong> <?php echo htmlspecialchars($absence['full_name']); ?><br>
                                        <strong>الدور:</strong> <?php echo $absence['role'] === 'teacher' ? 'معلم' : 'إداري'; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="reason" class="form-label">
                                <i class="fas fa-comment me-1"></i>
                                سبب الغياب
                            </label>
                            <textarea class="form-control" 
                                      id="reason" 
                                      name="reason" 
                                      rows="3" 
                                      placeholder="اكتب سبب الغياب (اختياري)"><?php echo htmlspecialchars($absence['reason'] ?? ''); ?></textarea>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="manage_absences.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة للقائمة
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ التعديلات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?php require_once '../includes/footer.php'; ?>
