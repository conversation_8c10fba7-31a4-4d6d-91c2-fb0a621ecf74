<?php
/**
 * تعديل الإداري
 * Edit Administrator
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    redirect_to('../dashboard/');
}

$success_message = '';
$error_message = '';

// التحقق من وجود معرف الإداري
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: index.php');
    exit();
}

$administrator_id = intval($_GET['id']);

// جلب بيانات الإداري
$admin_stmt = $conn->prepare("
    SELECT a.*, u.full_name, u.username, u.email, u.phone as user_phone, u.status as user_status
    FROM staff a
    LEFT JOIN users u ON a.user_id = u.id
    WHERE a.id = ? AND u.role = 'staff'
");

$admin_stmt->bind_param("i", $administrator_id);
$admin_stmt->execute();
$admin_result = $admin_stmt->get_result();

if ($admin_result->num_rows === 0) {
    header('Location: index.php');
    exit();
}

$administrator = $admin_result->fetch_assoc();

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // جمع البيانات
    $full_name = trim($_POST['full_name'] ?? '');
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $employee_id = trim($_POST['employee_id'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $address = trim($_POST['address'] ?? '');
    $date_of_birth = trim($_POST['date_of_birth'] ?? '');
    $gender = trim($_POST['gender'] ?? '');
    $nationality = trim($_POST['nationality'] ?? '');
    $national_id = trim($_POST['national_id'] ?? '');
    $qualification = trim($_POST['qualification'] ?? '');
    $position = trim($_POST['position'] ?? '');
    $department = trim($_POST['department'] ?? '');
    $experience_years = intval($_POST['experience_years'] ?? 0);
    $hire_date = trim($_POST['hire_date'] ?? '');
    $salary = floatval($_POST['salary'] ?? 0);
    $bank_account = trim($_POST['bank_account'] ?? '');
    $emergency_contact_name = trim($_POST['emergency_contact_name'] ?? '');
    $emergency_contact_phone = trim($_POST['emergency_contact_phone'] ?? '');
    $status = trim($_POST['status'] ?? 'active');
    $user_status = trim($_POST['user_status'] ?? 'active');

    // التحقق من صحة البيانات
    $errors = [];

    if (empty($full_name)) {
        $errors[] = __('full_name_required');
    }

    if (empty($username)) {
        $errors[] = __('username_required');
    } elseif (strlen($username) < 3) {
        $errors[] = __('username_min_length');
    }

    if (empty($email)) {
        $errors[] = __('email_required');
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = __('invalid_email');
    }

    // التحقق من عدم تكرار البيانات (باستثناء السجل الحالي)
    if (!empty($username)) {
        $check_stmt = $conn->prepare("SELECT id FROM users WHERE username = ? AND id != ?");
        $check_stmt->bind_param("si", $username, $administrator['user_id']);
        $check_stmt->execute();
        if ($check_stmt->get_result()->num_rows > 0) {
            $errors[] = __('username_exists');
        }
    }

    if (!empty($email)) {
        $check_stmt = $conn->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
        $check_stmt->bind_param("si", $email, $administrator['user_id']);
        $check_stmt->execute();
        if ($check_stmt->get_result()->num_rows > 0) {
            $errors[] = __('email_exists');
        }
    }

    if (!empty($employee_id)) {
        $check_stmt = $conn->prepare("SELECT id FROM staff WHERE employee_id = ? AND id != ?");
        $check_stmt->bind_param("si", $employee_id, $administrator_id);
        $check_stmt->execute();
        if ($check_stmt->get_result()->num_rows > 0) {
            $errors[] = __('employee_id_exists');
        }
    }

    if (!empty($national_id)) {
        $check_stmt = $conn->prepare("SELECT id FROM staff WHERE national_id = ? AND id != ?");
        $check_stmt->bind_param("si", $national_id, $administrator_id);
        $check_stmt->execute();
        if ($check_stmt->get_result()->num_rows > 0) {
            $errors[] = __('national_id_exists');
        }
    }

    // التحقق من كلمة المرور
    if (!empty($password)) {
        if (strlen($password) < 6) {
            $errors[] = __('password_too_short');
        }
        if ($password !== $confirm_password) {
            $errors[] = __('passwords_do_not_match');
        }
    }

    // إذا لم توجد أخطاء، قم بتحديث البيانات
    if (empty($errors)) {
        try {
            $conn->begin_transaction();

            // تحديث بيانات المستخدم
            if (!empty($password)) {
                // تحديث مع كلمة المرور
                $hashed_password = hash_password($password);
                $user_stmt = $conn->prepare("
                    UPDATE users
                    SET username = ?, email = ?, full_name = ?, phone = ?, password = ?, status = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $user_stmt->bind_param("ssssssi", $username, $email, $full_name, $phone, $hashed_password, $user_status, $administrator['user_id']);
            } else {
                // تحديث بدون كلمة المرور
                $user_stmt = $conn->prepare("
                    UPDATE users
                    SET username = ?, email = ?, full_name = ?, phone = ?, status = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $user_stmt->bind_param("sssssi", $username, $email, $full_name, $phone, $user_status, $administrator['user_id']);
            }
            $user_stmt->execute();

            // تحديث بيانات الإداري
            $admin_stmt = $conn->prepare("
                UPDATE staff
                SET employee_id = ?, phone = ?, address = ?, date_of_birth = ?, gender = ?, nationality = ?,
                    national_id = ?, qualification = ?, position = ?, department = ?, experience_years = ?,
                    hire_date = ?, salary = ?, bank_account = ?, emergency_contact_name = ?,
                    emergency_contact_phone = ?, status = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $admin_stmt->bind_param(
                "sssssssssissdsssi",
                $employee_id, $phone, $address, $date_of_birth, $gender, $nationality, $national_id,
                $qualification, $position, $department, $experience_years, $hire_date, $salary,
                $bank_account, $emergency_contact_name, $emergency_contact_phone, $status, $administrator_id
            );
            $admin_stmt->execute();

            $conn->commit();

            // تسجيل النشاط
            log_activity($_SESSION['user_id'], 'edit_administrator', 'administrators', $administrator_id, 
                $administrator, [
                    'administrator_name' => $full_name,
                    'employee_id' => $employee_id
                ]
            );

            $_SESSION['success_message'] = __('administrator_updated_successfully');
            header('Location: view.php?id=' . $administrator_id);
            exit();

        } catch (Exception $e) {
            $conn->rollback();
            log_error("Error updating administrator: " . $e->getMessage());
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                $error_message = __('error_occurred') . '<br>' . htmlspecialchars($e->getMessage());
            } else {
                $error_message = __('error_occurred');
            }
        }
    } else {
        $error_message = implode('<br>', $errors);
    }
} else {
    // تعبئة النموذج بالبيانات الحالية
    $full_name = $administrator['full_name'];
    $username = $administrator['username'];
    $email = $administrator['email'];
    $employee_id = $administrator['employee_id'];
    $phone = $administrator['phone'];
    $address = $administrator['address'];
    $date_of_birth = $administrator['date_of_birth'];
    $gender = $administrator['gender'];
    $nationality = $administrator['nationality'];
    $national_id = $administrator['national_id'];
    $qualification = $administrator['qualification'];
    $position = $administrator['position'];
    $department = $administrator['department'];
    $experience_years = $administrator['experience_years'];
    $hire_date = $administrator['hire_date'];
    $salary = $administrator['salary'];
    $bank_account = $administrator['bank_account'];
    $emergency_contact_name = $administrator['emergency_contact_name'];
    $emergency_contact_phone = $administrator['emergency_contact_phone'];
    $status = $administrator['status'];
    $user_status = $administrator['user_status'];
}

$page_title = 'تعديل الإداري: ' . ($administrator['full_name'] ?? $administrator['username']);
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-user-edit text-primary me-2"></i>
                تعديل الإداري
            </h2>
            <p class="text-muted mb-0"><?php echo htmlspecialchars($administrator['full_name'] ?? $administrator['username']); ?></p>
        </div>
        <div>
            <a href="view.php?id=<?php echo $administrator_id; ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة للتفاصيل
            </a>
        </div>
    </div>

    <!-- Messages -->
    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Edit Administrator Form -->
    <div class="card">
        <div class="card-body">
            <form method="POST" class="needs-validation" novalidate>
                <div class="row">
                    <!-- معلومات الحساب -->
                    <div class="col-md-6">
                        <h5 class="mb-3">
                            <i class="fas fa-user me-2"></i><?php echo __('account_information'); ?>
                        </h5>
                        
                        <div class="mb-3">
                            <label for="full_name" class="form-label"><?php echo __('full_name'); ?> <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="full_name" name="full_name" 
                                   value="<?php echo htmlspecialchars($full_name ?? ''); ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="username" class="form-label"><?php echo __('username'); ?> <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="username" name="username" 
                                   value="<?php echo htmlspecialchars($username ?? ''); ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label"><?php echo __('email'); ?> <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" name="email"
                                   value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label"><?php echo __('new_password'); ?></label>
                            <input type="password" class="form-control" id="password" name="password"
                                   placeholder="<?php echo __('leave_empty_to_keep_current'); ?>">
                            <div class="form-text"><?php echo __('leave_empty_to_keep_current_password'); ?></div>
                        </div>

                        <div class="mb-3">
                            <label for="confirm_password" class="form-label"><?php echo __('confirm_new_password'); ?></label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password"
                                   placeholder="<?php echo __('leave_empty_to_keep_current'); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="user_status" class="form-label"><?php echo __('account_status'); ?></label>
                            <select class="form-select" id="user_status" name="user_status">
                                <option value="active" <?php echo ($user_status ?? 'active') === 'active' ? 'selected' : ''; ?>><?php echo __('active'); ?></option>
                                <option value="inactive" <?php echo ($user_status ?? '') === 'inactive' ? 'selected' : ''; ?>><?php echo __('inactive'); ?></option>
                                <option value="suspended" <?php echo ($user_status ?? '') === 'suspended' ? 'selected' : ''; ?>><?php echo __('suspended'); ?></option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="status" class="form-label"><?php echo __('employment_status'); ?></label>
                            <select class="form-select" id="status" name="status">
                                <option value="active" <?php echo ($status ?? 'active') === 'active' ? 'selected' : ''; ?>><?php echo __('active'); ?></option>
                                <option value="inactive" <?php echo ($status ?? '') === 'inactive' ? 'selected' : ''; ?>><?php echo __('inactive'); ?></option>
                                <option value="on_leave" <?php echo ($status ?? '') === 'on_leave' ? 'selected' : ''; ?>><?php echo __('on_leave'); ?></option>
                                <option value="terminated" <?php echo ($status ?? '') === 'terminated' ? 'selected' : ''; ?>><?php echo __('terminated'); ?></option>
                            </select>
                        </div>
                    </div>

                    <!-- المعلومات الشخصية -->
                    <div class="col-md-6">
                        <h5 class="mb-3">
                            <i class="fas fa-id-card me-2"></i><?php echo __('personal_information'); ?>
                        </h5>

                        <div class="mb-3">
                            <label for="employee_id" class="form-label"><?php echo __('employee_id'); ?></label>
                            <input type="text" class="form-control" id="employee_id" name="employee_id" 
                                   value="<?php echo htmlspecialchars($employee_id ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="national_id" class="form-label"><?php echo __('national_id'); ?></label>
                            <input type="text" class="form-control" id="national_id" name="national_id" 
                                   value="<?php echo htmlspecialchars($national_id ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="phone" class="form-label"><?php echo __('phone'); ?></label>
                            <input type="text" class="form-control" id="phone" name="phone" 
                                   value="<?php echo htmlspecialchars($phone ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="date_of_birth" class="form-label"><?php echo __('date_of_birth'); ?></label>
                            <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" 
                                   value="<?php echo htmlspecialchars($date_of_birth ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="gender" class="form-label"><?php echo __('gender'); ?></label>
                            <select class="form-select" id="gender" name="gender">
                                <option value=""><?php echo __('select_gender'); ?></option>
                                <option value="male" <?php echo ($gender ?? '') === 'male' ? 'selected' : ''; ?>><?php echo __('male'); ?></option>
                                <option value="female" <?php echo ($gender ?? '') === 'female' ? 'selected' : ''; ?>><?php echo __('female'); ?></option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="nationality" class="form-label"><?php echo __('nationality'); ?></label>
                            <input type="text" class="form-control" id="nationality" name="nationality" 
                                   value="<?php echo htmlspecialchars($nationality ?? ''); ?>">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- المعلومات الوظيفية -->
                    <div class="col-md-6">
                        <h5 class="mb-3">
                            <i class="fas fa-briefcase me-2"></i><?php echo __('job_information'); ?>
                        </h5>

                        <div class="mb-3">
                            <label for="position" class="form-label"><?php echo __('position'); ?></label>
                            <input type="text" class="form-control" id="position" name="position" 
                                   value="<?php echo htmlspecialchars($position ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="department" class="form-label"><?php echo __('department'); ?></label>
                            <input type="text" class="form-control" id="department" name="department" 
                                   value="<?php echo htmlspecialchars($department ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="qualification" class="form-label"><?php echo __('qualification'); ?></label>
                            <input type="text" class="form-control" id="qualification" name="qualification" 
                                   value="<?php echo htmlspecialchars($qualification ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="experience_years" class="form-label"><?php echo __('experience_years'); ?></label>
                            <input type="number" class="form-control" id="experience_years" name="experience_years" 
                                   value="<?php echo htmlspecialchars($experience_years ?? 0); ?>" min="0">
                        </div>

                        <div class="mb-3">
                            <label for="hire_date" class="form-label"><?php echo __('hire_date'); ?></label>
                            <input type="date" class="form-control" id="hire_date" name="hire_date" 
                                   value="<?php echo htmlspecialchars($hire_date ?? ''); ?>">
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="col-md-6">
                        <h5 class="mb-3">
                            <i class="fas fa-info-circle me-2"></i><?php echo __('additional_information'); ?>
                        </h5>

                        <div class="mb-3">
                            <label for="salary" class="form-label"><?php echo __('salary'); ?></label>
                            <input type="number" class="form-control" id="salary" name="salary" 
                                   value="<?php echo htmlspecialchars($salary ?? 0); ?>" min="0" step="0.01">
                        </div>

                        <div class="mb-3">
                            <label for="bank_account" class="form-label"><?php echo __('bank_account'); ?></label>
                            <input type="text" class="form-control" id="bank_account" name="bank_account" 
                                   value="<?php echo htmlspecialchars($bank_account ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="emergency_contact_name" class="form-label"><?php echo __('emergency_contact_name'); ?></label>
                            <input type="text" class="form-control" id="emergency_contact_name" name="emergency_contact_name" 
                                   value="<?php echo htmlspecialchars($emergency_contact_name ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="emergency_contact_phone" class="form-label"><?php echo __('emergency_contact_phone'); ?></label>
                            <input type="text" class="form-control" id="emergency_contact_phone" name="emergency_contact_phone" 
                                   value="<?php echo htmlspecialchars($emergency_contact_phone ?? ''); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label"><?php echo __('address'); ?></label>
                            <textarea class="form-control" id="address" name="address" rows="3"><?php echo htmlspecialchars($address ?? ''); ?></textarea>
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="row">
                    <div class="col-12">
                        <hr>
                        <div class="d-flex justify-content-end gap-2">
                            <a href="view.php?id=<?php echo $administrator_id; ?>" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i><?php echo __('save_changes'); ?>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
