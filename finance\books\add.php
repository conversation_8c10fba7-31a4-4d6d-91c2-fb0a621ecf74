<?php
/**
 * صفحة إضافة كتاب جديد
 * Add New Book Page
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    header('Location: ../../dashboard/');
    exit();
}

$errors = [];
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'])) {
        die('CSRF token validation failed.');
    }

    // Sanitize and validate inputs
    $book_title = clean_input($_POST['book_title']);
    $book_title_en = clean_input($_POST['book_title_en']);
    $subject_id = filter_input(INPUT_POST, 'subject_id', FILTER_VALIDATE_INT);
    $grade_level = clean_input($_POST['grade_level']);
    $isbn = clean_input($_POST['isbn']);
    $publisher = clean_input($_POST['publisher']);
    $academic_year = clean_input($_POST['academic_year']);
    $price = filter_input(INPUT_POST, 'price', FILTER_VALIDATE_FLOAT);
    $stock_quantity = filter_input(INPUT_POST, 'stock_quantity', FILTER_VALIDATE_INT);
    $status = clean_input($_POST['status']);

    // Basic validation
    if (empty($book_title)) $errors[] = __('book_title_required');
    if ($subject_id === false) $errors[] = __('invalid_subject');
    if (empty($grade_level)) $errors[] = __('grade_level_required');
    if ($price === false || $price < 0) $errors[] = __('invalid_price');
    if ($stock_quantity === false || $stock_quantity < 0) $errors[] = __('invalid_stock_quantity');

    if (empty($errors)) {
        $stmt = $conn->prepare("INSERT INTO books (book_title, book_title_en, subject_id, grade_level, isbn, publisher, academic_year, price, stock_quantity, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())");
        $stmt->bind_param('ssisssdiss', $book_title, $book_title_en, $subject_id, $grade_level, $isbn, $publisher, $academic_year, $price, $stock_quantity, $status);

        if ($stmt->execute()) {
            $_SESSION['success_message'] = __('book_added_successfully');
            header('Location: index.php');
            exit();
        } else {
            $errors[] = __('error_adding_book') . ': ' . $stmt->error;
        }
    }
}

// Fetch data for dropdowns
$subjects = $conn->query("SELECT id, subject_name FROM subjects WHERE status = 'active' ORDER BY subject_name");
$grade_levels = $conn->query("SELECT DISTINCT grade_level FROM classes ORDER BY grade_level");

include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('add_new_book'); ?></h1>
            <p class="text-muted"><?php echo __('fill_form_to_add_book'); ?></p>
        </div>
        <div>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_books'); ?>
            </a>
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-body">
            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <?php foreach ($errors as $error): ?>
                        <p class="mb-0"><?php echo $error; ?></p>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>

            <form action="add.php" method="POST">
                <?php echo generate_csrf_token(); ?>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="book_title" class="form-label"><?php echo __('book_title_ar'); ?> <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="book_title" name="book_title" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="book_title_en" class="form-label"><?php echo __('book_title_en'); ?></label>
                        <input type="text" class="form-control" id="book_title_en" name="book_title_en">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="subject_id" class="form-label"><?php echo __('subject'); ?> <span class="text-danger">*</span></label>
                        <select class="form-select" id="subject_id" name="subject_id" required>
                            <option value=""><?php echo __('select_subject'); ?></option>
                            <?php while($row = $subjects->fetch_assoc()): ?>
                                <option value="<?php echo $row['id']; ?>"><?php echo htmlspecialchars($row['subject_name']); ?></option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="grade_level" class="form-label"><?php echo __('grade_level'); ?> <span class="text-danger">*</span></label>
                        <select class="form-select" id="grade_level" name="grade_level" required>
                            <option value=""><?php echo __('select_grade'); ?></option>
                            <?php while($row = $grade_levels->fetch_assoc()): ?>
                                <option value="<?php echo $row['grade_level']; ?>"><?php echo htmlspecialchars($row['grade_level']); ?></option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="isbn" class="form-label"><?php echo __('isbn'); ?></label>
                        <input type="text" class="form-control" id="isbn" name="isbn">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="publisher" class="form-label"><?php echo __('publisher'); ?></label>
                        <input type="text" class="form-control" id="publisher" name="publisher">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="academic_year" class="form-label"><?php echo __('academic_year'); ?></label>
                        <input type="text" class="form-control" id="academic_year" name="academic_year" value="<?php echo get_current_academic_year(); ?>">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="price" class="form-label"><?php echo __('price'); ?> <span class="text-danger">*</span></label>
                        <input type="number" step="0.01" class="form-control" id="price" name="price" required>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="stock_quantity" class="form-label"><?php echo __('stock_quantity'); ?> <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="stock_quantity" name="stock_quantity" required>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="status" class="form-label"><?php echo __('status'); ?></label>
                    <select class="form-select" id="status" name="status">
                        <option value="active"><?php echo __('active'); ?></option>
                        <option value="inactive"><?php echo __('inactive'); ?></option>
                    </select>
                </div>

                <button type="submit" class="btn btn-primary"><?php echo __('add_book'); ?></button>
            </form>
        </div>
    </div>
</div>

<?php include_once '../../includes/footer.php'; ?>
