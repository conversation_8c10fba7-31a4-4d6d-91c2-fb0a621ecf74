<?php
/**
 * نظام الصلاحيات المتقدم
 * Advanced Permissions System
 */

if (!defined('SYSTEM_INIT')) {
    die('Direct access not allowed');
}

/**
 * تعريف الصلاحيات المفصلة
 */
define('DETAILED_PERMISSIONS', [
    // صلاحيات المستخدمين
    'users' => [
        'view' => 'عرض المستخدمين',
        'create' => 'إضافة مستخدم جديد',
        'edit' => 'تعديل المستخدمين',
        'delete' => 'حذف المستخدمين',
        'manage_roles' => 'إدارة الأدوار'
    ],
    
    // صلاحيات الطلاب
    'students' => [
        'view' => 'عرض الطلاب',
        'create' => 'إضافة طالب جديد',
        'edit' => 'تعديل بيانات الطلاب',
        'delete' => 'حذف الطلاب',
        'view_grades' => 'عرض درجات الطلاب',
        'edit_grades' => 'تعديل درجات الطلاب'
    ],
    
    // صلاحيات النظام المالي
    'finance' => [
        'view' => 'عرض البيانات المالية',
        'create' => 'إضافة معاملات مالية',
        'edit' => 'تعديل المعاملات المالية',
        'delete' => 'حذف المعاملات المالية',
        'view_reports' => 'عرض التقارير المالية',
        'manage_fees' => 'إدارة الرسوم',
        'manage_expenses' => 'إدارة المصروفات'
    ],
    
    // صلاحيات الحضور والغياب
    'attendance' => [
        'view' => 'عرض سجلات الحضور',
        'create' => 'تسجيل الحضور',
        'edit' => 'تعديل سجلات الحضور',
        'delete' => 'حذف سجلات الحضور',
        'view_reports' => 'عرض تقارير الحضور'
    ],
    
    // صلاحيات الامتحانات
    'exams' => [
        'view' => 'عرض الامتحانات',
        'create' => 'إنشاء امتحان جديد',
        'edit' => 'تعديل الامتحانات',
        'delete' => 'حذف الامتحانات',
        'grade' => 'تصحيح الامتحانات',
        'view_results' => 'عرض نتائج الامتحانات'
    ],
    
    // صلاحيات التقارير
    'reports' => [
        'view' => 'عرض التقارير',
        'create' => 'إنشاء تقارير جديدة',
        'export' => 'تصدير التقارير',
        'print' => 'طباعة التقارير'
    ],
    
    // صلاحيات التواصل
    'communication' => [
        'view' => 'عرض الرسائل',
        'send' => 'إرسال رسائل',
        'bulk_send' => 'إرسال رسائل جماعية',
        'manage_templates' => 'إدارة قوالب الرسائل'
    ],
    
    // صلاحيات النظام
    'system' => [
        'settings' => 'إعدادات النظام',
        'backup' => 'نسخ احتياطي',
        'restore' => 'استعادة النظام',
        'maintenance' => 'صيانة النظام',
        'logs' => 'عرض سجلات النظام'
    ]
]);

/**
 * تعريف الأدوار وصلاحياتها
 */
define('ROLE_PERMISSIONS', [
    'admin' => [
        'users.*',
        'students.*',
        'finance.*',
        'attendance.*',
        'exams.*',
        'reports.*',
        'communication.*',
        'system.*'
    ],
    
    'financial_manager' => [
        'finance.*',
        'students.view',
        'reports.view',
        'reports.export'
    ],
    
    'teacher' => [
        'students.view',
        'students.view_grades',
        'students.edit_grades',
        'attendance.view',
        'attendance.create',
        'attendance.edit',
        'exams.view',
        'exams.create',
        'exams.edit',
        'exams.grade',
        'reports.view',
        'communication.view',
        'communication.send'
    ],
    
    'staff' => [
        'students.view',
        'attendance.view',
        'reports.view',
        'communication.view'
    ],
    
    'student' => [
        'attendance.view',
        'exams.view',
        'exams.view_results',
        'finance.view'
    ],
    
    'parent' => [
        'students.view',
        'attendance.view',
        'exams.view_results',
        'finance.view',
        'communication.view'
    ]
]);

/**
 * التحقق من صلاحية محددة
 * تم نقل هذه الدالة إلى functions.php لتجنب التكرار
 * This function has been moved to functions.php to avoid duplication
 */

/**
 * التحقق من صلاحيات متعددة (يجب توفر جميعها)
 */
function has_all_permissions($permissions) {
    foreach ($permissions as $permission) {
        if (!has_permission($permission)) {
            return false;
        }
    }
    return true;
}

/**
 * التحقق من صلاحيات متعددة (يكفي توفر واحدة منها)
 */
function has_any_permission($permissions) {
    foreach ($permissions as $permission) {
        if (has_permission($permission)) {
            return true;
        }
    }
    return false;
}

/**
 * الحصول على جميع صلاحيات المستخدم الحالي
 */
function get_user_permissions() {
    if (!is_logged_in()) {
        return [];
    }
    
    $user_role = $_SESSION['role'] ?? '';
    return ROLE_PERMISSIONS[$user_role] ?? [];
}

/**
 * التحقق من الصلاحية مع إعادة توجيه في حالة عدم التوفر
 */
function require_permission($permission, $redirect_url = '../dashboard/') {
    if (!has_permission($permission)) {
        header('Location: ' . $redirect_url);
        exit();
    }
}

/**
 * عرض رسالة خطأ للصلاحيات
 */
function permission_denied($message = 'غير مسموح لك بالوصول لهذه الصفحة') {
    http_response_code(403);
    die('<div class="alert alert-danger text-center mt-5">
            <i class="fas fa-lock fa-3x mb-3"></i>
            <h4>وصول مرفوض</h4>
            <p>' . htmlspecialchars($message) . '</p>
            <a href="../dashboard/" class="btn btn-primary">العودة للرئيسية</a>
         </div>');
}
?>
