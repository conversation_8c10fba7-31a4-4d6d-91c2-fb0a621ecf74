<?php
/**
 * إدارة المصروفات اليومية - الصفحة الرئيسية
 * Daily Expenses Management - Main Page
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    header('Location: ../../dashboard/');
    exit();
}

// معالجة الفلترة
$date_filter = $_GET['date'] ?? date('Y-m-d');
$category_filter = $_GET['category'] ?? '';
$status_filter = $_GET['status'] ?? '';

// بناء شروط البحث
$where_conditions = ["1=1"];
$params = [];
$types = "";

if ($date_filter) {
    $where_conditions[] = "de.expense_date = ?";
    $params[] = $date_filter;
    $types .= "s";
}

if ($category_filter) {
    $where_conditions[] = "de.category_id = ?";
    $params[] = $category_filter;
    $types .= "i";
}

if ($status_filter) {
    $where_conditions[] = "de.status = ?";
    $params[] = $status_filter;
    $types .= "s";
}

$where_clause = implode(" AND ", $where_conditions);

// جلب إحصائيات اليوم
$today_stats_query = "
    SELECT 
        COUNT(*) as total_expenses,
        SUM(amount) as total_amount,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
        COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_count,
        SUM(CASE WHEN status = 'approved' THEN amount ELSE 0 END) as approved_amount
    FROM daily_expenses 
    WHERE expense_date = ?
";

$today_stats_stmt = $conn->prepare($today_stats_query);
$today_stats_stmt->bind_param("s", $date_filter);
$today_stats_stmt->execute();
$today_stats = $today_stats_stmt->get_result()->fetch_assoc();

// تنظيف البيانات
$today_stats = [
    'total_expenses' => intval($today_stats['total_expenses'] ?? 0),
    'total_amount' => floatval($today_stats['total_amount'] ?? 0),
    'pending_count' => intval($today_stats['pending_count'] ?? 0),
    'approved_count' => intval($today_stats['approved_count'] ?? 0),
    'approved_amount' => floatval($today_stats['approved_amount'] ?? 0)
];

// جلب المصروفات
$expenses_query = "
    SELECT 
        de.*,
        ec.category_name,
        ec.icon,
        ec.color,
        u.full_name as created_by_name,
        au.full_name as approved_by_name
    FROM daily_expenses de
    JOIN expense_categories ec ON de.category_id = ec.id
    LEFT JOIN users u ON de.created_by = u.id
    LEFT JOIN users au ON de.approved_by = au.id
    WHERE $where_clause
    ORDER BY de.expense_date DESC, de.created_at DESC
";

$expenses_stmt = $conn->prepare($expenses_query);
if ($types) {
    $expenses_stmt->bind_param($types, ...$params);
}
$expenses_stmt->execute();
$expenses_result = $expenses_stmt->get_result();

// جلب الفئات للفلتر
$categories_query = "SELECT id, category_name FROM expense_categories WHERE is_active = 1 ORDER BY category_name";
$categories_result = $conn->query($categories_query);

$page_title = 'إدارة المصروفات اليومية';
include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-money-bill-wave me-2"></i>إدارة المصروفات اليومية
            </h1>
            <p class="text-muted mb-0">تسجيل ومتابعة المصروفات اليومية للمدرسة</p>
        </div>
        <div>
            <a href="add.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة مصروف جديد
            </a>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-info dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-tools me-2"></i>أدوات إضافية
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="categories.php">
                        <i class="fas fa-tags me-2"></i>إدارة الفئات
                    </a></li>
                    <li><a class="dropdown-item" href="reports.php">
                        <i class="fas fa-chart-bar me-2"></i>التقارير
                    </a></li>
                    <li><a class="dropdown-item" href="daily_summary.php">
                        <i class="fas fa-calendar-day me-2"></i>الملخص اليومي
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="navigation_guide.php">
                        <i class="fas fa-map-signs me-2"></i>دليل التنقل
                    </a></li>
                    <li><a class="dropdown-item" href="../index.php">
                        <i class="fas fa-arrow-left me-2"></i>العودة للمالية
                    </a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي المصروفات</h6>
                            <h3 class="mb-0"><?php echo $today_stats['total_expenses']; ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-list fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">المبلغ الإجمالي</h6>
                            <h3 class="mb-0"><?php echo format_currency($today_stats['total_amount']); ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-money-bill fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">في انتظار الموافقة</h6>
                            <h3 class="mb-0"><?php echo $today_stats['pending_count']; ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">المعتمدة</h6>
                            <h3 class="mb-0"><?php echo format_currency($today_stats['approved_amount']); ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="card mb-4">
        <div class="card-header">
            <h6 class="mb-0">
                <i class="fas fa-filter me-2"></i>فلاتر البحث
            </h6>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">التاريخ</label>
                    <input type="date" class="form-control" name="date" value="<?php echo htmlspecialchars($date_filter); ?>">
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">الفئة</label>
                    <select class="form-select" name="category">
                        <option value="">جميع الفئات</option>
                        <?php while ($category = $categories_result->fetch_assoc()): ?>
                            <option value="<?php echo $category['id']; ?>" 
                                    <?php echo ($category_filter == $category['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($category['category_name']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">الحالة</label>
                    <select class="form-select" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="pending" <?php echo ($status_filter == 'pending') ? 'selected' : ''; ?>>في انتظار الموافقة</option>
                        <option value="approved" <?php echo ($status_filter == 'approved') ? 'selected' : ''; ?>>معتمد</option>
                        <option value="rejected" <?php echo ($status_filter == 'rejected') ? 'selected' : ''; ?>>مرفوض</option>
                        <option value="paid" <?php echo ($status_filter == 'paid') ? 'selected' : ''; ?>>مدفوع</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>بحث
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- قائمة المصروفات -->
    <div class="card">
        <div class="card-header">
            <h6 class="mb-0">
                <i class="fas fa-list me-2"></i>قائمة المصروفات
                <?php if ($date_filter): ?>
                    - <?php echo date('d/m/Y', strtotime($date_filter)); ?>
                <?php endif; ?>
            </h6>
        </div>
        <div class="card-body">
            <?php if ($expenses_result->num_rows > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>التاريخ</th>
                                <th>الفئة</th>
                                <th>الوصف</th>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>الحالة</th>
                                <th>المسجل بواسطة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($expense = $expenses_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo date('d/m/Y', strtotime($expense['expense_date'])); ?></td>
                                    <td>
                                        <span class="badge" style="background-color: <?php echo $expense['color']; ?>">
                                            <i class="<?php echo $expense['icon']; ?> me-1"></i>
                                            <?php echo htmlspecialchars($expense['category_name']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="fw-bold"><?php echo htmlspecialchars($expense['description']); ?></div>
                                        <?php if ($expense['vendor_name']): ?>
                                            <small class="text-muted">المورد: <?php echo htmlspecialchars($expense['vendor_name']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td class="fw-bold"><?php echo format_currency($expense['amount']); ?></td>
                                    <td>
                                        <?php
                                        $payment_methods = [
                                            'cash' => ['نقدي', 'success'],
                                            'bank_transfer' => ['تحويل بنكي', 'info'],
                                            'credit_card' => ['بطاقة ائتمان', 'warning'],
                                            'check' => ['شيك', 'secondary']
                                        ];
                                        $method = $payment_methods[$expense['payment_method']] ?? ['غير محدد', 'light'];
                                        ?>
                                        <span class="badge bg-<?php echo $method[1]; ?>"><?php echo $method[0]; ?></span>
                                    </td>
                                    <td>
                                        <?php
                                        $status_badges = [
                                            'pending' => ['في انتظار الموافقة', 'warning'],
                                            'approved' => ['معتمد', 'success'],
                                            'rejected' => ['مرفوض', 'danger'],
                                            'paid' => ['مدفوع', 'info']
                                        ];
                                        $status = $status_badges[$expense['status']] ?? ['غير محدد', 'light'];
                                        ?>
                                        <span class="badge bg-<?php echo $status[1]; ?>"><?php echo $status[0]; ?></span>
                                    </td>
                                    <td>
                                        <small><?php echo htmlspecialchars($expense['created_by_name'] ?? 'غير محدد'); ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="view.php?id=<?php echo $expense['id']; ?>" class="btn btn-outline-info" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="edit.php?id=<?php echo $expense['id']; ?>" class="btn btn-outline-primary" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php if ($expense['status'] == 'pending'): ?>
                                                <button class="btn btn-outline-success" onclick="approveExpense(<?php echo $expense['id']; ?>)" title="اعتماد">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            <?php endif; ?>
                                            <a href="delete.php?id=<?php echo $expense['id']; ?>" class="btn btn-outline-danger" title="حذف" onclick="return confirm('هل أنت متأكد من حذف هذا المصروف؟')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد مصروفات</h5>
                    <p class="text-muted">لم يتم تسجيل أي مصروفات للفترة المحددة</p>
                    <a href="add.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إضافة مصروف جديد
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include_once '../../includes/footer.php'; ?>

<script>
function approveExpense(expenseId) {
    if (confirm('هل أنت متأكد من اعتماد هذا المصروف؟')) {
        fetch('approve_expense.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                expense_id: expenseId,
                action: 'approve'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
        });
    }
}

function rejectExpense(expenseId) {
    if (confirm('هل أنت متأكد من رفض هذا المصروف؟')) {
        fetch('approve_expense.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                expense_id: expenseId,
                action: 'reject'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
        });
    }
}
</script>
