<?php
/**
 * صفحة عرض تفاصيل الطالب
 * Student Details View Page
 */

require_once '../includes/functions.php';
require_once '../includes/security.php';

$page_title = __('student_details');
require_once '../includes/header.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('teacher') && !check_permission('staff') && !has_permission('students_view') && !has_permission('student_affairs')) {
    header('Location: ../dashboard/');
    exit();
}

$student_id = intval($_GET['id'] ?? 0);

if (!$student_id) {
    $_SESSION['error_message'] = __('invalid_student_id');
    header('Location: index.php');
    exit();
}

// جلب بيانات الطالب
$query = "
    SELECT 
        s.*,
        u.full_name,
        u.username,
        u.email,
        u.phone,
        u.status,
        u.profile_picture,
        u.last_login,
        u.created_at as user_created_at,
        c.class_name,
        c.grade_level,
        c.section
    FROM students s
    JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    WHERE s.id = ?
";

$stmt = $conn->prepare($query);
$stmt->bind_param("i", $student_id);
$stmt->execute();
$student = $stmt->get_result()->fetch_assoc();

if (!$student) {
    $_SESSION['error_message'] = __('student_not_found');
    header('Location: index.php');
    exit();
}

// جلب إحصائيات الطالب
$stats_query = "
    SELECT
        (SELECT AVG(percentage) FROM student_grades WHERE student_id = ?) as avg_grade,
        (SELECT COUNT(*) FROM student_grades WHERE student_id = ?) as total_grades,
        (SELECT COUNT(*) FROM attendance WHERE student_id = ?) as total_attendance_days,
        (SELECT COUNT(*) FROM attendance WHERE student_id = ? AND status = 'present') as present_days,
        (SELECT COUNT(*) FROM exam_attempts WHERE student_id = ?) as total_exams_taken
";

$stats_stmt = $conn->prepare($stats_query);
$stats_stmt->bind_param("iiiii", $student_id, $student_id, $student_id, $student_id, $student_id);
$stats_stmt->execute();
$stats = $stats_stmt->get_result()->fetch_assoc();

// حساب معدل الحضور
$attendance_rate = 0;
if ($stats['total_attendance_days'] > 0) {
    $attendance_rate = ($stats['present_days'] / $stats['total_attendance_days']) * 100;
}

// جلب أحدث الدرجات
$recent_grades_query = "
    SELECT
        g.*,
        s.subject_name,
        e.exam_title,
        g.exam_type as grade_type,
        g.total_marks as max_score
    FROM student_grades g
    LEFT JOIN subjects s ON g.subject_id = s.id
    LEFT JOIN exams e ON g.exam_id = e.id
    WHERE g.student_id = ?
    ORDER BY g.graded_at DESC
    LIMIT 5
";

$recent_grades_stmt = $conn->prepare($recent_grades_query);
$recent_grades_stmt->bind_param("i", $student_id);
$recent_grades_stmt->execute();
$recent_grades = $recent_grades_stmt->get_result();

// جلب سجل الحضور الأخير
$recent_attendance_query = "
    SELECT 
        a.*,
        s.subject_name,
        c.class_name
    FROM attendance a
    LEFT JOIN subjects s ON a.subject_id = s.id
    LEFT JOIN classes c ON a.class_id = c.id
    WHERE a.student_id = ?
    ORDER BY a.attendance_date DESC
    LIMIT 10
";

$recent_attendance_stmt = $conn->prepare($recent_attendance_query);
$recent_attendance_stmt->bind_param("i", $student_id);
$recent_attendance_stmt->execute();
$recent_attendance = $recent_attendance_stmt->get_result();
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('student_details'); ?></h1>
            <p class="text-muted"><?php echo htmlspecialchars($student['full_name']); ?></p>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
            </a>
            <?php if (check_permission('admin')): ?>
            <a href="edit.php?id=<?php echo $student_id; ?>" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i><?php echo __('edit'); ?>
            </a>
            <?php endif; ?>
        </div>
    </div>

    <div class="row">
        <!-- Student Profile Card -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-body text-center">
                    <?php if (!empty($student['profile_picture'])): ?>
                        <img src="../uploads/profiles/<?php echo $student['profile_picture']; ?>" 
                             alt="<?php echo htmlspecialchars($student['full_name']); ?>" 
                             class="rounded-circle mb-3" 
                             width="120" 
                             height="120"
                             style="object-fit: cover;">
                    <?php else: ?>
                        <div class="bg-secondary rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                             style="width: 120px; height: 120px;">
                            <i class="fas fa-user text-white fs-1"></i>
                        </div>
                    <?php endif; ?>
                    
                    <h4 class="mb-1"><?php echo htmlspecialchars($student['full_name']); ?></h4>
                    <p class="text-muted mb-2"><?php echo htmlspecialchars($student['student_id']); ?></p>
                    
                    <?php
                    $status_class = '';
                    $status_text = '';
                    switch ($student['status']) {
                        case 'active':
                            $status_class = 'bg-success';
                            $status_text = __('active');
                            break;
                        case 'inactive':
                            $status_class = 'bg-warning';
                            $status_text = __('inactive');
                            break;
                        case 'suspended':
                            $status_class = 'bg-danger';
                            $status_text = __('suspended');
                            break;
                        default:
                            $status_class = 'bg-secondary';
                            $status_text = $student['status'];
                    }
                    ?>
                    <span class="badge <?php echo $status_class; ?> mb-3"><?php echo $status_text; ?></span>
                    
                    <?php if (!empty($student['class_name'])): ?>
                        <div class="mb-3">
                            <span class="badge bg-info fs-6">
                                <?php echo htmlspecialchars($student['class_name']); ?>
                            </span>
                            <br>
                            <small class="text-muted"><?php echo htmlspecialchars($student['grade_level']); ?></small>
                        </div>
                    <?php endif; ?>
                    
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="border-end">
                                <h5 class="mb-0"><?php echo number_format($stats['avg_grade'] ?? 0, 1); ?>%</h5>
                                <small class="text-muted"><?php echo __('avg_grade'); ?></small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="border-end">
                                <h5 class="mb-0"><?php echo number_format($attendance_rate, 1); ?>%</h5>
                                <small class="text-muted"><?php echo __('attendance'); ?></small>
                            </div>
                        </div>
                        <div class="col-4">
                            <h5 class="mb-0"><?php echo $stats['total_exams_taken']; ?></h5>
                            <small class="text-muted"><?php echo __('exams'); ?></small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-address-book me-2"></i><?php echo __('contact_information'); ?>
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($student['email'])): ?>
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('email'); ?></label>
                            <div>
                                <i class="fas fa-envelope me-2"></i>
                                <a href="mailto:<?php echo $student['email']; ?>"><?php echo htmlspecialchars($student['email']); ?></a>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($student['phone'])): ?>
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('phone'); ?></label>
                            <div>
                                <i class="fas fa-phone me-2"></i>
                                <a href="tel:<?php echo $student['phone']; ?>"><?php echo htmlspecialchars($student['phone']); ?></a>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($student['address'])): ?>
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('address'); ?></label>
                            <div>
                                <i class="fas fa-map-marker-alt me-2"></i>
                                <?php echo htmlspecialchars($student['address']); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($student['parent_name'])): ?>
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('parent_name'); ?></label>
                            <div>
                                <i class="fas fa-user me-2"></i>
                                <?php echo htmlspecialchars($student['parent_name']); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($student['parent_phone'])): ?>
                        <div class="mb-3">
                            <label class="form-label text-muted"><?php echo __('parent_phone'); ?></label>
                            <div>
                                <i class="fas fa-phone me-2"></i>
                                <a href="tel:<?php echo $student['parent_phone']; ?>"><?php echo htmlspecialchars($student['parent_phone']); ?></a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Personal Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-user me-2"></i><?php echo __('personal_information'); ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted"><?php echo __('full_name'); ?></label>
                                <div><?php echo htmlspecialchars($student['full_name']); ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted"><?php echo __('username'); ?></label>
                                <div><?php echo htmlspecialchars($student['username']); ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted"><?php echo __('student_id'); ?></label>
                                <div><?php echo htmlspecialchars($student['student_id']); ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted"><?php echo __('national_id'); ?></label>
                                <div><?php echo htmlspecialchars($student['national_id'] ?: __('not_provided')); ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted"><?php echo __('date_of_birth'); ?></label>
                                <div>
                                    <?php 
                                    if ($student['date_of_birth']) {
                                        echo format_date($student['date_of_birth']);
                                        $age = calculate_age($student['date_of_birth']);
                                        echo " <small class='text-muted'>(" . $age . " " . __('years_old') . ")</small>";
                                    } else {
                                        echo __('not_provided');
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted"><?php echo __('gender'); ?></label>
                                <div>
                                    <?php echo $student['gender'] === 'male' ? __('male') : __('female'); ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted"><?php echo __('enrollment_date'); ?></label>
                                <div><?php echo format_date($student['enrollment_date']); ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted"><?php echo __('last_login'); ?></label>
                                <div>
                                    <?php 
                                    if ($student['last_login']) {
                                        echo format_datetime($student['last_login']);
                                    } else {
                                        echo __('never_logged_in');
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <?php if (!empty($student['medical_conditions'])): ?>
                        <div class="mt-3">
                            <label class="form-label text-muted"><?php echo __('medical_conditions'); ?></label>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo nl2br(htmlspecialchars($student['medical_conditions'])); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Grades -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i><?php echo __('recent_grades'); ?>
                    </h6>
                    <a href="../grades/?student_id=<?php echo $student_id; ?>" class="btn btn-sm btn-outline-primary">
                        <?php echo __('view_all'); ?>
                    </a>
                </div>
                <div class="card-body">
                    <?php if ($recent_grades->num_rows > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th><?php echo __('subject'); ?></th>
                                        <th><?php echo __('exam'); ?></th>
                                        <th><?php echo __('score'); ?></th>
                                        <th><?php echo __('percentage'); ?></th>
                                        <th><?php echo __('grade'); ?></th>
                                        <th><?php echo __('date'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($grade = $recent_grades->fetch_assoc()): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($grade['subject_name'] ?? __('general')); ?></td>
                                            <td>
                                                <?php
                                                $exam_title = $grade['exam_title'] ?? '';
                                                $grade_type = $grade['grade_type'] ?? '';
                                                echo htmlspecialchars($exam_title ?: $grade_type ?: __('assessment'));
                                                ?>
                                            </td>
                                            <td>
                                                <?php
                                                $score = $grade['score'] ?? 0;
                                                $max_score = $grade['max_score'] ?? $grade['total_marks'] ?? 100;
                                                echo $score . '/' . $max_score;
                                                ?>
                                            </td>
                                            <td>
                                                <span class="badge <?php echo ($grade['percentage'] ?? 0) >= 60 ? 'bg-success' : 'bg-danger'; ?>">
                                                    <?php echo number_format($grade['percentage'] ?? 0, 1); ?>%
                                                </span>
                                            </td>
                                            <td>
                                                <?php
                                                $grade_letter = $grade['grade_letter'] ?? '';
                                                echo $grade_letter ?: (function_exists('grade_to_letter') ? grade_to_letter($grade['percentage'] ?? 0) : '-');
                                                ?>
                                            </td>
                                            <td><?php echo format_date($grade['graded_at'] ?? ''); ?></td>
                                        </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center text-muted py-3">
                            <i class="fas fa-chart-line fa-2x mb-2"></i>
                            <p><?php echo __('no_grades_yet'); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Attendance -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-calendar-check me-2"></i><?php echo __('recent_attendance'); ?>
                    </h6>
                    <a href="../attendance/?student_id=<?php echo $student_id; ?>" class="btn btn-sm btn-outline-primary">
                        <?php echo __('view_all'); ?>
                    </a>
                </div>
                <div class="card-body">
                    <?php if ($recent_attendance->num_rows > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th><?php echo __('date'); ?></th>
                                        <th><?php echo __('subject'); ?></th>
                                        <th><?php echo __('status'); ?></th>
                                        <th><?php echo __('notes'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($attendance = $recent_attendance->fetch_assoc()): ?>
                                        <tr>
                                            <td><?php echo format_date($attendance['attendance_date']); ?></td>
                                            <td><?php echo htmlspecialchars($attendance['subject_name'] ?: $attendance['class_name'] ?: __('general')); ?></td>
                                            <td>
                                                <?php
                                                $status_class = '';
                                                $status_text = '';
                                                switch ($attendance['status']) {
                                                    case 'present':
                                                        $status_class = 'bg-success';
                                                        $status_text = __('present');
                                                        break;
                                                    case 'absent':
                                                        $status_class = 'bg-danger';
                                                        $status_text = __('absent');
                                                        break;
                                                    case 'late':
                                                        $status_class = 'bg-warning';
                                                        $status_text = __('late');
                                                        break;
                                                    case 'excused':
                                                        $status_class = 'bg-info';
                                                        $status_text = __('excused');
                                                        break;
                                                }
                                                ?>
                                                <span class="badge <?php echo $status_class; ?>">
                                                    <?php echo $status_text; ?>
                                                </span>
                                            </td>
                                            <td><?php echo htmlspecialchars($attendance['notes'] ?: '-'); ?></td>
                                        </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center text-muted py-3">
                            <i class="fas fa-calendar-check fa-2x mb-2"></i>
                            <p><?php echo __('no_attendance_records'); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
