<?php
/**
 * العمليات الجماعية للطلاب
 * Bulk Operations for Students
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بهذه العملية']);
    exit();
}

// التحقق من CSRF Token
if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'رمز الأمان غير صحيح']);
    exit();
}

$operation = $_POST['operation'] ?? '';
$student_ids = $_POST['student_ids'] ?? [];

// التحقق من صحة البيانات
if (empty($operation) || empty($student_ids) || !is_array($student_ids)) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
    exit();
}

// تنظيف معرفات الطلاب
$student_ids = array_map('intval', $student_ids);
$student_ids = array_filter($student_ids, function($id) { return $id > 0; });

if (empty($student_ids)) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'لم يتم تحديد طلاب صحيحين']);
    exit();
}

$conn->begin_transaction();

try {
    $success_count = 0;
    $error_count = 0;
    $messages = [];

    switch ($operation) {
        case 'delete':
            $result = bulkDeleteStudents($student_ids);
            break;
            
        case 'transfer':
            $new_class_id = intval($_POST['new_class_id'] ?? 0);
            if ($new_class_id <= 0) {
                throw new Exception('يجب تحديد الفصل الجديد');
            }
            $result = bulkTransferStudents($student_ids, $new_class_id);
            break;
            
        case 'change_status':
            $new_status = $_POST['new_status'] ?? '';
            if (!in_array($new_status, ['active', 'inactive', 'graduated', 'transferred'])) {
                throw new Exception('حالة غير صحيحة');
            }
            $result = bulkChangeStatus($student_ids, $new_status);
            break;
            
        case 'promote':
            $target_class_id = intval($_POST['target_class_id'] ?? 0);
            if ($target_class_id <= 0) {
                throw new Exception('يجب تحديد الفصل الجديد للترقية');
            }
            $result = bulkPromoteStudents($student_ids, $target_class_id);
            break;
            
        default:
            throw new Exception('عملية غير مدعومة');
    }

    $conn->commit();
    
    // تسجيل النشاط
    log_activity($_SESSION['user_id'], "bulk_$operation", 'students', null, [
        'operation' => $operation,
        'student_count' => count($student_ids),
        'success_count' => $result['success_count'],
        'error_count' => $result['error_count']
    ]);

    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'message' => $result['message'],
        'success_count' => $result['success_count'],
        'error_count' => $result['error_count'],
        'details' => $result['details'] ?? []
    ]);

} catch (Exception $e) {
    $conn->rollback();
    
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ: ' . $e->getMessage()
    ]);
}

/**
 * حذف جماعي للطلاب
 */
function bulkDeleteStudents($student_ids) {
    global $conn;
    
    $success_count = 0;
    $error_count = 0;
    $details = [];
    
    // جلب بيانات الطلاب قبل الحذف
    $placeholders = str_repeat('?,', count($student_ids) - 1) . '?';
    $students_query = "
        SELECT s.id, s.user_id, u.full_name 
        FROM students s 
        JOIN users u ON s.user_id = u.id 
        WHERE s.id IN ($placeholders)
    ";
    
    $stmt = $conn->prepare($students_query);
    $stmt->bind_param(str_repeat('i', count($student_ids)), ...$student_ids);
    $stmt->execute();
    $students = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    foreach ($students as $student) {
        try {
            // حذف السجلات المرتبطة
            deleteRelatedRecords($student['id']);
            
            // حذف الطالب
            $delete_student = $conn->prepare("DELETE FROM students WHERE id = ?");
            $delete_student->bind_param("i", $student['id']);
            $delete_student->execute();
            
            // حذف المستخدم
            if ($student['user_id']) {
                $delete_user = $conn->prepare("DELETE FROM users WHERE id = ?");
                $delete_user->bind_param("i", $student['user_id']);
                $delete_user->execute();
            }
            
            $success_count++;
            $details[] = "تم حذف الطالب: " . $student['full_name'];
            
        } catch (Exception $e) {
            $error_count++;
            $details[] = "فشل حذف الطالب " . $student['full_name'] . ": " . $e->getMessage();
        }
    }
    
    return [
        'success_count' => $success_count,
        'error_count' => $error_count,
        'message' => "تم حذف $success_count طالب بنجاح" . ($error_count > 0 ? " وفشل في حذف $error_count" : ""),
        'details' => $details
    ];
}

/**
 * نقل جماعي للطلاب
 */
function bulkTransferStudents($student_ids, $new_class_id) {
    global $conn;
    
    // التحقق من وجود الفصل الجديد
    $class_check = $conn->prepare("SELECT class_name FROM classes WHERE id = ?");
    $class_check->bind_param("i", $new_class_id);
    $class_check->execute();
    $class_result = $class_check->get_result();
    
    if ($class_result->num_rows === 0) {
        throw new Exception('الفصل المحدد غير موجود');
    }
    
    $class_name = $class_result->fetch_assoc()['class_name'];
    
    $success_count = 0;
    $error_count = 0;
    $details = [];
    
    $placeholders = str_repeat('?,', count($student_ids) - 1) . '?';
    $update_query = "UPDATE students SET class_id = ? WHERE id IN ($placeholders)";
    
    $stmt = $conn->prepare($update_query);
    $params = array_merge([$new_class_id], $student_ids);
    $types = 'i' . str_repeat('i', count($student_ids));
    $stmt->bind_param($types, ...$params);
    
    if ($stmt->execute()) {
        $success_count = $stmt->affected_rows;
        $details[] = "تم نقل $success_count طالب إلى فصل: $class_name";
    } else {
        throw new Exception('فشل في تحديث بيانات الطلاب');
    }
    
    return [
        'success_count' => $success_count,
        'error_count' => $error_count,
        'message' => "تم نقل $success_count طالب إلى فصل: $class_name",
        'details' => $details
    ];
}

/**
 * تغيير حالة جماعي
 */
function bulkChangeStatus($student_ids, $new_status) {
    global $conn;
    
    $success_count = 0;
    $error_count = 0;
    $details = [];
    
    $placeholders = str_repeat('?,', count($student_ids) - 1) . '?';
    
    // تحديث حالة الطلاب
    $update_students = "UPDATE students SET status = ? WHERE id IN ($placeholders)";
    $stmt = $conn->prepare($update_students);
    $params = array_merge([$new_status], $student_ids);
    $types = 's' . str_repeat('i', count($student_ids));
    $stmt->bind_param($types, ...$params);
    
    if ($stmt->execute()) {
        $students_updated = $stmt->affected_rows;
        
        // تحديث حالة المستخدمين المرتبطين
        $user_status = ($new_status === 'active') ? 'active' : 'inactive';
        $update_users = "
            UPDATE users u 
            JOIN students s ON u.id = s.user_id 
            SET u.status = ? 
            WHERE s.id IN ($placeholders)
        ";
        $stmt2 = $conn->prepare($update_users);
        $stmt2->bind_param($types, ...$params);
        $stmt2->execute();
        
        $success_count = $students_updated;
        $status_text = [
            'active' => 'نشط',
            'inactive' => 'غير نشط',
            'graduated' => 'متخرج',
            'transferred' => 'محول'
        ][$new_status] ?? $new_status;
        
        $details[] = "تم تغيير حالة $success_count طالب إلى: $status_text";
    } else {
        throw new Exception('فشل في تحديث حالة الطلاب');
    }
    
    return [
        'success_count' => $success_count,
        'error_count' => $error_count,
        'message' => "تم تغيير حالة $success_count طالب بنجاح",
        'details' => $details
    ];
}

/**
 * ترقية جماعية للطلاب
 */
function bulkPromoteStudents($student_ids, $target_class_id) {
    global $conn;

    // التحقق من وجود الفصل المستهدف وجلب معلوماته
    $class_query = "
        SELECT c.id, c.class_name, c.grade_level,
               g.grade_name, es.stage_name
        FROM classes c
        LEFT JOIN grades g ON c.grade_id = g.id
        LEFT JOIN educational_stages es ON g.stage_id = es.id
        WHERE c.id = ?
    ";

    $class_stmt = $conn->prepare($class_query);
    $class_stmt->bind_param("i", $target_class_id);
    $class_stmt->execute();
    $class_result = $class_stmt->get_result();

    if ($class_result->num_rows === 0) {
        throw new Exception("الفصل المحدد غير موجود");
    }

    $target_class = $class_result->fetch_assoc();

    $success_count = 0;
    $error_count = 0;
    $details = [];

    // جلب بيانات الطلاب قبل الترقية
    $placeholders = str_repeat('?,', count($student_ids) - 1) . '?';
    $students_query = "
        SELECT s.id, u.full_name, c.class_name as current_class
        FROM students s
        JOIN users u ON s.user_id = u.id
        LEFT JOIN classes c ON s.class_id = c.id
        WHERE s.id IN ($placeholders)
    ";

    $stmt = $conn->prepare($students_query);
    $stmt->bind_param(str_repeat('i', count($student_ids)), ...$student_ids);
    $stmt->execute();
    $students = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);

    foreach ($students as $student) {
        try {
            // نقل الطالب إلى الفصل الجديد
            $update_stmt = $conn->prepare("
                UPDATE students
                SET class_id = ?, promotion_date = CURDATE(), status = 'active'
                WHERE id = ?
            ");
            $update_stmt->bind_param("ii", $target_class_id, $student['id']);
            $update_stmt->execute();

            $success_count++;
            $current_class = $student['current_class'] ?? 'غير محدد';
            $details[] = "تم ترقية الطالب {$student['full_name']} من {$current_class} إلى {$target_class['class_name']}";

        } catch (Exception $e) {
            $error_count++;
            $details[] = "فشل في ترقية الطالب {$student['full_name']}: " . $e->getMessage();
        }
    }

    $class_info = $target_class['class_name'];
    if ($target_class['stage_name'] && $target_class['grade_name']) {
        $class_info .= " ({$target_class['stage_name']} - {$target_class['grade_name']})";
    }

    return [
        'success_count' => $success_count,
        'error_count' => $error_count,
        'message' => "تم ترقية $success_count طالب إلى فصل: $class_info",
        'details' => $details
    ];
}

/**
 * حذف السجلات المرتبطة بالطالب
 */
function deleteRelatedRecords($student_id) {
    global $conn;
    
    // حذف سجلات الحضور
    $conn->prepare("DELETE FROM attendance WHERE student_id = ?")->execute([$student_id]);
    
    // حذف الدرجات
    $conn->prepare("DELETE FROM student_grades WHERE student_id = ?")->execute([$student_id]);
    
    // حذف محاولات الامتحانات
    $conn->prepare("DELETE FROM exam_attempts WHERE student_id = ?")->execute([$student_id]);
    
    // حذف الرسائل
    $conn->prepare("DELETE FROM messages WHERE sender_id = ? OR recipient_id = ?")->execute([$student_id, $student_id]);
    
    // يمكن إضافة المزيد من الجداول المرتبطة حسب الحاجة
}
?>
