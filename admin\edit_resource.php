<?php
/**
 * تعديل مورد النظام - صفحة مستقلة
 * Edit System Resource - Standalone Page
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من الصلاحيات (مدير فقط)
check_session();
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

// التحقق من معرف المورد
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: system_resources.php');
    exit();
}

$resource_id = intval($_GET['id']);
$success_message = '';
$error_message = '';

// جلب بيانات المورد
$resource_stmt = $conn->prepare("SELECT * FROM system_resources WHERE id = ?");
$resource_stmt->bind_param("i", $resource_id);
$resource_stmt->execute();
$resource = $resource_stmt->get_result()->fetch_assoc();

if (!$resource) {
    header('Location: system_resources.php');
    exit();
}

// معالجة تحديث المورد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_resource'])) {
    $resource_name = clean_input($_POST['resource_name']);
    $resource_description = clean_input($_POST['resource_description']);
    $resource_path = clean_input($_POST['resource_path']);
    $icon = clean_input($_POST['icon']);
    $sort_order = intval($_POST['sort_order']);
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    
    if (!empty($resource_name)) {
        $stmt = $conn->prepare("
            UPDATE system_resources 
            SET resource_name = ?, resource_description = ?, resource_path = ?, icon = ?, sort_order = ?, is_active = ?
            WHERE id = ?
        ");
        $stmt->bind_param("ssssiii", $resource_name, $resource_description, $resource_path, $icon, $sort_order, $is_active, $resource_id);
        
        if ($stmt->execute()) {
            $success_message = "تم تحديث المورد بنجاح";
            // تحديث البيانات المعروضة
            $resource['resource_name'] = $resource_name;
            $resource['resource_description'] = $resource_description;
            $resource['resource_path'] = $resource_path;
            $resource['icon'] = $icon;
            $resource['sort_order'] = $sort_order;
            $resource['is_active'] = $is_active;
        } else {
            $error_message = "خطأ في تحديث المورد: " . $conn->error;
        }
    } else {
        $error_message = "يرجى ملء جميع الحقول المطلوبة";
    }
}

// جلب عدد الاستخدامات
$usage_stmt = $conn->prepare("SELECT COUNT(*) as count FROM user_custom_permissions WHERE permission_key = ?");
$usage_stmt->bind_param("s", $resource['resource_key']);
$usage_stmt->execute();
$usage_count = $usage_stmt->get_result()->fetch_assoc()['count'];

$page_title = 'تعديل المورد: ' . $resource['resource_name'];
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-edit me-2"></i><?php echo $page_title; ?></h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="../admin/">الإدارة</a></li>
                    <li class="breadcrumb-item"><a href="system_resources.php">موارد النظام</a></li>
                    <li class="breadcrumb-item active">تعديل المورد</li>
                </ol>
            </nav>
        </div>
        <a href="system_resources.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
        </a>
    </div>

    <!-- رسائل التنبيه -->
    <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- نموذج التعديل -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-edit me-2"></i>تعديل بيانات المورد</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <!-- معلومات غير قابلة للتعديل -->
                        <div class="alert alert-info">
                            <div class="row">
                                <div class="col-md-4">
                                    <strong>نوع المورد:</strong>
                                    <br>
                                    <span class="badge bg-<?php 
                                        echo match($resource['resource_type']) {
                                            'page' => 'primary',
                                            'action' => 'success',
                                            'data' => 'info',
                                            'report' => 'warning',
                                            default => 'secondary'
                                        };
                                    ?>">
                                        <?php 
                                        $type_names = [
                                            'page' => 'صفحة',
                                            'action' => 'إجراء',
                                            'data' => 'بيانات',
                                            'report' => 'تقرير'
                                        ];
                                        echo $type_names[$resource['resource_type']] ?? $resource['resource_type'];
                                        ?>
                                    </span>
                                </div>
                                <div class="col-md-4">
                                    <strong>مفتاح المورد:</strong>
                                    <br><code><?php echo htmlspecialchars($resource['resource_key']); ?></code>
                                </div>
                                <div class="col-md-4">
                                    <strong>عدد الاستخدامات:</strong>
                                    <br>
                                    <span class="badge bg-<?php echo $usage_count > 0 ? 'success' : 'secondary'; ?>">
                                        <?php echo $usage_count; ?> مستخدم
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">اسم المورد *</label>
                            <input type="text" class="form-control" name="resource_name" 
                                   value="<?php echo htmlspecialchars($resource['resource_name']); ?>" 
                                   required>
                            <div class="form-text">
                                الاسم الذي سيظهر في واجهة إدارة الصلاحيات
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">وصف المورد</label>
                            <textarea class="form-control" name="resource_description" rows="3"><?php echo htmlspecialchars($resource['resource_description']); ?></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">مسار المورد</label>
                                <input type="text" class="form-control" name="resource_path" 
                                       value="<?php echo htmlspecialchars($resource['resource_path']); ?>">
                                <div class="form-text">
                                    المسار النسبي للصفحة (للصفحات فقط)
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الأيقونة</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" name="icon" 
                                           value="<?php echo htmlspecialchars($resource['icon']); ?>" 
                                           id="iconInput">
                                    <span class="input-group-text">
                                        <i class="<?php echo htmlspecialchars($resource['icon']); ?>" id="iconPreview"></i>
                                    </span>
                                </div>
                                <div class="form-text">
                                    فئة CSS للأيقونة (Font Awesome)
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">ترتيب العرض</label>
                                <input type="number" class="form-control" name="sort_order" 
                                       value="<?php echo $resource['sort_order']; ?>">
                                <div class="form-text">
                                    رقم الترتيب (الأصغر يظهر أولاً)
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">حالة المورد</label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="is_active" 
                                           id="is_active" <?php echo $resource['is_active'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_active">
                                        مورد نشط
                                    </label>
                                </div>
                                <div class="form-text">
                                    الموارد غير النشطة لا تظهر في قوائم الصلاحيات
                                </div>
                            </div>
                        </div>

                        <?php if ($usage_count > 0): ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>تنبيه:</strong> هذا المورد مستخدم حالياً من قبل <?php echo $usage_count; ?> مستخدم. 
                                تعديل بياناته قد يؤثر على صلاحياتهم.
                            </div>
                        <?php endif; ?>

                        <div class="d-flex justify-content-between">
                            <a href="system_resources.php" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" name="update_resource" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-info-circle me-2"></i>معلومات المورد</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless table-sm">
                        <tr>
                            <td><strong>تاريخ الإنشاء:</strong></td>
                            <td><?php echo date('Y-m-d H:i', strtotime($resource['created_at'])); ?></td>
                        </tr>
                        <tr>
                            <td><strong>المورد الأب:</strong></td>
                            <td>
                                <?php if ($resource['parent_resource']): ?>
                                    <code><?php echo htmlspecialchars($resource['parent_resource']); ?></code>
                                <?php else: ?>
                                    <span class="text-muted">لا يوجد</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>الحالة:</strong></td>
                            <td>
                                <span class="badge bg-<?php echo $resource['is_active'] ? 'success' : 'secondary'; ?>">
                                    <?php echo $resource['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                </span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <?php if ($usage_count > 0): ?>
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-users me-2"></i>المستخدمون المرتبطون</h6>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">
                            يستخدم هذا المورد حالياً من قبل <strong><?php echo $usage_count; ?></strong> مستخدم.
                        </p>
                        <a href="permissions_manager.php" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye me-2"></i>عرض المستخدمين
                        </a>
                    </div>
                </div>
            <?php endif; ?>

            <!-- روابط سريعة -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6><i class="fas fa-link me-2"></i>إجراءات أخرى</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="add_resource.php" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-plus me-2"></i>إضافة مورد جديد
                        </a>
                        <?php if ($usage_count == 0): ?>
                            <a href="delete_resource.php?id=<?php echo $resource_id; ?>" 
                               class="btn btn-outline-danger btn-sm">
                                <i class="fas fa-trash me-2"></i>حذف هذا المورد
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// معاينة الأيقونة
document.getElementById('iconInput').addEventListener('input', function() {
    const iconPreview = document.getElementById('iconPreview');
    iconPreview.className = this.value || 'fas fa-question';
});
</script>

<?php include_once '../includes/footer.php'; ?>
