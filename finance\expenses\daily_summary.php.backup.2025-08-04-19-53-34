<?php
/**
 * الملخص اليومي للمصروفات
 * Daily Expenses Summary
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    header('Location: ../../dashboard/');
    exit();
}

// تحديد التاريخ
$selected_date = $_GET['date'] ?? date('Y-m-d');

// جلب إحصائيات اليوم
$daily_stats_query = "
    SELECT 
        COUNT(*) as total_expenses,
        SUM(amount) as total_amount,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
        COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_count,
        COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_count,
        SUM(CASE WHEN status = 'approved' THEN amount ELSE 0 END) as approved_amount,
        SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as pending_amount
    FROM daily_expenses 
    WHERE expense_date = ?
";

$daily_stats_stmt = $conn->prepare($daily_stats_query);
$daily_stats_stmt->bind_param("s", $selected_date);
$daily_stats_stmt->execute();
$daily_stats = $daily_stats_stmt->get_result()->fetch_assoc();

// تنظيف البيانات
$daily_stats = [
    'total_expenses' => intval($daily_stats['total_expenses'] ?? 0),
    'total_amount' => floatval($daily_stats['total_amount'] ?? 0),
    'pending_count' => intval($daily_stats['pending_count'] ?? 0),
    'approved_count' => intval($daily_stats['approved_count'] ?? 0),
    'rejected_count' => intval($daily_stats['rejected_count'] ?? 0),
    'approved_amount' => floatval($daily_stats['approved_amount'] ?? 0),
    'pending_amount' => floatval($daily_stats['pending_amount'] ?? 0)
];

// جلب المصروفات حسب الفئة
$category_stats_query = "
    SELECT 
        ec.category_name,
        ec.icon,
        ec.color,
        COUNT(de.id) as expenses_count,
        SUM(de.amount) as total_amount,
        SUM(CASE WHEN de.status = 'approved' THEN de.amount ELSE 0 END) as approved_amount
    FROM expense_categories ec
    LEFT JOIN daily_expenses de ON ec.id = de.category_id AND de.expense_date = ?
    WHERE ec.is_active = 1
    GROUP BY ec.id
    HAVING expenses_count > 0
    ORDER BY total_amount DESC
";

$category_stats_stmt = $conn->prepare($category_stats_query);
$category_stats_stmt->bind_param("s", $selected_date);
$category_stats_stmt->execute();
$category_stats_result = $category_stats_stmt->get_result();

// جلب المصروفات التفصيلية
$expenses_query = "
    SELECT 
        de.*,
        ec.category_name,
        ec.icon,
        ec.color,
        u.full_name as created_by_name
    FROM daily_expenses de
    JOIN expense_categories ec ON de.category_id = ec.id
    LEFT JOIN users u ON de.created_by = u.id
    WHERE de.expense_date = ?
    ORDER BY de.created_at DESC
";

$expenses_stmt = $conn->prepare($expenses_query);
$expenses_stmt->bind_param("s", $selected_date);
$expenses_stmt->execute();
$expenses_result = $expenses_stmt->get_result();

$page_title = 'الملخص اليومي للمصروفات';
include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-calendar-day me-2"></i>الملخص اليومي للمصروفات
            </h1>
            <p class="text-muted mb-0">
                ملخص شامل لمصروفات يوم <?php echo date('d/m/Y', strtotime($selected_date)); ?>
            </p>
        </div>
        <div>
            <form method="GET" class="d-flex align-items-center">
                <label class="form-label me-2 mb-0">التاريخ:</label>
                <input type="date" class="form-control me-2" name="date" 
                       value="<?php echo $selected_date; ?>" onchange="this.form.submit()">
                <a href="index.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>العودة
                </a>
            </form>
        </div>
    </div>

    <!-- الإحصائيات الرئيسية -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي المصروفات</h6>
                            <h3 class="mb-0"><?php echo $daily_stats['total_expenses']; ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-list fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">المبلغ المعتمد</h6>
                            <h3 class="mb-0"><?php echo format_currency($daily_stats['approved_amount']); ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">في انتظار الموافقة</h6>
                            <h3 class="mb-0"><?php echo format_currency($daily_stats['pending_amount']); ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي المبلغ</h6>
                            <h3 class="mb-0"><?php echo format_currency($daily_stats['total_amount']); ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-money-bill fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- المصروفات حسب الفئة -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>المصروفات حسب الفئة
                    </h6>
                </div>
                <div class="card-body">
                    <?php if ($category_stats_result->num_rows > 0): ?>
                        <?php while ($category = $category_stats_result->fetch_assoc()): ?>
                            <?php 
                            $percentage = $daily_stats['total_amount'] > 0 ? 
                                         ($category['total_amount'] / $daily_stats['total_amount']) * 100 : 0;
                            ?>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <div class="d-flex align-items-center">
                                        <i class="<?php echo $category['icon']; ?> me-2" 
                                           style="color: <?php echo $category['color']; ?>"></i>
                                        <span><?php echo htmlspecialchars($category['category_name']); ?></span>
                                    </div>
                                    <div class="text-end">
                                        <span class="fw-bold"><?php echo format_currency($category['total_amount']); ?></span>
                                        <small class="text-muted d-block"><?php echo $category['expenses_count']; ?> مصروف</small>
                                    </div>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar" 
                                         style="width: <?php echo $percentage; ?>%; background-color: <?php echo $category['color']; ?>">
                                    </div>
                                </div>
                                <small class="text-muted"><?php echo number_format($percentage, 1); ?>%</small>
                            </div>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد مصروفات لهذا اليوم</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- تفاصيل المصروفات -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-list-ul me-2"></i>تفاصيل المصروفات
                    </h6>
                </div>
                <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                    <?php if ($expenses_result->num_rows > 0): ?>
                        <?php while ($expense = $expenses_result->fetch_assoc()): ?>
                            <div class="d-flex align-items-center mb-3 p-2 border rounded">
                                <div class="me-3">
                                    <i class="<?php echo $expense['icon']; ?> fa-lg" 
                                       style="color: <?php echo $expense['color']; ?>"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="fw-bold"><?php echo htmlspecialchars($expense['description']); ?></div>
                                    <small class="text-muted">
                                        <?php echo htmlspecialchars($expense['category_name']); ?>
                                        <?php if ($expense['vendor_name']): ?>
                                            • <?php echo htmlspecialchars($expense['vendor_name']); ?>
                                        <?php endif; ?>
                                    </small>
                                </div>
                                <div class="text-end">
                                    <div class="fw-bold"><?php echo format_currency($expense['amount']); ?></div>
                                    <?php
                                    $status_badges = [
                                        'pending' => ['في انتظار الموافقة', 'warning'],
                                        'approved' => ['معتمد', 'success'],
                                        'rejected' => ['مرفوض', 'danger'],
                                        'paid' => ['مدفوع', 'info']
                                    ];
                                    $status = $status_badges[$expense['status']] ?? ['غير محدد', 'light'];
                                    ?>
                                    <span class="badge bg-<?php echo $status[1]; ?> small"><?php echo $status[0]; ?></span>
                                </div>
                            </div>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد مصروفات لهذا اليوم</p>
                            <a href="add.php" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>إضافة مصروف جديد
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- مقارنة مع الأيام السابقة -->
    <div class="card">
        <div class="card-header">
            <h6 class="mb-0">
                <i class="fas fa-chart-line me-2"></i>مقارنة مع الأيام السابقة
            </h6>
        </div>
        <div class="card-body">
            <?php
            // جلب بيانات آخر 7 أيام للمقارنة
            $comparison_query = "
                SELECT 
                    expense_date,
                    COUNT(*) as expenses_count,
                    SUM(amount) as total_amount
                FROM daily_expenses 
                WHERE expense_date >= DATE_SUB(?, INTERVAL 6 DAY) 
                AND expense_date <= ?
                AND status != 'rejected'
                GROUP BY expense_date
                ORDER BY expense_date DESC
            ";
            
            $comparison_stmt = $conn->prepare($comparison_query);
            $comparison_stmt->bind_param("ss", $selected_date, $selected_date);
            $comparison_stmt->execute();
            $comparison_result = $comparison_stmt->get_result();
            ?>
            
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead class="table-light">
                        <tr>
                            <th>التاريخ</th>
                            <th>عدد المصروفات</th>
                            <th>إجمالي المبلغ</th>
                            <th>المقارنة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $previous_amount = 0;
                        $is_first = true;
                        while ($day = $comparison_result->fetch_assoc()): 
                        ?>
                            <tr <?php echo ($day['expense_date'] == $selected_date) ? 'class="table-primary"' : ''; ?>>
                                <td>
                                    <?php echo date('d/m/Y', strtotime($day['expense_date'])); ?>
                                    <?php if ($day['expense_date'] == $selected_date): ?>
                                        <span class="badge bg-primary ms-2">اليوم المحدد</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo $day['expenses_count']; ?></td>
                                <td><?php echo format_currency($day['total_amount']); ?></td>
                                <td>
                                    <?php if (!$is_first): ?>
                                        <?php 
                                        $difference = $day['total_amount'] - $previous_amount;
                                        $percentage_change = $previous_amount > 0 ? ($difference / $previous_amount) * 100 : 0;
                                        ?>
                                        <?php if ($difference > 0): ?>
                                            <span class="text-danger">
                                                <i class="fas fa-arrow-up"></i> +<?php echo format_currency($difference); ?>
                                                (<?php echo number_format($percentage_change, 1); ?>%)
                                            </span>
                                        <?php elseif ($difference < 0): ?>
                                            <span class="text-success">
                                                <i class="fas fa-arrow-down"></i> <?php echo format_currency($difference); ?>
                                                (<?php echo number_format($percentage_change, 1); ?>%)
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">
                                                <i class="fas fa-minus"></i> لا تغيير
                                            </span>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php 
                            $previous_amount = $day['total_amount'];
                            $is_first = false;
                            ?>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php include_once '../../includes/footer.php'; ?>

<script>
// تحديث تلقائي للصفحة كل 5 دقائق
setInterval(function() {
    location.reload();
}, 300000);
</script>
