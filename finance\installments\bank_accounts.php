<?php
/**
 * صفحة إدارة أرقام الحسابات البنكية
 * Bank Accounts Management Page
 */

require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';
require_once '../../includes/security.php';

// التحقق من الصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    header('Location: ../../dashboard/');
    exit();
}

// معالجة رسائل النجاح والخطأ من الصفحات الأخرى
$success_message = $_GET['success'] ?? '';
$error_message = $_GET['error'] ?? '';

// جلب جميع الحسابات البنكية مع أنواع الرسوم المرتبطة
$accounts_query = "
    SELECT
        ba.*,
        GROUP_CONCAT(ft.type_name SEPARATOR ', ') as linked_fee_types
    FROM bank_accounts ba
    LEFT JOIN bank_account_fee_types baft ON ba.id = baft.bank_account_id
    LEFT JOIN fee_types ft ON baft.fee_type_id = ft.id
    GROUP BY ba.id
    ORDER BY ba.is_active DESC, ba.bank_name ASC
";
$accounts_result = $conn->query($accounts_query);

$page_title = 'إدارة أرقام الحسابات البنكية';
include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-university me-2"></i>
                        إدارة أرقام الحسابات البنكية
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($success_message)): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo htmlspecialchars($success_message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($error_message)): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?php echo htmlspecialchars($error_message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- أزرار الإجراءات -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <a href="bank_account_add.php" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>
                                إضافة حساب بنكي جديد
                            </a>
                        </div>
                        <div class="col-md-6 text-end">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>
                                العودة لإدارة الأقساط
                            </a>
                        </div>
                    </div>

                    <!-- جدول الحسابات البنكية -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم الحساب</th>
                                    <th>اسم البنك</th>
                                    <th>اسم صاحب الحساب</th>
                                    <th>أنواع الرسوم المرتبطة</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($accounts_result && $accounts_result->num_rows > 0): ?>
                                    <?php while ($account = $accounts_result->fetch_assoc()): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($account['account_number']); ?></strong>
                                            </td>
                                            <td>
                                                <i class="fas fa-university me-2 text-primary"></i>
                                                <?php echo htmlspecialchars($account['bank_name']); ?>
                                            </td>
                                            <td>
                                                <?php echo htmlspecialchars($account['account_name'] ?? 'غير محدد'); ?>
                                            </td>
                                            <td>
                                                <?php if (!empty($account['linked_fee_types'])): ?>
                                                    <span class="badge bg-info">
                                                        <?php echo htmlspecialchars($account['linked_fee_types']); ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">لا توجد أنواع رسوم مرتبطة</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($account['is_active']): ?>
                                                    <span class="badge bg-success">نشط</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">غير نشط</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php echo date('Y-m-d', strtotime($account['created_at'])); ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group" aria-label="إجراءات الحساب البنكي">
                                                    <!-- زر التعديل -->
                                                    <a href="bank_account_edit.php?id=<?php echo $account['id']; ?>"
                                                       class="btn btn-sm btn-outline-primary"
                                                       title="تعديل الحساب البنكي"
                                                       data-bs-toggle="tooltip">
                                                        <i class="fas fa-edit"></i>
                                                    </a>

                                                    <!-- زر تبديل حالة النشاط -->
                                                    <?php if ($account['is_active']): ?>
                                                        <a href="bank_account_toggle.php?id=<?php echo $account['id']; ?>&action=deactivate"
                                                           class="btn btn-sm btn-outline-warning"
                                                           title="إلغاء تفعيل الحساب"
                                                           data-bs-toggle="tooltip"
                                                           onclick="return confirm('⚠️ إلغاء تفعيل الحساب البنكي\n\n' +
                                                                                 'الحساب: <?php echo addslashes($account['bank_name'] . ' - ' . $account['account_number']); ?>\n\n' +
                                                                                 'سيتم منع استخدام هذا الحساب في الأقساط الجديدة.\n\n' +
                                                                                 'هل تريد المتابعة؟')">
                                                            <i class="fas fa-pause-circle"></i>
                                                        </a>
                                                    <?php else: ?>
                                                        <a href="bank_account_toggle.php?id=<?php echo $account['id']; ?>&action=activate"
                                                           class="btn btn-sm btn-outline-success"
                                                           title="تفعيل الحساب"
                                                           data-bs-toggle="tooltip"
                                                           onclick="return confirm('✅ تفعيل الحساب البنكي\n\n' +
                                                                                 'الحساب: <?php echo addslashes($account['bank_name'] . ' - ' . $account['account_number']); ?>\n\n' +
                                                                                 'سيصبح هذا الحساب متاحاً للاستخدام في الأقساط الجديدة.\n\n' +
                                                                                 'هل تريد المتابعة؟')">
                                                            <i class="fas fa-play-circle"></i>
                                                        </a>
                                                    <?php endif; ?>

                                                    <!-- زر الحذف -->
                                                    <a href="bank_account_delete.php?id=<?php echo $account['id']; ?>"
                                                       class="btn btn-sm btn-outline-danger"
                                                       title="حذف الحساب البنكي"
                                                       data-bs-toggle="tooltip"
                                                       onclick="return confirm('🗑️ حذف الحساب البنكي\n\n' +
                                                                             'الحساب: <?php echo addslashes($account['bank_name'] . ' - ' . $account['account_number']); ?>\n\n' +
                                                                             '⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه!\n\n' +
                                                                             'سيتم التحقق من استخدام الحساب في الأقساط قبل الحذف.\n\n' +
                                                                             'هل أنت متأكد من المتابعة؟')">
                                                        <i class="fas fa-trash-alt"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-university fa-3x mb-3"></i>
                                                <h5>لا توجد حسابات بنكية</h5>
                                                <p>لم يتم إضافة أي حسابات بنكية بعد</p>
                                                <a href="bank_account_add.php" class="btn btn-primary">
                                                    <i class="fas fa-plus me-2"></i>
                                                    إضافة أول حساب بنكي
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تفعيل tooltips
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل Bootstrap tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // إضافة تأثيرات hover للأزرار
    const actionButtons = document.querySelectorAll('.btn-group .btn');
    actionButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
            this.style.transition = 'transform 0.2s ease';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });

    // تحسين رسائل التأكيد
    const deleteButtons = document.querySelectorAll('a[href*="bank_account_delete.php"]');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const accountInfo = this.onclick.toString().match(/الحساب: ([^\\n]+)/);
            const accountName = accountInfo ? accountInfo[1] : 'الحساب المحدد';

            // إنشاء modal تأكيد مخصص
            if (confirm(`🗑️ حذف الحساب البنكي\n\n${accountName}\n\n⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه!\n\nسيتم التحقق من استخدام الحساب في الأقساط قبل الحذف.\n\nهل أنت متأكد من المتابعة؟`)) {
                window.location.href = this.href;
            }
        });
    });
});

// دالة لتحديث الصفحة بعد العمليات
function refreshPage() {
    setTimeout(() => {
        window.location.reload();
    }, 1500);
}

// إخفاء رسائل التنبيه تلقائياً
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert.classList.contains('alert-success')) {
                alert.style.opacity = '0';
                alert.style.transition = 'opacity 0.5s ease';
                setTimeout(() => {
                    alert.remove();
                }, 500);
            }
        }, 5000); // إخفاء رسائل النجاح بعد 5 ثوان
    });
});
</script>

<style>
/* تحسينات CSS للأزرار */
.btn-group .btn {
    margin: 0 1px;
    border-radius: 4px !important;
}

.btn-group .btn:hover {
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    z-index: 1;
    position: relative;
}

/* تحسين شكل الجدول */
.table-hover tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
}

/* تحسين شكل الشارات */
.badge {
    font-size: 0.75em;
    padding: 0.35em 0.65em;
}

/* تحسين شكل الأيقونات */
.fas {
    width: 14px;
    text-align: center;
}

/* تحسين الرسائل */
.alert {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f1aeb5 100%);
}
</style>

<?php include_once '../../includes/footer.php'; ?>
