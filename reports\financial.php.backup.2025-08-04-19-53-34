<?php
/**
 * التقارير المالية
 * Financial Reports
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();

$user_role = $_SESSION['role'] ?? '';
$user_id = $_SESSION['user_id'] ?? 0;

// التحقق من الصلاحيات - فقط الإداريين
if (!check_permission('admin')) {
    header('Location: ../dashboard/');
    exit();
}

// فلاتر التقرير
$filter_year = clean_input($_GET['year'] ?? date('Y'));
$filter_month = clean_input($_GET['month'] ?? '');
$filter_type = clean_input($_GET['type'] ?? 'overview');

// الحصول على الإحصائيات المالية
$current_year = get_current_academic_year();

// إحصائيات الرسوم
$fees_query = "
    SELECT 
        COUNT(*) as total_fees,
        SUM(final_amount) as total_amount,
        SUM(CASE WHEN status = 'paid' THEN final_amount ELSE 0 END) as paid_amount,
        SUM(CASE WHEN status = 'pending' THEN final_amount ELSE 0 END) as pending_amount,
        SUM(CASE WHEN status = 'overdue' THEN final_amount ELSE 0 END) as overdue_amount
    FROM student_fees 
    WHERE YEAR(created_at) = ?
    " . (!empty($filter_month) ? " AND MONTH(created_at) = ?" : "");

$fees_stmt = $conn->prepare($fees_query);
if (!empty($filter_month)) {
    $fees_stmt->bind_param("ii", $filter_year, $filter_month);
} else {
    $fees_stmt->bind_param("i", $filter_year);
}
$fees_stmt->execute();
$fees_stats = $fees_stmt->get_result()->fetch_assoc();

// إحصائيات المدفوعات
$payments_query = "
    SELECT 
        COUNT(*) as total_payments,
        SUM(amount) as total_amount,
        COUNT(CASE WHEN payment_method = 'cash' THEN 1 END) as cash_payments,
        SUM(CASE WHEN payment_method = 'cash' THEN amount ELSE 0 END) as cash_amount,
        COUNT(CASE WHEN payment_method = 'bank_transfer' THEN 1 END) as bank_payments,
        SUM(CASE WHEN payment_method = 'bank_transfer' THEN amount ELSE 0 END) as bank_amount
    FROM student_payments 
    WHERE status = 'confirmed'
    AND YEAR(payment_date) = ?
    " . (!empty($filter_month) ? " AND MONTH(payment_date) = ?" : "");

$payments_stmt = $conn->prepare($payments_query);
if (!empty($filter_month)) {
    $payments_stmt->bind_param("ii", $filter_year, $filter_month);
} else {
    $payments_stmt->bind_param("i", $filter_year);
}
$payments_stmt->execute();
$payments_stats = $payments_stmt->get_result()->fetch_assoc();

// التأكد من أن القيم ليست null
$fees_stats = [
    'total_fees' => $fees_stats['total_fees'] ?? 0,
    'total_amount' => $fees_stats['total_amount'] ?? 0,
    'paid_amount' => $fees_stats['paid_amount'] ?? 0,
    'pending_amount' => $fees_stats['pending_amount'] ?? 0,
    'overdue_amount' => $fees_stats['overdue_amount'] ?? 0
];

$payments_stats = [
    'total_payments' => $payments_stats['total_payments'] ?? 0,
    'total_amount' => $payments_stats['total_amount'] ?? 0,
    'cash_payments' => $payments_stats['cash_payments'] ?? 0,
    'cash_amount' => $payments_stats['cash_amount'] ?? 0,
    'bank_payments' => $payments_stats['bank_payments'] ?? 0,
    'bank_amount' => $payments_stats['bank_amount'] ?? 0
];

include_once '../includes/header.php';
?>

<div class="container-fluid my-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i><?php echo __('financial_reports'); ?>
                        </h5>
                        <div>
                            <a href="../finance/" class="btn btn-light btn-sm">
                                <i class="fas fa-money-bill-wave me-1"></i><?php echo __('finance'); ?>
                            </a>
                            <a href="index.php" class="btn btn-secondary btn-sm">
                                <i class="fas fa-arrow-left me-1"></i><?php echo __('back_to_reports'); ?>
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- فلاتر التقرير -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-filter me-2"></i><?php echo __('report_filters'); ?>
                            </h6>
                        </div>
                        <div class="card-body">
                            <form method="get" class="row g-3">
                                <div class="col-md-3">
                                    <label for="year" class="form-label"><?php echo __('year'); ?></label>
                                    <select class="form-select" id="year" name="year">
                                        <?php for ($y = date('Y') - 2; $y <= date('Y') + 1; $y++): ?>
                                            <option value="<?php echo $y; ?>" <?php echo $filter_year == $y ? 'selected' : ''; ?>><?php echo $y; ?></option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="month" class="form-label"><?php echo __('month'); ?></label>
                                    <select class="form-select" id="month" name="month">
                                        <option value=""><?php echo __('all_months'); ?></option>
                                        <?php for ($m = 1; $m <= 12; $m++): ?>
                                            <option value="<?php echo $m; ?>" <?php echo $filter_month == $m ? 'selected' : ''; ?>>
                                                <?php echo date('F', mktime(0, 0, 0, $m, 1)); ?>
                                            </option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="type" class="form-label"><?php echo __('report_type'); ?></label>
                                    <select class="form-select" id="type" name="type">
                                        <option value="overview" <?php echo $filter_type === 'overview' ? 'selected' : ''; ?>><?php echo __('overview'); ?></option>
                                        <option value="detailed" <?php echo $filter_type === 'detailed' ? 'selected' : ''; ?>><?php echo __('detailed'); ?></option>
                                    </select>
                                </div>
                                
                                <div class="col-md-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search me-2"></i><?php echo __('generate_report'); ?>
                                        </button>
                                        <a href="export_financial.php?export=excel&<?php echo http_build_query($_GET); ?>" class="btn btn-success">
                                            <i class="fas fa-file-excel me-2"></i><?php echo __('export_excel'); ?>
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- إحصائيات الرسوم -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="mb-0"><?php echo number_format($fees_stats['total_fees']); ?></h4>
                                            <p class="mb-0"><?php echo __('total_fees'); ?></p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-receipt fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="mb-0"><?php echo number_format($fees_stats['paid_amount'], 2); ?></h4>
                                            <p class="mb-0"><?php echo __('paid_amount'); ?></p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-check-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="mb-0"><?php echo number_format($fees_stats['pending_amount'], 2); ?></h4>
                                            <p class="mb-0"><?php echo __('pending_amount'); ?></p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-clock fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="mb-0"><?php echo number_format($fees_stats['overdue_amount'], 2); ?></h4>
                                            <p class="mb-0"><?php echo __('overdue_amount'); ?></p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إحصائيات المدفوعات -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="mb-0"><?php echo number_format($payments_stats['total_payments']); ?></h4>
                                            <p class="mb-0"><?php echo __('total_payments'); ?></p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-credit-card fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card bg-secondary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="mb-0"><?php echo number_format($payments_stats['cash_amount'], 2); ?></h4>
                                            <p class="mb-0"><?php echo __('cash_payments'); ?></p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-money-bill fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card bg-dark text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="mb-0"><?php echo number_format($payments_stats['bank_amount'], 2); ?></h4>
                                            <p class="mb-0"><?php echo __('bank_transfers'); ?></p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-university fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معدل التحصيل -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-percentage me-2"></i><?php echo __('collection_rate'); ?>
                            </h6>
                        </div>
                        <div class="card-body">
                            <?php 
                            $collection_rate = $fees_stats['total_amount'] > 0 ? 
                                ($fees_stats['paid_amount'] / $fees_stats['total_amount']) * 100 : 0;
                            ?>
                            <div class="progress mb-3" style="height: 30px;">
                                <div class="progress-bar bg-success" role="progressbar" 
                                     style="width: <?php echo $collection_rate; ?>%" 
                                     aria-valuenow="<?php echo $collection_rate; ?>" 
                                     aria-valuemin="0" aria-valuemax="100">
                                    <?php echo number_format($collection_rate, 1); ?>%
                                </div>
                            </div>
                            <div class="row text-center">
                                <div class="col-md-4">
                                    <h5><?php echo number_format($fees_stats['total_amount'], 2); ?></h5>
                                    <small class="text-muted"><?php echo __('total_amount'); ?></small>
                                </div>
                                <div class="col-md-4">
                                    <h5 class="text-success"><?php echo number_format($fees_stats['paid_amount'], 2); ?></h5>
                                    <small class="text-muted"><?php echo __('collected_amount'); ?></small>
                                </div>
                                <div class="col-md-4">
                                    <h5 class="text-danger"><?php echo number_format($fees_stats['total_amount'] - $fees_stats['paid_amount'], 2); ?></h5>
                                    <small class="text-muted"><?php echo __('remaining_amount'); ?></small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once '../includes/footer.php'; ?>
