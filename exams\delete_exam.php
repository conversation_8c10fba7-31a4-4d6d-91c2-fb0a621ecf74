<?php
/**
 * حذف امتحان - صفحة تأكيد مستقلة
 * Delete Exam - Standalone Confirmation Page
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من تسجيل الدخول
check_session();

// التحقق من معرف الامتحان
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: index.php');
    exit();
}

$exam_id = intval($_GET['id']);
$success_message = '';
$error_message = '';

// جلب بيانات الامتحان
$exam_stmt = $conn->prepare("
    SELECT e.*, s.subject_name, c.class_name, c.grade_level, u.full_name as teacher_name,
           COUNT(DISTINCT eq.id) as question_count,
           COUNT(DISTINCT ea.id) as attempt_count
    FROM exams e
    LEFT JOIN subjects s ON e.subject_id = s.id
    LEFT JOIN classes c ON e.class_id = c.id
    LEFT JOIN teachers t ON e.teacher_id = t.id
    LEFT JOIN users u ON t.user_id = u.id
    LEFT JOIN exam_questions eq ON e.id = eq.exam_id
    LEFT JOIN exam_attempts ea ON e.id = ea.exam_id
    WHERE e.id = ?
    GROUP BY e.id
");
$exam_stmt->bind_param("i", $exam_id);
$exam_stmt->execute();
$exam = $exam_stmt->get_result()->fetch_assoc();

if (!$exam) {
    header('Location: index.php');
    exit();
}

// التحقق من الصلاحيات
$user_role = $_SESSION['role'];
$user_id = $_SESSION['user_id'];

if ($user_role === 'teacher') {
    // المعلم يمكنه حذف امتحاناته فقط
    $teacher_stmt = $conn->prepare("SELECT id FROM teachers WHERE user_id = ?");
    $teacher_stmt->bind_param("i", $user_id);
    $teacher_stmt->execute();
    $teacher_result = $teacher_stmt->get_result();
    $teacher_data = $teacher_result->fetch_assoc();
    $teacher_id = $teacher_data['id'] ?? 0;
    
    if ($exam['teacher_id'] != $teacher_id) {
        header('Location: index.php');
        exit();
    }
} elseif (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    header('Location: index.php');
    exit();
}

// معالجة الحذف
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_delete'])) {
    $conn->begin_transaction();
    try {
        // حذف محاولات الامتحان
        $delete_attempts = $conn->prepare("DELETE FROM exam_attempts WHERE exam_id = ?");
        $delete_attempts->bind_param("i", $exam_id);
        $delete_attempts->execute();
        
        // حذف أسئلة الامتحان
        $delete_questions = $conn->prepare("DELETE FROM exam_questions WHERE exam_id = ?");
        $delete_questions->bind_param("i", $exam_id);
        $delete_questions->execute();
        
        // حذف الامتحان
        $delete_exam = $conn->prepare("DELETE FROM exams WHERE id = ?");
        $delete_exam->bind_param("i", $exam_id);
        $delete_exam->execute();
        
        $conn->commit();
        header('Location: index.php?deleted=1');
        exit();
        
    } catch (Exception $e) {
        $conn->rollback();
        $error_message = "خطأ في حذف الامتحان: " . $e->getMessage();
    }
}

$page_title = 'حذف الامتحان: ' . $exam['exam_title'];
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-trash-alt me-2 text-danger"></i><?php echo $page_title; ?></h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="index.php">الامتحانات</a></li>
                    <li class="breadcrumb-item active">حذف الامتحان</li>
                </ol>
            </nav>
        </div>
        <a href="index.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
        </a>
    </div>

    <!-- رسائل التنبيه -->
    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>تأكيد حذف الامتحان</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>تحذير: هذا الإجراء لا يمكن التراجع عنه!</h6>
                        <p class="mb-0">
                            سيتم حذف الامتحان وجميع الأسئلة والمحاولات المرتبطة به نهائياً من النظام.
                        </p>
                    </div>

                    <?php if ($exam['attempt_count'] > 0): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-users me-2"></i>
                            <strong>تنبيه:</strong> هذا الامتحان يحتوي على <?php echo $exam['attempt_count']; ?> محاولة من الطلاب. 
                            حذف الامتحان سيؤدي إلى فقدان جميع هذه المحاولات والدرجات.
                        </div>
                    <?php endif; ?>

                    <!-- معلومات الامتحان -->
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6>معلومات الامتحان المراد حذفه:</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <td><strong>عنوان الامتحان:</strong></td>
                                            <td><?php echo htmlspecialchars($exam['exam_title']); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>المادة:</strong></td>
                                            <td><?php echo htmlspecialchars($exam['subject_name']); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>الفصل:</strong></td>
                                            <td><?php echo htmlspecialchars($exam['class_name'] . ' - ' . $exam['grade_level']); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>المعلم:</strong></td>
                                            <td><?php echo htmlspecialchars($exam['teacher_name']); ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <td><strong>نوع الامتحان:</strong></td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo match($exam['exam_type']) {
                                                        'quiz' => 'info',
                                                        'midterm' => 'warning',
                                                        'final' => 'danger',
                                                        'assignment' => 'success',
                                                        default => 'secondary'
                                                    };
                                                ?>">
                                                    <?php 
                                                    $exam_types = [
                                                        'quiz' => 'اختبار قصير',
                                                        'midterm' => 'امتحان نصف الفصل',
                                                        'final' => 'امتحان نهائي',
                                                        'assignment' => 'واجب'
                                                    ];
                                                    echo $exam_types[$exam['exam_type']] ?? $exam['exam_type'];
                                                    ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>عدد الأسئلة:</strong></td>
                                            <td><span class="badge bg-primary"><?php echo $exam['question_count']; ?></span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>عدد المحاولات:</strong></td>
                                            <td><span class="badge bg-<?php echo $exam['attempt_count'] > 0 ? 'warning' : 'secondary'; ?>"><?php echo $exam['attempt_count']; ?></span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>الدرجة الكاملة:</strong></td>
                                            <td><?php echo $exam['total_marks']; ?> درجة</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            
                            <?php if ($exam['exam_description']): ?>
                                <div class="mt-3">
                                    <strong>وصف الامتحان:</strong>
                                    <p class="text-muted"><?php echo htmlspecialchars($exam['exam_description']); ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- إحصائيات الحذف -->
                    <div class="row mt-4">
                        <div class="col-md-4 text-center">
                            <div class="bg-danger text-white p-3 rounded">
                                <h4>1</h4>
                                <small>امتحان سيتم حذفه</small>
                            </div>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="bg-warning text-white p-3 rounded">
                                <h4><?php echo $exam['question_count']; ?></h4>
                                <small>سؤال سيتم حذفه</small>
                            </div>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="bg-info text-white p-3 rounded">
                                <h4><?php echo $exam['attempt_count']; ?></h4>
                                <small>محاولة سيتم حذفها</small>
                            </div>
                        </div>
                    </div>

                    <!-- نموذج التأكيد -->
                    <form method="POST" class="mt-4">
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                            <label class="form-check-label" for="confirmDelete">
                                <strong>أؤكد أنني أريد حذف هذا الامتحان وجميع البيانات المرتبطة به نهائياً</strong>
                            </label>
                        </div>

                        <?php if ($exam['attempt_count'] > 0): ?>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="confirmAttempts" required>
                                <label class="form-check-label" for="confirmAttempts">
                                    <strong>أؤكد أنني أدرك أن حذف الامتحان سيؤدي إلى فقدان <?php echo $exam['attempt_count']; ?> محاولة من الطلاب</strong>
                                </label>
                            </div>
                        <?php endif; ?>

                        <div class="d-flex justify-content-between">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <div>
                                <a href="view.php?id=<?php echo $exam_id; ?>" class="btn btn-outline-info me-2">
                                    <i class="fas fa-eye me-2"></i>عرض الامتحان
                                </a>
                                <a href="edit.php?id=<?php echo $exam_id; ?>" class="btn btn-outline-primary me-2">
                                    <i class="fas fa-edit me-2"></i>تعديل بدلاً من الحذف
                                </a>
                                <button type="submit" name="confirm_delete" class="btn btn-danger" id="deleteButton" disabled>
                                    <i class="fas fa-trash-alt me-2"></i>حذف نهائياً
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-danger">ما يحدث عند الحذف:</h6>
                            <ul class="small">
                                <li>حذف الامتحان نهائياً</li>
                                <li>حذف جميع الأسئلة المرتبطة</li>
                                <li>حذف جميع محاولات الطلاب</li>
                                <li>فقدان جميع الدرجات والنتائج</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">بدائل الحذف:</h6>
                            <ul class="small">
                                <li>تعطيل الامتحان بدلاً من حذفه</li>
                                <li>تعديل بيانات الامتحان</li>
                                <li>نسخ الامتحان لاستخدامه لاحقاً</li>
                                <li>أرشفة الامتحان للمراجعة</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تفعيل زر الحذف عند تأكيد جميع الاختيارات
function checkDeleteButton() {
    const confirmDelete = document.getElementById('confirmDelete').checked;
    const confirmAttempts = document.getElementById('confirmAttempts');
    const attemptsChecked = !confirmAttempts || confirmAttempts.checked;
    
    document.getElementById('deleteButton').disabled = !(confirmDelete && attemptsChecked);
}

document.getElementById('confirmDelete').addEventListener('change', checkDeleteButton);
const confirmAttempts = document.getElementById('confirmAttempts');
if (confirmAttempts) {
    confirmAttempts.addEventListener('change', checkDeleteButton);
}
</script>

<?php include_once '../includes/footer.php'; ?>
