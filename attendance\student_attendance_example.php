<?php
/**
 * مثال على صفحة حضور الطلاب مع نظام الصلاحيات الجديد
 * Example Student Attendance Page with New Permissions System
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';

// التحقق من تسجيل الدخول
check_session();

// التحقق من صلاحية عرض حضور الطلاب
if (!can_view_student_attendance()) {
    $error_message = "ليس لديك صلاحية للوصول إلى حضور الطلاب";
    if ($_SESSION['role'] === 'staff_affairs_admin') {
        $error_message .= ". يمكنك الوصول إلى <a href='staff_attendance_example.php'>حضور العاملين</a> بدلاً من ذلك.";
    }
    
    include_once '../includes/header.php';
    ?>
    <div class="container-fluid">
        <div class="alert alert-danger">
            <h4><i class="fas fa-exclamation-triangle me-2"></i>غير مصرح</h4>
            <p><?php echo $error_message; ?></p>
            <a href="../dashboard/" class="btn btn-primary">العودة للرئيسية</a>
        </div>
    </div>
    <?php
    include_once '../includes/footer.php';
    exit();
}

// جلب بيانات الطلاب (مثال)
$students_query = "
    SELECT s.id, s.student_id, s.first_name, s.last_name, c.class_name, c.grade_level
    FROM students s
    LEFT JOIN classes c ON s.class_id = c.id
    WHERE s.status = 'active'
    ORDER BY c.grade_level, c.class_name, s.first_name
    LIMIT 20
";
$students_result = $conn->query($students_query);

$page_title = 'حضور الطلاب - ' . get_role_name_arabic($_SESSION['role']);
include_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-user-graduate me-2"></i><?php echo $page_title; ?></h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../dashboard/">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="index.php">الحضور والغياب</a></li>
                    <li class="breadcrumb-item active">حضور الطلاب</li>
                </ol>
            </nav>
        </div>
        <div>
            <?php if (can_manage_student_attendance()): ?>
                <button class="btn btn-primary me-2">
                    <i class="fas fa-plus me-2"></i>تسجيل حضور
                </button>
            <?php endif; ?>
            
            <?php if (has_permission('student_attendance_reports')): ?>
                <button class="btn btn-success">
                    <i class="fas fa-chart-bar me-2"></i>التقارير
                </button>
            <?php endif; ?>
        </div>
    </div>

    <!-- معلومات الدور والصلاحيات -->
    <div class="alert alert-info">
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-user me-2"></i>دورك الحالي:</h6>
                <span class="badge bg-primary"><?php echo get_role_name_arabic($_SESSION['role']); ?></span>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-key me-2"></i>صلاحياتك:</h6>
                <div>
                    <?php if (can_view_student_attendance()): ?>
                        <span class="badge bg-success me-1">عرض حضور الطلاب</span>
                    <?php endif; ?>
                    
                    <?php if (can_manage_student_attendance()): ?>
                        <span class="badge bg-warning me-1">إدارة حضور الطلاب</span>
                    <?php endif; ?>
                    
                    <?php if (has_permission('student_attendance_reports')): ?>
                        <span class="badge bg-info me-1">تقارير الطلاب</span>
                    <?php endif; ?>
                    
                    <?php if (can_access_student_data()): ?>
                        <span class="badge bg-secondary me-1">بيانات الطلاب</span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول الطلاب -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-table me-2"></i>قائمة الطلاب</h5>
        </div>
        <div class="card-body">
            <?php if ($students_result && $students_result->num_rows > 0): ?>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>رقم الطالب</th>
                                <th>الاسم</th>
                                <th>الفصل</th>
                                <th>المرحلة</th>
                                <th>الحالة</th>
                                <?php if (can_manage_student_attendance()): ?>
                                    <th>الإجراءات</th>
                                <?php endif; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($student = $students_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($student['student_id']); ?></td>
                                    <td>
                                        <?php if (can_access_student_data()): ?>
                                            <strong><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></strong>
                                        <?php else: ?>
                                            <span class="text-muted">غير مصرح بعرض الاسم</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($student['class_name'] ?? 'غير محدد'); ?></td>
                                    <td><?php echo htmlspecialchars($student['grade_level'] ?? 'غير محدد'); ?></td>
                                    <td>
                                        <span class="badge bg-success">حاضر</span>
                                    </td>
                                    <?php if (can_manage_student_attendance()): ?>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" title="تعديل الحضور">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                        </td>
                                    <?php endif; ?>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد بيانات طلاب</h5>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- معلومات إضافية حسب الدور -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-info-circle me-2"></i>معلومات دورك</h6>
                </div>
                <div class="card-body">
                    <?php
                    $role = $_SESSION['role'];
                    switch ($role) {
                        case 'admin':
                            echo '<p class="text-success">كمدير للنظام، يمكنك الوصول إلى جميع بيانات الطلاب والعاملين.</p>';
                            echo '<a href="staff_attendance_example.php" class="btn btn-outline-primary btn-sm">عرض حضور العاملين</a>';
                            break;
                        case 'student_affairs_admin':
                            echo '<p class="text-info">كمدير لشؤون الطلاب، يمكنك الوصول إلى بيانات الطلاب فقط.</p>';
                            echo '<p class="text-muted small">لا يمكنك الوصول إلى بيانات العاملين.</p>';
                            break;
                        case 'teacher':
                            echo '<p class="text-warning">كمعلم، يمكنك عرض حضور طلابك في الفصول التي تدرسها.</p>';
                            break;
                        default:
                            echo '<p class="text-muted">دور غير محدد.</p>';
                    }
                    ?>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-link me-2"></i>روابط سريعة</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <?php if (can_view_staff_attendance()): ?>
                            <a href="staff_attendance_example.php" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-users me-2"></i>حضور العاملين
                            </a>
                        <?php endif; ?>
                        
                        <?php if (has_permission('student_attendance_reports')): ?>
                            <a href="#" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-chart-bar me-2"></i>تقارير حضور الطلاب
                            </a>
                        <?php endif; ?>
                        
                        <a href="../dashboard/" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// إضافة تأثيرات تفاعلية
document.addEventListener('DOMContentLoaded', function() {
    // تمييز الصلاحيات
    const badges = document.querySelectorAll('.badge');
    badges.forEach(badge => {
        badge.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.1)';
        });
        badge.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
});
</script>

<?php include_once '../includes/footer.php'; ?>
