<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// بدء الجلسة إذا لم تكن مبدوءة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من الصلاحيات قبل أي output
check_session();
if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    header('Location: ../dashboard/');
    exit();
}

$class_id = intval($_GET['id'] ?? 0);
if (!$class_id) {
    $_SESSION['error_message'] = __('invalid_class_id');
    header('Location: index.php');
    exit();
}

// معالجة التحديث قبل أي output
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $class_name = clean_input($_POST['class_name'] ?? '');
    $grade_level = clean_input($_POST['grade_level'] ?? '');
    $stage_id = intval($_POST['stage_id'] ?? 0);
    $grade_id = intval($_POST['grade_id'] ?? 0);
    $capacity = intval($_POST['capacity'] ?? 0);
    $description = clean_input($_POST['description'] ?? '');
    $status = clean_input($_POST['status'] ?? 'active');
    $selected_subjects = isset($_POST['subject_ids']) ? $_POST['subject_ids'] : [];

    $errors = [];
    if (empty($class_name)) {
        $errors[] = __('class_name_required');
    }
    if (empty($grade_level)) {
        $errors[] = __('grade_level_required');
    }
    if ($capacity <= 0) {
        $errors[] = __('capacity_must_be_positive');
    }

    if (empty($errors)) {
        global $conn;

        // التحقق من وجود اتصال قاعدة البيانات
        if (!$conn) {
            $errors[] = 'خطأ في الاتصال بقاعدة البيانات';
        } else {
            // التحقق من عدم تكرار اسم الفصل
            $check_stmt = $conn->prepare("SELECT id FROM classes WHERE class_name = ? AND id != ?");
        $check_stmt->bind_param("si", $class_name, $class_id);
        $check_stmt->execute();
        $existing = $check_stmt->get_result();

        if ($existing->num_rows > 0) {
            $errors[] = __('class_name_exists');
        } else {
            // تحديث الفصل
            if ($stage_id > 0 && $grade_id > 0) {
                $stmt = $conn->prepare("UPDATE classes SET class_name=?, grade_level=?, stage_id=?, grade_id=?, capacity=?, description=?, status=?, updated_at=NOW() WHERE id=?");
                $stmt->bind_param("ssiisssi", $class_name, $grade_level, $stage_id, $grade_id, $capacity, $description, $status, $class_id);
            } else {
                $stmt = $conn->prepare("UPDATE classes SET class_name=?, grade_level=?, stage_id=?, grade_id=?, capacity=?, status=?, updated_at=NOW() WHERE id=?");
                $stmt->bind_param("ssiissi", $class_name, $grade_level, $stage_id, $grade_id, $capacity, $status, $class_id);
            }

            if ($stmt->execute()) {
                // تحديث المواد المرتبطة بالفصل
                // حذف التكليفات القديمة للفصل
                $delete_assignments = $conn->prepare("DELETE FROM teacher_assignments WHERE class_id = ?");
                $delete_assignments->bind_param('i', $class_id);
                $delete_assignments->execute();
                $delete_assignments->close();

                // إضافة التكليفات الجديدة
                if (!empty($selected_subjects)) {
                    $assignment_query = "INSERT INTO teacher_assignments (teacher_id, class_id, subject_id, status, weekly_hours, academic_year_id, semester, assigned_at, created_at, updated_at) VALUES (?, ?, ?, 'active', 4, 1, 'first', NOW(), NOW(), NOW())";
                    $assignment_stmt = $conn->prepare($assignment_query);

                    // استخدام معلم افتراضي (ID = 1) - يمكن تحسين هذا لاحقاً
                    $default_teacher_id = 1;

                    foreach ($selected_subjects as $subject_id) {
                        $subject_id = intval($subject_id);
                        if ($subject_id > 0) {
                            $assignment_stmt->bind_param('iii', $default_teacher_id, $class_id, $subject_id);
                            $assignment_stmt->execute();
                        }
                    }
                    $assignment_stmt->close();
                }

                $_SESSION['success_message'] = __('class_updated_successfully');
                header('Location: index.php');
                exit();
            } else {
                $errors[] = __('error_occurred') . ': ' . $stmt->error;
            }
        }
        }
    }
}

// تضمين header بعد معالجة POST
$page_title = __('edit_class');
require_once '../includes/header.php';

// جلب بيانات الفصل
$class = null;
global $conn;
$stmt = $conn->prepare("SELECT * FROM classes WHERE id = ?");
$stmt->bind_param("i", $class_id);
$stmt->execute();
$result = $stmt->get_result();
if ($result->num_rows === 0) {
    $_SESSION['error_message'] = __('class_not_found');
    header('Location: index.php');
    exit();
}
$class = $result->fetch_assoc();

// جلب المواد المتاحة
$subjects_query = "
    SELECT s.id, s.subject_name, s.subject_code, g.grade_name, es.stage_name
    FROM subjects s
    LEFT JOIN grades g ON s.grade_id = g.id
    LEFT JOIN educational_stages es ON s.stage_id = es.id
    WHERE s.status = 'active'
    ORDER BY es.sort_order, g.sort_order, s.subject_name
";
$subjects_result = $conn->query($subjects_query);
$subjects = [];
if ($subjects_result) {
    while ($row = $subjects_result->fetch_assoc()) {
        $subjects[] = $row;
    }
}

// جلب المواد المرتبطة حالياً بالفصل
$current_subjects_query = "SELECT DISTINCT subject_id FROM teacher_assignments WHERE class_id = ? AND status = 'active'";
$current_subjects_stmt = $conn->prepare($current_subjects_query);
$current_subjects_stmt->bind_param('i', $class_id);
$current_subjects_stmt->execute();
$current_subjects_result = $current_subjects_stmt->get_result();
$current_subject_ids = [];
while ($row = $current_subjects_result->fetch_assoc()) {
    $current_subject_ids[] = $row['subject_id'];
}
$current_subjects_stmt->close();

// جلب المراحل الدراسية
$stages = [];
$stages_result = $conn->query("SELECT id, stage_name, stage_code FROM educational_stages WHERE status = 'active' ORDER BY sort_order");
if ($stages_result) {
    while ($row = $stages_result->fetch_assoc()) {
        $stages[] = $row;
    }
}

// جلب الصفوف الدراسية
$grades = [];
$grades_result = $conn->query("
    SELECT g.id, g.grade_name, g.grade_code, g.stage_id, es.stage_name
    FROM grades g
    INNER JOIN educational_stages es ON g.stage_id = es.id
    WHERE g.status = 'active'
    ORDER BY es.sort_order, g.sort_order
");
if ($grades_result) {
    while ($row = $grades_result->fetch_assoc()) {
        $grades[] = $row;
    }
}

// تعريف متغيرات الرسائل
$error_message = '';
$success_message = '';

// إذا كان هناك أخطاء من معالجة POST، عرضها
if (isset($errors) && !empty($errors)) {
    $error_message = implode('<br>', $errors);
}

// تعريف متغيرات POST للاستخدام في النموذج
$post_class_name = $_POST['class_name'] ?? '';
$post_grade_level = $_POST['grade_level'] ?? '';
$post_stage_id = $_POST['stage_id'] ?? '';
$post_grade_id = $_POST['grade_id'] ?? '';
$post_capacity = $_POST['capacity'] ?? '';
$post_description = $_POST['description'] ?? '';
$post_status = $_POST['status'] ?? '';
?>
<link rel="stylesheet" href="../assets/css/modern-style.css">
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?php echo __('edit_class'); ?></h1>
            <p class="text-muted"><?php echo __('update_class_info'); ?></p>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i><?php echo __('back_to_list'); ?>
            </a>
        </div>
    </div>
    <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php elseif (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $_SESSION['success_message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php unset($_SESSION['success_message']); ?>
    <?php endif; ?>
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-school me-2"></i><?php echo __('class_information'); ?>
            </h5>
        </div>
        <div class="card-body">
            <form method="POST" class="needs-validation" novalidate>
                <div class="mb-3">
                    <label for="class_name" class="form-label"><?php echo __('class_name'); ?> <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="class_name" name="class_name" value="<?php echo htmlspecialchars($post_class_name ?: $class['class_name']); ?>" required>
                    <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                </div>
                <div class="mb-3">
                    <label for="grade_level" class="form-label"><?php echo __('grade_level'); ?> <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="grade_level" name="grade_level" value="<?php echo htmlspecialchars($post_grade_level ?: $class['grade_level']); ?>" required>
                    <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="stage_id" class="form-label"><?php echo __('educational_stage'); ?> <span class="text-danger">*</span></label>
                        <select class="form-select" id="stage_id" name="stage_id" required onchange="updateGrades()">
                            <option value=""><?php echo __('select_stage'); ?></option>
                            <?php foreach ($stages as $stage): ?>
                                <option value="<?php echo $stage['id']; ?>"
                                        <?php echo ($post_stage_id ?: $class['stage_id']) == $stage['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($stage['stage_name']); ?> (<?php echo htmlspecialchars($stage['stage_code']); ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="grade_id" class="form-label"><?php echo __('school_grade'); ?> <span class="text-danger">*</span></label>
                        <select class="form-select" id="grade_id" name="grade_id" required>
                            <option value=""><?php echo __('select_grade_first'); ?></option>
                            <?php foreach ($grades as $grade): ?>
                                <option value="<?php echo $grade['id']; ?>"
                                        data-stage="<?php echo $grade['stage_id']; ?>"
                                        <?php echo ($post_grade_id ?: $class['grade_id']) == $grade['id'] ? 'selected' : ''; ?>
                                        style="display: none;">
                                    <?php echo htmlspecialchars($grade['grade_name']); ?> (<?php echo htmlspecialchars($grade['grade_code']); ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="capacity" class="form-label"><?php echo __('capacity'); ?> <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" id="capacity" name="capacity" value="<?php echo htmlspecialchars($post_capacity ?: $class['capacity']); ?>" required min="1">
                    <div class="invalid-feedback"><?php echo __('required_field'); ?></div>
                </div>
                <div class="mb-3">
                    <label for="description" class="form-label"><?php echo __('description'); ?></label>
                    <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($post_description ?: ($class['description'] ?? '')); ?></textarea>
                </div>
                <div class="mb-3">
                    <label for="status" class="form-label"><?php echo __('status'); ?></label>
                    <select class="form-select" id="status" name="status">
                        <option value="active" <?php echo (($post_status ?: $class['status']) == 'active' ? 'selected' : ''); ?>><?php echo __('active'); ?></option>
                        <option value="inactive" <?php echo (($post_status ?: $class['status']) == 'inactive' ? 'selected' : ''); ?>><?php echo __('inactive'); ?></option>
                    </select>
                </div>
                <!-- المواد المرتبطة بالفصل -->
                <div class="mb-3">
                    <label for="subject_ids" class="form-label">
                        <i class="fas fa-book me-2"></i>المواد المرتبطة بالفصل
                        <small class="text-muted">(اختياري)</small>
                    </label>
                    <select class="form-select" id="subject_ids" name="subject_ids[]" multiple size="6">
                        <?php
                        $current_stage = '';
                        foreach ($subjects as $subject):
                            // إضافة عنوان المرحلة إذا تغيرت
                            if ($current_stage != $subject['stage_name'] && !empty($subject['stage_name'])) {
                                if ($current_stage != '') echo '</optgroup>';
                                echo '<optgroup label="' . htmlspecialchars($subject['stage_name']) . '">';
                                $current_stage = $subject['stage_name'];
                            }

                            // تحديد النص المعروض للمادة
                            $display_text = $subject['subject_name'];
                            if (!empty($subject['subject_code'])) {
                                $display_text .= ' (' . $subject['subject_code'] . ')';
                            }
                            if (!empty($subject['grade_name'])) {
                                $display_text .= ' - ' . $subject['grade_name'];
                            }

                            // تحديد ما إذا كانت المادة مختارة حالياً
                            $selected = in_array($subject['id'], $current_subject_ids) ? 'selected' : '';
                        ?>
                            <option value="<?php echo $subject['id']; ?>" <?php echo $selected; ?>>
                                <?php echo htmlspecialchars($display_text); ?>
                            </option>
                        <?php endforeach; ?>
                        <?php if ($current_stage != '') echo '</optgroup>'; ?>
                    </select>
                    <div class="form-text">
                        <i class="fas fa-info-circle me-1"></i>
                        اضغط Ctrl (أو Cmd) + النقر لاختيار عدة مواد. سيتم تحديث ربط الفصل بالمواد المحددة.
                        <br><strong>المواد المختارة حالياً:</strong> <?php echo count($current_subject_ids); ?> مادة
                    </div>
                </div>

                <div class="d-flex justify-content-end gap-2">
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i><?php echo __('cancel'); ?>
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i><?php echo __('update_class'); ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function updateGrades() {
    const stageSelect = document.getElementById('stage_id');
    const gradeSelect = document.getElementById('grade_id');
    const selectedStage = stageSelect.value;

    // إخفاء جميع الخيارات
    const gradeOptions = gradeSelect.querySelectorAll('option[data-stage]');
    gradeOptions.forEach(option => {
        option.style.display = 'none';
    });

    if (selectedStage) {
        // إظهار الصفوف المرتبطة بالمرحلة المختارة
        const relevantOptions = gradeSelect.querySelectorAll(`option[data-stage="${selectedStage}"]`);
        relevantOptions.forEach(option => {
            option.style.display = 'block';
        });
    }
}

// إضافة مؤشر بصري للمواد المختارة
function updateSubjectsDisplay() {
    const subjectsSelect = document.getElementById('subject_ids');
    const selectedCount = subjectsSelect.selectedOptions.length;
    const label = document.querySelector('label[for="subject_ids"]');

    if (selectedCount > 0) {
        label.innerHTML = `<i class="fas fa-book me-2"></i>المواد المرتبطة بالفصل <span class="badge bg-primary">${selectedCount}</span> <small class="text-muted">(اختياري)</small>`;
    } else {
        label.innerHTML = `<i class="fas fa-book me-2"></i>المواد المرتبطة بالفصل <small class="text-muted">(اختياري)</small>`;
    }
}

// تشغيل التحديث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateGrades();

    // إضافة مستمع لتحديث عرض المواد
    const subjectsSelect = document.getElementById('subject_ids');
    if (subjectsSelect) {
        subjectsSelect.addEventListener('change', updateSubjectsDisplay);
        updateSubjectsDisplay(); // تحديث أولي
    }
});
</script>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        Array.prototype.forEach.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<style>
/* تحسين مظهر حقل المواد */
#subject_ids {
    min-height: 150px;
}

#subject_ids optgroup {
    font-weight: bold;
    color: #495057;
    background-color: #f8f9fa;
}

#subject_ids option {
    padding: 8px 12px;
    font-weight: normal;
}

#subject_ids option:checked {
    background-color: #007bff;
    color: white;
}

/* تحسين عرض الشارات */
.badge {
    font-size: 0.75em;
    vertical-align: middle;
}

/* تحسين النص التوضيحي */
.form-text {
    font-size: 0.875em;
    margin-top: 0.5rem;
}
</style>

<?php require_once '../includes/footer.php'; ?>