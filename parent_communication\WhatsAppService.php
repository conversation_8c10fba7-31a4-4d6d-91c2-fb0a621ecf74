<?php
/**
 * خدمة واتساب للتواصل مع أولياء الأمور
 * WhatsApp Service for Parent Communication
 */

class WhatsAppService {
    private $apiUrl;
    private $accessToken;
    private $phoneNumberId;
    private $conn;
    
    public function __construct($conn) {
        $this->conn = $conn;
        $this->loadSettings();
    }
    
    /**
     * تحميل إعدادات واتساب من قاعدة البيانات
     */
    private function loadSettings() {
        $settings_query = "SELECT setting_key, setting_value FROM communication_settings WHERE setting_key IN ('whatsapp_api_url', 'whatsapp_access_token', 'whatsapp_phone_number_id')";
        $result = $this->conn->query($settings_query);
        
        $settings = [];
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }
        }
        
        $this->apiUrl = $settings['whatsapp_api_url'] ?? '';
        $this->accessToken = $settings['whatsapp_access_token'] ?? '';
        $this->phoneNumberId = $settings['whatsapp_phone_number_id'] ?? '';
    }
    
    /**
     * تنسيق رقم الهاتف
     */
    private function formatPhone($phone) {
        // إزالة المسافات والرموز
        $phone = preg_replace('/[^0-9+]/', '', $phone);
        
        // إذا بدأ بـ 05، استبدله بـ +9665
        if (substr($phone, 0, 2) === '05') {
            $phone = '+9665' . substr($phone, 2);
        }
        // إذا بدأ بـ 5، أضف +966
        elseif (substr($phone, 0, 1) === '5' && strlen($phone) === 9) {
            $phone = '+966' . $phone;
        }
        // إذا بدأ بـ 966، أضف +
        elseif (substr($phone, 0, 3) === '966') {
            $phone = '+' . $phone;
        }
        
        return $phone;
    }
    
    /**
     * إرسال رسالة نصية عبر واتساب
     */
    public function sendMessage($phone, $message, $messageId = null) {
        try {
            $phone = $this->formatPhone($phone);
            
            // إذا لم تكن إعدادات واتساب متوفرة، استخدم الطريقة البديلة
            if (empty($this->apiUrl) || empty($this->accessToken) || empty($this->phoneNumberId)) {
                return $this->sendViaWhatsAppWeb($phone, $message, $messageId);
            }
            
            $data = [
                'messaging_product' => 'whatsapp',
                'to' => $phone,
                'type' => 'text',
                'text' => [
                    'body' => $message
                ]
            ];
            
            $response = $this->makeApiRequest($data);
            
            if ($response && isset($response['messages'][0]['id'])) {
                $this->updateMessageStatus($messageId, 'sent', $response['messages'][0]['id']);
                return [
                    'success' => true,
                    'message_id' => $response['messages'][0]['id'],
                    'method' => 'whatsapp_api'
                ];
            } else {
                $error = $response['error']['message'] ?? 'Unknown API error';
                $this->updateMessageStatus($messageId, 'failed', null, $error);
                return [
                    'success' => false,
                    'error' => $error,
                    'method' => 'whatsapp_api'
                ];
            }
            
        } catch (Exception $e) {
            $this->updateMessageStatus($messageId, 'failed', null, $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'method' => 'whatsapp_api'
            ];
        }
    }
    
    /**
     * إرسال رسالة باستخدام قالب
     */
    public function sendTemplate($phone, $templateName, $parameters = [], $messageId = null) {
        try {
            $phone = $this->formatPhone($phone);
            
            if (empty($this->apiUrl) || empty($this->accessToken) || empty($this->phoneNumberId)) {
                // تحويل القالب إلى رسالة نصية
                $message = $this->processTemplate($templateName, $parameters);
                return $this->sendViaWhatsAppWeb($phone, $message, $messageId);
            }
            
            $data = [
                'messaging_product' => 'whatsapp',
                'to' => $phone,
                'type' => 'template',
                'template' => [
                    'name' => $templateName,
                    'language' => ['code' => 'ar'],
                    'components' => [
                        [
                            'type' => 'body',
                            'parameters' => $parameters
                        ]
                    ]
                ]
            ];
            
            $response = $this->makeApiRequest($data);
            
            if ($response && isset($response['messages'][0]['id'])) {
                $this->updateMessageStatus($messageId, 'sent', $response['messages'][0]['id']);
                return [
                    'success' => true,
                    'message_id' => $response['messages'][0]['id'],
                    'method' => 'whatsapp_template'
                ];
            } else {
                $error = $response['error']['message'] ?? 'Unknown template error';
                $this->updateMessageStatus($messageId, 'failed', null, $error);
                return [
                    'success' => false,
                    'error' => $error,
                    'method' => 'whatsapp_template'
                ];
            }
            
        } catch (Exception $e) {
            $this->updateMessageStatus($messageId, 'failed', null, $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'method' => 'whatsapp_template'
            ];
        }
    }
    
    /**
     * إرسال عبر واتساب ويب (الطريقة البديلة)
     */
    private function sendViaWhatsAppWeb($phone, $message, $messageId = null) {
        $phone = $this->formatPhone($phone);
        $encodedMessage = urlencode($message);
        $whatsappUrl = "https://wa.me/{$phone}?text={$encodedMessage}";
        
        // حفظ الرابط في قاعدة البيانات للمراجعة اليدوية
        $this->saveWhatsAppLink($messageId, $whatsappUrl, $message);
        
        return [
            'success' => true,
            'whatsapp_url' => $whatsappUrl,
            'method' => 'whatsapp_web',
            'message' => 'تم إنشاء رابط واتساب. يرجى النقر على الرابط لإرسال الرسالة.'
        ];
    }
    
    /**
     * إجراء طلب API
     */
    private function makeApiRequest($data) {
        $url = $this->apiUrl . '/' . $this->phoneNumberId . '/messages';
        
        $headers = [
            'Authorization: Bearer ' . $this->accessToken,
            'Content-Type: application/json'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new Exception('cURL Error: ' . $error);
        }
        
        $decodedResponse = json_decode($response, true);
        
        if ($httpCode !== 200) {
            throw new Exception('HTTP Error ' . $httpCode . ': ' . ($decodedResponse['error']['message'] ?? 'Unknown error'));
        }
        
        return $decodedResponse;
    }
    
    /**
     * تحديث حالة الرسالة
     */
    private function updateMessageStatus($messageId, $status, $whatsappMessageId = null, $errorMessage = null) {
        if (!$messageId) return;
        
        $updateFields = ['status = ?'];
        $params = [$status];
        $types = 's';
        
        if ($whatsappMessageId) {
            $updateFields[] = 'whatsapp_message_id = ?';
            $params[] = $whatsappMessageId;
            $types .= 's';
        }
        
        if ($errorMessage) {
            $updateFields[] = 'error_message = ?';
            $params[] = $errorMessage;
            $types .= 's';
        }
        
        if ($status === 'sent') {
            $updateFields[] = 'sent_at = NOW()';
        }
        
        $updateFields[] = 'updated_at = NOW()';
        $params[] = $messageId;
        $types .= 'i';
        
        $query = "UPDATE parent_communications SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $stmt->close();
    }
    
    /**
     * حفظ رابط واتساب
     */
    private function saveWhatsAppLink($messageId, $whatsappUrl, $message) {
        if (!$messageId) return;
        
        $query = "UPDATE parent_communications SET 
                  whatsapp_message_id = ?, 
                  status = 'pending',
                  error_message = ?,
                  updated_at = NOW() 
                  WHERE id = ?";
        
        $stmt = $this->conn->prepare($query);
        $linkInfo = "WhatsApp Web Link: " . $whatsappUrl;
        $stmt->bind_param('ssi', $whatsappUrl, $linkInfo, $messageId);
        $stmt->execute();
        $stmt->close();
    }
    
    /**
     * معالجة القالب
     */
    private function processTemplate($templateName, $parameters) {
        // جلب القالب من قاعدة البيانات
        $query = "SELECT message_body, variables FROM message_templates WHERE template_name = ? AND is_active = 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bind_param('s', $templateName);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($template = $result->fetch_assoc()) {
            $message = $template['message_body'];
            $variables = json_decode($template['variables'], true) ?? [];
            
            // استبدال المتغيرات
            foreach ($parameters as $key => $value) {
                $message = str_replace('{' . $key . '}', $value, $message);
            }
            
            return $message;
        }
        
        return "قالب الرسالة غير موجود";
    }
    
    /**
     * التحقق من إعدادات واتساب
     */
    public function isConfigured() {
        return !empty($this->apiUrl) && !empty($this->accessToken) && !empty($this->phoneNumberId);
    }
    
    /**
     * اختبار الاتصال
     */
    public function testConnection() {
        if (!$this->isConfigured()) {
            return [
                'success' => false,
                'message' => 'إعدادات واتساب غير مكتملة'
            ];
        }
        
        try {
            // محاولة جلب معلومات رقم الهاتف
            $url = $this->apiUrl . '/' . $this->phoneNumberId;
            $headers = [
                'Authorization: Bearer ' . $this->accessToken,
                'Content-Type: application/json'
            ];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode === 200) {
                return [
                    'success' => true,
                    'message' => 'الاتصال بواتساب يعمل بشكل صحيح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في الاتصال بواتساب (HTTP ' . $httpCode . ')'
                ];
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'خطأ في الاتصال: ' . $e->getMessage()
            ];
        }
    }
}
?>
