<?php
/**
 * صفحة حذف الإداري
 * Delete Administrator Page
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    redirect_to('../dashboard/');
}

// التحقق من وجود معرف الإداري
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error_message'] = 'معرف الإداري غير صحيح';
    redirect_to('index.php');
}

$administrator_id = intval($_GET['id']);

// جلب بيانات الإداري
$admin_stmt = $conn->prepare("
    SELECT a.*, u.full_name, u.username, u.email
    FROM staff a
    LEFT JOIN users u ON a.user_id = u.id
    WHERE a.id = ? AND u.role = 'staff'
");

$admin_stmt->bind_param("i", $administrator_id);
$admin_stmt->execute();
$admin_result = $admin_stmt->get_result();

if ($admin_result->num_rows === 0) {
    $_SESSION['error_message'] = 'الإداري غير موجود';
    redirect_to('index.php');
}

$administrator = $admin_result->fetch_assoc();

// معالجة طلب الحذف
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_delete'])) {
    try {
        // تعطيل فحص القيود الخارجية مؤقتاً
        $conn->query("SET FOREIGN_KEY_CHECKS = 0");

        $conn->begin_transaction();

        // تسجيل النشاط قبل الحذف
        log_activity($_SESSION['user_id'], 'delete_administrator', 'staff', $administrator_id, $administrator);

        // حذف سجلات الإجازات المرتبطة
        $delete_leaves_stmt = $conn->prepare("DELETE FROM staff_leaves WHERE user_id = ? AND (user_type = 'admin' OR user_type = 'staff')");
        $delete_leaves_stmt->bind_param("i", $administrator['user_id']);
        $delete_leaves_stmt->execute();

        // حذف أي سجلات حضور مرتبطة (إذا كانت موجودة)
        $check_table = $conn->query("SHOW TABLES LIKE 'staff_attendance'");
        if ($check_table->num_rows > 0) {
            $delete_attendance_stmt = $conn->prepare("DELETE FROM staff_attendance WHERE staff_id = ?");
            $delete_attendance_stmt->bind_param("i", $administrator_id);
            $delete_attendance_stmt->execute();
        }

        // حذف الإداري من جدول staff
        $delete_admin_stmt = $conn->prepare("DELETE FROM staff WHERE id = ?");
        $delete_admin_stmt->bind_param("i", $administrator_id);
        if (!$delete_admin_stmt->execute()) {
            throw new Exception("فشل في حذف سجل الإداري: " . $delete_admin_stmt->error);
        }

        // حذف المستخدم
        $delete_user_stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
        $delete_user_stmt->bind_param("i", $administrator['user_id']);
        if (!$delete_user_stmt->execute()) {
            throw new Exception("فشل في حذف المستخدم: " . $delete_user_stmt->error);
        }

        $conn->commit();

        // إعادة تفعيل فحص القيود الخارجية
        $conn->query("SET FOREIGN_KEY_CHECKS = 1");

        $_SESSION['success_message'] = 'تم حذف الإداري بنجاح';
        redirect_to('index.php');

    } catch (Exception $e) {
        $conn->rollback();
        // إعادة تفعيل فحص القيود الخارجية في حالة الخطأ
        $conn->query("SET FOREIGN_KEY_CHECKS = 1");
        log_error("Error deleting administrator: " . $e->getMessage());
        $_SESSION['error_message'] = 'حدث خطأ أثناء حذف الإداري: ' . $e->getMessage();
        redirect_to('index.php');
    }
}

$page_title = 'حذف الإداري: ' . ($administrator['full_name'] ?? $administrator['username']);
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-user-times text-danger me-2"></i>
                حذف الإداري
            </h2>
            <p class="text-muted mb-0">تأكيد حذف الإداري من النظام</p>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة للإداريين
            </a>
        </div>
    </div>

    <!-- Delete Confirmation -->
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تحذير: حذف الإداري
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger" role="alert">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            تحذير مهم!
                        </h6>
                        <p class="mb-0">
                            أنت على وشك حذف الإداري نهائياً من النظام. هذا الإجراء لا يمكن التراجع عنه وسيؤدي إلى:
                        </p>
                        <ul class="mt-2 mb-0">
                            <li>حذف جميع بيانات الإداري الشخصية والوظيفية</li>
                            <li>حذف حساب المستخدم المرتبط</li>
                            <li>حذف جميع سجلات الحضور والغياب</li>
                            <li>حذف جميع سجلات الإجازات</li>
                        </ul>
                    </div>

                    <!-- Administrator Details -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">بيانات الإداري المراد حذفه</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold">الاسم الكامل:</td>
                                            <td><?php echo htmlspecialchars($administrator['full_name'] ?? $administrator['username']); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">اسم المستخدم:</td>
                                            <td><?php echo htmlspecialchars($administrator['username']); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">البريد الإلكتروني:</td>
                                            <td><?php echo htmlspecialchars($administrator['email']); ?></td>
                                        </tr>
                                        <?php if (!empty($administrator['employee_id'])): ?>
                                        <tr>
                                            <td class="fw-bold">رقم الموظف:</td>
                                            <td><?php echo htmlspecialchars($administrator['employee_id']); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <?php if (!empty($administrator['position'])): ?>
                                        <tr>
                                            <td class="fw-bold">المنصب:</td>
                                            <td><?php echo htmlspecialchars($administrator['position']); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                        <?php if (!empty($administrator['department'])): ?>
                                        <tr>
                                            <td class="fw-bold">القسم:</td>
                                            <td><?php echo htmlspecialchars($administrator['department']); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                        <?php if (!empty($administrator['hire_date'])): ?>
                                        <tr>
                                            <td class="fw-bold">تاريخ التعيين:</td>
                                            <td><?php echo date('Y-m-d', strtotime($administrator['hire_date'])); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                        <tr>
                                            <td class="fw-bold">تاريخ الإنشاء:</td>
                                            <td><?php echo date('Y-m-d', strtotime($administrator['created_at'])); ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Confirmation Form -->
                    <form method="POST" class="text-center">
                        <div class="mb-4">
                            <div class="form-check d-inline-block">
                                <input class="form-check-input" type="checkbox" id="confirm_checkbox" required>
                                <label class="form-check-label" for="confirm_checkbox">
                                    أؤكد أنني أفهم عواقب هذا الإجراء وأريد المتابعة
                                </label>
                            </div>
                        </div>

                        <div class="d-flex justify-content-center gap-3">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <a href="view.php?id=<?php echo $administrator_id; ?>" class="btn btn-info">
                                <i class="fas fa-eye me-2"></i>عرض التفاصيل
                            </a>
                            <button type="submit" name="confirm_delete" class="btn btn-danger" id="delete_button" disabled>
                                <i class="fas fa-trash me-2"></i>حذف الإداري نهائياً
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const checkbox = document.getElementById('confirm_checkbox');
    const deleteButton = document.getElementById('delete_button');
    
    checkbox.addEventListener('change', function() {
        deleteButton.disabled = !this.checked;
    });
    
    // تأكيد إضافي عند الضغط على زر الحذف
    deleteButton.addEventListener('click', function(e) {
        if (!confirm('هل أنت متأكد من رغبتك في حذف هذا الإداري نهائياً؟\n\nهذا الإجراء لا يمكن التراجع عنه!')) {
            e.preventDefault();
        }
    });
});
</script>

<?php require_once '../includes/footer.php'; ?>
