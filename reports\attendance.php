<?php
/**
 * تقارير الحضور
 * Attendance Reports
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();

$user_role = $_SESSION['role'] ?? '';
$user_id = $_SESSION['user_id'] ?? 0;

// التحقق من الصلاحيات
if (!check_permission('admin') && !check_permission('teacher')) {
    header('Location: ../dashboard/');
    exit();
}

// فلاتر التقرير
$filter_date_from = clean_input($_GET['date_from'] ?? date('Y-m-01'));
$filter_date_to = clean_input($_GET['date_to'] ?? date('Y-m-d'));
$filter_class = clean_input($_GET['class'] ?? '');
$filter_type = clean_input($_GET['type'] ?? 'students');

// إحصائيات الحضور للطلاب
if ($filter_type === 'students') {
    $attendance_query = "
        SELECT 
            COUNT(*) as total_records,
            COUNT(CASE WHEN a.status = 'present' THEN 1 END) as present_count,
            COUNT(CASE WHEN a.status = 'absent' THEN 1 END) as absent_count,
            COUNT(CASE WHEN a.status = 'late' THEN 1 END) as late_count,
            COUNT(CASE WHEN a.status LIKE '%_leave' THEN 1 END) as leave_count
        FROM attendance a
        JOIN students s ON a.student_id = s.id
        JOIN classes c ON s.class_id = c.id
        WHERE a.attendance_date BETWEEN ? AND ?
        " . (!empty($filter_class) ? " AND s.class_id = ?" : "");
    
    $params = [$filter_date_from, $filter_date_to];
    $param_types = 'ss';
    
    if (!empty($filter_class)) {
        $params[] = $filter_class;
        $param_types .= 'i';
    }
} else {
    // إحصائيات الحضور للموظفين
    $attendance_query = "
        SELECT 
            COUNT(*) as total_records,
            COUNT(CASE WHEN status = 'present' THEN 1 END) as present_count,
            COUNT(CASE WHEN status = 'absent' THEN 1 END) as absent_count,
            COUNT(CASE WHEN status = 'late' THEN 1 END) as late_count,
            COUNT(CASE WHEN status LIKE '%_leave' THEN 1 END) as leave_count
        FROM (
            SELECT status FROM admin_attendance WHERE attendance_date BETWEEN ? AND ?
            UNION ALL
            SELECT status FROM teacher_attendance WHERE attendance_date BETWEEN ? AND ?
        ) combined_attendance
    ";
    
    $params = [$filter_date_from, $filter_date_to, $filter_date_from, $filter_date_to];
    $param_types = 'ssss';
}

$attendance_stmt = $conn->prepare($attendance_query);
$attendance_stmt->bind_param($param_types, ...$params);
$attendance_stmt->execute();
$attendance_stats = $attendance_stmt->get_result()->fetch_assoc();

// جلب قائمة الفصول
$classes_query = "SELECT id, class_name FROM classes ORDER BY class_name";
$classes_result = $conn->query($classes_query);

include_once '../includes/header.php';
?>

<div class="container-fluid my-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar-check me-2"></i><?php echo __('attendance_reports'); ?>
                        </h5>
                        <a href="index.php" class="btn btn-dark btn-sm">
                            <i class="fas fa-arrow-left me-1"></i><?php echo __('back_to_reports'); ?>
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- فلاتر التقرير -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-filter me-2"></i><?php echo __('report_filters'); ?>
                            </h6>
                        </div>
                        <div class="card-body">
                            <form method="get" class="row g-3">
                                <div class="col-md-2">
                                    <label for="type" class="form-label"><?php echo __('report_type'); ?></label>
                                    <select class="form-select" id="type" name="type">
                                        <option value="students" <?php echo $filter_type === 'students' ? 'selected' : ''; ?>><?php echo __('students'); ?></option>
                                        <option value="staff" <?php echo $filter_type === 'staff' ? 'selected' : ''; ?>><?php echo __('staff'); ?></option>
                                    </select>
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="date_from" class="form-label"><?php echo __('from_date'); ?></label>
                                    <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $filter_date_from; ?>">
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="date_to" class="form-label"><?php echo __('to_date'); ?></label>
                                    <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $filter_date_to; ?>">
                                </div>
                                
                                <?php if ($filter_type === 'students'): ?>
                                <div class="col-md-2">
                                    <label for="class" class="form-label"><?php echo __('class'); ?></label>
                                    <select class="form-select" id="class" name="class">
                                        <option value=""><?php echo __('all_classes'); ?></option>
                                        <?php 
                                        $classes_result->data_seek(0); // إعادة تعيين المؤشر
                                        while ($class = $classes_result->fetch_assoc()): 
                                        ?>
                                            <option value="<?php echo $class['id']; ?>" <?php echo $filter_class == $class['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($class['class_name']); ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                                <?php endif; ?>
                                
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search me-2"></i><?php echo __('generate_report'); ?>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- إحصائيات الحضور -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo number_format($attendance_stats['total_records'] ?? 0); ?></h3>
                                    <p class="mb-0"><?php echo __('total_records'); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo number_format($attendance_stats['present_count'] ?? 0); ?></h3>
                                    <p class="mb-0"><?php echo __('present'); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo number_format($attendance_stats['absent_count'] ?? 0); ?></h3>
                                    <p class="mb-0"><?php echo __('absent'); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo number_format($attendance_stats['late_count'] ?? 0); ?></h3>
                                    <p class="mb-0"><?php echo __('late'); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- معدل الحضور -->
                        <div class="col-lg-8 mb-4">
                            <div class="card h-100">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-percentage me-2"></i><?php echo __('attendance_rate'); ?>
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <?php
                                    $total_records = $attendance_stats['total_records'] ?? 0;
                                    $present_count = $attendance_stats['present_count'] ?? 0;
                                    $attendance_rate = $total_records > 0 ? ($present_count / $total_records) * 100 : 0;
                                    ?>
                                    <div class="progress mb-4" style="height: 30px;">
                                        <div class="progress-bar bg-success" role="progressbar"
                                             style="width: <?php echo $attendance_rate; ?>%"
                                             aria-valuenow="<?php echo $attendance_rate; ?>"
                                             aria-valuemin="0" aria-valuemax="100">
                                            <?php echo number_format($attendance_rate, 1); ?>%
                                        </div>
                                    </div>
                                    <div class="row text-center">
                                        <div class="col-6 col-md-3 mb-3">
                                            <div class="border rounded p-3">
                                                <h5 class="text-success mb-1"><?php echo number_format($present_count); ?></h5>
                                                <small class="text-muted"><?php echo __('present'); ?></small>
                                            </div>
                                        </div>
                                        <div class="col-6 col-md-3 mb-3">
                                            <div class="border rounded p-3">
                                                <h5 class="text-danger mb-1"><?php echo number_format($attendance_stats['absent_count'] ?? 0); ?></h5>
                                                <small class="text-muted"><?php echo __('absent'); ?></small>
                                            </div>
                                        </div>
                                        <div class="col-6 col-md-3 mb-3">
                                            <div class="border rounded p-3">
                                                <h5 class="text-warning mb-1"><?php echo number_format($attendance_stats['late_count'] ?? 0); ?></h5>
                                                <small class="text-muted"><?php echo __('late'); ?></small>
                                            </div>
                                        </div>
                                        <div class="col-6 col-md-3 mb-3">
                                            <div class="border rounded p-3">
                                                <h5 class="text-info mb-1"><?php echo number_format($attendance_stats['leave_count'] ?? 0); ?></h5>
                                                <small class="text-muted"><?php echo __('on_leave'); ?></small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- رسم بياني -->
                        <div class="col-lg-4 mb-4">
                            <div class="card h-100">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">
                                        <i class="fas fa-chart-pie me-2"></i><?php echo __('attendance_chart'); ?>
                                    </h6>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-secondary" onclick="resizeChart('small')" title="صغير">
                                            <i class="fas fa-compress-alt"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary active" onclick="resizeChart('medium')" title="متوسط">
                                            <i class="fas fa-expand-arrows-alt"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" onclick="resizeChart('large')" title="كبير">
                                            <i class="fas fa-expand-alt"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body text-center d-flex align-items-center justify-content-center">
                                    <div id="chartContainer" style="position: relative; height: 250px; width: 100%; max-width: 300px;">
                                        <canvas id="attendanceChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
#chartContainer {
    transition: all 0.3s ease;
}

.btn-group .btn {
    transition: all 0.2s ease;
}

.btn-group .btn:hover {
    transform: translateY(-1px);
}

.card-body {
    overflow: hidden;
}

/* تحسين مظهر الرسم البياني على الشاشات الصغيرة */
@media (max-width: 992px) {
    .col-lg-4 {
        margin-top: 1rem;
    }
}

@media (max-width: 768px) {
    #chartContainer {
        max-width: 100% !important;
        height: 200px !important;
    }

    .btn-group {
        flex-direction: row;
        flex-wrap: wrap;
    }

    .btn-group .btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .card-header .btn-group {
        margin-top: 0.5rem;
    }

    .card-header {
        flex-direction: column;
        align-items: flex-start !important;
    }
}

/* تحسين مظهر الأزرار */
.btn-group .btn.active {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// رسم بياني للحضور
const ctx = document.getElementById('attendanceChart').getContext('2d');
const attendanceChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: ['<?php echo __('present'); ?>', '<?php echo __('absent'); ?>', '<?php echo __('late'); ?>', '<?php echo __('on_leave'); ?>'],
        datasets: [{
            data: [
                <?php echo $attendance_stats['present_count'] ?? 0; ?>,
                <?php echo $attendance_stats['absent_count'] ?? 0; ?>,
                <?php echo $attendance_stats['late_count'] ?? 0; ?>,
                <?php echo $attendance_stats['leave_count'] ?? 0; ?>
            ],
            backgroundColor: [
                '#28a745',
                '#dc3545',
                '#ffc107',
                '#17a2b8'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 15,
                    usePointStyle: true,
                    font: {
                        size: 12
                    }
                }
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        const label = context.label || '';
                        const value = context.parsed;
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                        return label + ': ' + value + ' (' + percentage + '%)';
                    }
                }
            }
        },
        layout: {
            padding: {
                top: 10,
                bottom: 10
            }
        }
    }
});

// دالة تغيير حجم الرسم البياني
function resizeChart(size) {
    const container = document.getElementById('chartContainer');
    const buttons = document.querySelectorAll('.btn-group .btn');

    // إزالة الفئة النشطة من جميع الأزرار
    buttons.forEach(btn => btn.classList.remove('active'));

    // تحديد الحجم الجديد
    let height, maxWidth;
    switch(size) {
        case 'small':
            height = '180px';
            maxWidth = '200px';
            buttons[0].classList.add('active');
            break;
        case 'large':
            height = '350px';
            maxWidth = '400px';
            buttons[2].classList.add('active');
            break;
        default: // medium
            height = '250px';
            maxWidth = '300px';
            buttons[1].classList.add('active');
    }

    // تطبيق الحجم الجديد
    container.style.height = height;
    container.style.maxWidth = maxWidth;

    // حفظ التفضيل
    saveChartSize(size);

    // إعادة رسم الرسم البياني بعد تأخير قصير
    setTimeout(() => {
        attendanceChart.resize();
    }, 100);
}

// حفظ تفضيل الحجم في localStorage
function saveChartSize(size) {
    localStorage.setItem('attendanceChartSize', size);
}

// استرجاع تفضيل الحجم المحفوظ
function loadChartSize() {
    const savedSize = localStorage.getItem('attendanceChartSize');
    if (savedSize) {
        resizeChart(savedSize);
    }
}

// تحميل الحجم المحفوظ عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadChartSize();
});
</script>

<?php include_once '../includes/footer.php'; ?>
