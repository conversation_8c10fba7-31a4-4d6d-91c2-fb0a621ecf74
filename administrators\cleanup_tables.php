<?php
/**
 * سكريبت تنظيف الجداول وتوحيد جدول الإداريين
 * Script to cleanup tables and unify administrators table
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    die('غير مسموح لك بالوصول لهذه الصفحة');
}

echo "<h2>تنظيف وتوحيد جداول الإداريين</h2>";

try {
    // تعطيل فحص القيود الخارجية مؤقتاً
    $conn->query("SET FOREIGN_KEY_CHECKS = 0");
    echo "<p style='color: blue;'>ℹ️ تم تعطيل فحص القيود الخارجية مؤقتاً</p>";

    // التحقق من وجود الجداول
    echo "<h3>1. التحقق من الجداول الموجودة:</h3>";
    
    $tables_check = [
        'staff' => false,
        'administrators' => false
    ];
    
    foreach ($tables_check as $table => $exists) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows > 0) {
            $tables_check[$table] = true;
            echo "<p style='color: green;'>✅ جدول $table موجود</p>";
        } else {
            echo "<p style='color: red;'>❌ جدول $table غير موجود</p>";
        }
    }
    
    // التحقق من البيانات في كل جدول
    echo "<h3>2. التحقق من البيانات:</h3>";
    
    $data_count = [];
    
    if ($tables_check['staff']) {
        $result = $conn->query("SELECT COUNT(*) as count FROM staff");
        $data_count['staff'] = $result->fetch_assoc()['count'];
        echo "<p>جدول staff يحتوي على: " . $data_count['staff'] . " سجل</p>";
        
        if ($data_count['staff'] > 0) {
            $result = $conn->query("SELECT s.*, u.full_name, u.username FROM staff s JOIN users u ON s.user_id = u.id LIMIT 3");
            echo "<ul>";
            while ($row = $result->fetch_assoc()) {
                echo "<li>" . htmlspecialchars($row['full_name']) . " (" . htmlspecialchars($row['username']) . ")</li>";
            }
            echo "</ul>";
        }
    }
    
    if ($tables_check['administrators']) {
        $result = $conn->query("SELECT COUNT(*) as count FROM administrators");
        $data_count['administrators'] = $result->fetch_assoc()['count'];
        echo "<p>جدول administrators يحتوي على: " . $data_count['administrators'] . " سجل</p>";
        
        if ($data_count['administrators'] > 0) {
            $result = $conn->query("SELECT a.*, u.full_name, u.username FROM administrators a JOIN users u ON a.user_id = u.id LIMIT 3");
            echo "<ul>";
            while ($row = $result->fetch_assoc()) {
                echo "<li>" . htmlspecialchars($row['full_name']) . " (" . htmlspecialchars($row['username']) . ")</li>";
            }
            echo "</ul>";
        }
    }
    
    // اتخاذ القرار
    echo "<h3>3. القرار:</h3>";
    
    if ($tables_check['staff'] && $data_count['staff'] > 0) {
        echo "<p style='color: blue;'><strong>سنستخدم جدول staff كجدول رئيسي للإداريين</strong></p>";
        
        // إضافة الحقول المفقودة إلى جدول staff
        echo "<h4>إضافة الحقول المفقودة:</h4>";
        
        $fields_to_add = [
            'qualification' => "ALTER TABLE staff ADD COLUMN qualification varchar(100) DEFAULT NULL AFTER national_id",
            'experience_years' => "ALTER TABLE staff ADD COLUMN experience_years tinyint(3) UNSIGNED DEFAULT 0 AFTER qualification"
        ];
        
        foreach ($fields_to_add as $field => $sql) {
            $check_field = $conn->query("SHOW COLUMNS FROM staff LIKE '$field'");
            if ($check_field->num_rows == 0) {
                if ($conn->query($sql)) {
                    echo "<p style='color: green;'>✅ تم إضافة حقل $field</p>";
                } else {
                    echo "<p style='color: red;'>❌ فشل في إضافة حقل $field: " . $conn->error . "</p>";
                }
            } else {
                echo "<p style='color: blue;'>ℹ️ حقل $field موجود بالفعل</p>";
            }
        }
        
        // نقل البيانات من administrators إلى staff إذا كان هناك بيانات
        if ($tables_check['administrators'] && $data_count['administrators'] > 0) {
            echo "<h4>نقل البيانات من administrators إلى staff:</h4>";
            
            $migrate_query = "
                INSERT INTO staff (user_id, employee_id, phone, address, date_of_birth, gender, nationality, national_id, qualification, position, department, experience_years, hire_date, salary, bank_account, emergency_contact_name, emergency_contact_phone, status, created_at)
                SELECT user_id, employee_id, phone, address, date_of_birth, gender, nationality, national_id, qualification, position, department, experience_years, hire_date, salary, bank_account, emergency_contact_name, emergency_contact_phone, status, created_at
                FROM administrators a
                WHERE NOT EXISTS (SELECT 1 FROM staff s WHERE s.user_id = a.user_id)
            ";
            
            if ($conn->query($migrate_query)) {
                echo "<p style='color: green;'>✅ تم نقل البيانات بنجاح</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في نقل البيانات: " . $conn->error . "</p>";
            }
        }
        
        // حذف جدول administrators
        if ($tables_check['administrators']) {
            echo "<h4>حذف جدول administrators:</h4>";
            if ($conn->query("DROP TABLE IF EXISTS administrators")) {
                echo "<p style='color: green;'>✅ تم حذف جدول administrators</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في حذف جدول administrators: " . $conn->error . "</p>";
            }
        }
        
        // حذف جدول administrator_attendance إذا كان موجوداً
        $admin_attendance_check = $conn->query("SHOW TABLES LIKE 'administrator_attendance'");
        if ($admin_attendance_check->num_rows > 0) {
            echo "<h4>حذف جدول administrator_attendance:</h4>";
            if ($conn->query("DROP TABLE IF EXISTS administrator_attendance")) {
                echo "<p style='color: green;'>✅ تم حذف جدول administrator_attendance</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في حذف جدول administrator_attendance: " . $conn->error . "</p>";
            }
        }
        
        // إنشاء سجلات للمستخدمين بدور staff الذين ليس لهم سجل
        echo "<h4>إنشاء سجلات للمستخدمين بدور staff:</h4>";
        $create_records_query = "
            INSERT INTO staff (user_id, status, created_at)
            SELECT u.id, 'active', NOW()
            FROM users u
            LEFT JOIN staff s ON u.id = s.user_id
            WHERE u.role = 'staff' AND s.id IS NULL
        ";
        
        if ($conn->query($create_records_query)) {
            $affected_rows = $conn->affected_rows;
            echo "<p style='color: green;'>✅ تم إنشاء $affected_rows سجل جديد</p>";
        } else {
            echo "<p style='color: red;'>❌ فشل في إنشاء السجلات: " . $conn->error . "</p>";
        }
        
    } else {
        echo "<p style='color: red;'><strong>لا توجد بيانات في أي من الجدولين!</strong></p>";
    }
    
    // عرض النتيجة النهائية
    echo "<h3>4. النتيجة النهائية:</h3>";
    $final_count = $conn->query("SELECT COUNT(*) as count FROM staff")->fetch_assoc()['count'];
    $users_count = $conn->query("SELECT COUNT(*) as count FROM users WHERE role = 'staff'")->fetch_assoc()['count'];
    
    echo "<ul>";
    echo "<li>إجمالي السجلات في جدول staff: $final_count</li>";
    echo "<li>إجمالي المستخدمين بدور staff: $users_count</li>";
    echo "</ul>";
    
    if ($final_count == $users_count && $final_count > 0) {
        echo "<p style='color: green; font-size: 18px;'><strong>✅ تم التوحيد بنجاح! يمكنك الآن الذهاب إلى <a href='index.php'>صفحة الإداريين</a></strong></p>";
    } else {
        echo "<p style='color: orange;'><strong>⚠️ هناك عدم تطابق في البيانات. يرجى المراجعة.</strong></p>";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>حدث خطأ: " . htmlspecialchars($e->getMessage()) . "</p>";
} finally {
    // إعادة تفعيل فحص القيود الخارجية
    $conn->query("SET FOREIGN_KEY_CHECKS = 1");
    echo "<p style='color: blue;'>ℹ️ تم إعادة تفعيل فحص القيود الخارجية</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    direction: rtl;
}
h2, h3, h4 {
    color: #333;
}
ul {
    background: #f5f5f5;
    padding: 15px;
    border-radius: 5px;
}
li {
    margin: 5px 0;
}
a {
    color: #007bff;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
</style>
