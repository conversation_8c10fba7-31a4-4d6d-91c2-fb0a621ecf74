<?php
/**
 * تعديل القسط
 * Edit Installment
 */

require_once '../../includes/config.php';
require_once '../../includes/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin') && !check_permission('staff') && !has_permission('admin_access')) {
    header('Location: ../../dashboard/');
    exit();
}

$success_message = '';
$error_message = '';

// التحقق من وجود معرف القسط
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: index.php');
    exit();
}

$installment_id = intval($_GET['id']);

// جلب بيانات القسط
$installment_stmt = $conn->prepare("
    SELECT si.*, u.full_name as student_name, c.class_name, c.grade_level,
           ba.bank_name, ba.account_number, ba.iban,
           ft.type_name as fee_type_name
    FROM student_installments si
    LEFT JOIN students s ON si.student_id = s.id
    LEFT JOIN users u ON s.user_id = u.id
    LEFT JOIN classes c ON s.class_id = c.id
    LEFT JOIN bank_accounts ba ON si.bank_account_id = ba.id
    LEFT JOIN fee_types ft ON si.fee_type_id = ft.id
    WHERE si.id = ?
");

$installment_stmt->bind_param("i", $installment_id);
$installment_stmt->execute();
$installment_result = $installment_stmt->get_result();

if ($installment_result->num_rows === 0) {
    header('Location: index.php');
    exit();
}

$installment = $installment_result->fetch_assoc();

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'طلب غير صحيح';
    } else {
        $installment_type = clean_input($_POST['installment_type'] ?? '');
        $amount_requested = floatval($_POST['amount_requested'] ?? 0);
        $receipt_date = clean_input($_POST['receipt_date'] ?? '');
        $description = clean_input($_POST['description'] ?? '');
        $status = clean_input($_POST['status'] ?? 'pending');
        $bank_account_id = intval($_POST['bank_account_id'] ?? 0);
        
        // تحديد رقم القسط بناءً على النوع المختار
        $installment_number = 0;
        switch ($installment_type) {
            case 'first':
                $installment_number = 1;
                break;
            case 'second':
                $installment_number = 2;
                break;
            case 'third':
                $installment_number = 3;
                break;
            case 'complete':
                $installment_number = 0;
                break;
            default:
                $installment_number = 1;
        }
        
        // التحقق من صحة البيانات
        if (empty($installment_type)) {
            $error_message = 'يجب اختيار نوع القسط';
        } elseif ($amount_requested <= 0) {
            $error_message = 'يجب إدخال مبلغ صحيح';
        } elseif (empty($receipt_date)) {
            $error_message = 'يجب إدخال تاريخ الإيصال';
        } else {
            // التحقق من عدم تكرار البيانات (باستثناء القسط الحالي)
            $duplicate_check_stmt = $conn->prepare("
                SELECT si.id
                FROM student_installments si
                WHERE si.student_id = ? AND si.installment_number = ? AND si.id != ?
            ");
            $duplicate_check_stmt->bind_param("iii", $installment['student_id'], $installment_number, $installment_id);
            $duplicate_check_stmt->execute();
            $duplicate_result = $duplicate_check_stmt->get_result()->fetch_assoc();

            if ($duplicate_result) {
                $installment_text = '';
                switch ($installment_number) {
                    case 0: $installment_text = 'إجمالي'; break;
                    case 1: $installment_text = 'القسط الأول'; break;
                    case 2: $installment_text = 'القسط الثاني'; break;
                    case 3: $installment_text = 'القسط الثالث'; break;
                    default: $installment_text = 'القسط رقم ' . $installment_number;
                }
                
                $error_message = "لا يمكن حفظ البيانات المدخلة وذلك لتكرار البيانات.<br>";
                $error_message .= "يوجد قسط آخر بنفس البيانات: " . htmlspecialchars($installment_text);
            } else {
                // تحديث القسط
                $notes = $installment['fee_type_name'] . ': ' . $description;
                
                $update_stmt = $conn->prepare("
                    UPDATE student_installments SET
                        installment_number = ?,
                        amount = ?,
                        total_amount = ?,
                        due_date = ?,
                        status = ?,
                        notes = ?,
                        bank_account_id = ?,
                        updated_at = NOW()
                    WHERE id = ?
                ");

                $bank_account_id_param = $bank_account_id > 0 ? $bank_account_id : null;
                $update_stmt->bind_param("iddssiii", $installment_number, $amount_requested, $amount_requested, $receipt_date, $status, $notes, $bank_account_id_param, $installment_id);
                
                if ($update_stmt->execute()) {
                    $success_message = 'تم تحديث القسط بنجاح';
                    // تحديث البيانات المعروضة
                    $installment['installment_number'] = $installment_number;
                    $installment['amount'] = $amount_requested;
                    $installment['total_amount'] = $amount_requested;
                    $installment['due_date'] = $receipt_date;
                    $installment['status'] = $status;
                    $installment['notes'] = $notes;
                } else {
                    $error_message = 'خطأ في تحديث القسط: ' . $conn->error;
                }
            }
        }
    }
}

$page_title = 'تعديل القسط';
include_once '../../includes/header.php';
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">
                <i class="fas fa-edit me-2"></i>تعديل القسط
            </h1>
            <p class="text-muted mb-0">تعديل بيانات القسط رقم: <?php echo htmlspecialchars($installment['receipt_number'] ?? $installment['id']); ?></p>
        </div>
        <div>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
            </a>
        </div>
    </div>

    <!-- رسائل التنبيه -->
    <?php if (!empty($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-circle me-2"></i>
        <div><?php echo $error_message; ?></div>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <div class="row">
        <!-- نموذج التعديل -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>تعديل بيانات القسط
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        
                        <!-- معلومات الطالب (للقراءة فقط) -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">الطالب</label>
                                <input type="text" class="form-control" value="<?php echo htmlspecialchars($installment['student_name']); ?>" readonly>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">الصف</label>
                                <input type="text" class="form-control" value="<?php echo htmlspecialchars($installment['class_name'] . ' - ' . $installment['grade_level']); ?>" readonly>
                            </div>
                        </div>
                        
                        <!-- رقم الإيصال والحساب البنكي -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">رقم الإيصال</label>
                                <input type="text" class="form-control" value="<?php echo htmlspecialchars($installment['receipt_number'] ?? 'غير محدد'); ?>" readonly>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">الحساب البنكي</label>
                                <select class="form-select" name="bank_account_id">
                                    <option value="">اختر الحساب البنكي</option>
                                    <?php
                                    // جلب الحسابات البنكية النشطة
                                    $bank_accounts_query = "SELECT id, bank_name, account_number FROM bank_accounts WHERE is_active = 1 ORDER BY bank_name";
                                    $bank_accounts_result = $conn->query($bank_accounts_query);

                                    if ($bank_accounts_result && $bank_accounts_result->num_rows > 0) {
                                        while ($bank_account = $bank_accounts_result->fetch_assoc()) {
                                            $selected = ($installment['bank_account_id'] == $bank_account['id']) ? 'selected' : '';
                                            echo '<option value="' . $bank_account['id'] . '" ' . $selected . '>';
                                            echo htmlspecialchars($bank_account['account_number'] . ' - ' . $bank_account['bank_name']);
                                            echo '</option>';
                                        }
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                        
                        <!-- رقم القسط -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">رقم القسط <span class="text-danger">*</span></label>
                                <select class="form-select" name="installment_type" required>
                                    <option value="">اختر نوع القسط</option>
                                    <option value="complete" <?php if($installment['installment_number'] == 0) echo 'selected'; ?>>إجمالي</option>
                                    <option value="first" <?php if($installment['installment_number'] == 1) echo 'selected'; ?>>القسط الأول</option>
                                    <option value="second" <?php if($installment['installment_number'] == 2) echo 'selected'; ?>>القسط الثاني</option>
                                    <option value="third" <?php if($installment['installment_number'] == 3) echo 'selected'; ?>>القسط الثالث</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">الحالة</label>
                                <select class="form-select" name="status">
                                    <option value="pending" <?php if($installment['status'] == 'pending') echo 'selected'; ?>>معلق</option>
                                    <option value="paid" <?php if($installment['status'] == 'paid') echo 'selected'; ?>>مدفوع</option>
                                    <option value="overdue" <?php if($installment['status'] == 'overdue') echo 'selected'; ?>>متأخر</option>
                                    <option value="cancelled" <?php if($installment['status'] == 'cancelled') echo 'selected'; ?>>ملغي</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- المبلغ وتاريخ الاستحقاق -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">المبلغ <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" class="form-control" name="amount_requested" 
                                           min="0" step="0.01" 
                                           value="<?php echo $installment['total_amount']; ?>" required>
                                    <span class="input-group-text"><?php echo get_currency_symbol(); ?></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">تاريخ الاستحقاق <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" name="receipt_date" 
                                       value="<?php echo $installment['due_date']; ?>" required>
                            </div>
                        </div>
                        
                        <!-- الوصف -->
                        <div class="mb-3">
                            <label class="form-label">الوصف</label>
                            <textarea class="form-control" name="description" rows="3" 
                                      placeholder="وصف تفصيلي للقسط..."><?php 
                                // استخراج الوصف من الملاحظات
                                $notes = $installment['notes'] ?? '';
                                if (strpos($notes, ':') !== false) {
                                    echo htmlspecialchars(trim(substr($notes, strpos($notes, ':') + 1)));
                                } else {
                                    echo htmlspecialchars($notes);
                                }
                            ?></textarea>
                        </div>
                        
                        <!-- أزرار التحكم -->
                        <div class="d-flex justify-content-between">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ التعديلات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="col-lg-4">
            <!-- معلومات القسط -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>معلومات القسط
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <small class="text-muted">المبلغ المدفوع:</small>
                        <div><strong><?php echo format_currency($installment['paid_amount']); ?></strong></div>
                    </div>

                    <div class="mb-2">
                        <small class="text-muted">المبلغ المتبقي:</small>
                        <div><strong><?php echo format_currency($installment['total_amount'] - $installment['paid_amount']); ?></strong></div>
                    </div>
                    
                    <div class="mb-2">
                        <small class="text-muted">الحساب البنكي:</small>
                        <div><?php echo htmlspecialchars($installment['account_number'] ?? 'غير محدد'); ?></div>
                    </div>
                    
                    <div class="mb-2">
                        <small class="text-muted">تاريخ الإنشاء:</small>
                        <div><?php echo date('Y-m-d H:i', strtotime($installment['created_at'])); ?></div>
                    </div>
                    
                    <?php if ($installment['updated_at']): ?>
                    <div>
                        <small class="text-muted">آخر تحديث:</small>
                        <div><?php echo date('Y-m-d H:i', strtotime($installment['updated_at'])); ?></div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- تحذيرات -->
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>تنبيه
                    </h6>
                </div>
                <div class="card-body">
                    <p class="mb-0">
                        <small>
                            تعديل بيانات القسط قد يؤثر على التقارير المالية. 
                            تأكد من صحة البيانات قبل الحفظ.
                        </small>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include_once '../../includes/footer.php'; ?>
