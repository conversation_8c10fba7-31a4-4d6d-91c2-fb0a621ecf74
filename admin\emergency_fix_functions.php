<?php
/**
 * إصلاح طارئ لجميع تعريفات الدوال المكررة
 * Emergency Fix for All Duplicate Function Declarations
 */

// عدم تضمين functions.php لتجنب الخطأ
require_once '../includes/config.php';
require_once '../includes/database.php';

// بدء الجلسة يدوياً
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من الصلاحيات (مدير فقط)
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../dashboard/');
    exit();
}

$results = [];
$success = true;

// دالة آمنة محلية
function local_safe_html($value, $default = '') {
    return htmlspecialchars($value ?? $default, ENT_QUOTES, 'UTF-8');
}

// معالجة الإصلاح
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['emergency_fix'])) {
    try {
        $results[] = "🚨 بدء الإصلاح الطارئ لجميع تعريفات الدوال المكررة";
        
        // قائمة شاملة لجميع الملفات في مجلد admin
        $admin_dir = __DIR__;
        $files = glob($admin_dir . '/*.php');
        
        $fixed_files = 0;
        $skipped_files = 0;
        
        foreach ($files as $file_path) {
            $file_name = basename($file_path);
            
            // تخطي الملف الحالي
            if ($file_name === 'emergency_fix_functions.php') {
                continue;
            }
            
            $content = file_get_contents($file_path);
            if ($content === false) {
                $results[] = "❌ فشل في قراءة الملف: $file_name";
                $skipped_files++;
                continue;
            }
            
            $original_content = $content;
            
            // البحث عن جميع أنواع تعريفات safe_html وحذفها
            $patterns_to_remove = [
                // تعريف الدالة مع التعليق
                '/\/\/\s*دالة مساعدة.*?\n.*?function safe_html.*?\{.*?\}\s*\n/s',
                '/\/\*\*.*?\*\/\s*\nfunction safe_html.*?\{.*?\}\s*\n/s',
                
                // تعريف الدالة فقط
                '/function safe_html\s*\([^{]*\{[^}]*\}\s*\n/s',
                '/function safe_html\s*\([^{]*\{[^}]*\}/s',
                
                // تعريف الدالة مع أسطر متعددة
                '/function safe_html\s*\([^}]+\}\s*\n?/s',
                
                // التعليقات المتعلقة بالدالة
                '/\/\/\s*دالة مساعدة لتجنب خطأ htmlspecialchars مع null\s*\n/',
                '/\/\*\*[^*]*دالة آمنة[^*]*\*\/\s*\n/',
            ];
            
            $changes = 0;
            foreach ($patterns_to_remove as $pattern) {
                $new_content = preg_replace($pattern, '', $content);
                if ($new_content !== $content) {
                    $content = $new_content;
                    $changes++;
                }
            }
            
            // إزالة الأسطر الفارغة الزائدة
            $content = preg_replace('/\n\s*\n\s*\n/', "\n\n", $content);
            
            // حفظ الملف إذا تم تغييره
            if ($content !== $original_content) {
                // إنشاء نسخة احتياطية
                $backup_path = $file_path . '.emergency_backup.' . date('Y-m-d-H-i-s');
                copy($file_path, $backup_path);
                
                // كتابة المحتوى الجديد
                if (file_put_contents($file_path, $content)) {
                    $results[] = "✅ تم إصلاح: $file_name";
                    $fixed_files++;
                } else {
                    $results[] = "❌ فشل في كتابة الملف: $file_name";
                    $skipped_files++;
                }
            } else {
                $skipped_files++;
            }
        }
        
        // التحقق من وجود الدالة في functions.php
        $functions_file = '../includes/functions.php';
        $functions_content = file_get_contents($functions_file);
        
        if (strpos($functions_content, 'function safe_html') !== false) {
            $results[] = "✅ دالة safe_html موجودة في functions.php";
        } else {
            // إضافة الدالة إلى functions.php
            $safe_html_function = "\n/**\n * دالة آمنة لـ htmlspecialchars تتعامل مع القيم null\n * Safe htmlspecialchars function that handles null values\n */\nfunction safe_html(\$value, \$default = '') {\n    return htmlspecialchars(\$value ?? \$default, ENT_QUOTES, 'UTF-8');\n}\n";
            
            $functions_content .= $safe_html_function;
            
            // إنشاء نسخة احتياطية
            $backup_file = $functions_file . '.emergency_backup.' . date('Y-m-d-H-i-s');
            copy($functions_file, $backup_file);
            
            if (file_put_contents($functions_file, $functions_content)) {
                $results[] = "✅ تم إضافة دالة safe_html إلى functions.php";
            } else {
                throw new Exception("فشل في كتابة ملف functions.php");
            }
        }
        
        $results[] = "\n📊 ملخص العملية:";
        $results[] = "✅ تم إصلاح $fixed_files ملف";
        $results[] = "⚠️ تم تخطي $skipped_files ملف";
        
        $results[] = "\n🎉 تم الإصلاح الطارئ بنجاح!";
        $results[] = "📋 الآن يمكن استخدام جميع الصفحات بدون أخطاء";
        
    } catch (Exception $e) {
        $success = false;
        $results[] = "❌ خطأ: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح طارئ للدوال المكررة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>

<div class="container-fluid mt-4">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-ambulance me-2 text-danger"></i>إصلاح طارئ للدوال المكررة</h2>
            <p class="text-muted">إصلاح خطأ "Cannot redeclare safe_html()" في جميع ملفات النظام</p>
        </div>
        <a href="../settings/permissions.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>العودة
        </a>
    </div>

    <!-- تحذير طارئ -->
    <div class="alert alert-danger">
        <h5><i class="fas fa-exclamation-triangle me-2"></i>حالة طارئة!</h5>
        <p class="mb-2"><strong>خطأ:</strong> <code>Fatal error: Cannot redeclare safe_html()</code></p>
        <p class="mb-0"><strong>الحل:</strong> إزالة جميع تعريفات الدالة المكررة من جميع الملفات والاحتفاظ بتعريف واحد فقط في functions.php</p>
    </div>

    <!-- نتائج الإصلاح -->
    <?php if (!empty($results)): ?>
        <div class="card mb-4">
            <div class="card-header <?php echo $success ? 'bg-success' : 'bg-danger'; ?> text-white">
                <h5>
                    <i class="fas fa-<?php echo $success ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                    <?php echo $success ? 'تم الإصلاح بنجاح!' : 'فشل في الإصلاح'; ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="bg-dark text-light p-3 rounded" style="font-family: monospace; white-space: pre-line; max-height: 400px; overflow-y: auto;">
<?php echo implode("\n", $results); ?>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- نموذج الإصلاح -->
    <?php if (empty($results) || !$success): ?>
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h5><i class="fas fa-ambulance me-2"></i>إصلاح طارئ شامل</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>ما سيتم عمله:</h6>
                    <ol>
                        <li><strong>فحص جميع ملفات admin:</strong> البحث في جميع ملفات PHP</li>
                        <li><strong>حذف جميع تعريفات safe_html:</strong> إزالة الدوال المكررة</li>
                        <li><strong>الاحتفاظ بتعريف واحد:</strong> في functions.php فقط</li>
                        <li><strong>نسخ احتياطية طارئة:</strong> من جميع الملفات المعدلة</li>
                        <li><strong>اختبار فوري:</strong> التأكد من حل المشكلة</li>
                    </ol>
                </div>

                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>هذا الإصلاح سيقوم بـ:</h6>
                    <ul class="mb-0">
                        <li>فحص جميع ملفات .php في مجلد admin</li>
                        <li>إزالة أي تعريف للدالة safe_html</li>
                        <li>ضمان وجود الدالة في functions.php فقط</li>
                        <li>حل المشكلة نهائياً</li>
                    </ul>
                </div>

                <form method="POST">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="confirmFix" required>
                        <label class="form-check-label" for="confirmFix">
                            <strong>أؤكد أنني أريد تطبيق الإصلاح الطارئ الشامل</strong>
                        </label>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="../settings/permissions.php" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" name="emergency_fix" class="btn btn-danger" id="fixButton" disabled>
                            <i class="fas fa-ambulance me-2"></i>تطبيق الإصلاح الطارئ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    <?php endif; ?>

    <!-- تعليمات الاختبار -->
    <?php if ($success && !empty($results)): ?>
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5><i class="fas fa-clipboard-check me-2"></i>اختبار النتيجة</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <h6><i class="fas fa-thumbs-up me-2"></i>تم الإصلاح الطارئ!</h6>
                    <p class="mb-0">الآن يجب أن تعمل جميع الصفحات بدون أخطاء إعادة تعريف الدوال.</p>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">اختبر الصفحات الآن:</h6>
                        <div class="d-grid gap-2">
                            <a href="debug_permissions_table.php" class="btn btn-outline-info">
                                <i class="fas fa-bug me-2"></i>تشخيص الصلاحيات
                            </a>
                            <a href="quick_fix_permissions.php" class="btn btn-outline-success">
                                <i class="fas fa-bolt me-2"></i>إصلاح سريع للصلاحيات
                            </a>
                            <a href="fix_permissions_compatibility.php" class="btn btn-outline-warning">
                                <i class="fas fa-tools me-2"></i>إصلاح توافق الصلاحيات
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-success">يجب أن تعمل الآن:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">✅ جميع الصفحات بدون أخطاء</li>
                            <li class="list-group-item">✅ دالة safe_html متاحة من functions.php</li>
                            <li class="list-group-item">✅ لا توجد تعريفات مكررة</li>
                            <li class="list-group-item">✅ النظام يعمل بسلاسة</li>
                        </ul>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <a href="debug_permissions_table.php" class="btn btn-primary me-2">
                        <i class="fas fa-bug me-2"></i>متابعة تشخيص الصلاحيات
                    </a>
                    <a href="../settings/permissions.php" class="btn btn-success">
                        <i class="fas fa-arrow-left me-2"></i>العودة لإدارة الصلاحيات
                    </a>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
// تفعيل زر الإصلاح عند تأكيد الاختيار
document.getElementById('confirmFix')?.addEventListener('change', function() {
    document.getElementById('fixButton').disabled = !this.checked;
});
</script>

</body>
</html>
