<?php
/**
 * تقرير ملخص حضور الموظفين
 * Staff Attendance Summary Report
 */

// جلب إحصائيات الحضور للمعلمين
$teacher_stats_query = "
    SELECT 
        u.id,
        u.full_name,
        u.role,
        COUNT(ta.id) as total_days,
        SUM(CASE WHEN ta.status = 'present' THEN 1 ELSE 0 END) as present_days,
        SUM(CASE WHEN ta.status = 'absent' THEN 1 ELSE 0 END) as absent_days,
        SUM(CASE WHEN ta.status = 'late' THEN 1 ELSE 0 END) as late_days,
        SUM(CASE WHEN ta.status = 'sick_leave' THEN 1 ELSE 0 END) as sick_leave_days,
        SUM(CASE WHEN ta.status = 'regular_leave' THEN 1 ELSE 0 END) as regular_leave_days,
        SUM(CASE WHEN ta.status = 'absence_with_deduction' THEN 1 ELSE 0 END) as absence_with_deduction_days,
        ROUND((SUM(CASE WHEN ta.status = 'present' THEN 1 ELSE 0 END) / COUNT(ta.id)) * 100, 2) as attendance_rate
    FROM users u
    LEFT JOIN teachers t ON u.id = t.user_id
    LEFT JOIN teacher_attendance ta ON t.id = ta.teacher_id AND ta.attendance_date BETWEEN ? AND ?
    WHERE u.role = 'teacher' AND u.status = 'active'
    " . ($user_filter ? "AND u.id = ?" : "") . "
    GROUP BY u.id, u.full_name, u.role
    ORDER BY u.full_name
";

$teacher_params = [$date_from, $date_to];
$teacher_types = 'ss';
if ($user_filter) {
    $teacher_params[] = $user_filter;
    $teacher_types .= 'i';
}

$teacher_stmt = $conn->prepare($teacher_stats_query);
$teacher_stmt->bind_param($teacher_types, ...$teacher_params);
$teacher_stmt->execute();
$teacher_stats = $teacher_stmt->get_result();

// جلب إحصائيات الحضور للإداريين (باستثناء المديرين)
$admin_stats_query = "
    SELECT
        u.id,
        u.full_name,
        u.role,
        COUNT(aa.id) as total_days,
        SUM(CASE WHEN aa.status = 'present' THEN 1 ELSE 0 END) as present_days,
        SUM(CASE WHEN aa.status = 'absent' THEN 1 ELSE 0 END) as absent_days,
        SUM(CASE WHEN aa.status = 'late' THEN 1 ELSE 0 END) as late_days,
        SUM(CASE WHEN aa.status = 'sick_leave' THEN 1 ELSE 0 END) as sick_leave_days,
        SUM(CASE WHEN aa.status = 'regular_leave' THEN 1 ELSE 0 END) as regular_leave_days,
        SUM(CASE WHEN aa.status = 'absence_with_deduction' THEN 1 ELSE 0 END) as absence_with_deduction_days,
        ROUND((SUM(CASE WHEN aa.status = 'present' THEN 1 ELSE 0 END) / COUNT(aa.id)) * 100, 2) as attendance_rate
    FROM users u
    LEFT JOIN admin_attendance aa ON u.id = aa.admin_id AND aa.attendance_date BETWEEN ? AND ?
    WHERE u.role = 'staff' AND u.status = 'active'
    AND u.role NOT IN ('admin', 'system_admin')
    " . ($user_filter ? "AND u.id = ?" : "") . "
    GROUP BY u.id, u.full_name, u.role
    ORDER BY u.full_name
";

$admin_params = [$date_from, $date_to];
$admin_types = 'ss';
if ($user_filter) {
    $admin_params[] = $user_filter;
    $admin_types .= 'i';
}

$admin_stmt = $conn->prepare($admin_stats_query);
$admin_stmt->bind_param($admin_types, ...$admin_params);
$admin_stmt->execute();
$admin_stats = $admin_stmt->get_result();

// جلب إحصائيات الغياب بالخصم من الجدول المنفصل
$absence_deduction_query = "
    SELECT
        u.id as user_id,
        COUNT(sad.id) as absence_with_deduction_count
    FROM users u
    LEFT JOIN staff_absences_with_deduction sad ON u.id = sad.user_id
        AND DATE(sad.absence_date) BETWEEN ? AND ?
        AND sad.status = 'processed'
    WHERE u.role IN ('teacher', 'staff') AND u.status = 'active'
    " . ($user_filter ? "AND u.id = ?" : "") . "
    GROUP BY u.id
";

$absence_params = [$date_from, $date_to];
$absence_types = 'ss';
if ($user_filter) {
    $absence_params[] = $user_filter;
    $absence_types .= 'i';
}

$absence_stmt = $conn->prepare($absence_deduction_query);
$absence_stmt->bind_param($absence_types, ...$absence_params);
$absence_stmt->execute();
$absence_deduction_result = $absence_stmt->get_result();

// تحويل نتائج الغياب بالخصم إلى مصفوفة للوصول السريع
$absence_deduction_data = [];
while ($row = $absence_deduction_result->fetch_assoc()) {
    $absence_deduction_data[$row['user_id']] = $row['absence_with_deduction_count'];
}

// جلب إحصائيات الإجازات من جدول staff_leaves
$leaves_stats_query = "
    SELECT
        u.id as user_id,
        SUM(CASE WHEN sl.leave_type = 'sick' AND sl.status = 'approved' THEN DATEDIFF(sl.end_date, sl.start_date) + 1 ELSE 0 END) as sick_leave_days,
        SUM(CASE WHEN sl.leave_type = 'regular' AND sl.status = 'approved' THEN DATEDIFF(sl.end_date, sl.start_date) + 1 ELSE 0 END) as regular_leave_days,
        SUM(CASE WHEN sl.leave_type = 'emergency' AND sl.status = 'approved' THEN DATEDIFF(sl.end_date, sl.start_date) + 1 ELSE 0 END) as emergency_leave_days
    FROM users u
    LEFT JOIN staff_leaves sl ON u.id = sl.user_id
        AND ((sl.start_date BETWEEN ? AND ?) OR (sl.end_date BETWEEN ? AND ?) OR (sl.start_date <= ? AND sl.end_date >= ?))
    WHERE u.role IN ('teacher', 'staff') AND u.status = 'active'
    " . ($user_filter ? "AND u.id = ?" : "") . "
    GROUP BY u.id
";

$leaves_params = [$date_from, $date_to, $date_from, $date_to, $date_from, $date_to];
$leaves_types = 'ssssss';
if ($user_filter) {
    $leaves_params[] = $user_filter;
    $leaves_types .= 'i';
}

$leaves_stmt = $conn->prepare($leaves_stats_query);
$leaves_stmt->bind_param($leaves_types, ...$leaves_params);
$leaves_stmt->execute();
$leaves_result = $leaves_stmt->get_result();

// تحويل نتائج الإجازات إلى مصفوفة للوصول السريع
$leaves_data = [];
while ($row = $leaves_result->fetch_assoc()) {
    $leaves_data[$row['user_id']] = [
        'sick_leave_days' => $row['sick_leave_days'] ?: 0,
        'regular_leave_days' => $row['regular_leave_days'] ?: 0,
        'emergency_leave_days' => $row['emergency_leave_days'] ?: 0
    ];
}


?>

<div class="row">
    <!-- إحصائيات المعلمين -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chalkboard-teacher me-2"></i>
                    <?php echo __('teachers_attendance_summary'); ?>
                    (<?php echo date('d/m/Y', strtotime($date_from)); ?> - <?php echo date('d/m/Y', strtotime($date_to)); ?>)
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-bordered">
                        <thead class="table-dark">
                            <tr>
                                <th><?php echo __('teacher_name'); ?></th>
                                <th><?php echo __('total_days'); ?></th>
                                <th><?php echo __('present'); ?></th>
                                <th><?php echo __('absent'); ?></th>
                                <th><?php echo __('late'); ?></th>
                                <th>إجازة مرضية</th>
                                <th>إجازة اعتيادية</th>
                                <th>إجازة طارئة</th>
                                <th>غياب بالخصم</th>
                                <th><?php echo __('attendance_rate'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($teacher = $teacher_stats->fetch_assoc()): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($teacher['full_name']); ?></strong>
                                    </td>
                                    <td><?php echo $teacher['total_days'] ?: 0; ?></td>
                                    <td>
                                        <span class="badge bg-success">
                                            <?php echo $teacher['present_days'] ?: 0; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-danger">
                                            <?php echo $teacher['absent_days'] ?: 0; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning text-dark">
                                            <?php echo $teacher['late_days'] ?: 0; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-danger">
                                            <?php echo $leaves_data[$teacher['id']]['sick_leave_days'] ?? 0; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">
                                            <?php echo $leaves_data[$teacher['id']]['regular_leave_days'] ?? 0; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?php echo $leaves_data[$teacher['id']]['emergency_leave_days'] ?? 0; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning text-dark">
                                            <?php echo $absence_deduction_data[$teacher['id']] ?? 0; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php 
                                        $rate = $teacher['attendance_rate'] ?: 0;
                                        $color = $rate >= 90 ? 'success' : ($rate >= 75 ? 'warning' : 'danger');
                                        ?>
                                        <span class="badge bg-<?php echo $color; ?>">
                                            <?php echo $rate; ?>%
                                        </span>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات الإداريين -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users-cog me-2"></i>
                    <?php echo __('admins_attendance_summary'); ?>
                    (<?php echo date('d/m/Y', strtotime($date_from)); ?> - <?php echo date('d/m/Y', strtotime($date_to)); ?>)
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-bordered">
                        <thead class="table-dark">
                            <tr>
                                <th>اسم الموظف</th>
                                <th><?php echo __('total_days'); ?></th>
                                <th><?php echo __('present'); ?></th>
                                <th><?php echo __('absent'); ?></th>
                                <th><?php echo __('late'); ?></th>
                                <th>إجازة مرضية</th>
                                <th>إجازة اعتيادية</th>
                                <th>إجازة طارئة</th>
                                <th>غياب بالخصم</th>
                                <th><?php echo __('attendance_rate'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($admin = $admin_stats->fetch_assoc()): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($admin['full_name']); ?></strong>
                                    </td>
                                    <td><?php echo $admin['total_days'] ?: 0; ?></td>
                                    <td>
                                        <span class="badge bg-success">
                                            <?php echo $admin['present_days'] ?: 0; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-danger">
                                            <?php echo $admin['absent_days'] ?: 0; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning text-dark">
                                            <?php echo $admin['late_days'] ?: 0; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-danger">
                                            <?php echo $leaves_data[$admin['id']]['sick_leave_days'] ?? 0; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">
                                            <?php echo $leaves_data[$admin['id']]['regular_leave_days'] ?? 0; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?php echo $leaves_data[$admin['id']]['emergency_leave_days'] ?? 0; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning text-dark">
                                            <?php echo $absence_deduction_data[$admin['id']] ?? 0; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php 
                                        $rate = $admin['attendance_rate'] ?: 0;
                                        $color = $rate >= 90 ? 'success' : ($rate >= 75 ? 'warning' : 'danger');
                                        ?>
                                        <span class="badge bg-<?php echo $color; ?>">
                                            <?php echo $rate; ?>%
                                        </span>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>


</div>
