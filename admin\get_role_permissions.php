<?php
/**
 * API للحصول على صلاحيات الدور
 * Role Permissions API
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/functions.php';
require_once '../includes/enhanced_permissions.php';

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من الصلاحيات
check_session();
require_permission('settings_permissions', 'read');

// تعيين نوع المحتوى
header('Content-Type: application/json; charset=utf-8');

try {
    $role_id = intval($_GET['role_id'] ?? 0);
    
    if ($role_id <= 0) {
        throw new Exception('معرف الدور غير صحيح');
    }
    
    // الحصول على بيانات الدور
    $role_stmt = $conn->prepare("
        SELECT id, role_name, role_display_name, role_description, is_system_role 
        FROM custom_roles 
        WHERE id = ?
    ");
    $role_stmt->bind_param("i", $role_id);
    $role_stmt->execute();
    $role = $role_stmt->get_result()->fetch_assoc();
    
    if (!$role) {
        throw new Exception('الدور غير موجود');
    }
    
    // الحصول على صلاحيات الدور
    $permissions_stmt = $conn->prepare("
        SELECT resource_key, permission_level 
        FROM role_permissions 
        WHERE role_name = ? AND is_granted = 1
    ");
    $permissions_stmt->bind_param("s", $role['role_name']);
    $permissions_stmt->execute();
    $permissions_result = $permissions_stmt->get_result();
    
    $permissions = [];
    while ($row = $permissions_result->fetch_assoc()) {
        $permissions[$row['resource_key']] = $row['permission_level'];
    }
    
    // إرسال الاستجابة
    echo json_encode([
        'success' => true,
        'role' => $role,
        'permissions' => $permissions
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
