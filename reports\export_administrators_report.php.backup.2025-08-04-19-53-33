<?php
/**
 * تصدير تقرير الإداريين إلى Excel
 * Export Administrators Report to Excel
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    redirect_to('../dashboard/');
}

// جلب المعاملات
$admin_name = clean_input($_GET['admin_name'] ?? '');
$department_filter = clean_input($_GET['department'] ?? '');
$status_filter = clean_input($_GET['status'] ?? '');

// بناء الاستعلام
$where = [];
$params = [];
$types = '';

if ($admin_name) {
    $where[] = 'u.full_name LIKE ?';
    $params[] = "%$admin_name%";
    $types .= 's';
}
if ($department_filter) {
    $where[] = 'a.department = ?';
    $params[] = $department_filter;
    $types .= 's';
}
if ($status_filter) {
    $where[] = 'u.status = ?';
    $params[] = $status_filter;
    $types .= 's';
}

$where_sql = $where ? '(' . implode(' AND ', $where) . ')' : '1=1';

$query = "SELECT
            u.full_name,
            a.employee_id,
            a.position,
            a.department,
            a.hire_date,
            a.salary,
            u.email,
            a.phone,
            u.status,
            a.qualification,
            a.experience_years,
            a.national_id,
            a.date_of_birth,
            a.gender,
            a.nationality,
            a.bank_account,
            a.emergency_contact_name,
            a.emergency_contact_phone,
            a.address
          FROM staff a
          JOIN users u ON a.user_id = u.id
          WHERE u.role = 'staff' AND $where_sql
          ORDER BY u.full_name";

$stmt = $conn->prepare($query);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$result = $stmt->get_result();

// إعداد headers للتحميل
$filename = 'administrators_report_' . date('Y-m-d_H-i-s') . '.csv';
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Pragma: no-cache');
header('Expires: 0');

// إنشاء ملف CSV
$output = fopen('php://output', 'w');

// إضافة BOM للدعم العربي في Excel
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// كتابة العناوين
$headers = [
    'الاسم الكامل',
    'رقم الموظف',
    'المنصب',
    'القسم',
    'تاريخ التعيين',
    'الراتب',
    'البريد الإلكتروني',
    'رقم الهاتف',
    'حالة الحساب',
    'المؤهل',
    'سنوات الخبرة',
    'الرقم القومي',
    'تاريخ الميلاد',
    'الجنس',
    'الجنسية',
    'رقم الحساب البنكي',
    'جهة الاتصال في الطوارئ',
    'هاتف جهة الاتصال',
    'العنوان'
];

fputcsv($output, $headers);

// كتابة البيانات
while ($row = $result->fetch_assoc()) {
    $data = [
        $row['full_name'],
        $row['employee_id'] ?? '',
        $row['position'] ?? '',
        $row['department'] ?? '',
        $row['hire_date'] ? date('Y-m-d', strtotime($row['hire_date'])) : '',
        $row['salary'] ? number_format($row['salary'], 2) : '',
        $row['email'],
        $row['phone'] ?? '',
        __($row['status']),
        $row['qualification'] ?? '',
        $row['experience_years'] ?? '0',
        $row['national_id'] ?? '',
        $row['date_of_birth'] ? date('Y-m-d', strtotime($row['date_of_birth'])) : '',
        $row['gender'] ? __($row['gender']) : '',
        $row['nationality'] ?? '',
        $row['bank_account'] ?? '',
        $row['emergency_contact_name'] ?? '',
        $row['emergency_contact_phone'] ?? '',
        $row['address'] ?? ''
    ];
    
    fputcsv($output, $data);
}

// تسجيل النشاط
log_activity($_SESSION['user_id'], 'export_administrators_report', 'reports', null, null, [
    'filters' => [
        'admin_name' => $admin_name,
        'department' => $department_filter,
        'status' => $status_filter
    ]
]);

fclose($output);
exit();
?>
