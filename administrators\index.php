<?php
/**
 * صفحة الإداريين المحسنة
 * Enhanced Administrators Page
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/database.php';
require_once '../includes/security.php';

// التحقق من تسجيل الدخول والصلاحيات
check_session();
if (!check_permission('admin')) {
    redirect_to('../dashboard/');
}

$page_title = __('administrators');

// معالجة البحث والفلترة
$search = trim($_GET['search'] ?? '');
$department_filter = trim($_GET['department'] ?? '');
$status_filter = trim($_GET['status'] ?? '');

// بناء شروط البحث
$where_conditions = [];
$params = [];
$types = '';

if (!empty($search)) {
    $where_conditions[] = "(u.full_name LIKE ? OR u.username LIKE ? OR u.email LIKE ? OR a.employee_id LIKE ? OR a.national_id LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param]);
    $types .= "sssss";
}

if (!empty($department_filter)) {
    $where_conditions[] = "a.department = ?";
    $params[] = $department_filter;
    $types .= "s";
}

if (!empty($status_filter)) {
    $where_conditions[] = "u.status = ?";
    $params[] = $status_filter;
    $types .= "s";
}

$where_clause = !empty($where_conditions) ? '(' . implode(' AND ', $where_conditions) . ')' : '1=1';

// عد إجمالي السجلات
$count_query = "
    SELECT COUNT(*) as total
    FROM staff a
    JOIN users u ON a.user_id = u.id
    WHERE u.role = 'staff' AND $where_clause
";

$count_stmt = $conn->prepare($count_query);
if (!empty($params)) {
    $count_stmt->bind_param($types, ...$params);
}
$count_stmt->execute();
$total_records = $count_stmt->get_result()->fetch_assoc()['total'];

// إعدادات الترقيم
$page = intval($_GET['page'] ?? 1);
$records_per_page = ITEMS_PER_PAGE;
$total_pages = ceil($total_records / $records_per_page);
$offset = ($page - 1) * $records_per_page;

// جلب الإداريين
$query = "
    SELECT
        a.*,
        u.username,
        u.email,
        u.status,
        u.last_login,
        u.created_at as user_created_at,
        COALESCE(u.full_name, u.username) as display_name
    FROM staff a
    JOIN users u ON a.user_id = u.id
    WHERE u.role = 'staff' AND $where_clause
    ORDER BY u.username ASC
    LIMIT ? OFFSET ?
";

$stmt = $conn->prepare($query);
if (!$stmt) {
    die("SQL Error: " . $conn->error . "<br>Query: " . htmlspecialchars($query));
}
$params[] = $records_per_page;
$params[] = $offset;
$types .= "ii";

if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$administrators = $stmt->get_result();

// جلب قائمة الأقسام للفلترة
$departments = $conn->query("SELECT DISTINCT department FROM staff WHERE department IS NOT NULL ORDER BY department");

// إحصائيات سريعة
$stats_query = "
    SELECT
        COUNT(*) as total_administrators,
        SUM(CASE WHEN u.status = 'active' THEN 1 ELSE 0 END) as active_administrators,
        COUNT(DISTINCT a.department) as total_departments,
        AVG(DATEDIFF(CURDATE(), a.hire_date) / 365.25) as avg_experience
    FROM staff a
    JOIN users u ON a.user_id = u.id
    WHERE u.role = 'staff'
";
$stats_result = $conn->query($stats_query);
$stats = $stats_result->fetch_assoc();

require_once '../includes/header.php';
?>

<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-user-tie text-primary me-2"></i>
                <?php echo __('administrators'); ?>
            </h2>
            <p class="text-muted mb-0"><?php echo __('manage_administrators'); ?></p>
        </div>
        <div>
            <a href="add.php" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i><?php echo __('add_administrator'); ?>
            </a>
            <a href="import.php" class="btn btn-success">
                <i class="fas fa-file-import me-2"></i><?php echo __('import_administrators'); ?>
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['total_administrators'] ?? 0); ?></h4>
                            <p class="mb-0"><?php echo __('total_administrators'); ?></p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-tie fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['active_administrators'] ?? 0); ?></h4>
                            <p class="mb-0"><?php echo __('active_administrators'); ?></p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['total_departments'] ?? 0); ?></h4>
                            <p class="mb-0"><?php echo __('departments'); ?></p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-building fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['avg_experience'] ?? 0, 1); ?></h4>
                            <p class="mb-0"><?php echo __('avg_experience_years'); ?></p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label"><?php echo __('search'); ?></label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?php echo htmlspecialchars($search); ?>" 
                           placeholder="<?php echo __('search_administrators'); ?>">
                </div>
                <div class="col-md-3">
                    <label for="department" class="form-label"><?php echo __('department'); ?></label>
                    <select class="form-select" id="department" name="department">
                        <option value=""><?php echo __('all_departments'); ?></option>
                        <?php while ($dept = $departments->fetch_assoc()): ?>
                            <option value="<?php echo htmlspecialchars($dept['department']); ?>" 
                                    <?php echo $department_filter === $dept['department'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($dept['department']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label"><?php echo __('status'); ?></label>
                    <select class="form-select" id="status" name="status">
                        <option value=""><?php echo __('all_statuses'); ?></option>
                        <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>><?php echo __('active'); ?></option>
                        <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>><?php echo __('inactive'); ?></option>
                        <option value="suspended" <?php echo $status_filter === 'suspended' ? 'selected' : ''; ?>><?php echo __('suspended'); ?></option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i><?php echo __('search'); ?>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Administrators Table -->
    <div class="card">
        <div class="card-body">
            <?php if ($administrators->num_rows > 0): ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th><?php echo __('administrator_info'); ?></th>
                                <th><?php echo __('position'); ?></th>
                                <th><?php echo __('department'); ?></th>
                                <th><?php echo __('contact_info'); ?></th>
                                <th><?php echo __('hire_date'); ?></th>
                                <th><?php echo __('status'); ?></th>
                                <th><?php echo __('last_login'); ?></th>
                                <th class="no-print"><?php echo __('actions'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($admin = $administrators->fetch_assoc()): ?>
                                <tr>
                                    <td>
                                        <?php if (!empty($admin['profile_picture'])): ?>
                                            <img src="../uploads/profiles/<?php echo $admin['profile_picture']; ?>" 
                                                 alt="<?php echo htmlspecialchars($admin['display_name']); ?>"
                                                 class="rounded-circle" 
                                                 width="40" 
                                                 height="40">
                                        <?php else: ?>
                                            <div class="bg-secondary rounded-circle d-inline-flex align-items-center justify-content-center" 
                                                 style="width: 40px; height: 40px;">
                                                <i class="fas fa-user text-white"></i>
                                            </div>
                                        <?php endif; ?>
                                        <div class="ms-2 d-inline-block">
                                            <div class="fw-bold"><?php echo htmlspecialchars($admin['display_name']); ?></div>
                                            <small class="text-muted"><?php echo htmlspecialchars($admin['employee_id'] ?? '-'); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?php echo htmlspecialchars($admin['position'] ?? '-'); ?></span>
                                    </td>
                                    <td><?php echo htmlspecialchars($admin['department'] ?? '-'); ?></td>
                                    <td>
                                        <div><?php echo htmlspecialchars($admin['email']); ?></div>
                                        <?php if (!empty($admin['phone'])): ?>
                                            <small class="text-muted"><?php echo htmlspecialchars($admin['phone']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($admin['hire_date'])): ?>
                                            <?php echo date('Y-m-d', strtotime($admin['hire_date'])); ?>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $status_class = match($admin['status']) {
                                            'active' => 'success',
                                            'inactive' => 'secondary',
                                            'suspended' => 'danger',
                                            default => 'secondary'
                                        };
                                        ?>
                                        <span class="badge bg-<?php echo $status_class; ?>">
                                            <?php echo __($admin['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if (!empty($admin['last_login'])): ?>
                                            <small><?php echo date('Y-m-d H:i', strtotime($admin['last_login'])); ?></small>
                                        <?php else: ?>
                                            <span class="text-muted"><?php echo __('never'); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="no-print">
                                        <div class="btn-group" role="group">
                                            <a href="view.php?id=<?php echo $admin['id']; ?>" 
                                               class="btn btn-sm btn-outline-info" 
                                               title="<?php echo __('view'); ?>">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="edit.php?id=<?php echo $admin['id']; ?>" 
                                               class="btn btn-sm btn-outline-primary" 
                                               title="<?php echo __('edit'); ?>">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button"
                                                    class="btn btn-sm btn-outline-danger"
                                                    title="<?php echo __('delete'); ?>"
                                                    onclick="deleteAdministrator(<?php echo $admin['id']; ?>, '<?php echo htmlspecialchars($admin['display_name'], ENT_QUOTES); ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo ($page - 1); ?>&search=<?php echo urlencode($search); ?>&department=<?php echo urlencode($department_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                                        <?php echo __('previous'); ?>
                                    </a>
                                </li>
                            <?php endif; ?>

                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&department=<?php echo urlencode($department_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>

                            <?php if ($page < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo ($page + 1); ?>&search=<?php echo urlencode($search); ?>&department=<?php echo urlencode($department_filter); ?>&status=<?php echo urlencode($status_filter); ?>">
                                        <?php echo __('next'); ?>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>

            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-user-tie fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted"><?php echo __('no_administrators_found'); ?></h5>
                    <p class="text-muted"><?php echo __('try_different_search'); ?></p>
                    <a href="add.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i><?php echo __('add_first_administrator'); ?>
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function deleteAdministrator(id, name) {
    if (confirm('هل أنت متأكد من حذف الإداري: ' + name + '؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
        // إنشاء نموذج مخفي لإرسال طلب POST
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = 'delete.php?id=' + id;

        var input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'confirm_delete';
        input.value = '1';

        form.appendChild(input);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php require_once '../includes/footer.php'; ?>
